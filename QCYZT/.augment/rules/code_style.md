---
type: "always_apply"
description: "代码风格规范"
---
# Objective-C 代码风格规范

## 设计原则和组件复用规范

### 单一职责原则 (SRP)
- 每个类只负责一个功能领域
- 避免在单个类中创建大量相似的UI组件
- 将相似功能的组件提取为独立的自定义View

### 开闭原则 (OCP) 
- 优先使用继承和协议扩展功能，而非修改现有代码
- 创建可配置的通用组件，而非硬编码的重复组件

### 合成复用原则 (CRP)
- **强制要求**：当需要创建3个或以上相似UI组件时，必须：
  - 创建自定义View类封装通用逻辑
  - 使用数据模型驱动组件创建
  - 通过循环或数组方式批量创建组件
- **禁止**：硬编码创建大量相似的Label、Button等组件

### 组件复用实践
```objective-c
// ❌ 错误做法：创建多个相似Label
UILabel *label1 = [[UILabel alloc] init...];
UILabel *label2 = [[UILabel alloc] init...];
UILabel *label3 = [[UILabel alloc] init...];

// ✅ 正确做法：创建自定义View + 数据驱动
@interface StockIndexView : UIView
- (void)configureWithModel:(StockIndexModel *)model;
@end

// 批量创建
NSArray *indexData = @[model1, model2, model3];
for (StockIndexModel *model in indexData) {
    StockIndexView *indexView = [[StockIndexView alloc] init];
    [indexView configureWithModel:model];
    [containerView addSubview:indexView];
}
```

### 代码复用检查清单
- [ ] 是否有3个以上相似的UI组件？→ 考虑创建自定义View
- [ ] 是否可以用数据模型驱动组件创建？
- [ ] 是否可以通过循环减少重复代码？
- [ ] 新功能是否可以通过扩展现有组件实现？

## 核心编程规范

### 控件作用域规范
- **固定文本控件规范**：如果控件显示的是固定文本内容（如静态标题、固定提示语等），应创建为局部变量，不要设置为属性或实例变量
- **动态内容控件规范**：只有需要动态更新内容或被其他方法访问的控件才设置为属性

```objective-c
// ❌ 错误做法：固定文本也设置为属性
@property (nonatomic, strong) UILabel *titleLabel; // 只显示"股票行情"

// ✅ 正确做法：固定文本使用局部变量
- (void)setupViews {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"股票行情"; // 固定文本，创建为局部变量
    
    // 需要动态更新的控件设置为属性
    self.stockPriceLabel.text = @"加载中..."; // 会动态更新价格
}
```

### 方法暴露规范
- **私有方法规范**：类内部使用的方法不要在.h文件中声明，保持接口简洁
- **公开方法规范**：只在.h文件中暴露需要被外部调用的方法
- **内部方法规范**：私有方法可以在.m文件的类扩展(Category)中声明，或直接在实现中定义

```objective-c
// ❌ 错误做法：在.h文件中暴露内部方法
@interface StockViewController : UIViewController
- (void)setupViews;          // 内部使用，不应暴露
- (void)configureLayout;     // 内部使用，不应暴露
- (void)loadStockData;       // 如果只内部调用，不应暴露
@end

// ✅ 正确做法：只暴露外部需要的方法
@interface StockViewController : UIViewController
- (void)refreshStockData;    // 外部可能需要调用
@end

// 在.m文件中处理私有方法
@interface StockViewController ()
- (void)setupViews;          // 私有方法在类扩展中声明
- (void)configureLayout;
@end
```

## 项目特定规范

### 创建文件时头部注释
- **重要**：创建日期必须使用实际创建文件的真实日期

```objective-c
//
//  ClassName.h
//  QCYZT
//
//  Created by Augment on {当前日期，格式YYYY-MM-DD}
//  Copyright © {当前年份} {公司名称}. All rights reserved.
//
```

### 命名约定
- 协议使用Delegate或DataSource后缀（如：TableViewDelegate、DataSourceProtocol）
- 通知名称使用模块+动作+Notification格式（如：UserLoginDidSuccessNotification）
- 图片资源使用模块名_功能名_类型格式（如：login_button_normal、home_icon_selected）

### 代码质量要求
- 方法实现不应过长，保持单一职责（建议不超过50行）
- 避免深层嵌套条件语句（最多3层）
- 避免使用magic number，定义常量

## 开发最佳实践

### 代码分析和修改流程
- **修改前必须先分析**：了解相关代码的完整上下文
- **渐进式修改**：将大的修改任务分解为小的、可验证的步骤
- **保持一致性**：新代码必须遵循项目现有的设计模式和编码风格

### 信息收集最佳实践
```objective-c
// 在修改网络请求相关代码前，先了解：
// 1. HttpRequestTool的所有方法和使用方式
// 2. 相关业务模块的现有API实现
// 3. 参数处理和回调的标准模式

// 在创建UI组件前，先了解：
// 1. 项目中现有的自定义View实现
// 2. 通用的UI创建方法和工具宏
// 3. 布局和样式的统一规范
```

### 代码复用检查增强
- [ ] 搜索是否已有类似实现？
- [ ] 是否可以扩展现有组件而非重新创建？
- [ ] 新功能是否符合项目的架构模式？
- [ ] 是否遵循了项目的命名和组织规范？
