[{
    "name": "up_market_stock_trade_tab_titles",
    "item": [
        "五档",
        "明细"
    ]
},
{
    "name": "up_market_stock_trade_none_l2_tab_titles",
    "item": [
        "五档",
        "明细"
    ]
},
{
    "name": "up_market_stock_trade_l2_tab_titles",
    "item": [
        "十档",
        "逐笔"
    ]
},
{
    "name": "up_market_sdk_five_sell_titles",
    "item": [
        "卖五",
        "卖四",
        "卖三",
        "卖二",
        "卖一"
    ]
},
{
    "name": "up_market_sdk_five_buy_titles",
    "item": [
        "买一",
        "买二",
        "买三",
        "买四",
        "买五"
    ]
},
{
    "name": "up_market_sdk_ten_sell_titles",
    "item": [
        "卖十",
        "卖九",
        "卖八",
        "卖七",
        "卖六",
        "卖五",
        "卖四",
        "卖三",
        "卖二",
        "卖一"
    ]
},
{
    "name": "up_market_sdk_ten_buy_titles",
    "item": [
        "买一",
        "买二",
        "买三",
        "买四",
        "买五",
        "买六",
        "买七",
        "买八",
        "买九",
        "买十"
    ]
},
{
    "name": "up_market_sdk_index_names",
    "item": [
        "沪",
        "深",
        "创"
    ]
},
{
    "name": "up_market_sdk_index_setCodes",
    "item": [
        "1",
        "0",
        "0"
    ]
},
{
    "name": "up_market_sdk_index_codes",
    "item": [
        "000001",
        "399001",
        "399006"
    ]
},
{
    "name": "up_market_sdk_hk_index_names",
    "item": [
        "沪",
        "深"
    ]
},
{
    "name": "up_market_sdk_hk_index_setCodes",
    "item": [
        "1",
        "0"
    ]
},
{
    "name": "up_market_sdk_hk_index_codes",
    "item": [
        "000001",
        "399001"
    ]
},
]
