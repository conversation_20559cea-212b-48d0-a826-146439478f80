//
//  UPMarket2StockSettingOtherView.h
//  UPMarket2
//
//  Created by fang on 2020/3/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingOtherView;

@protocol UPMarket2StockSettingOtherViewDelegate <NSObject>

- (void)up_market2StockSettingOtherView:(UPMarket2StockSettingOtherView *)view didClickKLineType:(UPMarket2StockSettingType)type;

- (void)up_market2StockSettingOtherView:(UPMarket2StockSettingOtherView *)view didClickKLineType:(UPMarket2StockSettingType)type atSegmentIndex:(NSInteger)index;

- (void)up_market2StockSettingOtherView:(UPMarket2StockSettingOtherView *)view didClickKLineType:(UPMarket2StockSettingType)type switchControlChangedOn:(BOOL)isOn;

- (void)up_market2StockSettingOtherViewSelectedDefaultHomeSetting:(UPMarket2StockSettingOtherView *)view;

@end

@interface UPMarket2StockSettingOtherView : UIView

@property (nonatomic, weak) id<UPMarket2StockSettingOtherViewDelegate> delegate;

@property (nonatomic, strong) NSArray *dataSetArr;

- (void)reloadData;

@property (nonatomic, assign, readonly) BOOL isChanged;

@end

NS_ASSUME_NONNULL_END
