//
//  UPMarket2StockSettingDetailSwitchCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/31.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailSwitchCell.h"

@interface UPMarket2StockSettingDetailSwitchCell ()<UITextFieldDelegate>
{
    UPMarket2StockSettingDetailDataSet *_dataSet;
}

@property (nonatomic, strong) UITextField *valueTextField;

@property (nonatomic, strong) UILabel *unitLabel;

@property (nonatomic, strong) UISwitch *switchControl;

@property (nonatomic, strong) UIButton *switchButton;

@end

@implementation UPMarket2StockSettingDetailSwitchCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupUI];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = UIColor.up_contentBgColor;
    [self.contentView addSubview:self.valueTextField];
    [self.contentView addSubview:self.unitLabel];
//    [self.contentView addSubview:self.switchControl];
    [self.contentView addSubview:self.switchButton];

    [self layoutContraints];
}

- (void)layoutContraints {
    [self.valueTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
        make.height.mas_equalTo(UPWidth(31));
        make.width.mas_equalTo(UPWidth(51));
    }];
    
    [self.unitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.valueTextField.mas_right).mas_offset(UPWidth(15));
    }];
    
//    [self.switchControl mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerY.equalTo(self.contentView);
//        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
//        make.height.mas_equalTo(UPWidth(31));
//        make.width.mas_equalTo(UPHeight(51));
//    }];
    [self.switchButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.height.mas_equalTo(UPWidth(25));
        make.width.mas_equalTo(UPHeight(44));
    }];
}

- (void)configUIWithDataSet:(UPMarket2StockSettingDetailDataSet *)dataSet {
    if (dataSet.switchDataSet) {
        _dataSet = dataSet;
        self.valueTextField.text = [NSString stringWithFormat:@"%zd", dataSet.switchDataSet.value];
        self.unitLabel.text = dataSet.switchDataSet.unit;
//        self.switchControl.on = dataSet.switchDataSet.isOn;
        self.switchButton.selected = dataSet.switchDataSet.isOn;

    }
}

- (void)switchControlChanged:(UISwitch *)sender {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingDetailSwitchCell:switchOn:)]) {
        [self.delegate up_market2StockSettingDetailSwitchCell:self switchOn:sender.on];
    }
}

- (void)switchButtonChanged:(UIButton *)sender {
    
    sender.selected = !sender.selected;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingDetailSwitchCell:switchOn:)]) {
        [self.delegate up_market2StockSettingDetailSwitchCell:self switchOn:sender.isSelected];
    }
}


/// MARK: UITextFieldDelegate
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    
    NSString *resultStr = [textField.text stringByReplacingCharactersInRange:range withString:string];
    NSInteger resultInt = [resultStr integerValue];
    // 若输入一位数后数值将超出范围则无法输入后一位数字
    if (resultInt > _dataSet.switchDataSet.maxValue) {
        return NO;
    }
    
    if (resultInt < _dataSet.switchDataSet.minValue) {
        resultInt = _dataSet.switchDataSet.minValue;
    }
    
    return YES;
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    
    NSString *targetStr = textField.text;
    NSInteger targetInt = [targetStr integerValue];
    if (!IsValidateString(targetStr)) {
        targetInt = _dataSet.switchDataSet.defaultValue;
        textField.text = [NSString stringWithFormat:@"%zd", targetInt];
    } else {
        if (targetInt < _dataSet.switchDataSet.minValue) {
            targetInt = _dataSet.switchDataSet.minValue;
            textField.text = [NSString stringWithFormat:@"%zd", _dataSet.switchDataSet.minValue];
        }
        if (targetInt > _dataSet.switchDataSet.maxValue) {
            targetInt = _dataSet.switchDataSet.maxValue;
            textField.text = [NSString stringWithFormat:@"%zd", _dataSet.switchDataSet.maxValue];
        }
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingDetailSwitchCell:valueChanged:)]) {
        [self.delegate up_market2StockSettingDetailSwitchCell:self valueChanged:targetInt];
    }
}

/// MARK: Lazy loading
- (UITextField *)valueTextField {
    if (!_valueTextField) {
        _valueTextField = [[UITextField alloc] init];
        _valueTextField.font = [UIFont up_fontOfSize:UPWidth(16)];
        _valueTextField.textColor = UIColor.up_textPrimaryColor;
        _valueTextField.textAlignment = NSTextAlignmentCenter;
        _valueTextField.layer.cornerRadius = UPWidth(15);
        _valueTextField.layer.masksToBounds = YES;
        _valueTextField.layer.borderColor = UIColor.upmarketui_maskCandleColor.CGColor;
        _valueTextField.layer.borderWidth = 0.5f;
        _valueTextField.keyboardType = UIKeyboardTypeNumberPad;
        _valueTextField.delegate = self;
    }
    return _valueTextField;
}

- (UILabel *)unitLabel {
    if (!_unitLabel) {
        _unitLabel = [[UILabel alloc] init];
        _unitLabel.textColor = UIColor.up_textSecondaryColor;
        _unitLabel.font = [UIFont up_fontOfSize:UPWidth(16)];
    }
    return _unitLabel;
}

- (UISwitch *)switchControl {
    if (!_switchControl) {
        _switchControl = [[UISwitch alloc] init];
//        _switchControl.onTintColor = UIColor.up_brandColor;
        _switchControl.onImage = UPTImg(@"指标设置/switchOn");
        _switchControl.offImage = UPTImg(@"指标设置/switchOff");
        [_switchControl addTarget:self action:@selector(switchControlChanged:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _switchControl;
}

- (UIButton *)switchButton {
    if (!_switchButton) {
        _switchButton = [[UIButton alloc] init];
        [_switchButton setImage:UPTImg(@"指标设置/switchOff") forState:UIControlStateNormal];
        [_switchButton setImage:UPTImg(@"指标设置/switchOn") forState:UIControlStateSelected];
        [_switchButton addTarget:self action:@selector(switchButtonChanged:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _switchButton;
}

@end
