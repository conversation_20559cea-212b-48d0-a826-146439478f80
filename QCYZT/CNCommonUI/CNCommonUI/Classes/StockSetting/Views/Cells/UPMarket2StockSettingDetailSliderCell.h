//
//  UPMarket2StockSettingDetailSliderCell.h
//  UPMarket2
//
//  Created by 方恒 on 2020/3/30.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingDetailDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingDetailSliderCell;

@protocol UPMarket2StockSettingDetailSliderCellDelegate <NSObject>

@optional
- (void)up_market2StockSettingDetailSliderCell:(UPMarket2StockSettingDetailSliderCell *)cell valueChanged:(NSInteger)value;

@end

@interface UPMarket2StockSettingDetailSliderCell : UITableViewCell

+ (NSString *)cellIdentifier;

- (void)configUIWithDataSet:(UPMarket2StockSettingDetailDataSet *)dataSet;

@property (nonatomic, weak) id<UPMarket2StockSettingDetailSliderCellDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
