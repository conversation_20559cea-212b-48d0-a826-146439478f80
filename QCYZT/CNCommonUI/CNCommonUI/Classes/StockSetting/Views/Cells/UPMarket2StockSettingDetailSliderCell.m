//
//  UPMarket2StockSettingDetailSliderCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/30.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailSliderCell.h"

@interface UPMarket2StockSettingDetailSliderCell ()<UITextFieldDelegate> {
    UPMarket2StockSettingDetailDataSet *_dataSet;
}

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UITextField *valueTextField;

@property (nonatomic, strong) UILabel *unitLabel;

@property (nonatomic, strong) UILabel *minValueLabel;

@property (nonatomic, strong) UISlider *slider;

@property (nonatomic, strong) UILabel *maxValueLabel;

@end

@implementation UPMarket2StockSettingDetailSliderCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = UIColor.up_contentBgColor;
    [self.contentView addSubview:self.stackView];
    [self.stackView addArrangedSubview:self.titleLabel];
    [self.stackView addArrangedSubview:self.valueTextField];
    [self.stackView addArrangedSubview:self.unitLabel];
    [self.contentView addSubview:self.minValueLabel];
    [self.contentView addSubview:self.slider];
    [self.contentView addSubview:self.maxValueLabel];
    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
        make.right.equalTo(self.minValueLabel.mas_left).offset(UPWidth(-15));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_lessThanOrEqualTo(UPWidth(80));
    }];
    
    [self.valueTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.width.mas_equalTo(UPWidth(51));
        make.height.mas_equalTo(UPWidth(31));
    }];
    
    [self.unitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.maxValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.width.mas_equalTo(UPWidth(30));
    }];
    
    [self.slider mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.maxValueLabel.mas_left).mas_offset(-UPWidth(8));
//        make.width.mas_equalTo(UPWidth(125));
        make.height.mas_equalTo(UPWidth(25));
    }];
    
    [self.minValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.slider.mas_left).mas_offset(-UPWidth(8));
    }];
    
    [self.unitLabel setContentHuggingPriority:(UILayoutPriorityRequired) forAxis:(UILayoutConstraintAxisHorizontal)];
}

- (void)configUIWithDataSet:(UPMarket2StockSettingDetailDataSet *)dataSet {
    _dataSet = dataSet;
    if (dataSet.sliderDataSet) {
        self.titleLabel.text = dataSet.sliderDataSet.title;
        self.valueTextField.text = [NSString stringWithFormat:@"%zd", dataSet.sliderDataSet.value];
        self.unitLabel.text = dataSet.sliderDataSet.unit;
        self.unitLabel.hidden = !IsValidateString(dataSet.sliderDataSet.unit);
        self.minValueLabel.text = [NSString stringWithFormat:@"%zd", dataSet.sliderDataSet.minValue];
        self.maxValueLabel.text = [NSString stringWithFormat:@"%zd", dataSet.sliderDataSet.maxValue];
        self.slider.minimumValue = dataSet.sliderDataSet.minValue;
        self.slider.maximumValue = dataSet.sliderDataSet.maxValue;
        self.slider.value = dataSet.sliderDataSet.value;
        if (dataSet.sliderDataSet.title == nil || [dataSet.sliderDataSet.title isEqualToString:@""]) {
            [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(0);
            }];
        }
    }
}

- (void)sliderValueChanged:(UISlider *)slider {
    float value = slider.value;
    if (!_dataSet.sliderDataSet) {
        return;
    }
    _dataSet.sliderDataSet.value = value;
    // textField的text同步slider的value
    self.valueTextField.text = [NSString stringWithFormat:@"%zd", _dataSet.sliderDataSet.value];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingDetailSliderCell:valueChanged:)]) {
        [self.delegate up_market2StockSettingDetailSliderCell:self valueChanged:value];
    }
}

/// MARK: UITextFieldDelegate
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    
    NSString *resultStr = [textField.text stringByReplacingCharactersInRange:range withString:string];
    NSInteger resultInt = [resultStr integerValue];
    // 若输入一位数后数值将超出范围则无法输入后一位数字
    if (resultInt > _dataSet.sliderDataSet.maxValue) {
        return NO;
    }
    
    if (resultInt < _dataSet.sliderDataSet.minValue) {
        resultInt = _dataSet.sliderDataSet.minValue;
    }
    // slider的value同步textField的text
    _slider.value = resultInt;
    return YES;
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    
    NSString *targetStr = textField.text;
    NSInteger targetInt = [targetStr integerValue];
    
    if (!IsValidateString(targetStr)) {
        targetInt = _dataSet.sliderDataSet.defaultValue;
        textField.text = [NSString stringWithFormat:@"%zd", targetInt];
    } else {
        if (targetInt < _dataSet.sliderDataSet.minValue) {
            targetInt = _dataSet.sliderDataSet.minValue;
            textField.text = [NSString stringWithFormat:@"%zd", _dataSet.sliderDataSet.minValue];
        }
        if (targetInt > _dataSet.sliderDataSet.maxValue) {
            targetInt = _dataSet.sliderDataSet.maxValue;
            textField.text = [NSString stringWithFormat:@"%zd", _dataSet.sliderDataSet.maxValue];
        }
    }
    
    _slider.value = targetInt;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingDetailSliderCell:valueChanged:)]) {
        [self.delegate up_market2StockSettingDetailSliderCell:self valueChanged:targetInt];
    }
}

/// MARK: Lazy loading
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textSecondaryColor;
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(16)];
        _titleLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _titleLabel;
}

- (UITextField *)valueTextField {
    if (!_valueTextField) {
        _valueTextField = [[UITextField alloc] init];
        _valueTextField.font = [UIFont up_fontOfSize:UPWidth(16)];
        _valueTextField.textColor = UIColor.up_textPrimaryColor;
        _valueTextField.textAlignment = NSTextAlignmentCenter;
        _valueTextField.layer.cornerRadius = UPWidth(15);
        _valueTextField.layer.borderColor = UIColor.upmarketui_maskCandleColor.CGColor;
        _valueTextField.layer.borderWidth = 0.5f;
        _valueTextField.keyboardType = UIKeyboardTypeNumberPad;
        _valueTextField.delegate = self;
    }
    return _valueTextField;
}

- (UILabel *)unitLabel {
    if (!_unitLabel) {
        _unitLabel = [[UILabel alloc] init];
        _unitLabel.textColor = UIColor.up_textSecondaryColor;
        _unitLabel.font = [UIFont up_fontOfSize:UPWidth(16)];
    }
    return _unitLabel;
}

- (UILabel *)maxValueLabel {
    if (!_maxValueLabel) {
        _maxValueLabel = [[UILabel alloc] init];
        _maxValueLabel.textColor = UIColor.up_textPrimaryColor;
        _maxValueLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
    }
    return _maxValueLabel;
}

- (UISlider *)slider {
    if (!_slider) {
        _slider = [[UISlider alloc] init];
        [_slider setThumbImage:UPTImg(@"指标设置/指标设置-滑块") forState:UIControlStateNormal];
        _slider.minimumTrackTintColor = UIColor.up_controlSelectedColor;
        [_slider addTarget:self action:@selector(sliderValueChanged:) forControlEvents:UIControlEventValueChanged];
    }
    return _slider;
}

- (UILabel *)minValueLabel {
    if (!_minValueLabel) {
        _minValueLabel = [[UILabel alloc] init];
        _minValueLabel.textColor = UIColor.up_textPrimaryColor;
        _minValueLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
    }
    return _minValueLabel;
}


- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [UIStackView new];
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.distribution = UIStackViewDistributionFill;
        _stackView.alignment = UIStackViewAlignmentCenter;
        _stackView.spacing = 8;
    }
    return _stackView;
}

@end
