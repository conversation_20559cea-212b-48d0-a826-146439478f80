//
//  UPMarket2StockSettingMubanCell.m
//  UPMarket2
//
//  Created by lizhixiang on 2023/9/22.
//

#import "UPMarket2StockSettingMubanCell.h"
#import "UPMarket2StockSettingTagView.h"
#import "UPMarket2StockSettingManager.h"

@interface UPMarket2StockSettingMubanCell ()

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIButton *rightBtn;


@property (nonatomic, strong) UPMarket2StockSettingCellDataSet *dataSet;

@property (nonatomic, strong) UIButton *switchButton;

@end
@implementation UPMarket2StockSettingMubanCell
+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.titleLabel];
    [self.contentView addSubview:self.rightBtn];
    [self.contentView addSubview:self.switchButton];

    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self).mas_offset(UPWidth(15));
    }];
    [self.switchButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.height.mas_equalTo(UPWidth(25));
        make.width.mas_equalTo(UPHeight(44));
    }];
    
    [self.rightBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.switchButton.mas_left).mas_offset(-16);
        make.width.mas_equalTo(20);
        make.height.mas_equalTo(20);
    }];

}

- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet {
    self.dataSet = dataSet;
    self.titleLabel.text = dataSet.title;
    
    self.switchButton.hidden = self.rightBtn.hidden = dataSet.muBanId <= 0;
}

- (void)btn_click:(UIButton *)btn {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingMubanDeleteCell:)]) {
        [self.delegate up_market2StockSettingMubanDeleteCell:self];
    }
}

    /// MARK: Lazy loading
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
    }
    return _titleLabel;
}


- (UIButton *)rightBtn {
    if (!_rightBtn) {
        _rightBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _rightBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        [_rightBtn setImage:UPTImg(@"指标设置/删除模板") forState:UIControlStateNormal];
        [_rightBtn addTarget:self action:@selector(btn_click:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _rightBtn;
}



- (void)renameButtonChanged:(UIButton *)sender {
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingMuBanRenameCell:)]) {
        [self.delegate up_market2StockSettingMuBanRenameCell:self];
    }
}

- (UIButton *)switchButton {
    if (!_switchButton) {
        _switchButton = [[UIButton alloc] init];
        [_switchButton setImage:UPTImg(@"指标设置/重命名模板") forState:UIControlStateNormal];
        [_switchButton addTarget:self action:@selector(renameButtonChanged:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _switchButton;
}

@end
