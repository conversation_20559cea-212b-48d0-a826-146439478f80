//
//  UPMarket2StockSettingSwitchCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/30.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingSwitchCell.h"

@interface UPMarket2StockSettingSwitchCell ()
{
    UPMarket2StockSettingCellDataSet *_dataSet;
}

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UILabel *minorLabel;

@property (nonatomic, strong) UIButton *helpBtn;

@property (nonatomic, strong) UISwitch *switchControl;

@property (nonatomic, strong) UIButton *switchButton;

@end

@implementation UPMarket2StockSettingSwitchCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupUI];
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.titleLabel];
    [self.contentView addSubview:self.minorLabel];
    [self.contentView addSubview:self.helpBtn];
//    [self.contentView addSubview:self.switchControl];
    [self.contentView addSubview:self.switchButton];

    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self).mas_offset(UPWidth(15));
    }];
    [self.minorLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.titleLabel.mas_right).mas_offset(UPWidth(10));
    }];
//    [self.switchControl mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerY.equalTo(self.contentView);
//        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
//        make.width.mas_equalTo(UPWidth(51));
//        make.height.mas_equalTo(UPWidth(31));
//    }];
    
    [self.switchButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.width.mas_equalTo(UPWidth(44));
        make.height.mas_equalTo(UPWidth(25));
    }];
    
    [self.helpBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
//        make.right.equalTo(self.switchControl.mas_left).mas_offset(-12);
        make.right.equalTo(self.switchButton.mas_left).mas_offset(-12);
        make.width.height.mas_equalTo(20);
    }];
}

- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet {
    _dataSet = dataSet;
    
    self.titleLabel.text = dataSet.title;
    if (dataSet.switchDataSet) {
        self.switchButton.selected = dataSet.switchDataSet.on;
        self.minorLabel.text = dataSet.switchDataSet.minorTitle;
        self.helpBtn.hidden = dataSet.switchDataSet.hideHelpBtn;
    }
}

- (void)btn_click:(UIButton *)btn {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingSwitchCell:didClickHelpBtn:)]) {
        [self.delegate up_market2StockSettingSwitchCell:self didClickHelpBtn:btn];
    }
}

- (void)switchControlChanged:(UISwitch *)sender {
    _dataSet.switchDataSet.on = sender.isOn;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingSwitchCell:switchControlChangedOn:)]) {
        [self.delegate up_market2StockSettingSwitchCell:self switchControlChangedOn:sender.isOn];
    }
}

- (void)switchButtonChanged:(UIButton *)sender {
    
    sender.selected = !sender.selected;
    _dataSet.switchDataSet.on = sender.selected;
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingSwitchCell:switchControlChangedOn:)]) {
        [self.delegate up_market2StockSettingSwitchCell:self switchControlChangedOn:sender.isSelected];
    }
}

/// MARK: Lazy loading
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.font = [UIFont up_fontOfSize:15];
    }
    return _titleLabel;
}

- (UILabel *)minorLabel {
    if (!_minorLabel) {
        _minorLabel = [[UILabel alloc] init];
        _minorLabel.textColor = UIColor.up_textSecondaryColor;
    }
    return _minorLabel;
}

- (UIButton *)helpBtn {
    if (!_helpBtn) {
        _helpBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_helpBtn setImage:UPTImg(@"指标设置/指标设置-问号") forState:UIControlStateNormal];
        [_helpBtn addTarget:self action:@selector(btn_click:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _helpBtn;
}

//- (UISwitch *)switchControl {
//    if (!_switchControl) {
//        _switchControl = [[UISwitch alloc] init];
////        _switchControl.onTintColor = UIColor.up_brandColor;
//        _switchControl.onImage = UPTImg(@"指标设置/switchOn");
//        _switchControl.offImage = UPTImg(@"指标设置/switchOff");
//        [_switchControl addTarget:self action:@selector(switchControlChanged:) forControlEvents:UIControlEventValueChanged];
//    }
//    return _switchControl;
//}



- (UIButton *)switchButton {
    if (!_switchButton) {
        _switchButton = [[UIButton alloc] init];
        [_switchButton setImage:UPTImg(@"指标设置/switchOff") forState:UIControlStateNormal];
        [_switchButton setImage:UPTImg(@"指标设置/switchOn") forState:UIControlStateSelected];
        [_switchButton addTarget:self action:@selector(switchButtonChanged:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _switchButton;
}


@end
