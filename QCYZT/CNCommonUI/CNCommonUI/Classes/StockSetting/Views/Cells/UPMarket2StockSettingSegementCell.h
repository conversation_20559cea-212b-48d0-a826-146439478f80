//
//  UPMarket2StockSettingSegementCell.h
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingSegementCell;

@protocol UPMarket2StockSettingSegementCellDelegate <NSObject>

@optional
- (void)up_market2StockSettingSegementCell:(UPMarket2StockSettingSegementCell *)cell didClickHelpBtn:(UIButton *)btn;

- (void)up_market2StockSettingSegementCell:(UPMarket2StockSettingSegementCell *)cell didClickSegementIndex:(NSInteger)index;

@end

@interface UPMarket2StockSettingSegementCell : UITableViewCell

+ (NSString *)cellIdentifier;

- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet;

@property (nonatomic, weak) id<UPMarket2StockSettingSegementCellDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
