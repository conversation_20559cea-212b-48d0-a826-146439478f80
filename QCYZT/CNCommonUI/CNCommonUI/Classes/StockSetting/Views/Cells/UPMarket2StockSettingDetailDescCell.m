//
//  UPMarket2StockSettingDetailDescCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/30.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailDescCell.h"

@interface UPMarket2StockSettingDetailDescCell ()

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIImageView *chartImageView;

@property (nonatomic, strong) UILabel *descLabel;

@end

@implementation UPMarket2StockSettingDetailDescCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupUI];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = UIColor.up_contentBgColor;
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.chartImageView];
    [self.contentView addSubview:self.descLabel];
    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.contentView).mas_offset(UPWidth(15));
    }];
    
    [self.chartImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(UPWidth(5));
    }];
    
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.top.equalTo(self.chartImageView.mas_bottom).mas_offset(UPWidth(15));
        make.bottom.equalTo(self.contentView).mas_offset(-UPWidth(10));
    }];
}

- (void)configUIWithTitle:(NSString *)title imageNamed:(NSString *)imageNamed desc:(NSString *)desc strongTitleArr:(NSArray *)strongTitleArr {
    if (title) {
        self.titleLabel.text = title;
        [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(self.contentView).mas_offset(UPWidth(15));
        }];
    } else {
        [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
           	make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
            make.top.equalTo(self.contentView).mas_offset(UPWidth(0));
            make.height.mas_equalTo(0);
        }];
    }
    if (imageNamed) {
        self.chartImageView.image = UPTImg(imageNamed);
        CGFloat topMargin = title ? UPWidth(5) : UPWidth(15);
        [self.chartImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
            make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
            make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(topMargin);
        }];
    } else {
        [self.chartImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
            make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
            make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(UPWidth(0));
            make.height.mas_equalTo(0);
        }];
    }
    //格式调整
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    /**调行间距*/
    style.lineSpacing = UPWidth(10);
    style.alignment = NSTextAlignmentLeft;
    NSData *htmlData = [desc dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                              NSCharacterEncodingDocumentAttribute: @(NSUTF8StringEncoding)};
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString  alloc] initWithString:desc];
    if ([desc containsString:@"<p>"]) {
        attributedString = [[NSMutableAttributedString alloc] initWithData:htmlData
                                                                   options:options
                                                        documentAttributes:nil
                                                                     error:nil];
    }
    
    [attributedString addAttributes:@{NSForegroundColorAttributeName: UIColor.up_textSecondaryColor, NSFontAttributeName: [UIFont up_fontOfSize:UPWidth(14)],NSParagraphStyleAttributeName: style} range:NSMakeRange(0, attributedString.length)];
    
    for (NSString *strongTitle in strongTitleArr) {
        NSRange range = [desc rangeOfString:strongTitle];
        [attributedString addAttributes:@{NSForegroundColorAttributeName: UIColor.up_textPrimaryColor, NSFontAttributeName: [UIFont up_boldFontOfSize:UPWidth(14)]} range:range];
    }
    self.descLabel.attributedText = attributedString;
}

- (void)configUIWithDataSet:(UPMarket2StockSettingDetailDataSet *)dataSet {
    if (dataSet.descDataSet) {
        [self configUIWithTitle:dataSet.descDataSet.title imageNamed:dataSet.descDataSet.imageNamed desc:dataSet.descDataSet.desc strongTitleArr:dataSet.descDataSet.strongTitleArr];
    }
}

/// MARK: Lazy loading
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(11)];
    }
    return _titleLabel;
}

- (UIImageView *)chartImageView {
    if (!_chartImageView) {
        _chartImageView = [[UIImageView alloc] init];
        _chartImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _chartImageView;
}

- (UILabel *)descLabel {
    if (!_descLabel) {
        _descLabel = [[UILabel alloc] init];
        _descLabel.textColor = UIColor.up_textSecondaryColor;
        _descLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
        _descLabel.lineBreakMode = NSLineBreakByCharWrapping;
        _descLabel.numberOfLines = 0;
    }
    return _descLabel;
}

@end
