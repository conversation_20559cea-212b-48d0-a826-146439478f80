//
//  UPMarket2StockSettingDetailTipCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/30.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailTipCell.h"

@interface UPMarket2StockSettingDetailTipCell ()

@property (nonatomic, strong) UILabel *tipLabel;

@end

@implementation UPMarket2StockSettingDetailTipCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    [self.contentView addSubview:self.tipLabel];
    self.backgroundColor = UIColor.upmarket2_settingHeaderColor;
    [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
    }];
}

- (void)configUIWithTip:(NSString *)tip {
    self.tipLabel.text = tip;
}

- (void)configUIWithDataSet:(UPMarket2StockSettingDetailDataSet *)dataSet {
    if (dataSet.tipDataSet) {
        self.tipLabel.text = dataSet.tipDataSet.title;
    }
}

/// MARK: Lazy loading
- (UILabel *)tipLabel {
    if (!_tipLabel) {
        _tipLabel = [[UILabel alloc] init];
        _tipLabel.textColor = UIColor.up_textSecondary1Color;
        _tipLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
    }
    return _tipLabel;
}

@end
