//
//  UPMarket2StockSettingStepperCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingStepperCell.h"
#import "UPMarket2StepperView.h"

@interface UPMarket2StockSettingStepperCell ()<UPMarket2StepperViewDelegate>
{
    UPMarket2StockSettingCellDataSet *_dataSet;
}

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UPMarket2StepperView *stepperView;

@end

@implementation UPMarket2StockSettingStepperCell

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.titleLabel];
    [self.contentView addSubview:self.stepperView];
    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self).mas_offset(UPWidth(15));
    }];
    
    [self.stepperView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.width.mas_equalTo(UPWidth(150));
        make.height.mas_equalTo(UPWidth(30));
    }];
}

- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet {
    _dataSet = dataSet;
    
    self.titleLabel.text = dataSet.title;
    if (dataSet.stepperDataSet) {
        self.stepperView.value = dataSet.stepperDataSet.value;
        self.stepperView.minValue = dataSet.stepperDataSet.minValue;
        self.stepperView.maxValue = dataSet.stepperDataSet.maxValue;
        self.stepperView.stepValue = dataSet.stepperDataSet.stepValue;
        self.stepperView.disable = dataSet.disable;
    }
}

/// MARK: UPMarket2StepperViewDelegate
- (void)up_market2StepperView:(UPMarket2StepperView *)view valueChanged:(NSUInteger)value {
    if (_dataSet.stepperDataSet) {
        _dataSet.stepperDataSet.value = value;
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingStepperCell:stepperValueChanged:)]) {
        [self.delegate up_market2StockSettingStepperCell:self stepperValueChanged:value];
    }
}

/// MARK: Lazy loading
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
    }
    return _titleLabel;
}

- (UPMarket2StepperView *)stepperView {
    if (!_stepperView) {
        _stepperView = [[UPMarket2StepperView alloc] init];
        _stepperView.delegate = self;
    }
    return _stepperView;
}

@end
