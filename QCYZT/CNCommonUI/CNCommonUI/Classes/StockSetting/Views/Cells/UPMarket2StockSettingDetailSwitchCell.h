//
//  UPMarket2StockSettingDetailSwitchCell.h
//  UPMarket2
//
//  Created by 方恒 on 2020/3/31.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingDetailDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingDetailSwitchCell;

@protocol UPMarket2StockSettingDetailSwitchCellDelegate <NSObject>

@optional
- (void)up_market2StockSettingDetailSwitchCell:(UPMarket2StockSettingDetailSwitchCell *)cell valueChanged:(NSInteger)value;
- (void)up_market2StockSettingDetailSwitchCell:(UPMarket2StockSettingDetailSwitchCell *)cell switchOn:(BOOL)isOn;

@end

@interface UPMarket2StockSettingDetailSwitchCell : UITableViewCell

+ (NSString *)cellIdentifier;

- (void)configUIWithDataSet:(UPMarket2StockSettingDetailDataSet *)dataSet;

@property (nonatomic, weak) id<UPMarket2StockSettingDetailSwitchCellDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
