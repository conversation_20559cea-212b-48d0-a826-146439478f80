//
//  UPMarket2StockSettingSectionHeaderView.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/30.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingSectionHeaderView.h"

@interface UPMarket2StockSettingSectionHeaderView ()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *settingLabel;
@property (nonatomic, strong) UILabel *dragLabel;
@property (nonatomic, strong) UILabel *showLabel;

@end

@implementation UPMarket2StockSettingSectionHeaderView

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = UIColor.upmarket2_settingHeaderColor;
    [self addSubview:self.titleLabel];
    [self addSubview:self.settingLabel];
    [self addSubview:self.dragLabel];
    [self addSubview:self.showLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self.mas_safeAreaLayoutGuideLeft).offset(UPWidth(15));
    }];
    
    [self.dragLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.mas_right).offset(UPWidth(-15));
//        make.width.mas_equalTo(@(22));
      
    }];
    
    [self.showLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.dragLabel.mas_left).offset(UPWidth(-15));
        make.width.mas_equalTo(UPWidth(51));
        make.height.mas_equalTo(UPWidth(31));
    }];
    
    [self.settingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.showLabel.mas_left).offset(UPWidth(-15));
//        make.width.mas_equalTo(@(22));

    }];
}

- (void)configTitle:(NSString *)title {
    self.titleLabel.text = title;
    if ([title isEqualToString:@"指标模板设置"]) {
        self.settingLabel.text = @"删除";
        self.showLabel.text = @"重命名";
//        self.dragLabel.hidden = YES;
    }
}

- (void)configTitle:(NSString *)title isTubao:(BOOL) istubiao {
    [self configTitle:title];
    self.settingLabel.hidden = YES;
    self.showLabel.hidden = YES;
    self.dragLabel.hidden = YES;

}


/// MARK: Lazy loading
///
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textSecondaryColor;
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
    }
    return _titleLabel;
}

- (UILabel *)settingLabel {
    if (!_settingLabel) {
        _settingLabel = [[UILabel alloc] init];
        _settingLabel.textColor = UIColor.up_textSecondaryColor;
        _settingLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _settingLabel.text = @"设置";

    }
    return _settingLabel;
}


- (UILabel *)dragLabel {
    if (!_dragLabel) {
        _dragLabel = [[UILabel alloc] init];
        _dragLabel.textColor = UIColor.up_textSecondaryColor;
        _dragLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _dragLabel.text = @"拖动";
    }
    return _dragLabel;
}

- (UILabel *)showLabel {
    if (!_showLabel) {
        _showLabel = [[UILabel alloc] init];
        _showLabel.textColor = UIColor.up_textSecondaryColor;
        _showLabel.textAlignment = NSTextAlignmentCenter;
        _showLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _showLabel.text = @"显示";

    }
    return _showLabel;
}


@end
