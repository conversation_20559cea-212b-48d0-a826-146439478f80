//
//  UPMarket2StockSettingSwitchCell.h
//  UPMarket2
//
//  Created by 方恒 on 2020/3/30.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingSwitchCell;

@protocol UPMarket2StockSettingSwitchCellDelegate <NSObject>

@optional
- (void)up_market2StockSettingSwitchCell:(UPMarket2StockSettingSwitchCell *)cell didClickHelpBtn:(UIButton *)btn;

- (void)up_market2StockSettingSwitchCell:(UPMarket2StockSettingSwitchCell *)cell switchControlChangedOn:(BOOL)isOn;

@end

@interface UPMarket2StockSettingSwitchCell : UITableViewCell

+ (NSString *)cellIdentifier;

- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet;

@property (nonatomic, weak) id<UPMarket2StockSettingSwitchCellDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
