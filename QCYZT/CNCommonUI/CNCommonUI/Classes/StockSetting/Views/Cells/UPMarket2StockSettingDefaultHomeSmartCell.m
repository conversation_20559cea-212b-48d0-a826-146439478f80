//
//  UPMarket2StockSettingDefaultHomeSmartCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/31.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDefaultHomeSmartCell.h"

@interface UPMarket2StockSettingDefaultHomeSmartCell ()

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UILabel *detailLabel;

@property (nonatomic, strong) UIImageView *selectedIcon;

@end

@implementation UPMarket2StockSettingDefaultHomeSmartCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupUI];
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.detailLabel];
    [self.contentView addSubview:self.selectedIcon];
    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView.mas_centerY).mas_offset(-UPWidth(11));
        make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
    }];
    
    [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).mas_offset(UPWidth(15));
        make.top.equalTo(self.titleLabel.mas_bottom).mas_offset(UPWidth(10));
//        make.right.equalTo(self.selectedIcon.mas_left).mas_offset(UPWidth(0));
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(0));
    }];
    
    [self.selectedIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.width.mas_equalTo(UPWidth(14));
        make.height.mas_equalTo(UPWidth(12));
    }];
}

- (void)setSelect:(BOOL)select {
    self.selectedIcon.hidden = !select;
}

- (void)configUIWithDataSet:(UPMarket2StockSettingDefaultHomeDataSet *)dataSet {
    self.titleLabel.text = dataSet.title;
    self.detailLabel.text = dataSet.detailTitle;
    self.selectedIcon.hidden = !dataSet.isSelected;
}

    /// MARK: Lazy loading
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(16)];
    }
    return _titleLabel;
}

- (UILabel *)detailLabel {
    if (!_detailLabel) {
        _detailLabel = [[UILabel alloc] init];
        _detailLabel.textColor = UIColor.up_textSecondaryColor;
        _detailLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
    }
    return _detailLabel;
}

- (UIImageView *)selectedIcon {
    if (!_selectedIcon) {
        _selectedIcon = [[UIImageView alloc] initWithImage:UPTImg(@"指标设置/指标设置-选择")];
    }
    return _selectedIcon;
}

@end
