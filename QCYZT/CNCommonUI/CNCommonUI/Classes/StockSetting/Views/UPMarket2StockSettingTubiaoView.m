//
//  UPMarket2StockSettingTubiaoView.m
//  UPMarket2
//
//  Created by lizhixiang on 2023/3/17.
//

#import "UPMarket2StockSettingTubiaoView.h"
#import "UPMarket2StockSettingCellDataSet.h"
#import "UPMarket2StockSettingSwitchCell.h"
#import "UPMarket2StockSettingSectionHeaderView.h"
#import "UPMarket2StockSettingSegementCell.h"
#import "UPMarket2StockSettingStepperCell.h"
#import "UPMarket2StockSettingSingleBtnCell.h"
#import "UPMarket2StockSettingSectionHeaderView.h"
#import "UPMarket2StockSettingSwitchCell.h"
#import "UPMarket2StockSettingManager.h"


@interface UPMarket2StockSettingTubiaoView ()<UITableViewDelegate, UITableViewDataSource,UPMarket2StockSettingSwitchCellDelegate>

@property (nonatomic, strong) UITableView *tableView;

@end

@implementation UPMarket2StockSettingTubiaoView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        self.backgroundColor = UIColor.upmarket2_settingHeaderColor;
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

- (void)reloadData {
    [self.tableView reloadData];
}


/// MARK: UITableViewDelegate, UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataSetArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSDictionary *dict = self.dataSetArr[section];
    NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
    return rowItems.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSDictionary *dict = self.dataSetArr[indexPath.section];
    NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
    
    UPMarket2StockSettingCellDataSet *dataSet = rowItems[indexPath.row];
    
    if (dataSet.cellStyle == UPMarket2StockSettingCellStyleSwitch) {
        UPMarket2StockSettingSwitchCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingSwitchCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
   
    else {
        NSAssert(NO, @"check dataSet cellStyle");
        return nil;
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    NSDictionary *dict = self.dataSetArr[section];
    NSString *sectionTitle = dict[kUPMarket2StockSettingSectionTitleKey];
    UPMarket2StockSettingSectionHeaderView *sectionHeaderView = [[UPMarket2StockSettingSectionHeaderView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth(tableView.bounds), UPWidth(29))];
    [sectionHeaderView configTitle:sectionTitle isTubao:YES];
    return sectionHeaderView;
}


- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return UPWidth(29);
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UPWidth(50);
}


/// MARK: UPMarket2StockSettingSwitchCellDelegate
- (void)up_market2StockSettingSwitchCell:(UPMarket2StockSettingSwitchCell *)cell didClickHelpBtn:(UIButton *)btn {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    [self handleJumpEventForDataSet:dataSet];
}

- (void)up_market2StockSettingSwitchCell:(UPMarket2StockSettingSwitchCell *)cell switchControlChangedOn:(BOOL)isOn {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingTubiaoView:didClickKLineType:switchControlChangedOn:)]) {
        [self.delegate up_market2StockSettingTubiaoView:self didClickKLineType:dataSet.type switchControlChangedOn:isOn];
    }
}

/// MARK: Private
/// 处理点击设置或帮助按钮事件
- (void)handleJumpEventForDataSet:(UPMarket2StockSettingCellDataSet *)dataSet  {
//    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingOtherView:didClickKLineType:)]) {
//        [self.delegate up_market2StockSettingOtherView:self didClickKLineType:dataSet.type];
//    }
}

- (UPMarket2StockSettingCellDataSet *)dataSetForCell:(UITableViewCell *)cell {
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    NSDictionary *dict = self.dataSetArr[indexPath.section];
    NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
    UPMarket2StockSettingCellDataSet *dataSet = rowItems[indexPath.row];
    return dataSet;
}

- (BOOL)isChanged {
    __block BOOL tempChanged = NO;
    for (NSDictionary *dict in self.dataSetArr) {
        NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
        for (UPMarket2StockSettingCellDataSet *dataSet in rowItems) {
            if (dataSet.isChanged) {
                tempChanged = YES;
                break;
            }
        }
    }
    return tempChanged;
}

/// MARK: Lazy loading
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.tableFooterView = [[UIView alloc] init];
        _tableView.backgroundColor = UIColor.upmarket2_settingHeaderColor;
        _tableView.separatorColor = UIColor.up_dividerColor;

        [_tableView registerClass:[UPMarket2StockSettingSwitchCell class] forCellReuseIdentifier:[UPMarket2StockSettingSwitchCell cellIdentifier]];
    }
    return _tableView;
}

- (void)dealloc {
    _tableView.delegate = nil;
    _tableView.dataSource = nil;
}


@end
