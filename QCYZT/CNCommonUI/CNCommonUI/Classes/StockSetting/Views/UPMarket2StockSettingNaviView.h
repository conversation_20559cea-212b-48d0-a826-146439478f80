//
//  UPMarket2StockSettingNaviView.h
//  UPMarket2
//
//  Created by 方恒 on 2020/6/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingNaviView;

@protocol UPMarket2StockSettingNaviViewDelegate <NSObject>

- (void)up_market2StockSettingNaviView:(UPMarket2StockSettingNaviView *)naviView didClickedBackBtn:(UIButton *)btn;

- (void)up_market2StockSettingNaviView:(UPMarket2StockSettingNaviView *)naviView didSelectTabIndex:(NSInteger)index;

@end

@interface UPMarket2StockSettingNaviView : UIView

@property (nonatomic, weak) id<UPMarket2StockSettingNaviViewDelegate> delegate;

@property(nonatomic, assign) NSUInteger selectedSegmentIndex;

@property (nonatomic, assign) BOOL isHSA;


@end

NS_ASSUME_NONNULL_END
