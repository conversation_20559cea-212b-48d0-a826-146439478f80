//
//  UPMarket2SegmentView.h
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//


NS_ASSUME_NONNULL_BEGIN

@interface UPMarket2SegementAppearance : NSObject

// 选中背景色
@property (nonatomic, strong) UIColor *selectedBgColor;

// 未选中背景色
@property (nonatomic, strong) UIColor *normalBgColor;

// 选中状态文字颜色
@property (nonatomic, strong) UIColor *selectedTextColor;

// 未选中状态文字颜色
@property (nonatomic, strong) UIColor *normalTextColor;

// 字体
@property (nonatomic, strong) UIFont *font;

// 间隔线宽
@property (nonatomic, assign) CGFloat dividerWidth;

// 边框颜色
@property (nonatomic, strong) UIColor *borderColor;

@end

@class UPMarket2SegmentView;

@protocol UPMarket2SegmentViewDelegate <NSObject>

- (void)upMarkett2SegementView:(UPMarket2SegmentView *)segementView didClickIndex:(NSInteger)index;

@end

@interface UPMarket2SegmentView : UPBaseView

@property (nonatomic, weak) id<UPMarket2SegmentViewDelegate> delegate;

@property (nonatomic, copy) NSArray *items;

// 外观对象
@property (nonatomic, strong) UPMarket2SegementAppearance *appearance;

@property(nonatomic, assign) NSUInteger selectedSegmentIndex;

- (instancetype)initWithItems:(nullable NSArray *)items appearance:(UPMarket2SegementAppearance *)appearance;

@end

NS_ASSUME_NONNULL_END
