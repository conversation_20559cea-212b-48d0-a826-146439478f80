//
//  UPMarket2StockSettingNaviView.m
//  UPMarket2
//
//  Created by 方恒 on 2020/6/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingNaviView.h"
#import "UPMarket2SegmentView.h"

@interface UPMarket2StockSettingNaviView ()<UPMarket2SegmentViewDelegate>

@property (nonatomic, strong) UIButton *backBtn;

@property (nonatomic, strong) UPMarket2SegmentView *naviTitleView;

@property (nonatomic, strong) NSArray *dataArr;

@end

@implementation UPMarket2StockSettingNaviView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
//        [self setupUI];
    }
    return self;
}


- (void)setIsHSA:(BOOL)isHSA {
    _isHSA = isHSA;
    self.dataArr = @[@"分时", @"K线",@"其他"];
    [self setupUI];
}

- (void)setupUI {
    self.backgroundColor = UIColor.up_titleBarBgColor;
    
    self.backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.backBtn setImage:UPTImg(@"指标设置/指标设置-返回") forState:UIControlStateNormal];
    [self.backBtn addTarget:self action:@selector(backBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    UPMarket2SegementAppearance *appearance = [[UPMarket2SegementAppearance alloc] init];
    self.naviTitleView = [[UPMarket2SegmentView alloc] initWithItems:self.dataArr appearance:appearance];
    self.naviTitleView.frame = CGRectMake(0, 0, UPWidth(210.f), UPWidth(27.0f));
    self.naviTitleView.delegate = self;
    
    [self addSubview:self.backBtn];
    [self addSubview:self.naviTitleView];
    
    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).mas_offset(12);
        make.width.height.mas_equalTo(30);
        make.bottom.equalTo(self).mas_offset(-7);
    }];
    
    [self.naviTitleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backBtn);
        make.centerX.equalTo(self);
        make.width.mas_equalTo(UPWidth(210));
        make.height.mas_equalTo(UPWidth(27));
    }];
}


- (void)backBtnClicked:(UIButton *)btn {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingNaviView:didClickedBackBtn:)]) {
        [self.delegate up_market2StockSettingNaviView:self didClickedBackBtn:btn];
    }
}

- (void)upMarkett2SegementView:(UPMarket2SegmentView *)segementView didClickIndex:(NSInteger)index {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingNaviView:didSelectTabIndex:)]) {
        [self.delegate up_market2StockSettingNaviView:self didSelectTabIndex:index];
    }
}

- (void)setSelectedSegmentIndex:(NSUInteger)selectedSegmentIndex {
    self.naviTitleView.selectedSegmentIndex = selectedSegmentIndex;
}

- (NSUInteger)selectedSegmentIndex {
    return self.naviTitleView.selectedSegmentIndex;
}

@end
