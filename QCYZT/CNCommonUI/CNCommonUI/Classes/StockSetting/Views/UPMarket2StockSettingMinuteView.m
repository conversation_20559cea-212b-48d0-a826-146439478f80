//
//  UPMarket2StockSettingMinuteView.m
//  UPMarket2
//
//  Created by fang on 2020/3/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingMinuteView.h"
#import "UPMarket2StockSettingSegementCell.h"
#import "UPMarket2StockSettingStepperCell.h"
#import "UPMarket2StockSettingSingleBtnCell.h"
#import "UPMarket2StockSettingSectionHeaderView.h"
#import "UPMarket2StockSettingMubanCell.h"
#import "UPMarket2StockSettingManager.h"

@interface UPMarket2StockSettingMinuteView ()<UITableViewDelegate, UITableViewDataSource,UITextFieldDelegate, UPMarket2StockSettingSingleBtnCellDelegate, UPMarket2StockSettingSegementCellDelegate, UPMarket2StockSettingStepperCellDelegate,UPMarket2StockSettingMubanCellDelegate> {
    CGFloat _ketboradHeight;
    NSInteger _mubanId;
}

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, assign) BOOL isSorted;

@property (nonatomic, strong) UPPopupView *pop;
@property (nonatomic, strong) UIView *cmmView;
@property (nonatomic, strong) UITextField *newtextFiled;


@end

@implementation UPMarket2StockSettingMinuteView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        self.backgroundColor = UIColor.up_listBgColor;
        
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(keyboardWillShow:)
                                                     name:UIKeyboardWillShowNotification
                                                   object:nil];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

- (void)reloadSettingWhere {
    if (self.isIgnoreScroll) {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.isTS) {
            [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:1] atScrollPosition:(UITableViewScrollPositionTop) animated:NO];
        } else {
            [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:(self.dataSetArr.count -1)] atScrollPosition:(UITableViewScrollPositionTop) animated:NO];

        }
    });
    

}


/// MARK: UITableViewDelegate, UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataSetArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSDictionary *dict = self.dataSetArr[section];
    NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
    return rowItems.count;
}


- (void)extractedMuban:(UPMarket2StockSettingMubanCell *)cell dataSet:(UPMarket2StockSettingCellDataSet *)dataSet {
    [cell configUIWithDataSet:dataSet];
}

- (void)extracted:(UPMarket2StockSettingSingleBtnCell *)cell dataSet:(UPMarket2StockSettingCellDataSet *)dataSet {
    [cell configUIWithDataSet:dataSet];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSDictionary *dict = self.dataSetArr[indexPath.section];
    NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
    
    UPMarket2StockSettingCellDataSet *dataSet = rowItems[indexPath.row];
    if ([dataSet.title isEqualToString:@"显示缺口"]) {
        NSLog(@"%@",dataSet);
    }
    
    if (dataSet.cellStyle == UPMarket2StockSettingCellStyleSegment) {
        UPMarket2StockSettingSegementCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingSegementCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingCellStyleStepper) {
        UPMarket2StockSettingStepperCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingStepperCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingCellStyleSingleBtn) {
        UPMarket2StockSettingSingleBtnCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingSingleBtnCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        //特殊逻辑 因为分时和k线是一个indexID
        if (dataSet.indexID == UPMarketUIMinorIndexVOL) {
            dataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleHelp;
         }
        [self extracted:cell dataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingCellStyleMuBan) {
        UPMarket2StockSettingMubanCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingMubanCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        [self extractedMuban:cell dataSet:dataSet];
        return cell;
    }
    else {
        NSAssert(NO, @"check dataSet cellStyle");
        return nil;
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    NSDictionary *dict = self.dataSetArr[section];
    NSString *sectionTitle = dict[kUPMarket2StockSettingSectionTitleKey];
    UPMarket2StockSettingSectionHeaderView *sectionHeaderView = [[UPMarket2StockSettingSectionHeaderView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth(tableView.bounds), UPWidth(29))];
    if (section == 0) {
        [sectionHeaderView configTitle:sectionTitle isTubao:YES];

    } else {
        [sectionHeaderView configTitle:sectionTitle];

    }
    return sectionHeaderView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return UPWidth(29);
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UPWidth(50);
}


- (BOOL)tableView:(UITableView *)tableView canMoveRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section ==0) {
        return NO;
    }
    return YES;
}

- (void)tableView:(UITableView *)tableView moveRowAtIndexPath:(NSIndexPath *)sourceIndexPath toIndexPath:(NSIndexPath *)destinationIndexPath {
    NSMutableArray *muarr = [self.dataSetArr mutableCopy];
    if (sourceIndexPath.section == destinationIndexPath.section) {
        
        NSDictionary *dict = self.dataSetArr[sourceIndexPath.section];
        NSMutableArray *dictnew = [dict mutableCopy];

        NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
        NSMutableArray *newrowArr = [rowItems mutableCopy];
        
            // 从数组中读取需要移动行的数据
            id  object = [newrowArr objectAtIndex:sourceIndexPath.row];
            // 在数组中移动需要移动的行的数据
            [newrowArr removeObjectAtIndex:sourceIndexPath.row];
            // 把需要移动的单元格数据在数组中，移动到想要移动的数据前面
            [newrowArr insertObject:object atIndex:destinationIndexPath.row];
        
        
//        [newrowArr exchangeObjectAtIndex:sourceIndexPath.row withObjectAtIndex:destinationIndexPath.row];
        [dictnew setValue:newrowArr forKey:kUPMarket2StockSettingRowsKey];
        
        [muarr replaceObjectAtIndex:sourceIndexPath.section withObject:dictnew];
        self.dataSetArr = [muarr copy];
        
        
        self.isSorted = YES;
        
        [self _updateForNewOrder:muarr.copy section:destinationIndexPath.section];
        
        [self.tableView reloadData];
//        [self.tableView moveRowAtIndexPath:sourceIndexPath toIndexPath:destinationIndexPath];
    } else {
        [self.tableView reloadData];
        return;
    }
}


- (void)_updateForNewOrder:(NSArray *)newOrderArray section:(NSInteger)section
{
    NSInteger indexGroupTemplateSection = 3;
    
    if (section == indexGroupTemplateSection) {
        
        NSDictionary *dict = self.dataSetArr[section];
        NSArray *rowItems = [dict[kUPMarket2StockSettingRowsKey] valueForKeyPath:@"muBanId"];
        
        [UPMarket2StockSettingManager.sharedInstance updateOrderList:rowItems ForKey:keyForTemplate(NO)];
    }else{
        
        NSDictionary *dict = self.dataSetArr[section];
        NSArray *rowItems = [dict[kUPMarket2StockSettingRowsKey] valueForKeyPath:@"indexID"];
        [UPMarket2StockSettingManager.sharedInstance updateOrderList:rowItems ForKey:keyForCacheType(NO, section==1)];
    }
    
}



//默认编辑模式下，每个cell左边有个红色的删除按钮，设置为None即可去掉
- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewCellEditingStyleNone;
}

- (void)up_market2StockSettingMubanDeleteCell:(UPMarket2StockSettingMubanCell *)cell {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    _mubanId = dataSet.muBanId;
    WeakSelf(weakSelf);
    UPAlertActionParam *action1 = [[UPAlertActionParam alloc] initWithTitle:@"取消" titleColor:nil handler:nil];
    UPAlertActionParam *action2 = [[UPAlertActionParam alloc] initWithTitle:@"确认" titleColor:UIColor.up_brandColor handler:^(UPAlertView * _Nonnull alertView) {
        [UPMarketIndexManager.share deletedTemplateWithIds:@[@(_mubanId)] finish:^(NSString * _Nonnull errorMsg) {
            if (IsValidateString(errorMsg)) {
                [UPToastView showFail:errorMsg];
            } else {
                [UPToastView showSuccess:@"删除模板成功"];
                [weakSelf reloadSettingData];
            }
        }];
    }];
    
    [UPAlertView showAlertWithActionArray:@[action1, action2] title:nil message:@"确认要删除该指标模板吗?"];
}

- (void)up_market2StockSettingMuBanRenameCell:(UPMarket2StockSettingMubanCell *)cell {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    _mubanId = dataSet.muBanId;
    self.cmmView = [self getcreatView:dataSet.title];
    self.pop.contentView = self.cmmView;
    self.pop.backgroundColor = [UIColor up_colorFromHexString:@"#80363E56"];
    WeakSelf(weakSelf);
    [self.pop showInView:UPRouter.rootViewController.view constranitsHandler:^(UIView * _Nonnull popView) {
        [weakSelf.cmmView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(popView);
            make.bottom.equalTo(@(-_ketboradHeight));
//            make.height.mas_equalTo(@(200));
        }];
    }];
}

/// MARK: UPMarket2StockSettingSingleBtnCellDelegate
- (void)up_market2StockSettingSingleBtnCell:(UPMarket2StockSettingSingleBtnCell *)cell didClickSingleBtn:(UIButton *)btn {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    [self handleJumpEventForDataSet:dataSet];
}

/// MARK: UPMarket2StockSettingSegementCellDelegate
- (void)up_market2StockSettingSegementCell:(UPMarket2StockSettingSegementCell *)cell didClickHelpBtn:(UIButton *)btn {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    [self handleJumpEventForDataSet:dataSet];
}

- (void)up_market2StockSettingSegementCell:(UPMarket2StockSettingSegementCell *)cell didClickSegementIndex:(NSInteger)index {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingMinuteView:didClickMinuteType:atSegmentIndex:)]) {
        [self.delegate up_market2StockSettingMinuteView:self didClickMinuteType:dataSet.type atSegmentIndex:index];
    }
}

/// MARK: UPMarket2StockSettingStepperCellDelegate
- (void)up_market2StockSettingStepperCell:(UPMarket2StockSettingStepperCell *)cell stepperValueChanged:(NSUInteger)value {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingMinuteView:didClickMinuteType:stepperValueChanged:)]) {
        [self.delegate up_market2StockSettingMinuteView:self didClickMinuteType:dataSet.type stepperValueChanged:value];
    }
}

- (BOOL)up_market2StockSettingSingleSwitchCell:(UPMarket2StockSettingSingleBtnCell *)cell switchControlChangedOn:(BOOL)isOn
{
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    NSDictionary *dict = self.dataSetArr[indexPath.section];
    NSArray<UPMarket2StockSettingCellDataSet *> *rowItems = dict[kUPMarket2StockSettingRowsKey];
    
    NSInteger showCount = 0;
    for (UPMarket2StockSettingCellDataSet *dataSet in rowItems) {
        if (![[UPMarket2StockSettingManager sharedInstance] isHiddenForIndexId:dataSet.indexID]) {
            showCount ++;
        }
    }
    if (showCount>1|| isOn) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingMinuteView:didClickMinuteType:switchControlChangedOn:indexId:)]) {
            [self.delegate up_market2StockSettingMinuteView:self didClickMinuteType:dataSet.type switchControlChangedOn:isOn indexId:cell.dataSet.indexID];
        }
        return YES;
    }else{
        return NO;
    }
}


- (void)reloadSettingData
{
    NSArray *arr = [UPMarket2StockSettingManager.sharedInstance minuteDataSetArrStockHq:self.stockHq];
    self.dataSetArr = arr;
    [self.tableView reloadData];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self scrollToBottomAnimatied:NO];
    });
}
- (void)scrollToBottomAnimatied:(BOOL)animatied {
    const NSUInteger totalCount = self.dataSetArr.count;
    if (totalCount <= 0) {
        return;
    }
    
    [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:totalCount-1] atScrollPosition:UITableViewScrollPositionNone animated:animatied];
}

/// MARK: Private
/// 处理点击设置或帮助按钮事件
- (void)handleJumpEventForDataSet:(UPMarket2StockSettingCellDataSet *)dataSet {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingMinuteView:didClickMinuteType:indexConfigInfo:)]) {
        [self.delegate up_market2StockSettingMinuteView:self didClickMinuteType:dataSet.type indexConfigInfo:dataSet.indexPlarmInfo];
    }
}

- (UPMarket2StockSettingCellDataSet *)dataSetForCell:(UITableViewCell *)cell {
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    NSDictionary *dict = self.dataSetArr[indexPath.section];
    NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
    UPMarket2StockSettingCellDataSet *dataSet = rowItems[indexPath.row];
    
    if (dataSet.indexID == UPMarketUIMinorIndexVOL) {
        dataSet.type = UPMarket2StockSettingTypeMinuteVOL;
    }else if (dataSet.indexID == UPMarketUIMinorIndexMACD) {
        dataSet.type = UPMarket2StockSettingTypeMinuteMACD;
    }else if (dataSet.indexID == UPMarketUIMinorIndexLB) {
        dataSet.type = UPMarket2StockSettingTypeMinuteLB;
    }
    return dataSet;
}

- (BOOL)isChanged {
    if (self.isSorted) {
        return YES;
    }
    __block BOOL tempChanged = NO;
    for (NSDictionary *dict in self.dataSetArr) {
        NSArray *rowItems = dict[kUPMarket2StockSettingRowsKey];
        for (UPMarket2StockSettingCellDataSet *dataSet in rowItems) {
            if (dataSet.isChanged) {
                tempChanged = YES;
                break;
            }
        }
    }
    return tempChanged;
}

/// MARK: Lazy loading
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.tableFooterView = [[UIView alloc] init];
        _tableView.backgroundColor = UIColor.upmarket2_settingHeaderColor;
        _tableView.separatorColor = UIColor.up_dividerColor;
        _tableView.editing = YES;
        [_tableView registerClass:[UPMarket2StockSettingSegementCell class] forCellReuseIdentifier:[UPMarket2StockSettingSegementCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingStepperCell class] forCellReuseIdentifier:[UPMarket2StockSettingStepperCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingSingleBtnCell class] forCellReuseIdentifier:[UPMarket2StockSettingSingleBtnCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingMubanCell class] forCellReuseIdentifier:[UPMarket2StockSettingMubanCell cellIdentifier]];
    }
    return _tableView;
}

#pragma 重命名模板名称
- (void)renameMoBanText {
    WeakSelf(weakSelf);
    [UPMarketIndexManager.share updateTemplateIndexName:self.newtextFiled.text forTemplateId:_mubanId finish:^(NSString * _Nonnull errorMsg) {
        if (IsValidateString(errorMsg)) {
            [UPToastView showFail:errorMsg];
        } else {
            [UPToastView showSuccess:@"重命名模板成功"];
            [weakSelf reloadSettingData];
            [weakSelf.pop hide];
        }
    }];
    
}





- (UPPopupView *)pop {
    if (!_pop) {
        _pop = [[UPPopupView alloc] init];
    }
    return _pop;
}

- (void)chooseTemPlateViewHide {
    [self.pop hide];
}

- (UIView *)getcreatView:(NSString *)str {

    UIView *_creatView = [UIView new];
    _creatView.backgroundColor = UIColor.up_contentBgColor;
    UIView *vv = [UIView new];
    vv.layer.cornerRadius = 8.f;
    vv.backgroundColor = UIColor.up_contentBgColor;
    
    [_creatView addSubview:vv];
    [vv mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(_creatView);
        make.height.equalTo(@(16.f));
    }];
    
    UIView *headerView = [[UIView alloc] init];
    headerView.backgroundColor = UIColor.up_contentBgColor;
    
    [_creatView addSubview:headerView];
    [headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(_creatView);
        make.top.equalTo(vv.mas_top).offset(5.f);
        make.height.equalTo(@(45.f));
    }];
        
            UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            cancelBtn.titleLabel.font = [UIFont up_fontOfSize:15];
//            [cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
            [cancelBtn setImage:UPTImg(@"个股/自选关闭") forState:UIControlStateNormal];
            [cancelBtn setTitleColor:UIColor.upmarket2_moreViewTitleColor forState:UIControlStateNormal];
            [cancelBtn addTarget:self action:@selector(chooseTemPlateViewHide) forControlEvents:UIControlEventTouchUpInside];
            [headerView addSubview:cancelBtn];
            
            UILabel *titleLabel = [UILabel new];
            titleLabel.text = @"重命名模板";
            titleLabel.textColor = UIColor.up_textPrimaryColor;
            titleLabel.font = [UIFont up_boldFontOfSize:16];
            [headerView addSubview:titleLabel];
            
            UIButton *finishBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            finishBtn.titleLabel.font = [UIFont up_fontOfSize:15];
            [finishBtn setTitle:@"保存" forState:UIControlStateNormal];
            [finishBtn setTitleColor:UIColor.up_brandColor forState:UIControlStateNormal];
            [finishBtn addTarget:self action:@selector(renameMoBanText) forControlEvents:UIControlEventTouchUpInside];
            [headerView addSubview:finishBtn];
            
            UIView *divideLine1 = [UIView new];
            divideLine1.backgroundColor = [UIColor up_colorFromHexString:@"#D6D6F2"];
            [headerView addSubview:divideLine1];

            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.centerY.equalTo(headerView);
//                make.top.equalTo(_creatView.mas_top).offset(17);
            }];
            [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(titleLabel);
                make.left.equalTo(headerView).offset(15);
                make.width.height.equalTo(@35);
            }];
            
            [finishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(titleLabel);
                make.right.equalTo(headerView).offset(-15);
            }];
            
            
            [divideLine1 mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(cancelBtn.mas_left);
                make.right.equalTo(finishBtn.mas_right);
                make.bottom.equalTo(headerView.mas_bottom);
                make.height.equalTo(@1);
            }];
    self.newtextFiled.text = str;
        [_creatView addSubview:self.newtextFiled];

        [self.newtextFiled mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(cancelBtn.mas_right);
            make.right.equalTo(divideLine1.mas_right);
            make.top.equalTo(divideLine1.mas_bottom).offset(20);
            make.bottom.equalTo(_creatView.mas_bottom).offset(-17);
        }];
    [self.newtextFiled becomeFirstResponder];
    return _creatView;
}

- (UITextField *)newtextFiled {
    if (!_newtextFiled) {
        UITextField *textField = [UITextField new];
        
        textField.backgroundColor = UIColor.up_contentBgColor;
        textField.textColor = UIColor.upmarket2_newOptionPlaceHolderColor;
        textField.font = [UIFont up_fontOfSize:15];
        NSMutableAttributedString *att = [[NSMutableAttributedString alloc] initWithString:@"请输入自定义模板名称"];
        [att setAttributes:@{
            NSFontAttributeName : [UIFont up_fontOfSize:13],
            NSForegroundColorAttributeName : UIColor.up_textSecondary1Color
        } range:NSMakeRange(0, att.length)];
        textField.attributedPlaceholder = att;
        textField.delegate = self;
        textField.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 6, textField.up_height)];
        textField.leftViewMode = UITextFieldViewModeAlways;
        [textField becomeFirstResponder];
        _newtextFiled = textField;
    }
    return _newtextFiled;
}

// MARK: - UITextFieldDelegate
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    
    if (string.length) {
        NSRange rangeIndex = [string rangeOfComposedCharacterSequenceAtIndex:string.length-1];
        // 表情直接过滤
        if (rangeIndex.length > 1) return NO;
    }
    
    
    UITextRange *selectedRange = [textField markedTextRange];
    UITextPosition *position = [textField positionFromPosition:selectedRange.start offset:0];
    
    if (position) {
        return YES;
    }
    
    NSString *comcatstr = [textField.text stringByReplacingCharactersInRange:range withString:string];
    
    if (comcatstr.length > 8) {
        return NO;
    }
    return YES;
}

- (void)textDidChange:(UITextField *)textField {
    UITextRange *selectedRange = [textField markedTextRange];
    UITextPosition *position = [textField positionFromPosition:selectedRange.start offset:0];
    
    // 高粱状态不统计
    if (!position) {
        if (textField.text.length > 8) {
            textField.text = [textField.text substringToIndex:8];
        }
    }

}


- (void)keyboardWillShow:(NSNotification *)notification{

    //获取键盘的高度
    NSDictionary *userInfo = [notification userInfo];

    NSValue *value = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];

    CGRect keyboardRect = [value CGRectValue];
    _ketboradHeight = keyboardRect.size.height;
    if (self.cmmView.superview) {
        [self.cmmView mas_remakeConstraints:^(MASConstraintMaker *make) {
            [[make left] right];
            make.bottom.equalTo(@(-_ketboradHeight));
    //            make.height.mas_equalTo(@(200));
        }];
        [self.newtextFiled becomeFirstResponder];
    }

}
- (void)dealloc {
    _tableView.delegate = nil;
    _tableView.dataSource = nil;
}

@end
