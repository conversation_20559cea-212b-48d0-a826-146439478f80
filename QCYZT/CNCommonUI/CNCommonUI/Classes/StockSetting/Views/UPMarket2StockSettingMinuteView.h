//
//  UPMarket2StockSettingMinuteView.h
//  UPMarket2
//
//  Created by fang on 2020/3/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingMinuteView;

@protocol UPMarket2StockSettingMinuteViewDelegate <NSObject>

- (void)up_market2StockSettingMinuteView:(UPMarket2StockSettingMinuteView *)view didClickMinuteType:(UPMarket2StockSettingType)type indexConfigInfo:(MarketIndicatorSysIndexConfigInfo *)indexConfigInfo;

- (void)up_market2StockSettingMinuteView:(UPMarket2StockSettingMinuteView *)view didClickMinuteType:(UPMarket2StockSettingType)type atSegmentIndex:(NSInteger)index;

- (void)up_market2StockSettingMinuteView:(UPMarket2StockSettingMinuteView *)view didClickMinuteType:(UPMarket2StockSettingType)type stepperValueChanged:(NSUInteger)value;

- (void)up_market2StockSettingMinuteView:(UPMarket2StockSettingMinuteView *)view didClickMinuteType:(UPMarket2StockSettingType)type switchControlChangedOn:(BOOL)isOn indexId:(NSInteger)indexId;
@end

@interface UPMarket2StockSettingMinuteView : UPBaseView

@property (nonatomic, weak) id<UPMarket2StockSettingMinuteViewDelegate> delegate;

@property (nonatomic, strong) id indexHost;

@property (nonatomic, strong) UPHqStockHq *stockHq;

@property (nonatomic, strong) NSArray *dataSetArr;

@property (nonatomic, assign, readonly) BOOL isChanged;

@property (nonatomic, strong) id customConfig;

@property (nonatomic, assign) BOOL isTS;

@property (nonatomic, assign) BOOL isIgnoreScroll;

- (void)reloadSettingWhere;
@end

NS_ASSUME_NONNULL_END
