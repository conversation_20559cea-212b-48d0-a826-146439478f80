//
//  UPMarket2StockSettingOtherView.m
//  UPMarket2
//
//  Created by fang on 2020/3/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingOtherView.h"
#import "UPMarket2StockSettingCellDataSet.h"
#import "UPMarket2StockSettingSegementCell.h"
#import "UPMarket2StockSettingSwitchCell.h"
#import "UPMarket2StockSettingSingleBtnCell.h"
#import "UPMarket2StockSettingArrowCell.h"

@interface UPMarket2StockSettingOtherView ()<UITableViewDelegate, UITableViewDataSource, UPMarket2StockSettingSegementCellDelegate, UPMarket2StockSettingSwitchCellDelegate>

@property (nonatomic, strong) UITableView *tableView;

@end

@implementation UPMarket2StockSettingOtherView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        self.backgroundColor = UIColor.upmarket2_settingHeaderColor;
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

- (void)reloadData {
    [self.tableView reloadData];
}

/// MARK: UITableViewDelegate, UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataSetArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UPMarket2StockSettingCellDataSet *dataSet = self.dataSetArr[indexPath.row];
    if (dataSet.cellStyle == UPMarket2StockSettingCellStyleSegment) {
        UPMarket2StockSettingSegementCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingSegementCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingCellStyleSwitch) {
        UPMarket2StockSettingSwitchCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingSwitchCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingCellStyleArrow) {
        UPMarket2StockSettingArrowCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingArrowCell cellIdentifier] forIndexPath:indexPath];
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else {
        NSAssert(NO, @"check dataSet cellStyle");
        return nil;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UPWidth(50);
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    UPMarket2StockSettingCellDataSet *dataSet = self.dataSetArr[indexPath.row];
    if (dataSet.type == UPMarket2StockSettingTypeOtherSYSZ) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingOtherViewSelectedDefaultHomeSetting:)]) {
            [self.delegate up_market2StockSettingOtherViewSelectedDefaultHomeSetting:self];
        }
    }else if (dataSet.type == UPMarket2StockSettingTypePermissionManager) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingOtherView:didClickKLineType:)]) {
            [self.delegate up_market2StockSettingOtherView:self didClickKLineType:dataSet.type];
        }
    }
}

/// MARK: UPMarket2StockSettingSegementCellDelegate
- (void)up_market2StockSettingSegementCell:(UPMarket2StockSettingSegementCell *)cell didClickHelpBtn:(UIButton *)btn {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    [self handleJumpEventForDataSet:dataSet];
}

- (void)up_market2StockSettingSegementCell:(UPMarket2StockSettingSegementCell *)cell didClickSegementIndex:(NSInteger)index {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingOtherView:didClickKLineType:atSegmentIndex:)]) {
        [self.delegate up_market2StockSettingOtherView:self didClickKLineType:dataSet.type atSegmentIndex:index];
    }
}

/// MARK: UPMarket2StockSettingSwitchCellDelegate
- (void)up_market2StockSettingSwitchCell:(UPMarket2StockSettingSwitchCell *)cell didClickHelpBtn:(UIButton *)btn {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    [self handleJumpEventForDataSet:dataSet];
}

- (void)up_market2StockSettingSwitchCell:(UPMarket2StockSettingSwitchCell *)cell switchControlChangedOn:(BOOL)isOn {
    UPMarket2StockSettingCellDataSet *dataSet = [self dataSetForCell:cell];
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingOtherView:didClickKLineType:switchControlChangedOn:)]) {
        [self.delegate up_market2StockSettingOtherView:self didClickKLineType:dataSet.type switchControlChangedOn:isOn];
    }
}

/// MARK: Private
/// 处理点击设置或帮助按钮事件
- (void)handleJumpEventForDataSet:(UPMarket2StockSettingCellDataSet *)dataSet  {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingOtherView:didClickKLineType:)]) {
        [self.delegate up_market2StockSettingOtherView:self didClickKLineType:dataSet.type];
    }
}

- (UPMarket2StockSettingCellDataSet *)dataSetForCell:(UITableViewCell *)cell {
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    UPMarket2StockSettingCellDataSet *dataSet = self.dataSetArr[indexPath.row];
    return dataSet;
}

- (BOOL)isChanged {
    __block BOOL tempChanged = NO;
    for (UPMarket2StockSettingCellDataSet *dataSet in self.dataSetArr) {
        if (dataSet.isChanged) {
            tempChanged = YES;
            break;
        }
    }
    return tempChanged;
}

/// MARK: Lazy loading
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.tableFooterView = [[UIView alloc] init];
        _tableView.backgroundColor = UIColor.upmarket2_settingHeaderColor;
        _tableView.separatorColor = UIColor.clearColor;
        _tableView.layer.cornerRadius = 12.f;
        [_tableView registerClass:[UPMarket2StockSettingSegementCell class] forCellReuseIdentifier:[UPMarket2StockSettingSegementCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingArrowCell class] forCellReuseIdentifier:[UPMarket2StockSettingArrowCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingSwitchCell class] forCellReuseIdentifier:[UPMarket2StockSettingSwitchCell cellIdentifier]];
    }
    return _tableView;
}

- (void)dealloc {
    _tableView.delegate = nil;
    _tableView.dataSource = nil;
}

@end
