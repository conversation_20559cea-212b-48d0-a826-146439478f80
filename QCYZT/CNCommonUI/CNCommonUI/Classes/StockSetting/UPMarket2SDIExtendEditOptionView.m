//
//  UPMarket2SDIExtendEditOptionView.m
//  UPMarket2
//
//  Created by fang on 2020/4/23.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDIExtendEditOptionView.h"
#import "UPMarket2SDIExtendEditOptionViewCell.h"
#import "UPUserSDK/UPOptionalManager.h"
#import "UPUserSDK/UPUserManager.h"
#import "UPUserSDK/UPUserSDKDefine.h"


@interface UPMarket2SDIExtendDelegateOptionView ()
@end

@implementation UPMarket2SDIExtendDelegateOptionView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        UIView *maskView = [UIView new];
        maskView.backgroundColor = UIColor.upmarket2_maskBgColor;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(sdiExtendEditOptionViewHide)];
        [maskView addGestureRecognizer:tap];
        [self addSubview:maskView];
        [maskView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(self);
        }];
        
        UIView *contentView = [UIView new];
        contentView.backgroundColor = UIColor.upmarket2_maskBgColor;
        
        [self addSubview:contentView];
        
        [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.equalTo(self);
            make.top.equalTo(maskView.mas_bottom);
        }];
        

        
        UIView *contentHeaderView = [UIView new];
        contentHeaderView.layer.cornerRadius = 8.f;
        contentHeaderView.layer.masksToBounds = YES;
        contentHeaderView.backgroundColor = UIColor.up_contentBgColor;
        [contentView addSubview:contentHeaderView];
        
        [contentHeaderView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(contentView);
            make.height.equalTo(@54);
        }];
        
        UIView *divideLine = [UIView new];
        divideLine.backgroundColor = [UIColor up_colorFromHexString:@"#D6D6F2"];
        [contentHeaderView addSubview:divideLine];

        [divideLine mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(contentHeaderView.mas_left).offset(15);
            make.right.equalTo(contentHeaderView.mas_right).offset(-15);
            make.top.equalTo(contentHeaderView.mas_bottom).offset(-8);
            make.height.equalTo(@0.5);
        }];
                
        
        {
//            UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//            cancelBtn.titleLabel.font = [UIFont up_fontOfSize:15];
////            [cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
//            [cancelBtn setImage:UPTImg(@"个股/自选关闭") forState:UIControlStateNormal];
//            [cancelBtn setTitleColor:UIColor.upmarket2_moreViewTitleColor forState:UIControlStateNormal];
//            [cancelBtn addTarget:self action:@selector(sdiExtendEditOptionViewHide) forControlEvents:UIControlEventTouchUpInside];
//            [contentHeaderView addSubview:cancelBtn];
            
            UILabel *titleLabel = [UILabel new];
            titleLabel.text = @"设置自选";
            titleLabel.textColor = UIColor.up_textPrimaryColor;
            titleLabel.font = [UIFont up_boldFontOfSize:16];
            [contentHeaderView addSubview:titleLabel];
        
//
//            [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.centerY.equalTo(contentHeaderView);
//                make.right.equalTo(contentHeaderView).offset(-15);
//                make.width.height.equalTo(@35);
//            }];
            
            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(contentHeaderView);
                make.top.equalTo(contentHeaderView.mas_top).offset(17);
            }];
            
        }
        
        UIButton *chanageButton = [UIButton buttonWithType:UIButtonTypeCustom];
        chanageButton.titleLabel.font = [UIFont up_mediumFontOfSize:15];
        [chanageButton setBackgroundColor:UIColor.up_contentBgColor];
        [chanageButton setTitle:@"修改分组" forState:UIControlStateNormal];
        [chanageButton setTitleColor:UIColor.upmarket2_moreViewTitleColor forState:UIControlStateNormal];
        [chanageButton addTarget:self action:@selector(chanage) forControlEvents:UIControlEventTouchUpInside];
        [contentView addSubview:chanageButton];
        [chanageButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(contentView);
            make.top.equalTo(contentHeaderView.mas_bottom).offset(-7);
            make.height.equalTo(@45);
        }];
        

        UIButton *delegateBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        delegateBtn.titleLabel.font = [UIFont up_mediumFontOfSize:15];
        [delegateBtn setTitle:@"删除自选" forState:UIControlStateNormal];
        [delegateBtn setBackgroundColor:UIColor.up_contentBgColor];

        [delegateBtn setTitleColor:UIColor.up_brandColor forState:UIControlStateNormal];
        [delegateBtn addTarget:self action:@selector(sdiExtendDelegateOptionViewDelegate) forControlEvents:UIControlEventTouchUpInside];

        [contentView addSubview:delegateBtn];
        
        [delegateBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(contentView);
            make.top.equalTo(chanageButton.mas_bottom);
            make.height.equalTo(@45);
        }];
        
        
        UIView *divideLine1 = [UIView new];
        divideLine1.backgroundColor = [UIColor up_colorFromHexString:@"#F2F3F8"];
        [contentView addSubview:divideLine1];

        [divideLine1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(contentView);
            make.top.equalTo(delegateBtn.mas_bottom);
            make.height.equalTo(@10);
        }];
        
        UIButton *createBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        createBtn.titleLabel.font = [UIFont up_boldFontOfSize:18];
        [createBtn setTitle:@"取消" forState:UIControlStateNormal];
        [createBtn setTitleColor:[UIColor up_colorFromHexString:@"#787D87"] forState:UIControlStateNormal];
        [createBtn setBackgroundColor:UIColor.up_contentBgColor];
        [createBtn addTarget:self action:@selector(sdiExtendEditOptionViewHide) forControlEvents:UIControlEventTouchUpInside];
        [contentView addSubview:createBtn];
        
        [createBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.equalTo(contentView);
            make.top.equalTo(divideLine1.mas_bottom);
            make.height.equalTo(@60);
        }];
    }
    return self;
}

// MARK: Action
- (void)sdiExtendDelegateOptionViewDelegate {
    if (self.delegateBlock) {
        self.delegateBlock();
    }
}
- (void)chanage {
    if (self.chanageBlock) {
        self.chanageBlock();
    }
}

- (void)sdiExtendEditOptionViewHide {
    if (self.hideBlock) {
        self.hideBlock();
    }
}



- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
}


@end


@interface UPMarket2SDIExtendEditOptionView ()<UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) NSArray<UPOptionalGroupModel *> *optionGroupModels;

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) NSMutableDictionary *selectedDic;

@property (nonatomic, strong) UIButton *createBtn;


@end

@implementation UPMarket2SDIExtendEditOptionView {
    MASConstraint *_tableViewHeightConstraint;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        UIView *maskView = [UIView new];
        maskView.backgroundColor = UIColor.upmarket2_maskBgColor;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(sdiExtendEditOptionViewHide)];
        [maskView addGestureRecognizer:tap];
        [self addSubview:maskView];
        [maskView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(self);
        }];
        
        UIView *contentView = [UIView new];
        contentView.backgroundColor = UIColor.upmarket2_maskBgColor;
        [self addSubview:contentView];
        
        [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.equalTo(self);
            make.top.equalTo(maskView.mas_bottom);
        }];
        
        UIView *contentHeaderView = [UIView new];
        contentHeaderView.backgroundColor = UIColor.up_contentBgColor;
        contentHeaderView.layer.cornerRadius = 8;
        [contentView addSubview:contentHeaderView];
        
        [contentHeaderView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(contentView);
            make.height.equalTo(@54);
        }];
        
        UIView *divideLine = [UIView new];
        divideLine.backgroundColor = [UIColor up_colorFromHexString:@"#D6D6F2"];
        [contentHeaderView addSubview:divideLine];

        [divideLine mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(contentHeaderView.mas_left).offset(15);
            make.right.equalTo(contentHeaderView.mas_right).offset(-15);
            make.top.equalTo(contentHeaderView.mas_bottom).offset(-8);
            make.height.equalTo(@0.5);
        }];
                
        
        {
            UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            cancelBtn.titleLabel.font = [UIFont up_fontOfSize:15];
//            [cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
            [cancelBtn setImage:UPTImg(@"个股/自选关闭") forState:UIControlStateNormal];
            [cancelBtn setTitleColor:UIColor.upmarket2_moreViewTitleColor forState:UIControlStateNormal];
            [cancelBtn addTarget:self action:@selector(sdiExtendEditOptionViewHide) forControlEvents:UIControlEventTouchUpInside];
            [contentHeaderView addSubview:cancelBtn];
            
            UILabel *titleLabel = [UILabel new];
            titleLabel.text = @"选择自选分组";
            titleLabel.textColor = UIColor.up_textPrimaryColor;
            titleLabel.font = [UIFont up_boldFontOfSize:16];
            [contentHeaderView addSubview:titleLabel];
            
            UIButton *finishBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            finishBtn.titleLabel.font = [UIFont up_fontOfSize:15];
            [finishBtn setTitle:@"新建" forState:UIControlStateNormal];
            [finishBtn setTitleColor:UIColor.up_brandColor forState:UIControlStateNormal];
            [finishBtn addTarget:self action:@selector(sdiExtendEditOptionViewNew) forControlEvents:UIControlEventTouchUpInside];
            [contentHeaderView addSubview:finishBtn];
            
         
            
            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(contentHeaderView);
                make.top.equalTo(contentHeaderView.mas_top).offset(17);
            }];
            
            [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(titleLabel);
                make.left.equalTo(contentHeaderView).offset(15);
                make.width.height.equalTo(@35);
            }];
            
            [finishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(titleLabel);
                make.right.equalTo(contentHeaderView).offset(-15);
            }];
        }
        
        [contentView addSubview:self.tableView];
        
        [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(contentView);
            make.top.equalTo(contentHeaderView.mas_bottom).offset(-7);
            _tableViewHeightConstraint = make.height.equalTo(@90);
        }];
        
        UIView *divideLine1 = [UIView new];
        divideLine1.backgroundColor = [UIColor up_colorFromHexString:@"#F2F3F8"];
        [contentView addSubview:divideLine1];

        [divideLine1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(contentView);
            make.top.equalTo(self.tableView.mas_bottom);
            make.height.equalTo(@10);
        }];

        UIButton *createBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        createBtn.titleLabel.font = [UIFont up_boldFontOfSize:18];
        [createBtn setTitle:@"确认" forState:UIControlStateNormal];
        [createBtn setTitleColor:UIColor.up_textPrimaryColor forState:UIControlStateNormal];
        [createBtn setTitleColor:UIColor.up_textSecondary2Color forState:UIControlStateDisabled];
        [createBtn setBackgroundColor:UIColor.up_contentBgColor];
        [createBtn addTarget:self action:@selector(sdiExtendEditOptionViewFinish) forControlEvents:UIControlEventTouchUpInside];
//        createBtn.layer.shadowColor = [UIColor up_colorFromHexString:@"#3BF2D6D6"].CGColor;//阴影颜色
//        createBtn.layer.shadowOffset = CGSizeMake(0, -4);//偏移距离
//        createBtn.layer.shadowOpacity = 1.0;//不透明度
//        createBtn.layer.shadowRadius = 10.0;//半径
        [contentView addSubview:createBtn];
        self.createBtn = createBtn;
        
        [createBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.equalTo(contentView);
            make.top.equalTo(divideLine1.mas_bottom);
            make.height.equalTo(@60);
        }];
        
        [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(sdiNotifyOptionalDataUpdated) name:UPNotifyOptionalDataUpdated object:nil];
    }
    return self;
}

- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
}

- (void)sdiNotifyOptionalDataUpdated {
    self.optionGroupModels = [UPOptionalManager getCurrentOptionalGroupData];
    
    for (UPOptionalGroupModel *group in self.optionGroupModels) {
        
        if (IsValidateArray(self.stocksArr)) {
            if (group.groupId == 0 && [self.isNew isEqualToString:@"1"]) {
                [self.selectedDic setValue:@(1) forKey:[NSString stringWithFormat:@"%zd",group.groupId]];
            }
        } else {
            UPOptionalModel *optionModel = [UPOptionalManager getOptionalWithCode:self.stockModel.stockCode setCode:self.stockModel.stockSetCode groupId:group.groupId];
            if (optionModel) {
                [self.selectedDic setValue:@(1) forKey:[NSString stringWithFormat:@"%zd",group.groupId]];
            }
            if (group.groupId == 0 && [self.isNew isEqualToString:@"1"]) {
                [self.selectedDic setValue:@(1) forKey:[NSString stringWithFormat:@"%zd",group.groupId]];
            }
        }
       
    }
    
    NSMutableArray *groupIdList = [NSMutableArray array];
    [self.selectedDic enumerateKeysAndObjectsUsingBlock:^(NSNumber*  _Nonnull key, NSNumber *  _Nonnull obj, BOOL * _Nonnull stop) {
        if (obj.boolValue) {
            [groupIdList addObject:key];
        }
    }];
    
    if (!IsValidateArray(groupIdList)) {
        self.createBtn.enabled = NO;
        return;
    } else {
        self.createBtn.enabled = YES;
    }
    
    [self.tableView reloadData];
}

// MARK: UITableViewDelegate && UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSInteger groupCount = self.optionGroupModels.count;
    if (_tableViewHeightConstraint) {
        if (groupCount <= 6) {
            _tableViewHeightConstraint.equalTo(@(groupCount * 45));
        } else {
            _tableViewHeightConstraint.equalTo(@(6 * 45));
        }
    }
    
   
    
    return groupCount;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UPOptionalGroupModel *groupModel = self.optionGroupModels[indexPath.row];
    
    UPMarket2SDIExtendEditOptionViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(UPMarket2SDIExtendEditOptionViewCell.class)];
    
    BOOL isSelected = [[self.selectedDic valueForKey:[NSString stringWithFormat:@"%zd",groupModel.groupId]] boolValue];
    cell.isCheck = isSelected;
    if (groupModel.groupId == 0) {
        cell.nameLabel.text = @"我的自选";
        
    } else {
        cell.nameLabel.text = groupModel.groupName;
    }
    
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    UPOptionalGroupModel *groupModel = self.optionGroupModels[indexPath.row];
    BOOL selected = [[self.selectedDic valueForKey:[NSString stringWithFormat:@"%zd",groupModel.groupId]] boolValue];
    [self.selectedDic setValue:@(!selected) forKey:[NSString stringWithFormat:@"%zd",groupModel.groupId]];
    [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
    NSMutableArray *groupIdList = [NSMutableArray array];
    
    [self.selectedDic enumerateKeysAndObjectsUsingBlock:^(NSNumber*  _Nonnull key, NSNumber *  _Nonnull obj, BOOL * _Nonnull stop) {
        if (obj.boolValue) {
            [groupIdList addObject:key];
        }
    }];
    
    if (!IsValidateArray(groupIdList)) {
        self.createBtn.enabled = NO;
        return;
    } else {
        self.createBtn.enabled = YES;
    }
}

// MARK: Action

- (void)sdiExtendEditOptionViewHide {
    if (self.hideBlock) {
        self.hideBlock();
    }
}

- (void)sdiExtendEditOptionViewFinish {
    
    if (IsValidateArray(self.stocksArr)) {
        NSMutableArray *groupIdList = [NSMutableArray array];
        
        [self.selectedDic enumerateKeysAndObjectsUsingBlock:^(NSNumber*  _Nonnull key, NSNumber *  _Nonnull obj, BOOL * _Nonnull stop) {
            if (obj.boolValue) {
                [groupIdList addObject:key];
            }
        }];
        for (UPMarketUIBaseModel *model in self.stocksArr) {
            UPOptionalAddStatus addStatus = [UPOptionalManager addOptional:model.stockSetCode code:model.stockCode name:nil groupIdList:groupIdList.copy];
            if ([self.stocksArr indexOfObject:model] == self.stocksArr.count -1) {
                if (addStatus == UPOptionalAddStatusSuccess) {
                    [UPToastView show:@"已添加自选" icon:UPTImg(@"行情/发送成功stock")];
                } else if (addStatus == UPOptionalAddStatusLimit) {
                    [UPToastView show:@"自选股已达上限，添加失败"];
                } else {
                    [UPToastView show:@"添加失败"];
                }
            }
        }
       
        
    } else {
        NSMutableArray *groupIdList = [NSMutableArray array];
        
        [self.selectedDic enumerateKeysAndObjectsUsingBlock:^(NSNumber*  _Nonnull key, NSNumber *  _Nonnull obj, BOOL * _Nonnull stop) {
            if (obj.boolValue) {
                [groupIdList addObject:key];
            }
        }];
        
        UPOptionalAddStatus addStatus = [UPOptionalManager addOptional:self.stockModel.stockSetCode code:self.stockModel.stockCode name:nil groupIdList:groupIdList.copy];
        
        if (addStatus == UPOptionalAddStatusSuccess) {
            [UPToastView show:@"已添加自选" icon:UPTImg(@"行情/发送成功stock")];
        } else if (addStatus == UPOptionalAddStatusLimit) {
            [UPToastView show:@"自选股已达上限，添加失败"];
        } else {
            [UPToastView show:@"添加失败"];
        }

    }
    
    
    [self sdiExtendEditOptionViewHide];
}

- (void)sdiExtendEditOptionViewNew {
    if (self.newBlock) {
        self.newBlock();
    }
}

// MARK: Getter & Setter

- (void)setStockModel:(UPMarketUIBaseModel *)stockModel {
    _stockModel = stockModel;
    [self sdiNotifyOptionalDataUpdated];
}

- (void)setStocksArr:(NSArray *)stocksArr {
    _stocksArr = stocksArr;
    [self sdiNotifyOptionalDataUpdated];
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.backgroundColor = UIColor.up_listBgColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = 45;
        [_tableView registerClass:UPMarket2SDIExtendEditOptionViewCell.class forCellReuseIdentifier:NSStringFromClass(UPMarket2SDIExtendEditOptionViewCell.class)];
        
    }
    
    return _tableView;
}

- (NSMutableDictionary *)selectedDic {
    if (!_selectedDic) {
        _selectedDic = [NSMutableDictionary dictionary];
    }
    
    return _selectedDic;
}

@end
