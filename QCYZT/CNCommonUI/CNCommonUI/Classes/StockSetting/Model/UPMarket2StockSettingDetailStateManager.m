//
//  UPMarket2StockSettingDetailStateManager.m
//  UPMarket2
//
//  Created by 方恒 on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailStateManager.h"
#import <UPMarketIndex/UPMarketIndex.h>

@implementation UPMarket2StockSettingDetailStateManager

+ (UPMarket2StockSettingDetailDataMACDModel *)minuteMACDModel {
    
    UPMarket2StockSettingDetailDataMACDModel *macdModel = [[UPMarket2StockSettingDetailDataMACDModel alloc] init];
    UPMarketUIMACDPeriod *macdPeriod = [UPMarketUIIndexPeriod getMACDPeriod:NO position:0 indexID:UPMarketUIMinorIndexMACD isKline:NO];
    macdModel.diff1 = macdPeriod.diff1;
    macdModel.diff2 = macdPeriod.diff2;
    macdModel.dea = macdPeriod.dea;
    return macdModel;
}

+ (void)updateMinuteMACDModel:(UPMarket2StockSettingDetailDataMACDModel *)macd {
    UPMarketUIMACDPeriod *macdPeriod = [[UPMarketUIMACDPeriod alloc] init];
    macdPeriod.diff1 = macd.diff1;
    macdPeriod.diff2 = macd.diff2;
    macdPeriod.dea = macd.dea;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexMACD isKline:NO period:macdPeriod];
}

+ (UPMarket2StockSettingDetailDataDynamicModel *)dynamicModelForIndexId:(NSInteger)indexId
{
    UPMarketIndexParam *indexParam = [UPMarketIndexFactory dynamicParamForIndexPlatformIndexId:indexId];
    
    UPMarket2StockSettingDetailDataDynamicModel *dynamicModel = [UPMarket2StockSettingDetailDataDynamicModel modelWithIndexParam:indexParam];
    
    return dynamicModel;
}

+ (UPMarket2StockSettingDetailDataBOLLModel *)kLineBOLLModel {
    
    UPMarket2StockSettingDetailDataBOLLModel *bollModel = [[UPMarket2StockSettingDetailDataBOLLModel alloc] init];
    
    UPMarketUIBOLLPeriod *bollPeriod = [UPMarketUIIndexPeriod getBOLLPeriod:YES position:0 indexID:UPMarketUIMajorIndexBOLL isKline:YES];
    bollModel.diff1 = bollPeriod.boll;
//    bollModel.diff2 = bollPeriod.width;
    
    return bollModel;
}

+ (void)updateKLineBOLLModel:(UPMarket2StockSettingDetailDataBOLLModel *)boll {
    
    UPMarketUIBOLLPeriod *bollPeriod = [[UPMarketUIBOLLPeriod alloc] init];
    bollPeriod.boll = boll.diff1;
//    bollPeriod.width = boll.diff2;
    [UPMarketUIIndexPeriod setPeriod:YES position:0 indexID:UPMarketUIMajorIndexBOLL isKline:YES period:bollPeriod];
}

+ (UPMarket2StockSettingDetailDataVOLModel *)kLineVOLModel {
    
    UPMarket2StockSettingDetailDataVOLModel *volModel = [[UPMarket2StockSettingDetailDataVOLModel alloc] init];
    
    UPMarketUIVOLPeriod *volPeriod = [UPMarketUIIndexPeriod getVOLPeriod:NO position:0 indexID:UPMarketUIMinorIndexVOL isKline:YES];
    volModel.ma1 = volPeriod.ma1;
    volModel.ma2 = volPeriod.ma2;
    volModel.ma3 = volPeriod.ma3;
    
    return volModel;
}

+ (void)updateKLineVOLModel:(UPMarket2StockSettingDetailDataVOLModel *)vol {
    
    UPMarketUIVOLPeriod *period = [[UPMarketUIVOLPeriod alloc] init];
    period.ma1 = vol.ma1;
    period.ma2 = vol.ma2;
    period.ma3 = vol.ma3;
    
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexVOL isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataMACDModel *)kLineMACDModel {
    UPMarket2StockSettingDetailDataMACDModel *macdModel = [[UPMarket2StockSettingDetailDataMACDModel alloc] init];
    UPMarketUIMACDPeriod *macdPeriod = [UPMarketUIIndexPeriod getMACDPeriod:NO position:0 indexID:UPMarketUIMinorIndexMACD isKline:YES];
    macdModel.diff1 = macdPeriod.diff1;
    macdModel.diff2 = macdPeriod.diff2;
    macdModel.dea = macdPeriod.dea;
    return macdModel;
}

+ (void)updateKLineMACDModel:(UPMarket2StockSettingDetailDataMACDModel *)macd {
    UPMarketUIMACDPeriod *macdPeriod = [[UPMarketUIMACDPeriod alloc] init];
    macdPeriod.diff1 = macd.diff1;
    macdPeriod.diff2 = macd.diff2;
    macdPeriod.dea = macd.dea;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexMACD isKline:YES period:macdPeriod];
}

+ (UPMarket2StockSettingDetailDataKDJModel *)kLineKDJModel {
    UPMarket2StockSettingDetailDataKDJModel *kdjModel = [[UPMarket2StockSettingDetailDataKDJModel alloc] init];
    UPMarketUIKDJPeriod *kdjPeriod = [UPMarketUIIndexPeriod getKDJPeriod:NO position:0 indexID:UPMarketUIMinorIndexKDJ isKline:YES];
    kdjModel.k = kdjPeriod.k;
    kdjModel.d = kdjPeriod.d;
    kdjModel.j = kdjPeriod.j;
    return kdjModel;
}

+ (void)updateKLineKDJModel:(UPMarket2StockSettingDetailDataKDJModel *)kdj {
    UPMarketUIKDJPeriod *period = [[UPMarketUIKDJPeriod alloc] init];
    period.k = kdj.k;
    period.d = kdj.d;
    period.j = kdj.j;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexKDJ isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataRSIModel *)kLineRSIModel {
    UPMarket2StockSettingDetailDataRSIModel *rsiModel = [[UPMarket2StockSettingDetailDataRSIModel alloc] init];
    UPMarketUIRSIPeriod *rsiPeriod = [UPMarketUIIndexPeriod getRSIPeriod:NO position:0 indexID:UPMarketUIMinorIndexRSI isKline:YES];
    rsiModel.rsi1 = rsiPeriod.rsi1;
    rsiModel.rsi2 = rsiPeriod.rsi2;
    rsiModel.rsi3 = rsiPeriod.rsi3;
    return rsiModel;
}

+ (void)updateKLineRSIModel:(UPMarket2StockSettingDetailDataRSIModel *)rsi {
    UPMarketUIRSIPeriod *period = [[UPMarketUIRSIPeriod alloc] init];
    period.rsi1 = rsi.rsi1;
    period.rsi2 = rsi.rsi2;
    period.rsi3 = rsi.rsi3;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexRSI isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataBIASModel *)kLineBIASModel {
    UPMarket2StockSettingDetailDataBIASModel *biasModel = [[UPMarket2StockSettingDetailDataBIASModel alloc] init];
    UPMarketUIBIASPeriod *biasPeriod = [UPMarketUIIndexPeriod getBIASPeriod:NO position:0 indexID:UPMarketUIMinorIndexBIAS isKline:YES];
    biasModel.bias1 = biasPeriod.bias1;
    biasModel.bias2 = biasPeriod.bias2;
    biasModel.bias3 = biasPeriod.bias3;
    return biasModel;
}

+ (void)updateKLineBIASModel:(UPMarket2StockSettingDetailDataBIASModel *)bias {
    UPMarketUIBIASPeriod *period = [[UPMarketUIBIASPeriod alloc] init];
    period.bias1 = bias.bias1;
    period.bias2 = bias.bias2;
    period.bias3 = bias.bias3;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexBIAS isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataVRModel *)kLineVRModel {
    UPMarket2StockSettingDetailDataVRModel *vrModel = [[UPMarket2StockSettingDetailDataVRModel alloc] init];
    UPMarketUIVRPeriod *vrPeriod = [UPMarketUIIndexPeriod getVRPeriod:NO position:0 indexID:UPMarketUIMinorIndexVR isKline:YES];
    vrModel.vr1 = vrPeriod.n;
    return vrModel;
}

+ (void)updateKLineVRModel:(UPMarket2StockSettingDetailDataVRModel *)vr {
    UPMarketUIVRPeriod *period = [[UPMarketUIVRPeriod alloc] init];
    period.n = vr.vr1;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexVR isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataCRModel *)kLineCRModel {
    UPMarket2StockSettingDetailDataCRModel *crModel = [[UPMarket2StockSettingDetailDataCRModel alloc] init];
    UPMarketUICRPeriod *crPeriod = [UPMarketUIIndexPeriod getCRPeriod:NO position:0 indexID:UPMarketUIMinorIndexCR isKline:YES];
    crModel.cr1 = crPeriod.n;
    return crModel;
}

+ (void)updateKLineCRModel:(UPMarket2StockSettingDetailDataCRModel *)cr {
    UPMarketUICRPeriod *period = [[UPMarketUICRPeriod alloc] init];
    period.n = cr.cr1;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexCR isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataWRModel *)kLineWRModel {
    UPMarket2StockSettingDetailDataWRModel *wrModel = [[UPMarket2StockSettingDetailDataWRModel alloc] init];
    UPMarketUIWRPeriod *wrPeriod = [UPMarketUIIndexPeriod getWRPeriod:NO position:0 indexID:UPMarketUIMinorIndexWR isKline:YES];
    wrModel.wr1 = wrPeriod.n;
    return wrModel;
}

+ (void)updateKlineWRModel:(UPMarket2StockSettingDetailDataWRModel *)wr {
    UPMarketUIWRPeriod *period = [[UPMarketUIWRPeriod alloc] init];
    period.n = wr.wr1;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexWR isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataDMAModel *)kLineDMAModel {
    UPMarket2StockSettingDetailDataDMAModel *dmaModel = [[UPMarket2StockSettingDetailDataDMAModel alloc] init];
    UPMarketUIDMAPeriod *dmaPeriod = [UPMarketUIIndexPeriod getDMAPeriod:NO position:0 indexID:UPMarketUIMinorIndexDMA isKline:YES];
    dmaModel.ddd1 = dmaPeriod.n1;
    dmaModel.ddd2 = dmaPeriod.n2;
    dmaModel.ama1 = dmaPeriod.m;
    return dmaModel;
}

+ (void)updateKLineDMAModel:(UPMarket2StockSettingDetailDataDMAModel *)dma {
    UPMarketUIDMAPeriod *period = [[UPMarketUIDMAPeriod alloc] init];
    period.n1 = dma.ddd1;
    period.n2 = dma.ddd2;
    period.m = dma.ama1;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexDMA isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataOBVModel *)kLineOBVModel {
    UPMarket2StockSettingDetailDataOBVModel *obvModel = [[UPMarket2StockSettingDetailDataOBVModel alloc] init];
    UPMarketUIOBVPeriod *obvPeriod = [UPMarketUIIndexPeriod getOBVPeriod:NO position:0 indexID:UPMarketUIMinorIndexOBV isKline:YES];
    obvModel.obv1 = obvPeriod.m;
    return obvModel;
}

+ (void)updateKLineOBVModel:(UPMarket2StockSettingDetailDataOBVModel *)obv {
    UPMarketUIOBVPeriod *period = [[UPMarketUIOBVPeriod alloc] init];
    period.m = obv.obv1;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexOBV isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataCCIModel *)kLineCCIModel {
    UPMarket2StockSettingDetailDataCCIModel *cciModel = [[UPMarket2StockSettingDetailDataCCIModel alloc] init];
    UPMarketUICCIPeriod *cciPeriod = [UPMarketUIIndexPeriod getCCIPeriod:NO position:0 indexID:UPMarketUIMinorIndexCCI isKline:YES];
    cciModel.cci = cciPeriod.n;
    return cciModel;
}

+ (void)updateKLineCCIModel:(UPMarket2StockSettingDetailDataCCIModel *)cci {
    UPMarketUICCIPeriod *period = [[UPMarketUICCIPeriod alloc] init];
    period.n = cci.cci;
    [UPMarketUIIndexPeriod setPeriod:NO position:0 indexID:UPMarketUIMinorIndexCCI isKline:YES period:period];
}

+ (UPMarket2StockSettingDetailDataMAModel *)kLineMAModel {
    UPMarket2StockSettingDetailDataMAModel *maModel = [[UPMarket2StockSettingDetailDataMAModel alloc] init];
    UPMarketUIMAPeriod *maPeriod = [UPMarketUIIndexPeriod getMAPeriod:YES position:0 indexID:UPMarketUIMajorIndexMALine isKline:YES];
    maModel.ma1 = maPeriod.ma1;
    maModel.ma2 = maPeriod.ma2;
    maModel.ma3 = maPeriod.ma3;
    maModel.ma4 = maPeriod.ma4;
    maModel.ma5 = maPeriod.ma5;
    maModel.ma1On = maPeriod.ma1On;
    maModel.ma2On = maPeriod.ma2On;
    maModel.ma3On = maPeriod.ma3On;
    maModel.ma4On = maPeriod.ma4On;
    maModel.ma5On = maPeriod.ma5On;
    return maModel;
}

+ (void)updateKLineMAModel:(UPMarket2StockSettingDetailDataMAModel *)maModel {
    UPMarketUIMAPeriod *period = [[UPMarketUIMAPeriod alloc] init];
    period.ma1 = maModel.ma1;
    period.ma2 = maModel.ma2;
    period.ma3 = maModel.ma3;
    period.ma4 = maModel.ma4;
    period.ma5 = maModel.ma5;
    period.ma1On = maModel.ma1IsOn;
    period.ma2On = maModel.ma2IsOn;
    period.ma3On = maModel.ma3IsOn;
    period.ma4On = maModel.ma4IsOn;
    period.ma5On = maModel.ma5IsOn;
    [UPMarketUIIndexPeriod setPeriod:YES position:0 indexID:UPMarketUIMajorIndexMALine isKline:YES period:period];
}

/// MARK: --
+ (UPMarket2StockSettingDetailDataBaseModel *)modelWithType:(UPMarket2StockSettingType)type {
    switch (type) {
        case UPMarket2StockSettingTypeMinuteMACD:
            return [self minuteMACDModel];
        case UPMarket2StockSettingTypeKLineBOLL:
            return [self kLineBOLLModel];
        case UPMarket2StockSettingTypeKLineVOL:
            return [self kLineVOLModel];
        case UPMarket2StockSettingTypeKLineMACD:
            return [self kLineMACDModel];
        case UPMarket2StockSettingTypeKLineKDJ:
            return [self kLineKDJModel];
        case UPMarket2StockSettingTypeKLineRSI:
            return [self kLineRSIModel];
        case UPMarket2StockSettingTypeKLineBIAS:
            return [self kLineBIASModel];
        case UPMarket2StockSettingTypeKLineVR:
            return [self kLineVRModel];
        case UPMarket2StockSettingTypeKLineCR:
            return [self kLineCRModel];
        case UPMarket2StockSettingTypeKLineWR:
            return [self kLineWRModel];
        case UPMarket2StockSettingTypeKLineDMA:
            return [self kLineDMAModel];
        case UPMarket2StockSettingTypeKLineOBV:
            return [self kLineOBVModel];
        case UPMarket2StockSettingTypeKLineCCI:
            return [self kLineCCIModel];
        case UPMarket2StockSettingTypeKLineJX:
            return [self kLineMAModel];
        default:
            return [[UPMarket2StockSettingDetailDataBaseModel alloc] init];
    }
}

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wincompatible-pointer-types"

+ (void)saveModel:(UPMarket2StockSettingDetailDataBaseModel *)baseModel type:(UPMarket2StockSettingType)type {
    switch (type) {
        case UPMarket2StockSettingTypeMinuteMACD:
        {
        	[self updateMinuteMACDModel:baseModel];
        	break;
        }
        case UPMarket2StockSettingTypeKLineBOLL:
        {
        	[self updateKLineBOLLModel:baseModel];
        	break;
        }
        case UPMarket2StockSettingTypeKLineVOL:
        {
        	[self updateKLineVOLModel:baseModel];
        	break;
        }
        case UPMarket2StockSettingTypeKLineMACD:
        {
        	[self updateKLineMACDModel:baseModel];
        	break;
        }
        case UPMarket2StockSettingTypeKLineKDJ:
        {
        	[self updateKLineKDJModel:baseModel];
        	break;
        }
        case UPMarket2StockSettingTypeKLineRSI:
        {
        	[self updateKLineRSIModel:baseModel];
        	break;
        }
        case UPMarket2StockSettingTypeKLineBIAS:
        {
            [self updateKLineBIASModel:baseModel];
            break;
        }
        case UPMarket2StockSettingTypeKLineVR:
        {
            [self updateKLineVRModel:baseModel];
            break;
        }
        case UPMarket2StockSettingTypeKLineCR:
        {
            [self updateKLineCRModel:baseModel];
            break;
        }
        case UPMarket2StockSettingTypeKLineWR:
        {
            [self updateKlineWRModel:baseModel];
            break;
        }
        case UPMarket2StockSettingTypeKLineDMA:
        {
            [self updateKLineDMAModel:baseModel];
            break;
        }
        case UPMarket2StockSettingTypeKLineOBV:
        {
            [self updateKLineOBVModel:baseModel];
            break;
        }
        case UPMarket2StockSettingTypeKLineCCI:
        {
            [self updateKLineCCIModel:baseModel];
            break;
        }
        case UPMarket2StockSettingTypeKLineJX:
        {
            [self updateKLineMAModel:baseModel];
            break;
        }
        default:
            break;
    }
    
}

#pragma clang diagnostic pop

+ (void)resetModel:(UPMarket2StockSettingDetailDataBaseModel *)baseModel type:(UPMarket2StockSettingType)type {
    // 恢复默认值
    [baseModel resetDefaultValue];
    
    [self saveModel:baseModel type:type];
}

+ (void)updateDataWithType:(UPMarket2StockSettingType)type model:(UPMarket2StockSettingDetailDataBaseModel *)baseModel dataSet:(UPMarket2StockSettingDetailDataSet *)dataSet value:(NSInteger)value {
    switch (type) {
        case UPMarket2StockSettingTypeIndexPlarm:
        {
            UPMarket2StockSettingDetailDataDynamicModel *dynamicModel = (UPMarket2StockSettingDetailDataDynamicModel *)baseModel;
            [dynamicModel updateDataSet:dataSet.indexTag forValue:value];
        }
            break;
        case UPMarket2StockSettingTypeMinuteMACD:
        case UPMarket2StockSettingTypeKLineMACD:
        {
            UPMarket2StockSettingDetailDataMACDModel *macd = (UPMarket2StockSettingDetailDataMACDModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataMACDIndexTagDiff1) {
                macd.diff1 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMACDIndexTagDiff2) {
                macd.diff2 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMACDIndexTagDea) {
                macd.dea = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineBOLL:
        {
            UPMarket2StockSettingDetailDataBOLLModel *boll = (UPMarket2StockSettingDetailDataBOLLModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataBOLLIndexTagDiff1) {
                boll.diff1 = value;
            }
//            else if (dataSet.indexTag == UPMarket2StockSettingDetailDataBOLLIndexTagDiff2) {
//                boll.diff2 = value;
//            }
        }
            break;
        case UPMarket2StockSettingTypeKLineVOL:
        {
            UPMarket2StockSettingDetailDataVOLModel *vol = (UPMarket2StockSettingDetailDataVOLModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataVOLIndexTagMa1) {
                vol.ma1 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataVOLIndexTagMa2) {
                vol.ma2 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataVOLIndexTagMa3) {
                vol.ma3 = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineKDJ:
        {
            UPMarket2StockSettingDetailDataKDJModel *kdj = (UPMarket2StockSettingDetailDataKDJModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataKDJIndexTagK) {
                kdj.k = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataKDJIndexTagD) {
                kdj.d = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataKDJIndexTagJ) {
                kdj.j = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineRSI:
        {
            UPMarket2StockSettingDetailDataRSIModel *rsi = (UPMarket2StockSettingDetailDataRSIModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataRSIIndexTagRSI1) {
                rsi.rsi1 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataRSIIndexTagRSI2) {
                rsi.rsi2 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataRSIIndexTagRSI3) {
                rsi.rsi3 = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineBIAS:
        {
            UPMarket2StockSettingDetailDataBIASModel *bias = (UPMarket2StockSettingDetailDataBIASModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataBIASIndexTagBIAS1) {
                bias.bias1 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataBIASIndexTagBIAS2) {
                bias.bias2 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataBIASIndexTagBIAS3) {
                bias.bias3 = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineVR:
        {
            UPMarket2StockSettingDetailDataVRModel *vr = (UPMarket2StockSettingDetailDataVRModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataVRIndexTagVR1) {
                vr.vr1 = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineCR:
        {
            UPMarket2StockSettingDetailDataCRModel *cr = (UPMarket2StockSettingDetailDataCRModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataCRIndexTagCR1) {
                cr.cr1 = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineWR:
        {
            UPMarket2StockSettingDetailDataWRModel *wr = (UPMarket2StockSettingDetailDataWRModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataWRIndexTagWR1) {
                wr.wr1 = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineDMA:
        {
            UPMarket2StockSettingDetailDataDMAModel *dma = (UPMarket2StockSettingDetailDataDMAModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataDMAIndexTagDDD1) {
                dma.ddd1 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataDMAIndexTagDDD2) {
                dma.ddd2 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataDMAIndexTagAMA1) {
                dma.ama1 = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineOBV:
        {
            UPMarket2StockSettingDetailDataOBVModel *obv = (UPMarket2StockSettingDetailDataOBVModel *)baseModel;
            if (dataSet.indexTag  == UPMarket2StockSettingDetailDataOBVIndexTagOBV1) {
                obv.obv1 = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineCCI:
        {
            UPMarket2StockSettingDetailDataCCIModel *cci = (UPMarket2StockSettingDetailDataCCIModel *)baseModel;
            if (dataSet.indexTag  == UPMarket2StockSettingDetailDataCCIIndexTagCCI) {
                cci.cci = value;
            }
        }
            break;
        case UPMarket2StockSettingTypeKLineJX:
        {
            UPMarket2StockSettingDetailDataMAModel *ma = (UPMarket2StockSettingDetailDataMAModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa1) {
                ma.ma1 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa2) {
                ma.ma2 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa3) {
                ma.ma3 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa4) {
                ma.ma4 = value;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa5) {
                ma.ma5 = value;
            }
        }
            break;
        default:
            break;
    }
}

+ (void)updateDataWithType:(UPMarket2StockSettingType)type model:(UPMarket2StockSettingDetailDataBaseModel *)baseModel dataSet:(UPMarket2StockSettingDetailDataSet *)dataSet enable:(BOOL)isEnable {
    switch (type) {
        case UPMarket2StockSettingTypeKLineJX:
        {
            UPMarket2StockSettingDetailDataMAModel *ma = (UPMarket2StockSettingDetailDataMAModel *)baseModel;
            if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa1) {
                ma.ma1On = isEnable;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa2) {
                ma.ma2On = isEnable;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa3) {
                ma.ma3On = isEnable;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa4) {
                ma.ma4On = isEnable;
            } else if (dataSet.indexTag == UPMarket2StockSettingDetailDataMAIndexTagMa5) {
                ma.ma5On = isEnable;
            }
        }
            break;
            
        default:
            break;
    }
}

@end
