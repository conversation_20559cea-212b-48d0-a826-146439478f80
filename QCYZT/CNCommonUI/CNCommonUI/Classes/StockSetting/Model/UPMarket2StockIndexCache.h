//
//  UPMarket2StockIndexCache.h
//  UPMarket2
//
//  Created by 彭继宗 on 2023/5/30.
//

#import <Foundation/Foundation.h>
#import <UPMarketIndex/UPMarketIndex.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, UPMarket2StockIndexCacheType) {
    UPMarket2StockIndexCacheTypeUndefine,
    UPMarket2StockIndexCacheTypeDapan,//大盘
    UPMarket2StockIndexCacheTypeBlock,//板块
    UPMarket2StockIndexCacheTypeStock,//个股
};

@interface UPMarket2StockIndexCache : NSObject

//cache目前有三个维度 第一个是CacheType 第二是分时和k线 第三是主副图
- (void)setup;

/// 获取当前指标信息 (排序后)
/// - Parameter key:
- (NSArray<UPMarket2StockSettingCellDataSet *> *)getCacheIndexInfoListForKey:(NSString *)key;

#pragma mark - 指标模板
- (NSArray<UPMarket2StockSettingCellDataSet *> *)getSortedCacheTemplateIndexInfoListIsKline:(BOOL)isKline;

//sortIds
- (NSArray *)getSortedTemplateArrayIsKline:(BOOL)isKline;

//排序的方法
//cache目前有三个维度 第一个是CacheType 第二是分时和k线 第三是主副图
//初始化设置
///   - key: 对应的UPMarket2StockIndexCache 的key
- (void)removeOrderListForKey:(NSString *)key;


/// 更新指标排序方法
/// - Parameters:
///   - orderArray: 最新的排序过后的数组
///   - key: 对应的UPMarket2StockIndexCache 的key
- (void)updateOrderList:(NSArray<NSNumber *> *)orderArray
                 ForKey:(NSString *)key;


/// 获得当前缓存排序后数组方法
/// - Parameters:
///   - dataArray: 从网络和本地读取完的所有数据index数组 乱序的
///   - key: UPMarket2StockIndexCache 的key
///   - contaninsHiddenIndex: 是否包含隐藏的 的key
///   return 返回排序后的index数组
- (NSArray<NSNumber *> *)sortArrayByLocalSettingWithArray:(NSArray<NSNumber *> *)dataArray
                                                   ForKey:(NSString *)key
                                     contaninsHiddenIndex:(BOOL)contaninsHiddenIndex;
/// 设置里面隐藏或展示指标 默认都展示
/// - Parameters:
///   - hidden: 是否隐藏
///   - indexId: 对应指标id
- (void)handleHidden:(BOOL)hidden forIndexId:(NSInteger)indexId;
/// 获取当前状态
/// - Parameter indexId: 指标id
- (BOOL)isHiddenForIndexId:(NSInteger)indexId;

NSString *keyForCacheType(BOOL isKline, BOOL isMajor);
NSString *keyForTemplate(BOOL isKline);


@end

NS_ASSUME_NONNULL_END
