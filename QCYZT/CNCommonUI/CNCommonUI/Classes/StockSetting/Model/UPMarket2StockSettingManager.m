//
//  UPMarket2StockSettingManager.m
//  UPMarket2
//
//  Created by 方恒 on 2020/4/2.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingManager.h"
#import "UPMarket2StockSettingStateManager.h"
#import "UPMarket2StockSettingDefaultHomeDataSet.h"
#import <UPMarketUISDK/UPMarketUIIndexUtil.h>
#import <UPMarketIndex/UPMarketIndex.h>
#import <UPMarketIndex/UPMarketIndexManager.h>
#import "UPMarket2StockIndexCache.h"


@interface UPMarket2StockSettingManager ()

@end

@implementation UPMarket2StockSettingManager


+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static UPMarket2StockSettingManager *instance = nil;
    dispatch_once(&onceToken,^{
        instance = [[UPMarket2StockSettingManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    if (self = [super init]) {
//        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginNotify) name:UPNotifyUserDidLogin object:nil];
//        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginNotify) name:UPNotifyUserDidLogout object:nil];
    }
    return self;
}

- (void)loginNotify
{
    [self setupIndexMap];
}

- (void)setupIndexMap
{
    [self.indexCache setup];
}


- (void)removeOrderListForKey:(NSString *)key
{
    [self.indexCache removeOrderListForKey:key];
}

- (void)updateOrderList:(NSArray<NSNumber *> *)orderArray ForKey:(nonnull NSString *)key
{
    [self.indexCache updateOrderList:orderArray ForKey:key];
}

/// 设置里面隐藏或展示指标 默认都展示
/// - Parameters:
///   - hidden: 是否隐藏
///   - indexId: 对应指标id
- (void)handleHidden:(BOOL)hidden forIndexId:(NSInteger)indexId
{
    [self.indexCache handleHidden:hidden forIndexId:indexId];
}

/// 获取当前状态
/// - Parameter indexId: 指标id
- (BOOL)isHiddenForIndexId:(NSInteger)indexId
{
    return [self.indexCache isHiddenForIndexId:indexId];
}
- (NSArray<NSNumber *> *)sortArrayByLocalSettingWithArray:(NSArray<NSNumber *> *)dataArray ForKey:(nonnull NSString *)key contaninsHiddenIndex:(BOOL)contaninsHiddenIndex
{
    return [self.indexCache sortArrayByLocalSettingWithArray:dataArray ForKey:key contaninsHiddenIndex:contaninsHiddenIndex];
}


// 获取设置
+ (NSArray <UPMarket2StockSettingCellDataSet *> *)getStockSetting {
    
    return [NSArray array];
}

// 批量添加股票设置
+ (void)addStockSettings:(NSArray <UPMarket2StockSettingCellDataSet *> *)markModel {
    
}


// 删除设置
+ (void)cleanStockSettings:(void (^)(NSString *msg, BOOL success))handle {
    
}



+ (UPMarket2StockSettingAlertModel *)alertModelWithType:(UPMarket2StockSettingType)type {
    UPMarket2StockSettingAlertModel *alertModel = [[UPMarket2StockSettingAlertModel alloc] init];
    switch (type) {
        case UPMarket2StockSettingTypeOtherZDXS:
            alertModel.title = @"较上笔涨跌提示";
            alertModel.message = @"若较上一笔价格上涨，则在自选股列表中会有红色闪烁效果；若较上一笔下跌，则会有绿色闪烁效果，关闭开关则不提示，不显示闪烁效果。";
            break;
        case UPMarket2StockSettingTypeOtherSDXD:
            alertModel.title = @"闪电下单开关设置";
            alertModel.message = @"若打开闪电下单开关，则在个股详情底部导航显示闪电下单按钮；若关闭闪电下单开关，则在个股详情底部导航显示普通交易下单按钮。（默认为开启状态）";
            break;
        case UPMarket2StockSettingTypeOtherPFZT:
            alertModel.title = @"智能换肤说明";
            alertModel.message = @"智能换肤是指根据不同时段显示不同皮肤主题，在当日6:00-19:00时段内显示日间版皮肤，在当日19:00-次日6:00时段内显示夜间版皮肤。";
            break;
            
        default:
            break;
    }
    return alertModel;
}

- (NSArray *)minuteDataSetArrStockHq:(UPHqStockHq *)stockHq{
    // 集合竞价
    UPMarket2StockSettingCellDataSet *jhjjDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    jhjjDataSet.title = @"集合竞价";
    jhjjDataSet.type = UPMarket2StockSettingTypeMinuteJHJJ;
    UPMarket2StockSettingSegmentDataSet *jhjjSegment = [[UPMarket2StockSettingSegmentDataSet alloc] init];
    jhjjSegment.items = @[@"智能开启", @"保持开启", @"保持关闭"];
    jhjjSegment.itemWidth = UPWidth(70);
    jhjjSegment.needHelp = YES;
    jhjjSegment.selectSegmentIndex = [UPMarket2StockSettingStateManager jhjjState];
    jhjjSegment.initialIndex = [UPMarket2StockSettingStateManager jhjjState];
    jhjjDataSet.segmentDataSet = jhjjSegment;
    jhjjDataSet.cellStyle = UPMarket2StockSettingCellStyleSegment;
    
    // 五档位置
    UPMarket2StockSettingCellDataSet *wdwzDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    wdwzDataSet.title = @"五档位置";
    wdwzDataSet.type = UPMarket2StockSettingTypeMinuteWDWZ;
    UPMarket2StockSettingSegmentDataSet *wdwzSegment = [[UPMarket2StockSettingSegmentDataSet alloc] init];
    wdwzSegment.items = @[@"右边", @"下边"];
    wdwzSegment.itemWidth = UPWidth(50);
    wdwzSegment.needHelp = NO;
    wdwzSegment.selectSegmentIndex = [UPMarket2StockSettingStateManager wdwzPosition];
    wdwzSegment.initialIndex = [UPMarket2StockSettingStateManager wdwzPosition];
    wdwzDataSet.segmentDataSet = wdwzSegment;
    wdwzDataSet.cellStyle = UPMarket2StockSettingCellStyleSegment;
    
    // 副图数量
    UPMarket2StockSettingCellDataSet *ftslDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    ftslDataSet.title = @"副图数量";
    ftslDataSet.type = UPMarket2StockSettingTypeMinuteFTSL;
    UPMarket2StockSettingStepperDataSet *stepperDataSet = [[UPMarket2StockSettingStepperDataSet alloc] init];
    stepperDataSet.value = [UPMarket2StockSettingStateManager numberOfMinuteMinorChart];
    stepperDataSet.initialValue = [UPMarket2StockSettingStateManager numberOfMinuteMinorChart];
    stepperDataSet.minValue = 1;
    stepperDataSet.maxValue = 4;
    stepperDataSet.stepValue = 1;
    ftslDataSet.stepperDataSet = stepperDataSet;
    ftslDataSet.cellStyle = UPMarket2StockSettingCellStyleStepper;
    
    NSDictionary *funcDict = @{
        kUPMarket2StockSettingSectionTitleKey: @"分时功能设置",
        kUPMarket2StockSettingRowsKey: @[
                jhjjDataSet,
                wdwzDataSet,
                ftslDataSet
        ],
    };
    
    
    NSArray *majorArray = [self.indexCache getCacheIndexInfoListForKey:keyForCacheType(NO, YES)];
    
    majorArray = majorArray.up_filter(^BOOL(NSUInteger idx, UPMarket2StockSettingCellDataSet * _Nonnull obj) {
        return !IsValidateArray(obj.jce_adaptCategories) || [obj.jce_adaptCategories containsObject:@(stockHq.origCategory).stringValue];
    });
    
    NSDictionary *majorDict = @{
        kUPMarket2StockSettingSectionTitleKey: @"主图指标设置",
        kUPMarket2StockSettingRowsKey: majorArray,
    };
    
    NSArray *minorArray = [self.indexCache getCacheIndexInfoListForKey:keyForCacheType(NO, NO)];
    
    minorArray = minorArray.up_filter(^BOOL(NSUInteger idx, UPMarket2StockSettingCellDataSet * _Nonnull obj) {
        return !IsValidateArray(obj.jce_adaptCategories) || [obj.jce_adaptCategories containsObject:@(stockHq.origCategory).stringValue];
    });
    
    NSDictionary *minorDict = @{
        kUPMarket2StockSettingSectionTitleKey: @"副图指标设置",
        kUPMarket2StockSettingRowsKey:minorArray,
    };
    
    
    NSMutableArray *tmp = @[funcDict, majorDict, minorDict].mutableCopy;
    
    NSArray *templateArray = [self.indexCache getSortedCacheTemplateIndexInfoListIsKline:NO];
    
    if (templateArray.count) {
        
        NSDictionary *templateDict = @{
            kUPMarket2StockSettingSectionTitleKey: @"指标模板设置",
            kUPMarket2StockSettingRowsKey:templateArray,
        };
        
        [tmp addObject:templateDict];
    }
    
    
    return tmp.copy;
}

- (UPMarket2StockSettingCellDataSet *)generateSetFromObj:(MarketIndicatorSysIndexConfigInfo *)obj
{
    UPMarket2StockSettingCellDataSet * itemDataSet = [UPMarket2StockSettingCellDataSet new];
    itemDataSet.title = obj.jce_indexDescInfo.jce_name;
    itemDataSet.type = UPMarket2StockSettingTypeIndexPlarm;
    itemDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    itemDataSet.indexPlarmInfo = obj;
    itemDataSet.showVip = [UPMarketIndexManager.share isVipIndexWithId:obj.jce_indexDescInfo.jce_formulaID];
    itemDataSet.indexID = obj.jce_indexDescInfo.jce_formulaID;
    itemDataSet.singleBtnStyle = [UPMarketIndexManager.share dynamicParamIndexTypeFromPlatformIndexId:obj.jce_indexDescInfo.jce_formulaID] !=UPMarketIndexDynamicTypeNone ?  UPMarket2StockSettingSingleBtnStyleSet : UPMarket2StockSettingSingleBtnStyleHelp;
    
    return itemDataSet;
}


- (NSArray *)kLineDataSetArrWithStockHq:(UPHqStockHq *)stockHq
{
    // 除复权
    UPMarket2StockSettingCellDataSet *cfqDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    cfqDataSet.title = @"除复权";
    cfqDataSet.type = UPMarket2StockSettingTypeKLineCFQ;
    UPMarket2StockSettingSegmentDataSet *cfqSegment = [[UPMarket2StockSettingSegmentDataSet alloc] init];
    cfqSegment.items = @[@"前复权", @"后复权", @"不复权"];
    cfqSegment.itemWidth = UPWidth(60);
    cfqSegment.needHelp = YES;
    cfqSegment.selectSegmentIndex = [UPMarket2StockSettingStateManager cfqState];
    cfqSegment.initialIndex = 0;
    cfqDataSet.segmentDataSet = cfqSegment;
    cfqDataSet.cellStyle = UPMarket2StockSettingCellStyleSegment;

    
    // K线样式
    UPMarket2StockSettingCellDataSet *klineStyleDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    klineStyleDataSet.title = @"K线样式";
    klineStyleDataSet.type = UPMarket2StockSettingTypeKLineYS;
    UPMarket2StockSettingSegmentDataSet *kxysSegment = [[UPMarket2StockSettingSegmentDataSet alloc] init];
    kxysSegment.items = @[@"空心", @"实心"];
    kxysSegment.itemWidth = UPWidth(50);
    kxysSegment.needHelp = NO;
    kxysSegment.selectSegmentIndex = [UPMarket2StockSettingStateManager kLinePattern];
    kxysSegment.initialIndex = [UPMarket2StockSettingStateManager kLinePattern];
    klineStyleDataSet.segmentDataSet = kxysSegment;
    klineStyleDataSet.cellStyle = UPMarket2StockSettingCellStyleSegment;
    
    // 副图数量
    UPMarket2StockSettingCellDataSet *minorViewsCountDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    minorViewsCountDataSet.title = @"副图数量";
    minorViewsCountDataSet.type = UPMarket2StockSettingTypeKLineFTSL;
    UPMarket2StockSettingStepperDataSet *stepperDataSet = [[UPMarket2StockSettingStepperDataSet alloc] init];
    stepperDataSet.value = [UPMarket2StockSettingStateManager numberOfKLineMinorChart];
    stepperDataSet.initialValue = [UPMarket2StockSettingStateManager numberOfKLineMinorChart];
    stepperDataSet.minValue = 1;
    stepperDataSet.maxValue = 4;
    stepperDataSet.stepValue = 1;
    minorViewsCountDataSet.stepperDataSet = stepperDataSet;
    minorViewsCountDataSet.cellStyle = UPMarket2StockSettingCellStyleStepper;
    
    // 显示缺口
    UPMarket2StockSettingCellDataSet *xsqkDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    xsqkDataSet.title = @"显示缺口";
    xsqkDataSet.type = UPMarket2StockSettingTypeKLineXSQK;
    UPMarket2StockSettingSwitchDataSet *xsqkSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    xsqkSwitch.on = [UPMarket2StockSettingStateManager isShowGap];
    xsqkSwitch.initialOn = [UPMarket2StockSettingStateManager isShowGap];
    xsqkSwitch.minorTitle = @"(显示近3个)";
    xsqkDataSet.switchDataSet = xsqkSwitch;
    xsqkDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
    // 显示概念板块
    UPMarket2StockSettingCellDataSet *GLDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    GLDataSet.type = UPMarket2StockSettingTypeKlineGL;
    GLDataSet.title = @"显示概念板块";
    UPMarket2StockSettingSwitchDataSet *GLSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    GLSwitch.on = [UPMarket2StockSettingStateManager isShowGL];
    GLSwitch.initialOn = [UPMarket2StockSettingStateManager isShowGL];
    GLSwitch.hideHelpBtn = YES;
    GLDataSet.switchDataSet = GLSwitch;
    GLDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
    
    // 显示价笼
    UPMarket2StockSettingCellDataSet *pcDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    pcDataSet.type = UPMarket2StockSettingTypePriceCage;
    pcDataSet.title = @"价笼提示";
    UPMarket2StockSettingSwitchDataSet *pcSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    pcSwitch.on = [UPMarket2StockSettingStateManager isShowPriceCage];
    pcSwitch.initialOn = [UPMarket2StockSettingStateManager isShowPriceCage];
    pcSwitch.hideHelpBtn = YES;
    pcDataSet.switchDataSet = pcSwitch;
    pcDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
    
    // 加自选记录
    UPMarket2StockSettingCellDataSet *OptionalDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    OptionalDataSet.type = UPMarket2StockSettingTypeOptional;
    OptionalDataSet.title = @"加自选记录";
    UPMarket2StockSettingSwitchDataSet *OptionalSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    OptionalSwitch.on = ![UPMarket2StockSettingStateManager isopenOptional];
    OptionalSwitch.initialOn = ![UPMarket2StockSettingStateManager isopenOptional];;
    OptionalSwitch.hideHelpBtn = YES;
    OptionalDataSet.switchDataSet = OptionalSwitch;
    OptionalDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
    
    
    NSArray *allRows = @[
        cfqDataSet,
        klineStyleDataSet,
        minorViewsCountDataSet,
        xsqkDataSet,
        GLDataSet,
        pcDataSet,
        OptionalDataSet,
    ];
        
    
    
    NSDictionary *KLineDict = @{
        kUPMarket2StockSettingSectionTitleKey: @"K线功能设置",
        kUPMarket2StockSettingRowsKey: allRows,
    };
    
    
    NSArray *majorArray = [self.indexCache getCacheIndexInfoListForKey:keyForCacheType(YES, YES)];
    
    majorArray = majorArray.up_filter(^BOOL(NSUInteger idx, UPMarket2StockSettingCellDataSet * _Nonnull obj) {
        return !IsValidateArray(obj.jce_adaptCategories) || [obj.jce_adaptCategories containsObject:@(stockHq.origCategory).stringValue];
    });
    
    
    NSDictionary *majorDict = @{
        kUPMarket2StockSettingSectionTitleKey: @"主图指标设置",
        kUPMarket2StockSettingRowsKey: majorArray,
    };
    
    NSArray *minorArray = [self.indexCache getCacheIndexInfoListForKey:keyForCacheType(YES, NO)];
    
    minorArray = minorArray.up_filter(^BOOL(NSUInteger idx, UPMarket2StockSettingCellDataSet * _Nonnull obj) {
        return !IsValidateArray(obj.jce_adaptCategories) || [obj.jce_adaptCategories containsObject:@(stockHq.origCategory).stringValue];
    });
    
    NSDictionary *minorDict = @{
        kUPMarket2StockSettingSectionTitleKey: @"副图指标设置",
        kUPMarket2StockSettingRowsKey:minorArray,
    };
    
    
    
    NSMutableArray *tmp = @[KLineDict, majorDict, minorDict].mutableCopy;
    
    NSArray *templateArray = [self.indexCache getSortedCacheTemplateIndexInfoListIsKline:YES];
    
//    if (templateArray.count && [UPUserManager isUserLogin]) {
        
        NSDictionary *templateDict = @{
            kUPMarket2StockSettingSectionTitleKey: @"指标模板设置",
            kUPMarket2StockSettingRowsKey:templateArray,
        };
        
        [tmp addObject:templateDict];
//    }
    
    
    return tmp.copy;
}


+ (NSArray *)otherDataSetArr {
    // 比上笔涨跌显示
    UPMarket2StockSettingCellDataSet *zdxsDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    zdxsDataSet.type = UPMarket2StockSettingTypeOtherZDXS;
    zdxsDataSet.title = @"比上笔涨跌显示";
    UPMarket2StockSettingSwitchDataSet *zdxsSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    zdxsSwitch.on = [UPMarket2StockSettingStateManager isShowFlashEffect];
    zdxsSwitch.initialOn = [UPMarket2StockSettingStateManager isShowFlashEffect];
    zdxsDataSet.switchDataSet = zdxsSwitch;
    zdxsDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
    // 屏幕常亮
    UPMarket2StockSettingCellDataSet *pmclDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    pmclDataSet.type = UPMarket2StockSettingTypeOtherPMCL;
    pmclDataSet.title = @"屏幕常亮";
    UPMarket2StockSettingSwitchDataSet *pmclSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    pmclSwitch.on = [UPMarket2StockSettingStateManager isScreenWakey];
    pmclSwitch.initialOn = [UPMarket2StockSettingStateManager isScreenWakey];
    pmclSwitch.hideHelpBtn = YES;
    pmclDataSet.switchDataSet = pmclSwitch;
    pmclDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
//    // 默认首页设置
//    UPMarket2StockSettingCellDataSet *syszDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
//    syszDataSet.type = UPMarket2StockSettingTypeOtherSYSZ;
//    syszDataSet.title = @"默认首页设置";
//    UPMarket2StockSettingArrowDataSet *syszArrow = [[UPMarket2StockSettingArrowDataSet alloc] init];
//    syszArrow.arrowValue = [UPMarket2StockSettingStateManager defaultHomeStateTitle];
//    syszDataSet.arrowDataSet = syszArrow;
//    syszDataSet.cellStyle = UPMarket2StockSettingCellStyleArrow;
//
    
    // 权限管理
    UPMarket2StockSettingCellDataSet *permissionDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    permissionDataSet.type = UPMarket2StockSettingTypePermissionManager;
    permissionDataSet.title = @"权限管理";
    UPMarket2StockSettingArrowDataSet *permissionArrow = [[UPMarket2StockSettingArrowDataSet alloc] init];
    permissionDataSet.arrowDataSet = permissionArrow;
    permissionDataSet.cellStyle = UPMarket2StockSettingCellStyleArrow;
    
    
    
    return @[
              zdxsDataSet,
              pmclDataSet,
//              syszDataSet,
//              permissionDataSet,
            ];
}


+ (NSArray *)tuBiaoDataSetArr {
    
    // 龙虎榜
    UPMarket2StockSettingCellDataSet *LHBDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    LHBDataSet.type = UPMarket2StockSettingTypeTuBiaoLHB;
    LHBDataSet.title = @"龙虎榜";
    UPMarket2StockSettingSwitchDataSet *LHBSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    LHBSwitch.on = ![UPMarket2StockSettingStateManager isWinnersList];
    LHBSwitch.initialOn = ![UPMarket2StockSettingStateManager isWinnersList];
    LHBSwitch.hideHelpBtn = YES;
    LHBDataSet.switchDataSet = LHBSwitch;
    LHBDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
    
    NSDictionary *iconDict = @{
        kUPMarket2StockSettingSectionTitleKey: @"数据图标设置",
        kUPMarket2StockSettingRowsKey:@[LHBDataSet],
    };
    
    
    // 点击屏幕切换指标
    UPMarket2StockSettingCellDataSet *switchIndexDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    switchIndexDataSet.type = UPMarket2StockSettingTypeSwitchIndexTap;
    switchIndexDataSet.title = @"点击屏幕切换指标";
    UPMarket2StockSettingSwitchDataSet *tapSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    tapSwitch.on = [UPMarket2StockSettingStateManager isSwitchIndexTap];
    tapSwitch.initialOn = [UPMarket2StockSettingStateManager isSwitchIndexTap];
    tapSwitch.hideHelpBtn = YES;
    switchIndexDataSet.switchDataSet = tapSwitch;
    switchIndexDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
    // 行情大字版
    UPMarket2StockSettingCellDataSet *ztfdDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    ztfdDataSet.type = UPMarket2StockSettingTypeOtherZTFD;
    ztfdDataSet.title = @"行情大字版";
    UPMarket2StockSettingSwitchDataSet *ztfdSwitch = [[UPMarket2StockSettingSwitchDataSet alloc] init];
    ztfdSwitch.on = [UPMarket2StockSettingStateManager isFontScale];
    ztfdSwitch.initialOn = [UPMarket2StockSettingStateManager isFontScale];
    ztfdSwitch.hideHelpBtn = YES;
    ztfdDataSet.switchDataSet = ztfdSwitch;
    ztfdDataSet.cellStyle = UPMarket2StockSettingCellStyleSwitch;
    
    
    NSDictionary *otherDict = @{
        kUPMarket2StockSettingSectionTitleKey: @"其他设置",
        kUPMarket2StockSettingRowsKey:@[switchIndexDataSet,ztfdDataSet],
    };
    return @[iconDict,otherDict];
}

+ (NSArray *)defaultHomeDataSetArr {
    UPMarket2StockSettingDefaultHomeDataSet *smartDataSet = [[UPMarket2StockSettingDefaultHomeDataSet alloc] init];
    smartDataSet.cellStyle = UPMarket2StockSettingDefaultHomeStyleSmart;
    smartDataSet.type = UPMarket2StockSettingOtherDefaultHomeStateSmart;
    smartDataSet.selected = [UPMarket2StockSettingStateManager defaultHomeState] == UPMarket2StockSettingOtherDefaultHomeStateSmart;
    
    UPMarket2StockSettingDefaultHomeDataSet *homeDataSet = [[UPMarket2StockSettingDefaultHomeDataSet alloc] init];
    homeDataSet.cellStyle = UPMarket2StockSettingDefaultHomeStyleNormal;
    homeDataSet.type = UPMarket2StockSettingOtherDefaultHomeStateHome;
    homeDataSet.selected = [UPMarket2StockSettingStateManager defaultHomeState] == UPMarket2StockSettingOtherDefaultHomeStateHome;
    
    UPMarket2StockSettingDefaultHomeDataSet *marketDataSet = [[UPMarket2StockSettingDefaultHomeDataSet alloc] init];
    marketDataSet.cellStyle = UPMarket2StockSettingDefaultHomeStyleNormal;
    marketDataSet.type = UPMarket2StockSettingOtherDefaultHomeStateMarket;
    marketDataSet.selected = [UPMarket2StockSettingStateManager defaultHomeState] == UPMarket2StockSettingOtherDefaultHomeStateMarket;
    
    UPMarket2StockSettingDefaultHomeDataSet *financeDataSet = [[UPMarket2StockSettingDefaultHomeDataSet alloc] init];
    financeDataSet.cellStyle = UPMarket2StockSettingDefaultHomeStyleNormal;
    financeDataSet.type = UPMarket2StockSettingOtherDefaultHomeStateChoose;
    financeDataSet.selected = [UPMarket2StockSettingStateManager defaultHomeState] == UPMarket2StockSettingOtherDefaultHomeStateChoose;
    
    UPMarket2StockSettingDefaultHomeDataSet *tradeDataSet = [[UPMarket2StockSettingDefaultHomeDataSet alloc] init];
    tradeDataSet.cellStyle = UPMarket2StockSettingDefaultHomeStyleNormal;
    tradeDataSet.type = UPMarket2StockSettingOtherDefaultHomeStateStudy;
    tradeDataSet.selected = [UPMarket2StockSettingStateManager defaultHomeState] == UPMarket2StockSettingOtherDefaultHomeStateStudy;
    
    UPMarket2StockSettingDefaultHomeDataSet *personalDataSet = [[UPMarket2StockSettingDefaultHomeDataSet alloc] init];
    personalDataSet.cellStyle = UPMarket2StockSettingDefaultHomeStyleNormal;
    personalDataSet.type = UPMarket2StockSettingOtherDefaultHomeStateOptional;
    personalDataSet.selected = [UPMarket2StockSettingStateManager defaultHomeState] == UPMarket2StockSettingOtherDefaultHomeStateOptional;
    
    NSArray *arr = @[
        smartDataSet,
        homeDataSet,
        marketDataSet,
        financeDataSet,
        tradeDataSet,
        personalDataSet,
    ];
    
    return arr;
}

- (UPMarket2StockIndexCache *)indexCache {
    if (!_indexCache) {
        _indexCache = [[UPMarket2StockIndexCache alloc] init];
    }
    return _indexCache;
}

@end
