//
//  UPMarket2StockIndexCache.m
//  UPMarket2
//
//  Created by 彭继宗 on 2023/5/30.
//

#import "UPMarket2StockIndexCache.h"
#import <UPMarketSDK/UPMarketWUPUtil.h>
#import <UPMarketUISDK/UPMarketUIIndexUtil.h>
#import <MMKV/MMKV.h>
#import "UPMarket2StockSettingManager.h"
#import <UPMarketSDK/UPMarketWUPUtil.h>


static NSString *const kTemplateCacheStateKey = @"kTemplateCacheStateKey";
static NSString *const kIndexHiddenStateKey = @"kIndexHiddenStateKey";

@interface UPMarket2StockIndexCache ()

@property (nonatomic, strong) NSMutableDictionary<NSString *,NSMutableArray<UPMarketIndexIdNumber >*> *cache;//key由两部分组成 "$(cacheType)_$(isKline)"

@property (nonatomic, strong) NSMutableDictionary<UPMarketIndexIdNumber,UPMarket2StockSettingCellDataSet *> *allIndexDataMap;

@end

@implementation UPMarket2StockIndexCache

- (void)setup
{
    //重新初始化
    _cache = nil;
    
    [self setupLocalIndexMap];
    [self setupDefaultIndexIdListForAllType];
}

- (void)setupDefaultIndexIdListForAllType
{
    NSInteger kDapanOriCategory = 1;
    NSInteger kBlockOriCategory = 22;
    NSInteger kStockOriCategory = 0;
    
    NSArray *defaultMajorMinuteLocalIndexs =  @[@(UPMarketUIMajorIndexTraditional)];
    
    NSArray *defaultMajorKlineLocalIndexs =  @[@(UPMarketUIMajorIndexMALine),@(UPMarketUIMajorIndexBOLL)];
    
    //k线副图
    NSArray *defaultMinorKlineLocalIndexs = @[
        @(UPMarketUIMinorIndexVOL),
        @(UPMarketUIMinorIndexMACD),
        @(UPMarketUIMinorIndexKDJ),
        @(UPMarketUIMinorIndexRSI),
        @(UPMarketUIMinorIndexBIAS),
        @(UPMarketUIMinorIndexVR),
        @(UPMarketUIMinorIndexCR),
        @(UPMarketUIMinorIndexWR),
        @(UPMarketUIMinorIndexDMA),
        @(UPMarketUIMinorIndexOBV),
        @(UPMarketUIMinorIndexCCI)];
    
    //分时副图
    NSArray *defaultMinorMinuteLocalIndexs = @[
        @(UPMarketUIMinorIndexVOL),
        @(UPMarketUIMinorIndexMACD),
        @(UPMarketUIMinorIndexLB)];
    
    //指标平台数据
    {
        [self.cache[keyForCacheType(NO, YES)] addObjectsFromArray:defaultMajorMinuteLocalIndexs];
        [self.cache[keyForCacheType(YES ,YES)] addObjectsFromArray:defaultMajorKlineLocalIndexs];
        
        [self insertAndFillIndexPlatformDataForObjs:UPMarketIndexManager.share.indexDataArray];
        
        //分时副图
        NSArray *stockMinorMinuteLocalIndexs = [defaultMinorMinuteLocalIndexs arrayByAddingObjectsFromArray:@[@(UPMarketUIMinorIndexDDZ),
                                                                                                             @(UPMarketUIMinorIndexZJBY)]];
        
        [self.cache[keyForCacheType( NO ,NO)] addObjectsFromArray:stockMinorMinuteLocalIndexs];
        [self.cache[keyForCacheType( YES ,NO)] addObjectsFromArray:defaultMinorKlineLocalIndexs];
    }
    
}

- (void)setupLocalIndexMap
{
    // 本地数据
    
    // 均线
    UPMarket2StockSettingCellDataSet *jxDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    jxDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMajorIndexMALine];
    jxDataSet.type = UPMarket2StockSettingTypeKLineJX;
    jxDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    jxDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    jxDataSet.indexID = UPMarketUIMajorIndexMALine;
    
    [self.allIndexDataMap setObject:jxDataSet forKey:@(UPMarketUIMajorIndexMALine)];
    
    // BOLL
    UPMarket2StockSettingCellDataSet *bollDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    bollDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMajorIndexBOLL];
    bollDataSet.type = UPMarket2StockSettingTypeKLineBOLL;
    bollDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    bollDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    bollDataSet.indexID = UPMarketUIMajorIndexBOLL;
    
    [self.allIndexDataMap setObject:bollDataSet forKey:@(UPMarketUIMajorIndexBOLL)];
    
    // VOL
    UPMarket2StockSettingCellDataSet *volDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    volDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexVOL];
    volDataSet.type = UPMarket2StockSettingTypeKLineVOL;
    volDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    volDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    volDataSet.indexID = UPMarketUIMinorIndexVOL;
    
    [self.allIndexDataMap setObject:volDataSet forKey:@(UPMarketUIMinorIndexVOL)];
    
    // MACD
    UPMarket2StockSettingCellDataSet *macdDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    macdDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexMACD];
    macdDataSet.type = UPMarket2StockSettingTypeKLineMACD;
    macdDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    macdDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    macdDataSet.indexID = UPMarketUIMinorIndexMACD;
    
    [self.allIndexDataMap setObject:macdDataSet forKey:@(UPMarketUIMinorIndexMACD)];
    
    // KDJ
    UPMarket2StockSettingCellDataSet *kdjDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    kdjDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexKDJ];
    kdjDataSet.type = UPMarket2StockSettingTypeKLineKDJ;
    kdjDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    kdjDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    kdjDataSet.indexID = UPMarketUIMinorIndexKDJ;
    
    [self.allIndexDataMap setObject:kdjDataSet forKey:@(UPMarketUIMinorIndexKDJ)];
    
    // RSI
    UPMarket2StockSettingCellDataSet *rsiDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    rsiDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexRSI];
    rsiDataSet.type = UPMarket2StockSettingTypeKLineRSI;
    rsiDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    rsiDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    rsiDataSet.indexID = UPMarketUIMinorIndexRSI;
    
    [self.allIndexDataMap setObject:rsiDataSet forKey:@(UPMarketUIMinorIndexRSI)];
    
    // BIAS
    UPMarket2StockSettingCellDataSet *biasDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    biasDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexBIAS];
    biasDataSet.type = UPMarket2StockSettingTypeKLineBIAS;
    biasDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    biasDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    biasDataSet.indexID = UPMarketUIMinorIndexBIAS;
    
    [self.allIndexDataMap setObject:biasDataSet forKey:@(UPMarketUIMinorIndexBIAS)];
    
    // VR
    UPMarket2StockSettingCellDataSet *vrDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    vrDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexVR];
    vrDataSet.type = UPMarket2StockSettingTypeKLineVR;
    vrDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    vrDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    vrDataSet.indexID = UPMarketUIMinorIndexVR;
    
    [self.allIndexDataMap setObject:vrDataSet forKey:@(UPMarketUIMinorIndexVR)];
    
    // CR
    UPMarket2StockSettingCellDataSet *crDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    crDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexCR];
    crDataSet.type = UPMarket2StockSettingTypeKLineCR;
    crDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    crDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    crDataSet.indexID = UPMarketUIMinorIndexCR;
    
    [self.allIndexDataMap setObject:crDataSet forKey:@(UPMarketUIMinorIndexCR)];
    
    // WR
    UPMarket2StockSettingCellDataSet *wrDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    wrDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexWR];
    wrDataSet.type = UPMarket2StockSettingTypeKLineWR;
    wrDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    wrDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    wrDataSet.indexID = UPMarketUIMinorIndexWR;
    
    [self.allIndexDataMap setObject:wrDataSet forKey:@(UPMarketUIMinorIndexWR)];
    
    // DMA
    UPMarket2StockSettingCellDataSet *dmaDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    dmaDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDMA];
    dmaDataSet.type = UPMarket2StockSettingTypeKLineDMA;
    dmaDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    dmaDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    dmaDataSet.indexID = UPMarketUIMinorIndexDMA;
    
    [self.allIndexDataMap setObject:dmaDataSet forKey:@(UPMarketUIMinorIndexDMA)];
    
    // OBV
    UPMarket2StockSettingCellDataSet *obvDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    obvDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexOBV];
    obvDataSet.type = UPMarket2StockSettingTypeKLineOBV;
    obvDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    obvDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    obvDataSet.indexID = UPMarketUIMinorIndexOBV;
    
    [self.allIndexDataMap setObject:obvDataSet forKey:@(UPMarketUIMinorIndexOBV)];
    
    // CCI
    UPMarket2StockSettingCellDataSet *cciDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    cciDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexCCI];
    cciDataSet.type = UPMarket2StockSettingTypeKLineCCI;
    cciDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleSet;
    cciDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    cciDataSet.indexID = UPMarketUIMinorIndexCCI;
    [self.allIndexDataMap setObject:cciDataSet forKey:@(UPMarketUIMinorIndexCCI)];
    
    // 分时走势
    UPMarket2StockSettingCellDataSet *fszsDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    fszsDataSet.title = @"分时走势";
    fszsDataSet.type = UPMarket2StockSettingTypeIndexPlarm;
    fszsDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleNone;
    fszsDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    fszsDataSet.indexID = UPMarketUIMajorIndexTraditional;
    [self.allIndexDataMap setObject:fszsDataSet forKey:@(UPMarketUIMajorIndexTraditional)];
    
    // 量比
    UPMarket2StockSettingCellDataSet *lbDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
    lbDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexLB];
    lbDataSet.type = UPMarket2StockSettingTypeMinuteLB;
    lbDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleHelp;
    lbDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    lbDataSet.indexID = UPMarketUIMinorIndexLB;
    
    [self.allIndexDataMap setObject:lbDataSet forKey:@(UPMarketUIMinorIndexLB)];
    
    
    
//    // DDZ
//    UPMarket2StockSettingCellDataSet *ddzDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
//    ddzDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDDZ];
//    //    ddzDataSet.tagString = @"L2专享";
//    ddzDataSet.type = UPMarket2StockSettingTypeMinuteDDZ;
//    ddzDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleHelp;
//    ddzDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
//    ddzDataSet.indexID = UPMarketUIMinorIndexDDZ;
//    
//    [self.allIndexDataMap setObject:ddzDataSet forKey:@(UPMarketUIMinorIndexDDZ)];
//    
//    // 资金博弈
//    UPMarket2StockSettingCellDataSet *zjbyDataSet = [[UPMarket2StockSettingCellDataSet alloc] init];
//    zjbyDataSet.title = [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexZJBY];
//    //    zjbyDataSet.tagString = @"L2专享";
//    zjbyDataSet.type = UPMarket2StockSettingTypeMinuteZJBY;
//    zjbyDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleHelp;
//    zjbyDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
//    zjbyDataSet.indexID = UPMarketUIMinorIndexZJBY;
//    
//    [self.allIndexDataMap setObject:zjbyDataSet forKey:@(UPMarketUIMinorIndexZJBY)];

}

- (NSArray<UPMarketIndexIdNumber> *)getCacheIndexIdListOrderForKey:(NSString *)key
{
    NSMutableArray<UPMarketIndexIdNumber >*cacheArray = self.cache[key];
    
    return cacheArray;
}


- (NSArray<UPMarket2StockSettingCellDataSet *> *)getCacheIndexInfoListForKey:(NSString *)key
{
    NSArray<UPMarketIndexIdNumber> *indexIdList = [self getCacheIndexIdListOrderForKey:key];
    
    if (!IsValidateArray(indexIdList)) {
        return @[];
    }
    
    indexIdList = [self sortArrayByLocalSettingWithArray:indexIdList ForKey:key contaninsHiddenIndex:YES];
    
    //按顺序取出数据
    NSMutableArray<UPMarket2StockSettingCellDataSet *> *datas = [NSMutableArray array];
    for (UPMarketIndexIdNumber indexIdNum in indexIdList) {
        if ([self.allIndexDataMap.allKeys containsObject:indexIdNum]) {
            [datas addObject:self.allIndexDataMap[indexIdNum]];
        }
    }
    return datas.copy;
}

- (NSArray<UPMarket2StockSettingCellDataSet *> *)getSortedCacheTemplateIndexInfoListIsKline:(BOOL)isKline
{
    NSArray<NSNumber *> *templateIdList = [self getSortedTemplateArrayIsKline:isKline];
    
    if (!IsValidateArray(templateIdList)) {
        return @[];
    }
    
    NSArray<UPMarketIndexGroupModel *> *allDatas = [UPMarketIndexManager.share getAllTemplateData];
    
    
    //按顺序取出数据
    NSMutableArray<UPMarket2StockSettingCellDataSet *> *datas = [NSMutableArray array];
    for (NSNumber * templateId in templateIdList) {
        UPMarketIndexGroupModel *group = allDatas.up_first(^BOOL(NSUInteger idx, UPMarketIndexGroupModel*  _Nonnull obj) {
            return obj.identify == templateId.integerValue;
        });
        
        if ([self isSameTypeForIsKline:isKline indexId:group.mainIndexIdArray.firstObject.integerValue]) {
            UPMarket2StockSettingCellDataSet *dataSet = [self generateSetFromGroupModel:group];
            [datas addObject:dataSet];
        }
    }
    return datas.copy;
}


- (BOOL)isSameTypeForIsKline:(BOOL)isKline indexId:(NSInteger)indexId
{
    DGPlatformSystemIndexType sysIndexType = [UPMarketIndexHelper localSystemTypeForIndexId:indexId isKline:isKline];
    
    MarketIndicatorSysIndexConfigInfo *indexInfo = [UPMarketIndexManager.share indexConfigInfoWithId:indexId];
    if (IsValidateArray(indexInfo.jce_indexDescInfo.jce_vPeriod)) {
        NSInteger minutePeriod = [UPMarketWUPUtil getIndicatorPeriodType:UPMarketIndexLocalPeriodRTMin];
        if (![indexInfo.jce_indexDescInfo.jce_vPeriod containsObject:@(minutePeriod)]) {
            sysIndexType = DGPlatformSystemIndexTypeKline;
        }else
            sysIndexType = DGPlatformSystemIndexTypeMinute;
    }
    
    
    if(!isKline){
        if(sysIndexType == DGPlatformSystemIndexTypeMinute){
            return YES;
        }
    }else{
        if(sysIndexType == DGPlatformSystemIndexTypeKline){
            return YES;
        }
    }
    return NO;
}

//cache目前有三个维度 第一个是CacheType 第二是分时和k线 第三是主副图

//初始化设置
- (void)removeOrderListForKey:(NSString *)key
{
    if (IsValidateString(key)) {
        [MMKV.defaultMMKV removeValueForKey:key];
    }
}

- (void)updateOrderList:(NSArray<NSNumber *> *)orderArray ForKey:(nonnull NSString *)key
{
    if (IsValidateString(key) && IsValidateArray(orderArray)) {
        [MMKV.defaultMMKV setObject:orderArray forKey:key];
    }
}

/// 设置里面隐藏或展示指标 默认都展示
/// - Parameters:
///   - hidden: 是否隐藏
///   - indexId: 对应指标id
- (void)handleHidden:(BOOL)hidden forIndexId:(NSInteger)indexId
{
    NSMutableDictionary *cacheDict = [[MMKV.defaultMMKV getObjectOfClass:[NSDictionary class] forKey:kIndexHiddenStateKey] mutableCopy];
    if (!IsValidateDict(cacheDict)) {
        cacheDict = [NSMutableDictionary dictionary];
    }
    [cacheDict setObject:@(hidden) forKey:@(indexId)];
    
    [MMKV.defaultMMKV setObject:cacheDict forKey:kIndexHiddenStateKey];
}

/// 获取当前状态
/// - Parameter indexId: 指标id
- (BOOL)isHiddenForIndexId:(NSInteger)indexId
{
    NSMutableDictionary *cacheDict = [[MMKV.defaultMMKV getObjectOfClass:[NSDictionary class] forKey:kIndexHiddenStateKey] mutableCopy];
    if (!IsValidateDict(cacheDict)) {
        return NO;
    }
    BOOL hidden = [[cacheDict objectForKey:@(indexId)] boolValue];
    
    return hidden;
}
- (NSArray<NSNumber *> *)sortArrayByLocalSettingWithArray:(NSArray<NSNumber *> *)dataArray ForKey:(nonnull NSString *)key contaninsHiddenIndex:(BOOL)contaninsHiddenIndex
{
    NSArray<NSNumber *> *finalSortArray = [self getCacheIndexIdListOrderForKey:key];
    
    NSArray *mmkvCacheArray = [MMKV.defaultMMKV getObjectOfClass:[NSArray class] forKey:key];
    if (IsValidateArray(mmkvCacheArray)) {
        finalSortArray = mmkvCacheArray;
    }
    
    NSArray *outputArray = nil;
    if (IsValidateArray(finalSortArray)) {
        outputArray = dataArray.up_sorted(^BOOL(NSNumber * _Nonnull obj1, NSNumber * _Nonnull obj2) {
            NSInteger obj1Index = [finalSortArray indexOfObject:obj1];
            NSInteger obj2Index = [finalSortArray indexOfObject:obj2];
            return obj2Index > obj1Index;
        });
    } else {
        outputArray = dataArray;
    }
    
    if (!contaninsHiddenIndex) {
        //排除隐藏的
        NSArray *finalArray = outputArray.up_filter(^BOOL(NSUInteger idx, NSNumber*  _Nonnull indexId) {
            return ![UPMarket2StockSettingManager.sharedInstance isHiddenForIndexId:indexId.integerValue];
        });
        //保底展示一个
        if (!IsValidateArray(finalArray) && outputArray.firstObject) {
            finalArray = @[outputArray.firstObject];
        }
        return finalArray;
    }
    
    return outputArray;
}

#pragma mark - 指标模板
- (NSArray *)getSortedTemplateArrayIsKline:(BOOL)isKline
{
    NSArray<NSNumber *> *dataArray = [[UPMarketIndexManager.share getAllTemplateData] valueForKeyPath:@"identify"];
    
    NSArray *sortArray = [MMKV.defaultMMKV getObjectOfClass:[NSArray class] forKey:keyForTemplate(isKline)];
    
    NSArray *outputArray = nil;
    if (IsValidateArray(dataArray)) {
        outputArray = dataArray.up_sorted(^BOOL(NSNumber * _Nonnull obj1, NSNumber * _Nonnull obj2) {
            NSInteger obj1Index = [sortArray indexOfObject:obj1];
            NSInteger obj2Index = [sortArray indexOfObject:obj2];
            if (sortArray.count == 0) {
                NSInteger obj1OriIndex = [dataArray indexOfObject:obj1];
                NSInteger obj2OriIndex = [dataArray indexOfObject:obj2];
                if (obj1.integerValue < 0 || obj2.integerValue < 0) {
                    return obj2OriIndex > obj1OriIndex;
                }
            }
            return obj2Index > obj1Index;
        });
    } else {
        outputArray = dataArray;
    }
    return outputArray;
}


#pragma mark - privite
- (UPMarket2StockSettingCellDataSet *)generateSetFromObj:(MarketIndicatorSysIndexConfigInfo *)obj
{
    UPMarket2StockSettingCellDataSet * itemDataSet = [UPMarket2StockSettingCellDataSet new];
    itemDataSet.title = obj.jce_indexDescInfo.jce_name;
    itemDataSet.type = UPMarket2StockSettingTypeIndexPlarm;
    itemDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleHelp;
    itemDataSet.cellStyle = UPMarket2StockSettingCellStyleSingleBtn;
    itemDataSet.indexPlarmInfo = obj;
    itemDataSet.showVip = [UPMarketIndexManager.share isVipIndexWithId:obj.jce_indexDescInfo.jce_formulaID];
    itemDataSet.indexID = obj.jce_indexDescInfo.jce_formulaID;
    itemDataSet.singleBtnStyle = [UPMarketIndexManager.share dynamicParamIndexTypeFromPlatformIndexId:obj.jce_indexDescInfo.jce_formulaID] !=UPMarketIndexDynamicTypeNone ?  UPMarket2StockSettingSingleBtnStyleSet : UPMarket2StockSettingSingleBtnStyleHelp;
    itemDataSet.jce_adaptCategories = obj.jce_indexDescInfo.jce_adaptCategories;
    
    return itemDataSet;
}
- (UPMarket2StockSettingCellDataSet *)generateSetFromGroupModel:(UPMarketIndexGroupModel *)groupModel
{
    UPMarket2StockSettingCellDataSet * itemDataSet = [UPMarket2StockSettingCellDataSet new];
    itemDataSet.title = groupModel.name;
    itemDataSet.type = UPMarket2StockSettingTypeIndexGroupTemplate;
    itemDataSet.singleBtnStyle = UPMarket2StockSettingSingleBtnStyleNone;
    itemDataSet.cellStyle = UPMarket2StockSettingCellStyleMuBan;
    itemDataSet.muBanId = groupModel.identify;
    return itemDataSet;
}

- (void)insertAndFillIndexPlatformDataForObjs:(NSArray<MarketIndicatorSysIndexConfigInfo *>*)objs
{
    [objs enumerateObjectsUsingBlock:^(MarketIndicatorSysIndexConfigInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        UPMarket2StockSettingCellDataSet * itemDataSet = [self generateSetFromObj:obj];
        //判断无权限
        UPMarketIndexPermissionState state = [[UPMarketIndexManager share] permissionStateWithCurrentUserWithIndexId:obj.jce_indexDescInfo.jce_formulaID];
        if (state != UPMarketIndexPermissionStateHidden) {
            [self.allIndexDataMap setObject:itemDataSet forKey:@(obj.jce_indexDescInfo.jce_formulaID)];
            //默认的排序
            NSInteger period = [UPMarketWUPUtil getIndicatorPeriodType:UPMarketMinuteRTMin];
            BOOL isKline = ![obj.jce_indexDescInfo.jce_vPeriod containsObject:@(period)];
            BOOL isMajor = obj.jce_indexDescInfo.jce_graphType == MarketIndicatorSys_DRAWLINE_GRAPH_E_MAIN_GRAPH;
            [self.cache[keyForCacheType(isKline, isMajor)] addObject:@(obj.jce_indexDescInfo.jce_formulaID)];
        }
    }];
}

NSString *keyForCacheType(BOOL isKline, BOOL isMajor)
{
    return [NSString stringWithFormat:@"%d_%d",isKline,isMajor];
}

NSString *keyForTemplate(BOOL isKline)
{
    NSString *uid =  @"visitor";
    
    return [NSString stringWithFormat:@"template_%@_%d",uid,isKline];
}

#pragma mark - SETTER && GETTER
- (NSMutableDictionary *)cache {
    if (!_cache) {
        _cache = [[NSMutableDictionary alloc] init];
        [_cache setObject:[NSMutableArray array] forKey:keyForCacheType(YES, YES)];
        [_cache setObject:[NSMutableArray array] forKey:keyForCacheType(NO, YES)];
        [_cache setObject:[NSMutableArray array] forKey:keyForCacheType(YES, NO)];
        [_cache setObject:[NSMutableArray array] forKey:keyForCacheType(NO, NO)];
    }
    return _cache;
}


- (NSMutableDictionary *)allIndexDataMap {
    if (!_allIndexDataMap) {
        _allIndexDataMap = [[NSMutableDictionary alloc] init];
    }
    return _allIndexDataMap;
}

@end
