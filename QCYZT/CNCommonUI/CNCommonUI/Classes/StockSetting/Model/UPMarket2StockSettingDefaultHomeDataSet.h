//
//  UPMarket2StockSettingDefaultHomeDataSet.h
//  UPMarket2
//
//  Created by 方恒 on 2020/3/31.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarket2StockSettingStateManager.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDefaultHomeStyle) {
    UPMarket2StockSettingDefaultHomeStyleNormal,
    UPMarket2StockSettingDefaultHomeStyleSmart,
};

@interface UPMarket2StockSettingDefaultHomeDataSet : NSObject

@property (nonatomic, assign) UPMarket2StockSettingOtherDefaultHomeState type;

@property (nonatomic, assign) UPMarket2StockSettingDefaultHomeStyle cellStyle;

@property (nonatomic, copy, readonly) NSString *title;

@property (nonatomic, copy, readonly) NSString *detailTitle;

@property (nonatomic, assign, getter=isSelected) BOOL selected;

@end

NS_ASSUME_NONNULL_END
