//
//  UPMarket2SDIExtendEditOptionViewCell.m
//  UPMarket2
//
//  Created by fang on 2020/6/8.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDIExtendEditOptionViewCell.h"

@interface UPMarket2SDIExtendEditOptionViewCell ()

@property (nonatomic, strong) UIButton *checkButtonView;

@end

@implementation UPMarket2SDIExtendEditOptionViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.up_listCellBgColor;
        [self.contentView addSubview:self.nameLabel];
        [self.contentView addSubview:self.checkButtonView];
        
        [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(15);
            make.bottom.equalTo(self.contentView);
            make.top.equalTo(self.contentView).offset(22);
        }];
        
        [self.checkButtonView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.contentView).offset(-15);
            make.centerY.equalTo(self.nameLabel);
            make.width.height.equalTo(@16);
        }];
    }
    
    return self;
}

- (void)setIsCheck:(BOOL)isCheck {
    _isCheck = isCheck;
    self.checkButtonView.selected = isCheck;
//    if (isCheck) {
//        self.nameLabel.textColor = UIColor.upmarketui_riseColor;
//    } else {
//        self.nameLabel.textColor = UIColor.up_textPrimaryColor;
//    }
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [UILabel new];
        _nameLabel.font = [UIFont up_mediumFontOfSize:15];
        _nameLabel.textColor = UIColor.up_textPrimaryColor;
    }
    
    return _nameLabel;
}

- (UIButton *)checkButtonView {
    if (!_checkButtonView) {
        _checkButtonView = [UIButton new];
        _checkButtonView.userInteractionEnabled = NO;
        [_checkButtonView setBackgroundImage:UPTImg(@"个股/自选未选中") forState:UIControlStateNormal];
        [_checkButtonView setBackgroundImage:UPTImg(@"个股/自选选中") forState:UIControlStateSelected];

    }
    
    return _checkButtonView;
}
@end
