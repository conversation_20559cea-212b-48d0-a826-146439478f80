//
//  UPMarket2StockSettingController.m
//  UPMarket2
//
//  Created by sammy<PERSON> on 2020/2/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingController.h"
#import "UPMarket2StockSettingMinuteView.h"
#import "UPMarket2StockSettingKlineView.h"
#import "UPMarket2StockSettingOtherView.h"
#import "UPMarket2StockSettingDetailManager.h"
#import "UPMarket2StockSettingDetailController.h"
#import "UPMarket2StockSettingManager.h"
#import "UPMarket2StockSettingStateManager.h"
#import "UPMarket2StockSettingDefaultHomeController.h"
#import "UPMarket2StockSettingAlertView.h"
#import "UPMarket2StockSettingNaviView.h"
#import "UPMarket2StockSettingTubiaoView.h"
#import "UPMarket2LandscapeUtil.h"

NSString *const UPMarket2StockSettingStateChangedNotificationName = @"UPMarket2StockSettingStateChangedNotificationName";
NSString *const UPMarket2UpdateStockViewsNotificationName = @"UPMarket2UpdateStockViewsNotificationName";

@interface UPMarket2StockSettingController ()<UPMarket2StockSettingMinuteViewDelegate, UPMarket2StockSettingKlineViewDelegate, UPMarket2StockSettingOtherViewDelegate, UPMarket2StockSettingNaviViewDelegate,UPMarket2StockSettingTubiaoViewDelegate>

@property (nonatomic, strong) UPMarket2StockSettingNaviView *naviView;
@property (nonatomic, strong) UPMarket2StockSettingMinuteView *minuteView;
@property (nonatomic, strong) UPMarket2StockSettingKlineView *klineView;
@property (nonatomic, strong) UPMarket2StockSettingOtherView *otherView;
@property (nonatomic, strong) UPMarket2StockSettingTubiaoView *tubiaoView;


@property (nonatomic, assign, readonly) BOOL stateChanged;
@property (nonatomic, assign, readonly) BOOL minuteChanged;
@property (nonatomic, assign, readonly) BOOL kLineChanged;
@property (nonatomic, assign, readonly) BOOL otherChanged;
@property (nonatomic, assign) BOOL detailChanged;

@end

@implementation UPMarket2StockSettingController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.hidesNavigationBarWhenPush = YES;
    
    self.naviView = [[UPMarket2StockSettingNaviView alloc] init];
    self.naviView.isHSA = self.isHSA;
    self.naviView.delegate = self;
    
    [self.view addSubview:self.naviView];
    [self.view addSubview:self.minuteView];
    [self.view addSubview:self.klineView];
    [self.view addSubview:self.otherView];
    [self.view addSubview:self.tubiaoView];

    
    self.naviView.selectedSegmentIndex = self.tabIndex;
    [self showTab:self.tabIndex];
}

// 通过传参type设置默认的tab，默认为分时
- (void)handleRouter:(NSDictionary *)params {
    NSString *type = params[@"type"];
    
    if ([type isEqualToString:UPRouterMarketSettingTypeMinute]) {
        self.tabIndex = UPMarket2StockSettingTabIndexMinute;
    } else if ([type isEqualToString:UPRouterMarketSettingTypeKline]) {
        self.tabIndex = UPMarket2StockSettingTabIndexKline;
    }
//    else if ([type isEqualToString:UPRouterMarketSettingTypeOther]) {
//        self.tabIndex = UPMarket2StockSettingTabIndexOther;
//    }
    else if ([type isEqualToString:UPRouterMarketSettingTypeTuBiao]) {
        self.tabIndex = UPMarket2StockSettingTabIndextubiao;
    }
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    
    _naviView.frame = CGRectMake(0, 0, CGRectGetWidth(self.view.bounds), self.up_safeAreaInsets.top + 44);

    CGRect contentFrame =  CGRectMake(0, CGRectGetMaxY(self.naviView.frame), CGRectGetWidth(self.view.bounds), CGRectGetHeight(self.view.bounds)-CGRectGetMaxY(self.naviView.frame));
    self.minuteView.frame = contentFrame;
    self.klineView.frame = contentFrame;


    self.otherView.frame = contentFrame;
    self.tubiaoView.frame = contentFrame;
}

- (void)dealloc {
    if (self.stateChanged) { // stateChanged判断不准确，导致“修改MACD后，再次进入MACD设置，修改不生效”。此时发现不只是MACD，所有指标设置都不生效，原因就在此。故注释此行。
        [[NSNotificationCenter defaultCenter] postNotificationName:UPMarket2StockSettingStateChangedNotificationName object:nil userInfo:nil];
    }
}

- (void)setIsTS:(BOOL)isTS {
    _isTS = isTS;
 
}

- (void)showTab:(UPMarket2StockSettingTabIndex)tabIndex {
    switch (tabIndex) {
        case UPMarket2StockSettingTabIndexMinute:
        {
            self.minuteView.hidden = NO;
            self.minuteView.isTS = self.isTS;
            self.minuteView.isIgnoreScroll = self.isIgnoreScroll;
            [self.minuteView reloadSettingWhere];
            self.klineView.hidden = YES;
                
            self.otherView.hidden = YES;
            self.tubiaoView.hidden = YES;
        }
            break;
        case UPMarket2StockSettingTabIndexKline:
        {
            self.minuteView.hidden = YES;
            self.klineView.hidden = NO;
            self.klineView.isTS = self.isTS;
            self.klineView.isIgnoreScroll = self.isIgnoreScroll;
            [self.klineView reloadSettingWhere];
            self.otherView.hidden = YES;
            self.tubiaoView.hidden = YES;
        }
            break;
//        case UPMarket2StockSettingTabIndexOther:
//        {
//        self.minuteView.hidden = YES;
//        self.klineView.hidden = YES;
//        self.otherView.hidden = NO;
//            self.tubiaoView.hidden = YES;
//        }
//            break;
        case UPMarket2StockSettingTabIndextubiao:
        {
            self.minuteView.hidden = YES;
            self.klineView.hidden = YES;
            self.otherView.hidden = YES;
            self.tubiaoView.hidden = NO;

        }
            break;
        default:
            break;
    }
}

/// MARK: UPMarket2StockSettingMinuteViewDelegate
/// 点击帮助？或设置 跳转到详情页
- (void)up_market2StockSettingMinuteView:(UPMarket2StockSettingMinuteView *)view didClickMinuteType:(UPMarket2StockSettingType)type indexConfigInfo:(nonnull MarketIndicatorSysIndexConfigInfo *)indexConfigInfo{
    [self pushToDetailCOntrollerWithType:type indexConfigInfo:indexConfigInfo];
}

/// 点击segment切换状态
- (void)up_market2StockSettingMinuteView:(UPMarket2StockSettingMinuteView *)view didClickMinuteType:(UPMarket2StockSettingType)type atSegmentIndex:(NSInteger)index {
    switch (type) {
        case UPMarket2StockSettingTypeMinuteJHJJ:
            // 集合竞价
            [UPMarket2StockSettingStateManager updateJhjjState:index];
            break;
        case UPMarket2StockSettingTypeMinuteWDWZ:
            // 五档位置
            [UPMarket2StockSettingStateManager updateWdwzPosition:index];
            break;
        default:
            break;
    }
}

- (void)up_market2StockSettingMinuteView:(UPMarket2StockSettingKlineView *)view didClickMinuteType:(UPMarket2StockSettingType)type switchControlChangedOn:(BOOL)isOn indexId:(NSInteger)indexId{
    [UPMarket2StockSettingManager.sharedInstance handleHidden:!isOn forIndexId:indexId];
}

/// 点击stepper时值改变
- (void)up_market2StockSettingMinuteView:(UPMarket2StockSettingMinuteView *)view didClickMinuteType:(UPMarket2StockSettingType)type stepperValueChanged:(NSUInteger)value {
    if (type == UPMarket2StockSettingTypeMinuteFTSL) {
        // 分时-副图数量
        [UPMarket2StockSettingStateManager updateMinuteMinorChartNum:value];
    }
}

/// MARK: UPMarket2StockSettingKlineViewDelegate
- (void)up_market2StockSettingKLineView:(UPMarket2StockSettingKlineView *)view didClickKLineType:(UPMarket2StockSettingType)type indexConfigInfo:(nonnull MarketIndicatorSysIndexConfigInfo *)indexConfigInfo {
    if (type == UPMarket2StockSettingTypeKLineCFQ) {
        [UPRouter navigate:UPURLCFQ];
    } else {
        [self pushToDetailCOntrollerWithType:type indexConfigInfo:indexConfigInfo];
    }
}

/// 点击segment切换状态
- (void)up_market2StockSettingKLineView:(UPMarket2StockSettingKlineView *)view didClickKLineType:(UPMarket2StockSettingType)type atSegmentIndex:(NSInteger)index {
    switch (type) {
        case UPMarket2StockSettingTypeKLineCFQ:
            // 除复权
            [UPMarket2StockSettingStateManager updateCfqState:index];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"UPMarket2StockSettingTypeKLineCFQ" object:nil userInfo:nil];
            break;
        case UPMarket2StockSettingTypeKLineYS:
            // k线样式
            [UPMarket2StockSettingStateManager updateKLinePattern:index];
            break;
        default:
            break;
    }
}

/// 点击stepper时值改变
- (void)up_market2StockSettingKLineView:(UPMarket2StockSettingKlineView *)view didClickKLineType:(UPMarket2StockSettingType)type stepperValueChanged:(NSUInteger)value {
    if (type == UPMarket2StockSettingTypeKLineFTSL) {
        [UPMarket2StockSettingStateManager updateKLineMinorChartNum:value];
    }
}

- (void)up_market2StockSettingKLineView:(UPMarket2StockSettingKlineView *)view didClickKLineType:(UPMarket2StockSettingType)type switchControlChangedOn:(BOOL)isOn indexId:(NSInteger)indexId{
    switch (type) {
        case UPMarket2StockSettingTypeKLineXSQK:
            // 显示缺口
            [UPMarket2StockSettingStateManager updateShowGap:isOn];
            break;
        case UPMarket2StockSettingTypeKlineGL:
            // 显示概念板块
            [UPMarket2StockSettingStateManager updateShowGL:isOn];
            break;
        case UPMarket2StockSettingTypeKLineXSMMD:
            // 显示买卖点
            [UPMarket2StockSettingStateManager updateShowTradingPoint:isOn];
            break;
        case UPMarket2StockSettingTypePriceCage:
            // 显示价笼
            [UPMarket2StockSettingStateManager updateShowPriceCage:isOn];
            break;
        case UPMarket2StockSettingTypeOptional:
            // 显示加自选记录
            [UPMarket2StockSettingStateManager updateKlineOptional:!isOn];
            break;

        default:
            //展示隐藏指标
        {
            [UPMarket2StockSettingManager.sharedInstance handleHidden:!isOn forIndexId:indexId];
        }
            break;
    }
}

/// MARK: UPMarket2StockSettingOtherViewDelegate
- (void)up_market2StockSettingOtherView:(UPMarket2StockSettingOtherView *)view didClickKLineType:(UPMarket2StockSettingType)type {
    if (type == UPMarket2StockSettingTypeOtherCCCBX) {
        [self pushToDetailCOntrollerWithType:type indexConfigInfo:nil];
    } else {
        [self alertWithType:type];
    }
}

- (void)up_market2StockSettingOtherView:(UPMarket2StockSettingOtherView *)view didClickKLineType:(UPMarket2StockSettingType)type atSegmentIndex:(NSInteger)index {
    // 皮肤主题
    if (type == UPMarket2StockSettingTypeOtherPFZT) {
        [UPMarket2StockSettingStateManager updateSkinTheme:index];
    }
}

- (void)up_market2StockSettingOtherView:(UPMarket2StockSettingOtherView *)view didClickKLineType:(UPMarket2StockSettingType)type switchControlChangedOn:(BOOL)isOn {
    switch (type) {
        case UPMarket2StockSettingTypeOtherCCCBX:
            // 持仓成本线
            [UPMarket2StockSettingStateManager updateShowCostLine:isOn];
            break;
        case UPMarket2StockSettingTypeOtherZDXS:
            // 比上笔涨跌显示
            [UPMarket2StockSettingStateManager updateShowFlashEffect:isOn];
            break;
        case UPMarket2StockSettingTypeOtherSDXD:
            // 闪电下单
            [UPMarket2StockSettingStateManager updateFastTrade:isOn];
            break;
        
        case UPMarket2StockSettingTypeOtherPMCL:
            // 屏幕常亮
            [UPMarket2StockSettingStateManager updateScreenWakey:isOn];
            break;

        default:
            break;
    }
}

// 图标代理
- (void)up_market2StockSettingTubiaoView:(UPMarket2StockSettingTubiaoView *)view didClickKLineType:(UPMarket2StockSettingType)type switchControlChangedOn:(BOOL)isOn {
    switch (type) {
        case UPMarket2StockSettingTypeTuBiaoLHB:
            // 图标-龙虎榜
            [UPMarket2StockSettingStateManager updateWinnersList:!isOn];
            
            break;
        case UPMarket2StockSettingTypeSwitchIndexTap:
            // 点击切换指标
            [UPMarket2StockSettingStateManager updateSwitchIndexTap:isOn];
            break;
        case UPMarket2StockSettingTypeOtherZTFD:
            // 行情大字版
            [UPMarket2StockSettingStateManager updateFontScale:isOn];
            break;
        default:
            break;
    }
}


/// 点击默认首页设置
- (void)up_market2StockSettingOtherViewSelectedDefaultHomeSetting:(UPMarket2StockSettingOtherView *)view {
    UPMarket2StockSettingDefaultHomeController *vc = [[UPMarket2StockSettingDefaultHomeController alloc] init];
    vc.dataSource = [UPMarket2StockSettingManager defaultHomeDataSetArr];
    // 刷新状态
    __weak __typeof(self)weakSelf = self;
    vc.settingChanged = ^{
        weakSelf.otherView.dataSetArr = [UPMarket2StockSettingManager otherDataSetArr];
        [weakSelf.otherView reloadData];
    };
    [self.navigationController pushViewController:vc animated:YES];
}

/// MARK: Private
// 弹窗
- (void)alertWithType:(UPMarket2StockSettingType)type {
    UPMarket2StockSettingAlertModel *alertModel = [UPMarket2StockSettingManager alertModelWithType:type];
    if (!alertModel.title || !alertModel.message) {
        return;
    }
    UPMarket2StockSettingAlertView *alertView = [UPMarket2StockSettingAlertView alertWithTitle:alertModel.title message:alertModel.message];
    [alertView showInView:self.navigationController.view];
}

// 跳转到详情页
- (void)pushToDetailCOntrollerWithType:(UPMarket2StockSettingType)type indexConfigInfo:(MarketIndicatorSysIndexConfigInfo *)indexConfigInfo{
    UPMarket2StockSettingDetailController *detailVc = [[UPMarket2StockSettingDetailController alloc] init];
    detailVc.hidesNavigationBarWhenPush = NO;
    detailVc.type = type;
    detailVc.indexConfigInfo = indexConfigInfo;
    __weak __typeof(self)weakSelf = self;
    detailVc.stateChangedBlock = ^(BOOL isChanged) {
        weakSelf.detailChanged = isChanged;
    };
    [self.navigationController pushViewController:detailVc animated:YES];
}

/// MARK: - UPMarket2StockSettingNaviViewDelegate
- (void)up_market2StockSettingNaviView:(UPMarket2StockSettingNaviView *)naviView didClickedBackBtn:(UIButton *)btn {
    [self dismiss];
}

- (void)up_market2StockSettingNaviView:(UPMarket2StockSettingNaviView *)naviView didSelectTabIndex:(NSInteger)index {
    [self showTab:index];
}

/// MARK: - 状态相关
- (BOOL)stateChanged {
    if (self.detailChanged || self.minuteChanged || self.kLineChanged || self.otherChanged) {
        return YES;
    }
    return NO;
}

- (BOOL)minuteChanged {
    if (_minuteView) {
        return _minuteView.isChanged;
    }
    return NO;
}

- (BOOL)kLineChanged {
    if (_klineView) {
        return _klineView.isChanged;
    }
    return NO;
}

- (BOOL)otherChanged {
    if (_otherView) {
        return _otherView.isChanged;
    }
    return NO;
}

/// MARK: - 处理横屏
- (BOOL)prefersStatusBarHidden {
    UIInterfaceOrientation interfaceOrientation = [[UIApplication sharedApplication] statusBarOrientation];
    if (interfaceOrientation == UIInterfaceOrientationLandscapeLeft || interfaceOrientation == UIInterfaceOrientationLandscapeRight) {
        return YES;
    }
    return NO;
}

-(BOOL)shouldAutorotate {
    return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    if ([UPMarket2LandscapeUtil isLandscape]) {
        return UIInterfaceOrientationMaskLandscape;
    } else {
        return UIInterfaceOrientationMaskPortrait;
    }
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationLandscapeRight;
}

/// MARK: - Lazy loading
- (UPMarket2StockSettingMinuteView *)minuteView {
    if (!_minuteView) {
        _minuteView = [[UPMarket2StockSettingMinuteView alloc] init];
        _minuteView.delegate = self;
        _minuteView.customConfig = self.customConfig;
        _minuteView.dataSetArr = [UPMarket2StockSettingManager.sharedInstance minuteDataSetArrStockHq:self.stockHq];
    }
    return _minuteView;
}

- (UPMarket2StockSettingKlineView *)klineView {
    if (!_klineView) {
        _klineView = [[UPMarket2StockSettingKlineView alloc] init];
        _klineView.delegate = self;
        _klineView.customConfig = self.customConfig;
        NSArray *arr = [UPMarket2StockSettingManager.sharedInstance kLineDataSetArrWithStockHq:self.stockHq];
        _klineView.dataSetArr = arr;
        
    }
    return _klineView;
}

- (UPMarket2StockSettingOtherView *)otherView {
    if (!_otherView) {
        _otherView = [[UPMarket2StockSettingOtherView alloc] init];
        _otherView.delegate = self;
        _otherView.dataSetArr = [UPMarket2StockSettingManager otherDataSetArr];
    }
    return _otherView;
}

- (UPMarket2StockSettingTubiaoView *)tubiaoView {
    if (!_tubiaoView) {
        _tubiaoView = [[UPMarket2StockSettingTubiaoView alloc] init];
        _tubiaoView.dataSetArr = [UPMarket2StockSettingManager tuBiaoDataSetArr];
        _tubiaoView.delegate = self;
        
    }
    return _tubiaoView;
}

@end
