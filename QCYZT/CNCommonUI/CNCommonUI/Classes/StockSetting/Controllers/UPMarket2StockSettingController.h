//
//  UPMarket2StockSettingController.h
//  UPMarket2
//
//  Created by sammy<PERSON> on 2020/2/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPViewController.h"


NS_ASSUME_NONNULL_BEGIN

// 设置页面有改变时发出的通知名
extern NSString *const UPMarket2StockSettingStateChangedNotificationName;
// 主副图变更时候通知
extern NSString *const UPMarket2UpdateStockViewsNotificationName;

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingTabIndex) {
    UPMarket2StockSettingTabIndexMinute,        // 分时 tab
    UPMarket2StockSettingTabIndexKline,         // K线 tab
//    UPMarket2StockSettingTabIndexOther,         // 其它 tab
    UPMarket2StockSettingTabIndextubiao,         // 图标 tab
};

@interface UPMarket2StockSettingController : UPViewController

//因为设置初始化的时候需要显示当前显示的指标, 目前只有indexHost里面有
@property (nonatomic, strong) UPHqStockHq *stockHq;

@property (nonatomic, assign) UPMarket2StockSettingTabIndex tabIndex;

@property (nonatomic, assign) BOOL isHSA;

@property (nonatomic, strong) id customConfig;
@property (nonatomic, assign) BOOL isTS;

///是否忽略滚动到头部
@property (nonatomic, assign) BOOL isIgnoreScroll;

@end

NS_ASSUME_NONNULL_END
