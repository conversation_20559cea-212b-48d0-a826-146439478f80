//
//  UPMarket2StockSettingDefaultHomeController.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/31.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDefaultHomeController.h"
#import "UPMarket2StockSettingDefaultHomeSmartCell.h"
#import "UPMarket2StockSettingDefaultHomeNormalCell.h"
#import "UPMarket2StockSettingDefaultHomeDataSet.h"
#import "UPMarket2StockSettingStateManager.h"
#import "UPMarket2StockSettingDetailNaviView.h"

@interface UPMarket2StockSettingDefaultHomeController ()<UITableViewDelegate, UITableViewDataSource, UPMarket2StockSettingDetailNaviViewDelegate>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) UPMarket2StockSettingDetailNaviView *naviView;

@end

@implementation UPMarket2StockSettingDefaultHomeController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.hidesNavigationBarWhenPush = YES;
    self.view.backgroundColor = UIColor.upmarket2_settingHeaderColor;
    [self setupUI];
    
    self.naviView.typeTitle = @"默认首页设置";
    [self.naviView setResetable:NO];
}

- (void)setupUI {
    self.naviView = [[UPMarket2StockSettingDetailNaviView alloc] init];
    self.naviView.delegate = self;
    [self.view addSubview:self.naviView];
    [self.view addSubview:self.tableView];
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    _naviView.frame = CGRectMake(0, 0, CGRectGetWidth(self.view.bounds), self.up_safeAreaInsets.top + 44);
    _tableView.frame = CGRectMake(0, CGRectGetMaxY(self.naviView.frame), CGRectGetWidth(self.view.bounds), CGRectGetHeight(self.view.bounds)-CGRectGetMaxY(self.naviView.frame));
}

- (void)dealloc {
    self.tableView.delegate = nil;
    self.tableView.dataSource = nil;
}

/// MARK: - UPMarket2StockSettingDetailNaviViewDelegate
- (void)up_market2StockSettingDetailNaviView:(UPMarket2StockSettingDetailNaviView *)view didClickBackBtn:(UIButton *)btn {
    [self dismiss];
}

- (void)up_market2StockSettingDetailNaviView:(UPMarket2StockSettingDetailNaviView *)view didClickResetBtn:(UIButton *)btn {
    
}

/// MARK: UITableViewDelegate, UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataSource.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UPMarket2StockSettingDefaultHomeDataSet *dataSet = self.dataSource[indexPath.row];
    if (dataSet.cellStyle == UPMarket2StockSettingDefaultHomeStyleSmart) {
        UPMarket2StockSettingDefaultHomeSmartCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingDefaultHomeSmartCell cellIdentifier] forIndexPath:indexPath];
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDefaultHomeStyleNormal) {
        UPMarket2StockSettingDefaultHomeNormalCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingDefaultHomeNormalCell cellIdentifier] forIndexPath:indexPath];
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else {
        NSAssert(NO, @"check dataSet cellStyle");
        return nil;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    UPMarket2StockSettingDefaultHomeDataSet *dataSet = self.dataSource[indexPath.row];
    if (dataSet.cellStyle == UPMarket2StockSettingDefaultHomeStyleSmart) {
        return UPWidth(72);
    } else {
        return UPWidth(50);
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    [self clearDataSetsState];
    UPMarket2StockSettingDefaultHomeDataSet *dataSet = self.dataSource[indexPath.row];
    dataSet.selected = YES;
    [self.tableView reloadData];
    [UPMarket2StockSettingStateManager updateDefaultHomeState:(UPMarket2StockSettingOtherDefaultHomeState)dataSet.type];
    !self.settingChanged ?: self.settingChanged();
}

- (void)clearDataSetsState {
    [self.dataSource enumerateObjectsUsingBlock:^(UPMarket2StockSettingDefaultHomeDataSet * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.selected = NO;
    }];
}

/// MARK: Lazy loading
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.tableFooterView = [[UIView alloc] init];
        _tableView.backgroundColor = UIColor.up_bgColor;
        _tableView.separatorColor = UIColor.up_dividerColor;
        _tableView.backgroundColor = UIColor.upmarket2_settingHeaderColor;
        
        // register
        [_tableView registerClass:[UPMarket2StockSettingDefaultHomeSmartCell class] forCellReuseIdentifier:[UPMarket2StockSettingDefaultHomeSmartCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingDefaultHomeNormalCell class] forCellReuseIdentifier:[UPMarket2StockSettingDefaultHomeNormalCell cellIdentifier]];
    }
    return _tableView;
}

/// MARK: - 处理横屏
- (BOOL)prefersStatusBarHidden {
    UIInterfaceOrientation interfaceOrientation = [[UIApplication sharedApplication] statusBarOrientation];
    if (interfaceOrientation == UIInterfaceOrientationLandscapeLeft || interfaceOrientation == UIInterfaceOrientationLandscapeRight) {
        return YES;
    }
    return NO;
}

-(BOOL)shouldAutorotate {
    return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    
    UIInterfaceOrientation interfaceOrientation = [[UIApplication sharedApplication] statusBarOrientation];
    
    if (interfaceOrientation == UIInterfaceOrientationPortrait) {
        return UIInterfaceOrientationMaskPortrait;
    } else if (interfaceOrientation == UIInterfaceOrientationLandscapeLeft || interfaceOrientation == UIInterfaceOrientationLandscapeRight) {
        return UIInterfaceOrientationMaskLandscape;
    }
    return UIInterfaceOrientationMaskAll;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationPortrait;
}

@end
