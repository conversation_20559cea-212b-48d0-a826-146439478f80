//
//  UPMarket2MarketInfoAStockChangeSegBtn.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/10.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <CNCommonUI/UPMarket2MarketInfoAStockChangeSegBtn.h>

@implementation UPMarket2MarketInfoAStockChangeSegBtn
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor up_colorFromHexString:@"#F9FAFB"];
        self.titleLabel.font = [UIFont systemFontOfSize:UPWidth(12)];
        self.layer.cornerRadius = 2;
//        self.layer.borderWidth = .5f;    //边框宽度
        self.bSelect = NO;
    }
    return self;
}

- (void)setBSelect:(BOOL)bSelect {
    _bSelect = bSelect;
    
    if (bSelect) {
//        self.layer.borderColor = UIColor.up_brandColor.CGColor;
        [self setTitleColor:UIColor.up_contentBgColor forState:UIControlStateNormal];
        self.backgroundColor = UIColor.up_brandColor;
    } else {
//        self.layer.borderColor = UIColor.clearColor.CGColor;
        [self setTitleColor:[UIColor up_colorFromHexString:@"#787D87"] forState:UIControlStateNormal];
        self.backgroundColor = [UIColor up_colorFromHexString:@"#F9FAFB"];
    }
    
    [self setNeedsDisplay];
}

- (void)setBSelectBorder:(BOOL)bSelectBorder {
    _bSelectBorder = bSelectBorder;
    self.layer.borderWidth = .5f;    //边框宽度
    if (_bSelectBorder) {
        self.layer.borderColor = UIColor.up_brandColor.CGColor;
        [self setTitleColor:UIColor.up_contentBgColor forState:UIControlStateNormal];
    } else {
        self.layer.borderColor = UIColor.clearColor.CGColor;
        [self setTitleColor:[UIColor up_colorFromHexString:@"#787D87"]forState:UIControlStateNormal];
        self.backgroundColor = [UIColor up_colorFromHexString:@"#F9FAFB"];
    }
    
    [self setNeedsDisplay];
}

@end
