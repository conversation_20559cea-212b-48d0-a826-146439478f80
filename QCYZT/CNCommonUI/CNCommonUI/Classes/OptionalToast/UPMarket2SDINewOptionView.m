//
//  UPMarket2SDINewOptionView.m
//  UPMarket2
//
//  Created by fang on 2020/4/23.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDINewOptionView.h"
#import "UPUserSDK/UPOptionalManager.h"

@interface UPMarket2SDINewOptionView () <UITextFieldDelegate>

@property (nonatomic, strong) UIButton *contentView;

@property (nonatomic, assign) NSInteger maxWords;

@property (nonatomic, strong) UITextField *textField;

@property (nonatomic, strong) UILabel *numLabel;

@property (nonatomic, strong) UILabel *failLabel;

@property (nonatomic, strong) UPOptionalGroupModel *groupModel;

@property (nonatomic, strong) UIButton *finishBtn;

@end

@implementation UPMarket2SDINewOptionView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.maxWords = 10;
        
        self.backgroundColor = UIColor.upmarket2_maskBgColor;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(sdiExtendEditOptionViewHide)];
        [self addGestureRecognizer:tap];
        

        
        
        // 这里用UIButton为了截获上面的点击事件，否则点击contentView也会触发单击事件
        UIButton *contentView = [UIButton new];
        contentView.backgroundColor = UIColor.up_contentBgColor;
        [self addSubview:contentView];
        self.contentView = contentView;
        
        [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_centerY).offset(60);
            make.width.centerX.equalTo(self);
            make.height.equalTo(@120);
        }];
        
        UIView *contentCenterView = [UIView new];
        contentCenterView.backgroundColor = UIColor.up_contentBgColor;
        contentCenterView.layer.cornerRadius = 8;
        [self addSubview:contentCenterView];
        [contentCenterView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(contentView.mas_top).offset(8);
            make.width.centerX.equalTo(self);
            make.height.equalTo(@16);
        }];
        
        UIView *contentHeaderView = [UIView new];
        contentHeaderView.backgroundColor = UIColor.up_contentBgColor;
//        contentHeaderView.layer.cornerRadius = 8;
        [contentView addSubview:contentHeaderView];
        
        [contentHeaderView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(contentView);
            make.height.equalTo(@58);
        }];
        
        UIView *divideLine = [UIView new];
        divideLine.backgroundColor = [UIColor up_colorFromHexString:@"#D6D6F2"];
        [contentView addSubview:divideLine];

        [divideLine mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(contentView.mas_left).offset(15);
            make.right.equalTo(contentView.mas_right).offset(-15);
            make.top.equalTo(contentHeaderView.mas_bottom);
            make.height.equalTo(@0.5);
        }];
                
        
        {
            UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            cancelBtn.titleLabel.font = [UIFont up_fontOfSize:15];
//            [cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
            [cancelBtn setImage:UPTImg(@"个股/自选关闭") forState:UIControlStateNormal];
            [cancelBtn setTitleColor:UIColor.upmarket2_moreViewTitleColor forState:UIControlStateNormal];
            [cancelBtn addTarget:self action:@selector(sdiExtendEditOptionViewHide) forControlEvents:UIControlEventTouchUpInside];
            [contentHeaderView addSubview:cancelBtn];
            
            UILabel *titleLabel = [UILabel new];
            titleLabel.text = @"选择自选分组";
            titleLabel.textColor = UIColor.up_textPrimaryColor;
            titleLabel.font = [UIFont up_boldFontOfSize:16];
            [contentHeaderView addSubview:titleLabel];
            
            UIButton *finishBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            finishBtn.titleLabel.font = [UIFont up_fontOfSize:15];
            finishBtn.enabled = YES;
            [finishBtn setTitle:@"保存" forState:UIControlStateNormal];
            [finishBtn setTitleColor:UIColor.up_brandColor forState:UIControlStateNormal];
            finishBtn.titleLabel.alpha = 0.4;
            [finishBtn addTarget:self action:@selector(sdiExtendEditOptionViewEnsure) forControlEvents:UIControlEventTouchUpInside];
            [contentHeaderView addSubview:finishBtn];
            self.finishBtn = finishBtn;
            [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(contentHeaderView);
                make.left.equalTo(contentHeaderView).offset(15);
                make.width.height.equalTo(@35);
            }];
            
            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.equalTo(contentHeaderView);
            }];
            
            [finishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(contentHeaderView);
                make.right.equalTo(contentHeaderView).offset(-15);
            }];
        }
        
        
        
//
//        UILabel *titleLabel = [UILabel new];
//        titleLabel.text = @"新建分组";
//        titleLabel.textColor = UIColor.up_textPrimaryColor;
//        titleLabel.font = [UIFont up_boldFontOfSize:17];
//        [contentView addSubview:titleLabel];
//
//        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.centerX.equalTo(contentView);
//            make.top.equalTo(@25);
//            make.height.equalTo(@17);
//        }];
        
        


        
        UITextField *textField = [UITextField new];
        self.textField = textField;
        textField.backgroundColor = UIColor.up_contentBgColor;
        textField.textColor = UIColor.upmarket2_newOptionPlaceHolderColor;
        textField.font = [UIFont up_fontOfSize:15];
        NSMutableAttributedString *att = [[NSMutableAttributedString alloc] initWithString:@"请输入自定义分组名称"];
        
        [att setAttributes:@{
            NSFontAttributeName : [UIFont up_fontOfSize:13],
            NSForegroundColorAttributeName : UIColor.up_textSecondary1Color
        } range:NSMakeRange(0, att.length)];
        textField.attributedPlaceholder = att;
        textField.delegate = self;
        textField.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 6, textField.up_height)];
        textField.leftViewMode = UITextFieldViewModeAlways;
        [textField becomeFirstResponder];
        [contentView addSubview:textField];
        
        [textField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(contentView);
            make.top.equalTo(contentHeaderView.mas_bottom).offset(18);
            make.height.equalTo(@35);
            make.left.equalTo(contentView).offset(15);
            make.right.equalTo(contentView).offset(-15);
        }];
        [textField addTarget:self action:@selector(textDidChange:) forControlEvents:UIControlEventEditingChanged];
//
//        UILabel *numLabel = [UILabel new];
//        numLabel.textColor = UIColor.up_textSecondary1Color;
//        numLabel.font = [UIFont up_boldFontOfSize:13];
//        numLabel.textAlignment = NSTextAlignmentRight;
//        [contentView addSubview:numLabel];
//        self.numLabel = numLabel;
//        [self setWordLabelText:0];
//
//        [numLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.top.equalTo(textField.mas_bottom);
//            make.height.equalTo(@30);
//            make.width.equalTo(@35);
//            make.right.equalTo(textField);
//        }];
//
//        UILabel *failLabel = [UILabel new];
//        failLabel.textColor = UIColor.up_brandColor;
//        failLabel.font = [UIFont up_boldFontOfSize:13];
//        [contentView addSubview:failLabel];
//        self.failLabel = failLabel;
//
//        [failLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.top.height.equalTo(self.numLabel);
//            make.left.equalTo(textField);
//            make.right.equalTo(self.numLabel.mas_left);
//        }];
//
//        UIView *divideLine = [UIView new];
//        divideLine.backgroundColor = UIColor.up_dividerColor;
//        [contentView addSubview:divideLine];
//
//        [divideLine mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.right.equalTo(contentView);
//            make.top.equalTo(numLabel.mas_bottom);
//            make.height.equalTo(@0.5);
//        }];
//
//        UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//        cancelBtn.titleLabel.font = [UIFont up_fontOfSize:17];
//        [cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
//        [cancelBtn setTitleColor:UIColor.up_textSecondaryColor forState:UIControlStateNormal];
//        [cancelBtn addTarget:self action:@selector(sdiExtendEditOptionViewHide) forControlEvents:UIControlEventTouchUpInside];
//        cancelBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
//        [contentView addSubview:cancelBtn];
//
//        UIView *verLine = [UIView new];
//        verLine.backgroundColor = UIColor.up_dividerColor;
//        [contentView addSubview:verLine];
//
//        UIButton *ensureBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//        ensureBtn.titleLabel.font = [UIFont up_fontOfSize:15];
//        [ensureBtn setTitle:@"确定" forState:UIControlStateNormal];
//        [ensureBtn setTitleColor:UIColor.up_bgHotStockTextColor forState:UIControlStateNormal];
//        [ensureBtn addTarget:self action:@selector(sdiExtendEditOptionViewEnsure) forControlEvents:UIControlEventTouchUpInside];
//        ensureBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
//        [contentView addSubview:ensureBtn];
//
//        [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.top.equalTo(divideLine.mas_bottom);
//            make.left.bottom.equalTo(contentView);
//        }];
//
//        [verLine mas_makeConstraints:^(MASConstraintMaker *make) {
//               make.top.equalTo(divideLine.mas_bottom);
//               make.bottom.equalTo(contentView);
//               make.left.equalTo(cancelBtn.mas_right);
//               make.width.equalTo(@0.5);
//        }];
//
//        [ensureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.top.equalTo(divideLine.mas_bottom);
//            make.right.bottom.equalTo(contentView);
//            make.left.equalTo(cancelBtn.mas_right);
//            make.width.equalTo(cancelBtn);
//        }];
        [self registerAllNotifications];
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.02 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            UIBezierPath *rounded = [UIBezierPath bezierPathWithRoundedRect:contentView.bounds byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight cornerRadii:CGSizeMake(8, 8)];
//            CAShapeLayer *shape = [[CAShapeLayer alloc] init];
//            [shape setPath:rounded.CGPath];
//            contentView.layer.mask = shape;
//        });
    }
    return self;
}


- (void)registerAllNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHide:) name:UIKeyboardWillHideNotification object:nil];

}

- (void)keyboardWillShow:(NSNotification *)aNotification {

    //获取键盘的高度
    //    //3. 获取导航栏和状态栏的高度
        CGFloat navigationBarAreaHeight = [[UIApplication sharedApplication] statusBarFrame].size.height + 44;
    //
    NSDictionary *userInfo = [aNotification userInfo];

    NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];

    CGRect keyboardRect = [aValue CGRectValue];
    CGFloat keyboardTop = keyboardRect.origin.y;
    int height = keyboardRect.size.height;
    
    [self.contentView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_top).offset(keyboardTop);
    }];

 }

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
//    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

//- (void)keyboardWillShow:(NSNotification *)aNotification {
//    NSDictionary *userInfo = [aNotification userInfo];
//    //1. 获取弹出键盘顶的y值
//    NSValue  *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
//    CGRect keyboardRect = [aValue CGRectValue];
//    CGFloat keyboardTop = keyboardRect.origin.y;
//
//    //2. 获取键盘弹出动画时间值
//    NSNumber *animationDurationValue = [userInfo objectForKey:UIKeyboardAnimationDurationUserInfoKey];
//    NSTimeInterval animationDuration = [animationDurationValue doubleValue];
//
//    //3. 获取导航栏和状态栏的高度
//    CGFloat navigationBarAreaHeight = [[UIApplication sharedApplication] statusBarFrame].size.height + 44;
//
//    //4. 内容的最大y值
//    CGFloat maxY = CGRectGetMaxY(self.contentView.frame) + navigationBarAreaHeight;
//    maxY += 50;  // 输入框底部离键盘顶部为50的间距
//
//    //5. 有遮盖，上移
//    if (keyboardTop < maxY) {
//        CGFloat gap = maxY - keyboardTop;
//        WeakSelf(weakSelf)
//        [UIView animateWithDuration:animationDuration animations:^{
//            weakSelf.contentView.up_centerY -= gap;
//        }];
//    }
//}
//- (void)keyboardWillHide:(NSNotification *)aNotification {
//    NSDictionary *userInfo = [aNotification userInfo];
//    //1. 获取键盘弹出动画时间值
//    NSNumber *animationDurationValue = [userInfo objectForKey:UIKeyboardAnimationDurationUserInfoKey];
//    NSTimeInterval animationDuration = [animationDurationValue doubleValue];
//
//    //2. 有上移，下移
//    if (self.contentView.up_centerY < self.up_centerY) {
//        WeakSelf(weakSelf)
//        [UIView animateWithDuration:animationDuration animations:^{
//            weakSelf.contentView.up_centerY = self.up_centerY;
//        }];
//    }
//}

- (void)setWordLabelText:(NSInteger)count {
    NSString *text = [NSString stringWithFormat:@"%zd/%zd", count, _maxWords];
    self.numLabel.text = text;
}

- (void)sdiExtendEditOptionViewHide {
    if (self.hideBlock) {
        self.hideBlock();
    }
}

- (void)saveSuccsess {
    if (self.successBlock) {
        self.successBlock();
    }
}

- (void)sdiExtendEditOptionViewEnsure {
    NSString *groupName = self.textField.text;
    
    if (groupName.length > _maxWords) {
        groupName = [groupName substringToIndex:_maxWords];
        
        self.textField.text = groupName;
    }
    
    if (groupName.length > 0) {
        if ([self isSpecialCharacters:groupName]) {
            [UPToastView show: @"请输入中文,字母或数字作为组名"];
//            self.failLabel.text = @"请输入中文,字母或数字作为组名";
        } else if ([groupName isEqualToString:@"沪深京"] || [groupName isEqualToString:@"港股"] || [groupName isEqualToString:@"我的持仓"] || [groupName isEqualToString:@"我的自选"]) {
//            self.failLabel.text = @"该分组名称已存在";
            [UPToastView show: @"该分组名称已存在"];
        }
        else {
            [UPOptionalManager addOptionalGroup:groupName handler:^(UPOptionalAddStatus status, NSInteger groupId) {
                UPOptionalAddStatus addStatus = status;
                
                if (UPOptionalAddStatusSuccess == addStatus) {
                    UPOptionalModel *optionModel = [UPOptionalModel new];
                    optionModel.code = self.stockModel.stockCode;
                    optionModel.setCode = self.stockModel.stockSetCode;
                    optionModel.groupId = groupId;
                    
                   
                    
                    if (IsValidateArray(self.stockArr)) {
                        for (UPMarketUIBaseModel *model in self.stockArr) {
                            UPOptionalAddStatus optionAddStatus = [UPOptionalManager addOptional:model.stockSetCode code:model.stockCode name:nil groupIdList:@[@(groupId)]];
                            if ([self.stockArr indexOfObject:model] == self.stockArr.count -1) {
                                if (optionAddStatus == UPOptionalAddStatusSuccess) {
                                    [UPToastView show:@"已添加自选" icon:UPTImg(@"行情/发送成功stock")];
                                } else if (optionAddStatus == UPOptionalAddStatusLimit) {
                                    [UPToastView show:@"自选股已达上限，添加失败"];
                                } else {
                                    [UPToastView show:@"添加失败"];
                                }
                            }
                        }
                        [self saveSuccsess];
                    } else {
                        UPOptionalAddStatus optionAddStatus = [UPOptionalManager addOptional:optionModel];
                        if (optionAddStatus == UPOptionalAddStatusSuccess) {
    //                        [UPToastView show:[NSString stringWithFormat:@"已添加至【%@】",groupName]];
                            [UPToastView show:@"已添加自选" icon:UPTImg(@"行情/发送成功stock")];

                        } else {
                            [UPToastView showFail:@"添加自选失败"];
                        }
                        [self sdiExtendEditOptionViewHide];
                    }
                    

                    
                    
                } else {
                    NSString *text = nil;
                    if (UPOptionalAddStatusNameEmpty == addStatus) {
                        text = @"请输入自选股组名";
                    } else if (UPOptionalAddStatusGroupExist == addStatus) {
                        text = @"该分组名称已存在";
                    } else {
                        text = @"添加分组失败";
                    }
//                    self.failLabel.text = text;
                    [UPToastView show:text];
                }
            }];
            
        }
    } else {
//        self.failLabel.text = @"请输入自选股组名";
        [UPToastView show:@"请输入自选股组名"];
    }
}

/// 是否是特殊字符   YES：包含  NO：不包含
- (BOOL)isSpecialCharacters:(NSString *)text {
    NSString *regex = @"^[A-Za-z0-9\\u4e00-\u9fa5]+$";
    NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    BOOL bSC = ![pred evaluateWithObject:text];

    return bSC;
}

// MARK: - UITextFieldDelegate
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    
    if (string.length) {
        NSRange rangeIndex = [string rangeOfComposedCharacterSequenceAtIndex:string.length-1];
        // 表情直接过滤
        if (rangeIndex.length > 1) return NO;
    }
    
    
    UITextRange *selectedRange = [textField markedTextRange];
    UITextPosition *position = [textField positionFromPosition:selectedRange.start offset:0];
    
    if (position) {
        return YES;
    }
    
    NSString *comcatstr = [textField.text stringByReplacingCharactersInRange:range withString:string];
    
    if (comcatstr.length > _maxWords) {
        return NO;
    }
    return YES;
}

- (void)textDidChange:(UITextField *)textField {
    UITextRange *selectedRange = [textField markedTextRange];
    UITextPosition *position = [textField positionFromPosition:selectedRange.start offset:0];
    
    // 高粱状态不统计
    if (!position) {
        if (textField.text.length > _maxWords) {
            textField.text = [textField.text substringToIndex:_maxWords];
        }
        [self setWordLabelText:textField.text.length];
    }
    if (textField.text.length > 0) {
        self.finishBtn.enabled = YES;
        self.finishBtn.titleLabel.alpha = 1.0;
    }
    else {
        self.finishBtn.enabled = NO;
        self.finishBtn.titleLabel.alpha = 0.4;
    }
}

@end
