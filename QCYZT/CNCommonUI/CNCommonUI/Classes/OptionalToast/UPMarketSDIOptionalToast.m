//
//  UPMarketSDIOptionalToast.m
//  UPMarket2
//
//  Created by jay<PERSON> on 2020/5/5.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarketSDIOptionalToast.h"
#import <UPUserSDK/UPOptionalManager.h>
#import <UPUserSDK/UPUserManager.h>

@interface UPMarketSDIOptionalToast () <UPTAFHandlerDelegate>

@property (nonatomic, strong) UPPopupView *optionPopupView;

@property (nonatomic, strong) UIView *optionResultView;

@property (nonatomic, strong) UILabel *resultLabel;

@property (nonatomic, strong) UILabel *editGroupLabel;

@property (nonatomic, strong) UPTAFHandler *handler;

@end

@implementation UPMarketSDIOptionalToast

- (instancetype)init {
    self = [super init];
    if (self) {
        _optionPopupView = [[UPPopupView alloc] init];
        _optionPopupView.outsideTouchable = YES;
        _optionPopupView.hidesWhenTouchOutside = NO;
        _optionPopupView.contentView = self.optionResultView;
        
        _handler = [UPTAFHandler mainHandlerWithDelegate:self];
    }
    
    return self;
}

// MARK: - Public
- (void)showInView:(UIView *)parentView groupList:(NSArray<NSNumber *> *)groupIds optionalStatus:(UPMarketOptionStatus)status {
    if (!parentView) {
        NSLog(@"UPMarketSDIOptionalToast - Parent view is Nil!");
        return;
    }
    self.resultLabel.text = [self getTextByStatus:status groupIds:groupIds];
    self.editGroupLabel.text = (status == UPMarketOptionStatusAddSuccess) ? @"" : @"";
    
    [self.optionPopupView showInView:parentView constranitsHandler:^(UIView * _Nonnull popView) {
        [self.optionResultView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(popView);
            make.width.lessThanOrEqualTo(popView).offset(-30);
            make.height.mas_equalTo(45);
        }];
    }];
    
    [self.handler removeAll];
    [self.handler sendMessageDelayed:0 delayMillis:2 * 1000];
}

// MARK: - UPTAFHandlerDelegate
-(void)handleMessage:(int)what object:(id)anObject {
    if (self.optionPopupView.isShowing) {
        [self.optionPopupView hide];
    }
}


// MARK: - Private
- (NSString *)getTextByStatus:(UPMarketOptionStatus)status groupIds:(NSArray<NSNumber *> *)groupIds {
    if (status == UPMarketOptionStatusAddSuccess) {
        
        if (![UPUserManager uid].length || [UPUserManager isTouristLogin]) {
            //未登录
            return @"已添加至\"我的自选\"";
        }
        
        NSString *string = [NSString stringWithFormat:@"已添加至"];
        for (NSInteger i = 0; i < groupIds.count; i++) {
//            if ([groupIds[i] integerValue] == kGroupId_ALL) {
                string = [string stringByAppendingFormat:@"\"%@\"",@"我的自选"];
//            }else{
//                UPOptionalGroupModel *model = [UPOptionalManager getOptionalGroudWithGroupId:[groupIds[i] integerValue]];
//                string = [string stringByAppendingFormat:@"\"%@\"",model.groupName];
//            }
            
            
            if (i < groupIds.count - 1) {
                string = [string stringByAppendingString:@"、"];
            }
        }
        return string;
    } else if (status == UPMarketOptionStatusAddFail) {
        return @"添加失败";
    } else if (status == UPMarketOptionStatusAddLimit) {
        return @"自选股已达上限，添加失败";
    } else if (status == UPMarketOptionStatusAddNameEmpty) {
        return @"自选分组名称为空";
    } else if (status == UPMarketOptionStatusAddGroupExist) {
        return @"自选已经存在";
    } else if (status == UPMarketOptionStatusDelSuccess) {
        return @"已从\"我的自选\"移除";
    } else if (status == UPMarketOptionStatusDelFail) {
        return @"删除失败";
    }
    
    return @"";
}

- (void)onEditLabelClicked:(UITapGestureRecognizer *)recognizer {
    __strong id<UPMarketSDIOptionalToastDelegate> delegate = self.delegate;
    if (delegate && [delegate respondsToSelector:@selector(sdiOptionalToastEditGroupClicked)]) {
        [delegate sdiOptionalToastEditGroupClicked];
    }
}

// MARK: - Getter & Setter
- (UIView *)optionResultView  {
    if (!_optionResultView) {
        UIBlurEffect * blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
        UIVisualEffectView * effectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
        effectView.layer.cornerRadius = 5;
        effectView.clipsToBounds = YES;

        UIView *viewContainer = [[UIView alloc] init];
        [effectView.contentView addSubview:viewContainer];
        [viewContainer mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(effectView.contentView);
        }];

        UILabel *resultLabel = [[UILabel alloc] init];
        resultLabel.font = [UIFont up_fontOfSize:15];
        resultLabel.textColor = UIColor.whiteColor;
        [viewContainer addSubview:resultLabel];

        self.resultLabel = resultLabel;
        
        UILabel *editGroup = [[UILabel alloc] init];
        editGroup.textColor = UIColor.up_brandColor;
        editGroup.font = [UIFont up_fontOfSize:15];
        editGroup.userInteractionEnabled = YES;
        UITapGestureRecognizer * tapRecognizer = [[UITapGestureRecognizer alloc] init];
        [tapRecognizer addTarget:self action:@selector(onEditLabelClicked:)];
        [editGroup addGestureRecognizer:tapRecognizer];
        [viewContainer addSubview:editGroup];

        self.editGroupLabel = editGroup;

        [resultLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(viewContainer).offset(5);
            make.top.equalTo(viewContainer).offset(3);
            make.bottom.equalTo(viewContainer).offset(-2);
            make.right.equalTo(editGroup.mas_left).offset(-5);
        }];
        [resultLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];

        [editGroup mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.equalTo(resultLabel);
            make.right.equalTo(viewContainer).offset(-5);
        }];
        [editGroup setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        
        _optionResultView = effectView;
    }
    
    return _optionResultView;
}

@end
