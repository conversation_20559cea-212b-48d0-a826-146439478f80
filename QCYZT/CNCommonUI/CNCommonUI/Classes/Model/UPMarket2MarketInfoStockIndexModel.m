//
//  UPMarket2MarketInfoStockIndexModel.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2MarketInfoStockIndexModel.h"
#import <UPMarketUISDK/UPMarketUICalculateUtil.h>

@implementation UPMarket2MarketInfoStockIndexModel

- (void)setStockHq:(UPHqStockHq *)stockHq {
    _stockHq = stockHq;
    self.topHeaderName = stockHq.name;
    if (stockHq.nowPrice > 0) {
        self.centerData = [UPMarketUICalculateUtil transNumberByRoundingOff:stockHq.nowPrice precise:stockHq.precise needsymbol:NO];
        self.bottomLeftData = [UPMarketUICalculateUtil transNumberByRoundingOff:stockHq.changeValue precise:stockHq.precise needsymbol:YES];
        self.bottomRightData = [UPMarketUICalculateUtil transChgRatio:stockHq.changeRatio chgValue:stockHq.changeValue baseValue:stockHq.nowPrice needSymbol:YES];
    } else {
        self.centerData = @"--";
        self.bottomLeftData = @"--";
        self.bottomRightData = @"--";
    }
}
@end
