//
//  UPMarket2MarketInfoStockRankLHZYYZGMModel.h
//  UPMarket2
//
//  Created by liz<PERSON>xiang on 2023/8/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarket2MarketInfoStockRankLHZYYZGMModel : NSObject


/**
 LHZY.0daysReason.net_buy_ratio  净买占比   double
 LHZY.0saleCapital.reasons 卖出游资标签  str
 LHZY.0buyCapital.reasons Maria游资标签  str
 LHZY.0daysReason.buy_seat_org_num   买席机构数 int
 LHZY.0daysReason.sale_seat_org_num  卖席机构数 int
 LHZY.0daysReason.org_net_buy  机构净买 double
    szzl  双紫追龙  int
 LHZY.0daysReason.icon_flag  移动模型标签  str
 LHZY.0daysReason.stock_name  股票名称  str
 LHZY.0daysReason.net_buy 席位净买  double
 ztreason 涨停原因  str
 fChgRatio 涨跌幅 double
 induName 所属行业 str
 fNowPrice   现价 double
  
 */

@property (nonatomic, copy) NSString *buyCapital;
@property (nonatomic, copy) NSString *saleCapital;
@property (nonatomic, assign) int buy_seat_org_num;
@property (nonatomic, assign) int sale_seat_org_num;
@property (nonatomic, assign) int szzl;
@property (nonatomic, copy) NSString *icon_flag;
@property (nonatomic, copy) NSString *stock_name;
@property (nonatomic, copy) NSString *ztreason;
@property (nonatomic, copy) NSString *induName;
@property (nonatomic,  assign) double  fNowPrice;
@property (nonatomic, assign) int martetCode;
@property (nonatomic, copy) NSString *code;
@property (nonatomic, assign) int list_times;
@property (nonatomic, copy) NSString *reason_analysis;
@property (nonatomic, assign) int reason_type;

@property (nonatomic, assign) double daysReason_net_buy;
@property (nonatomic,  assign) double  net_buy_ratio;
@property (nonatomic, assign) double zlcb;
@property (nonatomic,  assign) double  fChgRatio;
@property (nonatomic,  assign) double  org_net_buy;

@end

NS_ASSUME_NONNULL_END
