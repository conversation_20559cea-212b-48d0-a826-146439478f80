//
//  UPMarketFreezeColumnView.m
//  UPBaseUI
//
//  Created by sammy<PERSON> on 2020/3/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarketFreezeColumnView.h"

// MARK: UPMarketFreezeColumnViewColumn

@implementation UPMarketFreezeColumnViewColumn

+ (instancetype)columnWithN:(NSString *)name w:(CGFloat)width t:(NSInteger)tag {
    UPMarketFreezeColumnViewColumn * column = [[UPMarketFreezeColumnViewColumn alloc] init];
    column.name = name;
    column.width = width;
    column.tag = tag;
    column.freeze = NO;
    return column;
}

+ (instancetype)freezeColumnWithN:(NSString *)name w:(CGFloat)width t:(NSInteger)tag {
    UPMarketFreezeColumnViewColumn * column = [[UPMarketFreezeColumnViewColumn alloc] init];
    column.name = name;
    column.width = width;
    column.tag = tag;
    column.freeze = YES;
    return column;
}

@end

// MARK: UPMarketFreezeColumnViewCell

@implementation UPMarketFreezeColumnViewCell
@end

// MARK: UPMarketFreezeFooterView

@implementation UPMarketFreezeFooterView

@end

// MARK: UPMarketFreezeColumnViewLayoutAttributes

@interface UPMarketFreezeColumnViewLayoutAttributes : UICollectionViewLayoutAttributes

@property(nonatomic, strong) UIColor * separatorColor;

@end

@implementation UPMarketFreezeColumnViewLayoutAttributes
@end

// MARK: UPMarketFreezeColumnViewSeparatorView

@interface UPMarketFreezeColumnViewSeparatorView : UICollectionViewCell
@end

@implementation UPMarketFreezeColumnViewSeparatorView

- (void)applyLayoutAttributes:(UICollectionViewLayoutAttributes *)layoutAttributes {
    [super applyLayoutAttributes:layoutAttributes];

    if([layoutAttributes isKindOfClass:UPMarketFreezeColumnViewLayoutAttributes.class]) {
        UPMarketFreezeColumnViewLayoutAttributes * attributes = (UPMarketFreezeColumnViewLayoutAttributes * )layoutAttributes;
        self.backgroundColor = attributes.separatorColor;
    }
}

@end

// MARK: UPMarketFreezeColumnViewLayout

@interface UPMarketFreezeColumnViewLayout : UICollectionViewLayout

@property(nonatomic, strong) NSArray<UPMarketFreezeColumnViewColumn *> * columns;
@property (nonatomic, copy) CGFloat (^footerHeightForColumnBlock)(NSInteger section);
@property (nonatomic, assign) CGFloat collectionFooterHeight;
@property (nonatomic, assign) CGFloat sectionFooterHeight;
@property(nonatomic, assign) CGFloat headerHeight;
@property(nonatomic, assign) CGFloat rowHeight;

@property(nonatomic, strong) UIColor * separatorColor;
@property(nonatomic, assign) UIEdgeInsets separatorInset;
@property(nonatomic, assign) CGFloat separatorHeight;
@property(nonatomic, assign) BOOL showsHeaderSeparator;
@property(nonatomic, assign) BOOL showsLastRowSeparator;

@property(nonatomic, assign) CGSize contentSize;

- (void)reset;

@end

@implementation UPMarketFreezeColumnViewLayout

- (instancetype)init {
    self = [super init];
    if (self) {
        [self registerClass:UPMarketFreezeColumnViewSeparatorView.class forDecorationViewOfKind:@"_Separator"];
    }
    return self;
}

- (void)reset {
    self.columns = nil;
    self.sectionFooterHeight = 0.0;
    self.headerHeight = 0.0;
    self.rowHeight = 0.0;
    self.contentSize = CGSizeZero;
}

- (void)prepareLayout {
    [super prepareLayout];
    
    NSInteger rows = [self.collectionView numberOfSections];
    NSInteger columns = self.columns.count;
    NSInteger itemRows = self.collectionFooterHeight>0 ? rows - 2 : rows - 1; //collectionFooter是在最下面多增加一个只有footer的section的方式实现的
    
    if (rows == 0 || columns == 0 || itemRows <= 0) {
        return;
    }

    if(!CGSizeEqualToSize(self.contentSize, CGSizeZero)) {
        return;
    }

    CGFloat contentWidth = 0.0;
    CGFloat contentHeight = 0.0;

    for (UPMarketFreezeColumnViewColumn * column in self.columns) {
        contentWidth += column.width;
    }

    contentHeight = self.headerHeight + itemRows * self.rowHeight;
    
    // 分割线
    contentHeight += (itemRows + 1) * self.separatorHeight;

    if(!self.showsHeaderSeparator) {
        contentHeight -= self.separatorHeight;
    }

    if(!self.showsLastRowSeparator) {
        contentHeight -= self.separatorHeight;
    }
    
    //section footer
    CGFloat allSectionFooterHeight = self.sectionFooterHeight * itemRows;
    if (self.footerHeightForColumnBlock) {
        allSectionFooterHeight = 0;
        for (int i = 0; i < itemRows; i++)
        {
            CGFloat sectionFooterHeight = self.footerHeightForColumnBlock(i);
            allSectionFooterHeight += sectionFooterHeight;
        }
    }
    contentHeight += allSectionFooterHeight;
    
    //collection footer
    contentHeight += self.collectionFooterHeight;
    
    self.contentSize = CGSizeMake(contentWidth, contentHeight);
}



- (CGSize)collectionViewContentSize {
    return self.contentSize;
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForSupplementaryViewOfKind:(NSString *)elementKind atIndexPath:(NSIndexPath *)indexPath {
    //section 0 不显示footer因为这是header的位置
    if (indexPath.section != 0 && [elementKind isEqualToString:UICollectionElementKindSectionFooter]) {
        NSInteger allSectionfooterHeight = (indexPath.section -1) * self.sectionFooterHeight;
        UICollectionViewLayoutAttributes *footerAttributes = [UICollectionViewLayoutAttributes layoutAttributesForSupplementaryViewOfKind:UICollectionElementKindSectionFooter withIndexPath:indexPath];
        if (self.footerHeightForColumnBlock) {
            allSectionfooterHeight = 0;
            for (int i = 0; i < indexPath.section; i++) {
                allSectionfooterHeight += self.footerHeightForColumnBlock(i);
            }
        }
        CGFloat y = self.headerHeight + (indexPath.section) * (self.rowHeight + self.separatorHeight) + allSectionfooterHeight;
        //如果collectionFooterHeight>0的情况下最后一行是footer的section
        if (indexPath.section == [self.collectionView numberOfSections] - 1 && self.collectionFooterHeight > 0) {
            y -= (self.rowHeight + self.separatorHeight);
        }
        CGFloat x = 0.0;
        for (NSUInteger column = 0; column < indexPath.row; ++column) {
            x  += self.columns[column].width;
        }
        x = self.collectionView.contentOffset.x <= 0 ? x : x + self.collectionView.contentOffset.x;
        CGFloat curSectionFooterHeight = self.sectionFooterHeight;
        if (self.footerHeightForColumnBlock) {
            curSectionFooterHeight = self.footerHeightForColumnBlock(indexPath.section);
        }
        footerAttributes.frame = CGRectMake(x, y, self.collectionView.frame.size.width, curSectionFooterHeight);
        footerAttributes.zIndex = 1022;
        return footerAttributes;
    } else {
        return nil;
    }
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat offsetX = self.collectionView.contentOffset.x;
    CGFloat offsetY = self.collectionView.contentOffset.y;
    
    UPMarketFreezeColumnViewColumn * columnAttr = nil;
    
    CGFloat x = 0.0;
    
    NSInteger allSectionfooterHeight = (indexPath.section -1) * self.sectionFooterHeight;
    if (self.footerHeightForColumnBlock) {
        allSectionfooterHeight = 0;
        for (int i = 0; i < indexPath.section; i++) {
            allSectionfooterHeight += self.footerHeightForColumnBlock(i);
        }
    }
    for (NSUInteger column = 0; column < indexPath.row; ++column) {
        columnAttr = self.columns[column];
        
        x += columnAttr.width;
    }
    
    CGFloat y = self.headerHeight + (indexPath.section - 1) * self.rowHeight + (allSectionfooterHeight);

    if(self.showsHeaderSeparator) {
        y += indexPath.section * self.separatorHeight;
    } else {
        y += (indexPath.section - 1) * self.separatorHeight;
    }

    CGRect cellRect = CGRectMake(x, y, columnAttr.width, self.headerHeight);

    UPMarketFreezeColumnViewLayoutAttributes * attributes =
        [UPMarketFreezeColumnViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
    
    if(columnAttr.freeze) {
        cellRect.origin.x = offsetX <= 0 ? x : x + offsetX;
        attributes.zIndex = 1023;
    }

    if(indexPath.section == 0) {
        cellRect.origin.y = offsetY <= 0 ? 0 : offsetY;
        attributes.zIndex = 1024;
    }

    attributes.frame = cellRect;

    return attributes;
}


- (NSArray *)layoutAttributesForElementsInRect:(CGRect)rect {
    NSMutableArray * attributesArray = [NSMutableArray arrayWithCapacity:32];
    
    NSInteger rows = [self.collectionView numberOfSections];
    NSInteger columns = self.columns.count;
    NSInteger itemRows = self.collectionFooterHeight>0 ? rows - 2 : rows - 1; //collectionFooter是在最下面多增加一个只有footer的section的方式实现的

    CGFloat width = self.collectionView.frame.size.width;
    CGFloat height = self.collectionView.frame.size.height;
    CGFloat offsetX = self.collectionView.contentOffset.x;
    CGFloat offsetY = self.collectionView.contentOffset.y;
    
    CGFloat rowHeight = self.rowHeight + self.separatorHeight + self.sectionFooterHeight;
    
    NSInteger firstVisiableRow = floor((offsetY + self.headerHeight) / rowHeight);
    firstVisiableRow = firstVisiableRow < 1 ? 1 : firstVisiableRow;
    NSInteger maxVisiableRows = ceil((height - self.headerHeight) / rowHeight);
    maxVisiableRows = maxVisiableRows < 1 ? 1 : maxVisiableRows;
    NSInteger lastVisiableRow = firstVisiableRow + maxVisiableRows;
    lastVisiableRow = lastVisiableRow > itemRows ? itemRows : lastVisiableRow;
    
    //自定义每个sectionfooter高度
    if (self.footerHeightForColumnBlock) {
        CGFloat totalOffset = 0;
        firstVisiableRow = -1;
        maxVisiableRows = -1;
        lastVisiableRow = -1;
        for (int i = 0; i < rows; i++)
        {
            CGFloat footerHeight = self.footerHeightForColumnBlock(i);
            CGFloat rowHeight = self.rowHeight + self.separatorHeight + footerHeight;
            totalOffset += rowHeight;
            if (offsetY - self.headerHeight <= totalOffset && firstVisiableRow == -1) {
                firstVisiableRow = i;
                firstVisiableRow = firstVisiableRow < 1 ? 1 : firstVisiableRow;
            }
            if (height - self.headerHeight <= totalOffset && maxVisiableRows == -1) {
                maxVisiableRows = i + 1;
            }
            lastVisiableRow = firstVisiableRow + maxVisiableRows;
            lastVisiableRow = lastVisiableRow > itemRows ? itemRows : lastVisiableRow;
        }
    }
    

    UPMarketFreezeColumnViewColumn * columnAttr = nil;
    
    // 表头总是可见的
    {
        CGFloat x = 0.0;
        
        for (NSUInteger column = 0; column < columns; ++column) {
            columnAttr = self.columns[column];

            CGRect cellRect = CGRectMake(x, offsetY <= 0 ? 0 : offsetY, columnAttr.width, self.headerHeight);
            
            if(columnAttr.freeze) {
                cellRect.origin.x = offsetX <= 0 ? x : x + offsetX;
            }

            if(CGRectIntersectsRect(rect, cellRect)) {
                NSIndexPath * indexPath = [NSIndexPath indexPathForRow:column inSection:0];
                UPMarketFreezeColumnViewLayoutAttributes * attributes =
                    [UPMarketFreezeColumnViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
                attributes.frame = cellRect;
                
                if(columnAttr.freeze) {
                    attributes.zIndex = 1024; // 需要盖住所有其他的
                } else {
                    attributes.zIndex = 1023;
                }

                [attributesArray addObject:attributes];

                // 分割线
                if(self.separatorHeight > 0.0 && self.showsHeaderSeparator) {
                    CGRect separatorRect = CGRectMake(0, 0, 0, self.separatorHeight);
                    separatorRect.origin.x = cellRect.origin.x + self.separatorInset.left;
                    separatorRect.origin.y = cellRect.origin.y + cellRect.size.height;
                    separatorRect.size.width = width - self.separatorInset.left + self.separatorInset.right;

                    UPMarketFreezeColumnViewLayoutAttributes * separatorAttributes =
                        [UPMarketFreezeColumnViewLayoutAttributes layoutAttributesForDecorationViewOfKind:@"_Separator"
                                                                                      withIndexPath:indexPath];
                    separatorAttributes.separatorColor = self.separatorColor;
                    separatorAttributes.frame = separatorRect;
                    separatorAttributes.zIndex = 1024;

                    [attributesArray addObject:separatorAttributes];
                }
            }
            
            x += columnAttr.width;
        }
    }
    
    // 数据行
    for (NSInteger row = firstVisiableRow; row <= lastVisiableRow; ++row) {
        CGFloat x = 0.0;
        NSInteger allSectionfooterHeight = (row - 1) * self.sectionFooterHeight;
        if (self.footerHeightForColumnBlock) {
            allSectionfooterHeight = 0;
            for (int i = 0; i < row; i++) {
                allSectionfooterHeight += self.footerHeightForColumnBlock(i);
            }
        }
        CGFloat y = self.headerHeight + (row - 1) * self.rowHeight + (allSectionfooterHeight);

        if(self.showsHeaderSeparator) {
            y += row * self.separatorHeight;
        } else {
            y += (row - 1) * self.separatorHeight;
        }

        for (NSUInteger column = 0; column < columns; ++column) {
            columnAttr = self.columns[column];

            CGRect cellRect = CGRectMake(x, y, columnAttr.width, self.rowHeight);

            // 固定列
            if(columnAttr.freeze) {
                cellRect.origin.x = offsetX <= 0 ? x : x + offsetX;
            }

            if(CGRectIntersectsRect(rect, cellRect)) {
                NSIndexPath * indexPath = [NSIndexPath indexPathForRow:column inSection:row];
                UPMarketFreezeColumnViewLayoutAttributes * attributes =
                    [UPMarketFreezeColumnViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
                attributes.frame = cellRect;

                if (columnAttr.freeze) {
                    attributes.zIndex = 1023;
                }

                [attributesArray addObject:attributes];

                // 分割线
                if(self.separatorHeight > 0.0 && (self.showsLastRowSeparator || row < lastVisiableRow)) {
                    CGRect separatorRect = CGRectMake(0, 0, 0, self.separatorHeight);
                    separatorRect.origin.x = cellRect.origin.x + self.separatorInset.left;
                    separatorRect.origin.y = y + cellRect.size.height;
                    separatorRect.size.width = width - self.separatorInset.left + self.separatorInset.right;

                    UPMarketFreezeColumnViewLayoutAttributes * separatorAttributes =
                        [UPMarketFreezeColumnViewLayoutAttributes layoutAttributesForDecorationViewOfKind:@"_Separator"
                                                                                      withIndexPath:indexPath];
                    separatorAttributes.separatorColor = self.separatorColor;
                    separatorAttributes.frame = separatorRect;

                    [attributesArray addObject:separatorAttributes];
                }
            }

            x += columnAttr.width;
        }
    }
    for (NSInteger section = 0; section < [self.collectionView numberOfSections]; ++section) {
        NSIndexPath *footerIndexPath = [NSIndexPath indexPathForItem:0 inSection:section];
        UICollectionViewLayoutAttributes *footerAttributes = [self layoutAttributesForSupplementaryViewOfKind:UICollectionElementKindSectionFooter atIndexPath:footerIndexPath];
        
        if (footerAttributes && CGRectIntersectsRect(rect, footerAttributes.frame)) {
            [attributesArray addObject:footerAttributes];
        }
    }
    return attributesArray;
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds {
    return YES;
}

@end

// MARK: UPMarketFreezeColumnView

typedef NS_ENUM(NSUInteger, UPMarketFreezeColumnViewScrollDirection) {
    UPMarketFreezeColumnViewScrollDirectionNone, // 初始状态
    UPMarketFreezeColumnViewScrollDirectionVertical, // 纵向
    UPMarketFreezeColumnViewScrollDirectionHoritonzal, // 横向
};

@interface UPMarketFreezeColumnView () <UICollectionViewDelegate, UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>

@property(nonatomic, strong) UICollectionView * collectionView;
@property(nonatomic, strong) UPMarketFreezeColumnViewLayout * viewLayout;

@property(nonatomic, assign) NSInteger numberOfRows;
@property(nonatomic, copy) NSArray<UPMarketFreezeColumnViewColumn *> * columns;

@property(nonatomic, strong) NSMutableSet * registeredClass;
@property(nonatomic, strong) Class registeredSectionFooterClass;
@property(nonatomic, strong) Class registeredCollectionFooterClass;

@property(nonatomic, assign) CGPoint initialContentOffset;
@property(nonatomic, assign) UPMarketFreezeColumnViewScrollDirection scrollDirection;

@end

@implementation UPMarketFreezeColumnView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _separatorInset = UIEdgeInsetsZero;
        _separatorHeight = 0.0;
        _showsHeaderSeparator = NO;
        _showsLastRowSeparator = NO;

        _registeredClass = [NSMutableSet setWithCapacity:4];

        _initialContentOffset = CGPointZero;
        _scrollDirection = UPMarketFreezeColumnViewScrollDirectionNone;

        [self addSubview:self.collectionView];
    }
    return self;
}


- (void)dealloc {
    [NSObject cancelPreviousPerformRequestsWithTarget:self];

    self.delegate = nil;

    if(_collectionView) {
        _collectionView.delegate = nil;
        _collectionView.dataSource = nil;
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];

    self.collectionView.frame = self.bounds;
}

// MARK: Public

- (void)reloadData {
    self.numberOfRows = [self loadNumberOfRows];
    self.columns = [self loadColumns];

    for (NSString * className in self.registeredClass) {
        [self.collectionView registerClass:nil forCellWithReuseIdentifier:className];
    }

    [self.registeredClass removeAllObjects];
    [self.viewLayout reset];
    self.viewLayout.columns = self.columns;
    self.viewLayout.headerHeight = self.headerHeight;
    self.viewLayout.sectionFooterHeight = self.sectionFooterHeight;
    self.viewLayout.collectionFooterHeight = self.collectionFooterHeight;
    self.viewLayout.rowHeight = self.rowHeight;

    self.viewLayout.separatorColor = self.separatorColor;
    self.viewLayout.separatorInset = self.separatorInset;
    self.viewLayout.separatorHeight = self.separatorHeight;
    self.viewLayout.showsHeaderSeparator = self.showsHeaderSeparator;
    self.viewLayout.showsLastRowSeparator = self.showsLastRowSeparator;
    __weak __typeof(self)weakSelf = self;
    if ([self.delegate respondsToSelector:@selector(footerHeightForFreezeColumnView:section:)] && self.enableDynamicFooterHeight) {
        self.viewLayout.footerHeightForColumnBlock = ^CGFloat(NSInteger section) {
            return [weakSelf.delegate footerHeightForFreezeColumnView:weakSelf section:section];
        };
    }
    [self.collectionView reloadData];
}

// MARK: Getter & Setter

- (void)setColumns:(NSArray<UPMarketFreezeColumnViewColumn *> *)columns {
    _columns = columns;
    
    if (columns.count) {
        columns.lastObject.isLastColumn = YES;
    }
}

-(UIScrollView *)scrollView {
    return self.collectionView;
}

- (CGPoint)contentOffset {
    return self.collectionView.contentOffset;
}

-(void)setContentOffset:(CGPoint)contentOffset {
    self.collectionView.contentOffset = contentOffset;
}

- (CGSize)contentSize {
    return self.collectionView.contentSize;
}

- (void)setContentSize:(CGSize)contentSize {
    self.collectionView.contentSize = contentSize;
}

- (BOOL)showsScrollIndicator {
    return self.collectionView.showsVerticalScrollIndicator;
}

-(void)setShowsScrollIndicator:(BOOL)showsScrollIndicator {
    self.collectionView.showsVerticalScrollIndicator = showsScrollIndicator;
}

- (void)setListBackgroundColor:(UIColor *)listBackgroundColor {
    _listBackgroundColor = listBackgroundColor;
    self.collectionView.backgroundColor = self.listBackgroundColor;
}

-(UICollectionView *)collectionView {
    if(!_collectionView) {
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:self.viewLayout];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.bounces = YES;
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.directionalLockEnabled = YES;
        _collectionView.delaysContentTouches = NO;
        if (self.listBackgroundColor) {
            _collectionView.backgroundColor = self.listBackgroundColor;
        } else {
//            _collectionView.backgroundColor = UPTColor(@"up_baseui_list_bg_color");
            _collectionView.backgroundColor = [UIColor up_colorFromHexString:@"#FFFFFF"];
        }
    }

    return _collectionView;
}

- (UPMarketFreezeColumnViewLayout *)viewLayout {
    if(!_viewLayout) {
        _viewLayout = [[UPMarketFreezeColumnViewLayout alloc] init];
    }

    return _viewLayout;
}

// MARK: UIScrollViewDelegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    self.initialContentOffset = scrollView.contentOffset;
    self.scrollDirection = UPMarketFreezeColumnViewScrollDirectionNone;
}

-(void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (self.scrollDirection == UPMarketFreezeColumnViewScrollDirectionNone) {
        if (fabs(self.initialContentOffset.y - scrollView.contentOffset.y) >= fabs(self.initialContentOffset.x - scrollView.contentOffset.x)) {
            self.scrollDirection = UPMarketFreezeColumnViewScrollDirectionVertical;
        } else {
            self.scrollDirection = UPMarketFreezeColumnViewScrollDirectionHoritonzal;
        }
    }

    if (self.scrollDirection == UPMarketFreezeColumnViewScrollDirectionVertical) {
        scrollView.contentOffset = CGPointMake(self.initialContentOffset.x, scrollView.contentOffset.y);
    } else if (self.scrollDirection == UPMarketFreezeColumnViewScrollDirectionHoritonzal) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, self.initialContentOffset.y);
    }

    [self performDidScroll:scrollView.contentOffset];

    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(scrollDidStop) object:nil];
    [self performSelector:@selector(scrollDidStop) withObject:nil afterDelay:0.3];
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (decelerate) {
        self.scrollDirection = UPMarketFreezeColumnViewScrollDirectionNone;
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    self.scrollDirection = UPMarketFreezeColumnViewScrollDirectionNone;
}

// MARK: UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if(indexPath.section == 0) {  // 表头
        UPMarketFreezeColumnViewColumn * column = self.columns[indexPath.row];
        if ([column.name containsString:@"点击"]) {

        }
        
        [self performHeaderDidSelect:column];
    } else {
        [self performRowDidSelect:indexPath.section - 1]; // 去掉表头
    }
}

- (BOOL)collectionView:(UICollectionView *)collectionView shouldHighlightItemAtIndexPath:(NSIndexPath *)indexPath {
    return YES;
}

- (void)collectionView:(UICollectionView *)collectionView didHighlightItemAtIndexPath:(NSIndexPath *)indexPath {
    if(indexPath.section == 0) { // 表头
        return;
    }

    UIColor * rowBackgroundColor = self.rowBackgroundColor;
    UIColor * selectedRowBackgroundColor = self.rowBackgroundColorSelected;

    if(rowBackgroundColor && selectedRowBackgroundColor) {
        for (NSUInteger i = 0; i < self.columns.count; ++i) {
            indexPath = [NSIndexPath indexPathForRow:i inSection:indexPath.section];
            UICollectionViewCell * cell = [collectionView cellForItemAtIndexPath:indexPath];
            cell.backgroundColor = selectedRowBackgroundColor;
        }
    }
}

- (void)collectionView:(UICollectionView *)collectionView  didUnhighlightItemAtIndexPath:(NSIndexPath *)indexPath {
    if(indexPath.section == 0) { // 表头
        return;
    }

    UIColor * rowBackgroundColor = self.rowBackgroundColor;

    if(rowBackgroundColor) {
        for (NSUInteger i = 0; i <  self.columns.count; ++i) {
            indexPath = [NSIndexPath indexPathForRow:i inSection:indexPath.section];
            UICollectionViewCell * cell = [collectionView cellForItemAtIndexPath:indexPath];
            [UIView animateWithDuration:0.15 animations:^{
                cell.backgroundColor = rowBackgroundColor;
            }];
        }
    }
}

// MARK: UICollectionViewDataSource


- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    
    if (self.collectionFooterHeight > 0 && indexPath.section == [collectionView numberOfSections]-1) {
        if ([self.delegate respondsToSelector:@selector(freezeColumnViewCollectionFooterViewClass:)]) {
            Class classz = [self.delegate freezeColumnViewCollectionFooterViewClass:self];
            if(classz && [classz isSubclassOfClass:[UPMarketFreezeFooterView class]] && [kind isEqualToString:UICollectionElementKindSectionFooter]) {
                NSString * className = NSStringFromClass(classz);
                
                if(!self.registeredCollectionFooterClass) {
                    [collectionView registerClass:classz forSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:className];
                    self.registeredCollectionFooterClass = classz;
                }
                
                UPMarketFreezeFooterView *footerView = (UPMarketFreezeFooterView *)[collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:className forIndexPath:indexPath];
                [self configCollectionFooterViewFor:footerView];
                
                return footerView;
            }
        }
    }else{
        //正常footer
        if ([self.delegate respondsToSelector:@selector(freezeColumnViewSectionFooterViewClass:)]) {
            Class classz = [self.delegate freezeColumnViewSectionFooterViewClass:self];
            if(classz && [classz isSubclassOfClass:[UPMarketFreezeFooterView class]] && [kind isEqualToString:UICollectionElementKindSectionFooter]) {
                NSString * className = NSStringFromClass(classz);
                
                if(!self.registeredSectionFooterClass) {
                    [collectionView registerClass:classz forSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:className];
                    self.registeredSectionFooterClass = classz;
                }
                
                UPMarketFreezeFooterView *footerView = (UPMarketFreezeFooterView *)[collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:className forIndexPath:indexPath];
                [self configSectionFooterViewFor:footerView section:indexPath.section-1];
                
                return footerView;
            }
        }
    }
    
    return nil;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return self.collectionFooterHeight > 0 ? self.numberOfRows + 2 : self.numberOfRows + 1;  // 多一行为表头
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.columns.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    UICollectionViewCell * cell;
    
    UPMarketFreezeColumnViewColumn * column = self.columns[indexPath.row];

    if(indexPath.section == 0) { // 表头
        Class clazz = [self loadHeaderCellClassFor:column];

        if(clazz) {
            NSString * className = NSStringFromClass(clazz);
            
            if(![self.registeredClass containsObject:className]) {
                [collectionView registerClass:clazz forCellWithReuseIdentifier:className];
                [self.registeredClass addObject:className];
            }

            cell = [collectionView dequeueReusableCellWithReuseIdentifier:className forIndexPath:indexPath];
            [self configHeaderCellFor:(UPMarketFreezeColumnViewCell *)cell column:column];
        }
    } else {
        Class clazz = [self loadDataCellClassFor:column];

        if(clazz) {
            NSString * className = NSStringFromClass(clazz);

            if(![self.registeredClass containsObject:className]) {
                [collectionView registerClass:clazz forCellWithReuseIdentifier:className];
                [self.registeredClass addObject:className];
            }

            cell = [collectionView dequeueReusableCellWithReuseIdentifier:className forIndexPath:indexPath];
            cell.backgroundColor = self.rowBackgroundColor;
            
            [self configDataCellFor:(UPMarketFreezeColumnViewCell *)cell column:column indexOfRow:indexPath.section - 1]; // 去掉表头
        }
    }

    return cell;
}

// MARK: Private

- (void)scrollDidStop {
    NSInteger firstRowIndex = NSNotFound;
    NSInteger lastRowIndex = NSNotFound;

    for (NSIndexPath * indexPath in self.collectionView.indexPathsForVisibleItems) {
        if(indexPath.section != 0) { // 0是表头
            if(firstRowIndex == NSNotFound) {
                firstRowIndex = indexPath.section;
            } else {
                firstRowIndex = MIN(indexPath.section, firstRowIndex);
            }

            if(lastRowIndex == NSNotFound) {
                lastRowIndex = indexPath.section;
            } else {
                lastRowIndex = MAX(indexPath.section, lastRowIndex);
            }
        }
    }

    NSRange range = NSMakeRange(firstRowIndex - 1, lastRowIndex - firstRowIndex + 1);

    [self performVisibleRowDidChange:range];
}

-(NSInteger)loadNumberOfRows {
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(numberOfRowsInFreezeColumnView:)]) {
        return [delegate numberOfRowsInFreezeColumnView:self];
    }

    return 0;
}

-(NSArray<UPMarketFreezeColumnViewColumn *> *)loadColumns {
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(columnsInFreezeColumnView:)]) {
        return [delegate columnsInFreezeColumnView:self];
    }
    
    return nil;
}

- (Class)loadHeaderCellClassFor:(UPMarketFreezeColumnViewColumn *)column {
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:headerCellClassFor:)]) {
        return [delegate freezeColumnView:self headerCellClassFor:column];
    }
    
    return nil;
}

- (Class)loadDataCellClassFor:(UPMarketFreezeColumnViewColumn *)column {
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:dataCellClassFor:)]) {
        return [delegate freezeColumnView:self dataCellClassFor:column];
    }
    
    return nil;
}

- (void)configHeaderCellFor:(UPMarketFreezeColumnViewCell *)cell column:(UPMarketFreezeColumnViewColumn *)column {
    
    cell.isLastColumn = column.isLastColumn;
    
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:configHeaderCell:column:)]) {
        [delegate freezeColumnView:self configHeaderCell:cell column:column];
    }
}

- (void)configSectionFooterViewFor:(UPMarketFreezeFooterView *)footerView section:(NSInteger)section{
    
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:configSectionFooterView:section:)]) {
        [delegate freezeColumnView:self configSectionFooterView:footerView section:section];
    }
}

- (void)configCollectionFooterViewFor:(UPMarketFreezeFooterView *)footerView{
    
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:configCollectionFooterView:)]) {
        [delegate freezeColumnView:self configCollectionFooterView:footerView];
    }
}


- (void)configDataCellFor:(UPMarketFreezeColumnViewCell *)cell column:(UPMarketFreezeColumnViewColumn *)column indexOfRow:(NSInteger)indexOfRow {
    
    cell.isLastColumn = column.isLastColumn;
    
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:configDataCell:column:indexOfRow:)]) {
        [delegate freezeColumnView:self configDataCell:cell column:column indexOfRow:indexOfRow];
    }
}

- (void)performVisibleRowDidChange:(NSRange)visibleRowRange {
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:visibleRowDidChange:)]) {
        [delegate freezeColumnView:self visibleRowDidChange:visibleRowRange];
    }
}

- (void)performHeaderDidSelect:(UPMarketFreezeColumnViewColumn *)column {
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:headerDidSelect:)]) {
        [delegate freezeColumnView:self headerDidSelect:column];
    }
}

- (void)performRowDidSelect:(NSInteger)indexOfRow {
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:rowDidSelect:)]) {
        [delegate freezeColumnView:self rowDidSelect:indexOfRow];
    }
}

- (void)performDidScroll:(CGPoint)offset {
    __strong id<UPMarketFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:didScroll:)]) {
        [delegate freezeColumnView:self didScroll:offset];
    }
}

@end
