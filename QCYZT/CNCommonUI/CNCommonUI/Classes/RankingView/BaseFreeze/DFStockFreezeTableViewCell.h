//
//  DFStockFreezeTableViewCell.h
//  UPMarket2
//
//  Created by 彭继宗 on 2023/6/27.
//

#import <UIKit/UIKit.h>
#import "UPMarketFreezeColumnView.h"

@class DFStockFreezeTableViewCell;

NS_ASSUME_NONNULL_BEGIN

@protocol DFStockFreezeTableViewDelegate <NSObject>

// Class必须继承自DFStockFreezeColumnViewCell
- (Class)freezeColumnViewHeaderCellClassFor:(UPMarketFreezeColumnViewColumn *)column;

// Class必须继承自DFStockFreezeColumnViewCell
- (Class)freezeColumnViewDataCellClassFor:(UPMarketFreezeColumnViewColumn *)column;


- (void)freezeColumnViewConfigDataCell:(UPMarketFreezeColumnViewCell *)dataCell
                                column:(UPMarketFreezeColumnViewColumn *)column
                            indexOfRow:(NSInteger)indexOfRow;

- (void)freezeColumnViewConfigHeaderCell:(UPMarketFreezeColumnViewCell *)columnCell
                  column:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnViewHeaderDidSelectRow:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnViewRowDidSelectRow:(NSInteger)indexOfRow column:(NSInteger)column;

- (void)scrollViewDidScrollToOffsetX:(CGFloat)offsetX index:(NSInteger)index;

@end


@interface DFStockFreezeTableViewCell : UITableViewCell

@property (nonatomic, weak) id<DFStockFreezeTableViewDelegate> delegate; 

- (void)updateWithColumns:(NSArray<UPMarketFreezeColumnViewColumn *> *)columns index:(NSInteger)index rowHeight:(CGFloat)rowHeight;
- (void)updateOffset:(CGFloat)offset;
- (void)updatefooterView:(UIView *)footerView;
@end

NS_ASSUME_NONNULL_END
