//
//  UPMarketFreezeColumnView.h
//  UPBaseUI
//
//  Created by sammy<PERSON> on 2020/3/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class UPMarketFreezeColumnView;

// MARK: UPMarketFreezeColumnViewColumn

@interface UPMarketFreezeColumnViewColumn : NSObject

@property(nonatomic, copy) NSString * name;
@property(nonatomic, assign) CGFloat width;
@property(nonatomic, assign) NSInteger tag;
@property(nonatomic, assign) BOOL freeze;
@property(nonatomic, assign) BOOL isLastColumn;

+ (instancetype)columnWithN:(NSString *)name w:(CGFloat)width t:(NSInteger)tag;

+ (instancetype)freezeColumnWithN:(NSString *)name w:(CGFloat)width t:(NSInteger)tag;

@end

// MARK: UPMarketFreezeColumnViewCell

@interface UPMarketFreezeColumnViewCell : UICollectionViewCell

@property(nonatomic, assign) BOOL isLastColumn;

@end

// MARK: UPMarketFreezeFooterView

@interface UPMarketFreezeFooterView : UICollectionReusableView


@end

// MARK: UPMarketFreezeColumnViewDelegate

@protocol UPMarketFreezeColumnViewDelegate <NSObject>
@optional

- (NSInteger)numberOfRowsInFreezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView;

- (NSArray<UPMarketFreezeColumnViewColumn *> *)columnsInFreezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView;

// Class必须继承自UPMarketFreezeColumnViewCell
- (Class)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
       headerCellClassFor:(UPMarketFreezeColumnViewColumn *)column;

// Class必须继承自UPMarketFreezeFooterView 每一行的section footer的类型目前只能注册一个类型
- (Class)freezeColumnViewSectionFooterViewClass:(UPMarketFreezeColumnView *)freezeColumnView;

// Class必须继承自UPMarketFreezeFooterView tableFooter的类型目前只能注册一个类型
- (Class)freezeColumnViewCollectionFooterViewClass:(UPMarketFreezeColumnView *)freezeColumnView;

- (Class)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
         dataCellClassFor:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
        configHeaderCell:(UPMarketFreezeColumnViewCell *)columnCell
                  column:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
 configSectionFooterView:(UPMarketFreezeFooterView *)footerView
                 section:(NSInteger)section;

- (void)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
        configCollectionFooterView:(UPMarketFreezeFooterView *)footerView;

- (void)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
          configDataCell:(UPMarketFreezeColumnViewCell *)dataCell
                  column:(UPMarketFreezeColumnViewColumn *)column
              indexOfRow:(NSInteger)indexOfRow;

- (void)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
     visibleRowDidChange:(NSRange)visibleRowRange;

- (void)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
         headerDidSelect:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
            rowDidSelect:(NSInteger)indexOfRow;

- (void)freezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
               didScroll:(CGPoint)offset;

- (CGFloat)footerHeightForFreezeColumnView:(UPMarketFreezeColumnView *)freezeColumnView
                                   section:(NSInteger)section;

@end

@interface UPMarketFreezeColumnView : UIView


@property(nonatomic, weak) id<UPMarketFreezeColumnViewDelegate> delegate;

@property(nonatomic, strong, readonly) UIScrollView * scrollView;

@property(nonatomic, assign) CGPoint contentOffset;
@property(nonatomic, assign) CGSize contentSize;

@property(nonatomic, assign) BOOL showsScrollIndicator;

@property(nonatomic, assign) CGFloat headerHeight; // 表头高度
@property(nonatomic, assign) CGFloat rowHeight; // 行高度
@property(nonatomic, assign) CGFloat sectionFooterHeight; // 表脚高度(同一高度)
@property(nonatomic, assign) CGFloat collectionFooterHeight; // collectionFooter高度(暂不支持动态撑开)

@property(nonatomic, strong) UIColor * listBackgroundColor; // collectionView背景色

@property(nonatomic, strong) UIColor * rowBackgroundColor; // 常态背景色
@property(nonatomic, strong) UIColor * rowBackgroundColorSelected; // 选中背景色

@property(nonatomic, strong) UIColor * separatorColor; // 分割线颜色
@property(nonatomic, assign) UIEdgeInsets separatorInset; // 分割线位置
@property(nonatomic, assign) CGFloat separatorHeight; // 分割线高度
@property(nonatomic, assign) BOOL showsHeaderSeparator; // header下面是是否显示分割线, 默认NO
@property(nonatomic, assign) BOOL showsLastRowSeparator; // 最后一行是否显示分割线, 默认NO
@property(nonatomic, assign) BOOL enableDynamicFooterHeight; // 是否展示不同高度的footer, 默认NO

- (void)reloadData;

@end

NS_ASSUME_NONNULL_END
