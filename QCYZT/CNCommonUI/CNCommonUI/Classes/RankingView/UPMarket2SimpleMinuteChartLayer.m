//
//  UPMarket2SimpleMinuteChartLayer.m
//  UPMarket2
//
//  Created by kds on 2022/9/5.
//

#import "UPMarket2SimpleMinuteChartLayer.h"
#import "UPMarket2SimpleMinuteChartView.h"
#import <UPMarket2/UPMarket2StockUIUtil.h>

@interface UPMarket2HHLayer : CAShapeLayer

@end

@implementation UPMarket2HHLayer

- (void)drawInContext:(CGContextRef)ctx {
    // 消除锯齿
    CGContextSetShouldAntialias(ctx, YES);
}

@end

@interface UPMarket2SimpleMinuteChartLayer()

/** 分时线 */
@property (nonatomic, strong) CAShapeLayer *minuteLineLayer;

/** 分时线填充 */
@property (nonatomic, strong) CAShapeLayer *minuteLineFillLayer;

/** 昨日收盘线 */
@property (nonatomic, strong) UPMarket2HHLayer *yClosePriceLineLayer;

@end

@implementation UPMarket2SimpleMinuteChartLayer

- (instancetype)init {
    if (self = [super init]) {
        [self sublayerInitialization];
        [self configLayer];
    }
    return self;
}

- (void)sublayerInitialization {
    self.minuteLineLayer = [CAShapeLayer layer];
    [self addSublayer:self.minuteLineLayer];
    
    self.minuteLineFillLayer = [CAShapeLayer layer];
    self.minuteLineFillLayer.lineWidth = 0;
    self.minuteLineFillLayer.strokeColor = [UIColor clearColor].CGColor;
    self.minuteLineFillLayer.fillColor = [UIColor whiteColor].CGColor;
    [self insertSublayer:self.minuteLineFillLayer atIndex:0];
        
    self.yClosePriceLineLayer = [UPMarket2HHLayer layer];
//    self.yClosePriceLineLayer.hidden= YES;
    [self addSublayer:self.yClosePriceLineLayer];
}

- (void)configLayer {
    UIColor *minuteColor = UIColor.upmarketui_nowPriceLineColor;
    self.minuteLineLayer.fillColor = [UIColor clearColor].CGColor;
    self.minuteLineLayer.strokeColor = UIColor.upmarketui_nowPriceLineColor.CGColor;
    self.minuteLineLayer.lineWidth = 0.5;
    self.minuteLineLayer.lineJoin = kCALineJoinRound;
    self.minuteLineLayer.lineCap = kCALineCapRound;
    
    self.minuteLineFillLayer.strokeColor = [UIColor clearColor].CGColor;
    self.minuteLineFillLayer.fillColor = [minuteColor colorWithAlphaComponent:0.1].CGColor;

    self.yClosePriceLineLayer.fillColor = [UIColor clearColor].CGColor;
    self.yClosePriceLineLayer.strokeColor = [UIColor up_colorFromHexString:@"#9BA5B8"].CGColor;
    self.yClosePriceLineLayer.lineWidth = 1.f;
    self.yClosePriceLineLayer.lineJoin = kCALineJoinRound;
    self.yClosePriceLineLayer.lineDashPattern = @[@2, @2];
}

- (void)updateLayout:(CGRect)frame {
    self.frame = frame;
    self.minuteLineFillLayer.frame = CGRectMake(0, 0, CGRectGetWidth(frame), CGRectGetHeight(frame));
    self.minuteLineLayer.frame = CGRectMake(0, 0, CGRectGetWidth(frame), CGRectGetHeight(frame));
    self.yClosePriceLineLayer.frame = CGRectMake(0, 0, CGRectGetWidth(frame), CGRectGetHeight(frame));
}

/** 绘制分时图 */
- (void)drawMinuteChart:(id)minuteView {
    UPMarket2SimpleMinuteChartView *view = (UPMarket2SimpleMinuteChartView *)minuteView;
    CGFloat unitHeight = view.unitHeight;
    CGFloat itemWidth = view.itemWidth;
    NSArray *dataList = view.model.stockHq.minuteData.minuteArray;
    
    CGFloat startX = 0;
    CGFloat endX = CGRectGetMaxX(self.minuteLineFillLayer.bounds);
    
    UIColor *textColor = [UPMarket2StockUIUtil getRiseFallTextColor:view.model.stockHq.changeValue baseValue:0];
    
    self.minuteLineLayer.strokeColor = textColor.CGColor;
    self.minuteLineFillLayer.fillColor = [textColor colorWithAlphaComponent:0.1].CGColor;
    
    // 绘制昨收线
    CGFloat yClosePointY = (view.maxValue - view.model.stockHq.yClosePrice) * unitHeight;
    
    UIBezierPath *yClosePath = [UIBezierPath bezierPath];
    [yClosePath moveToPoint:CGPointMake(startX, yClosePointY)];
    [yClosePath addLineToPoint:CGPointMake(endX, yClosePointY)];
    self.yClosePriceLineLayer.path = yClosePath.CGPath;
    [self.yClosePriceLineLayer setNeedsDisplay];
    
    // 绘制分时线（现价）
    UPHqMinuteItem *item = dataList.firstObject;
    CGFloat pointY = (view.maxValue - item.nowPrice) * unitHeight;
    
    UIBezierPath *timePath = [UIBezierPath bezierPath];
    [timePath moveToPoint:CGPointMake(startX, pointY)];
    
    if (dataList.count) {
        [dataList enumerateObjectsUsingBlock:^(UPHqMinuteItem *item, NSUInteger idx, BOOL * _Nonnull stop) {
            CGFloat pointY = (view.maxValue - item.nowPrice) * unitHeight;
            CGFloat centerX = idx * itemWidth;
            [timePath addLineToPoint:CGPointMake(centerX, pointY)];
        }];
        self.minuteLineLayer.path = timePath.CGPath;
    } else {
        self.minuteLineLayer.path = nil;
    }
    
    // 绘制分时填充
    if (dataList.count) {
        CGFloat endY = CGRectGetMaxY(self.minuteLineFillLayer.bounds);
        UIBezierPath *fillPath = [UIBezierPath bezierPathWithCGPath:timePath.CGPath];
        [fillPath addLineToPoint:CGPointMake(dataList.count * itemWidth, endY)];
        [fillPath addLineToPoint:CGPointMake(0, endY)];
        [fillPath closePath];
        self.minuteLineFillLayer.path = fillPath.CGPath;
    } else {
        self.minuteLineFillLayer.path = nil;
    }
}

@end
