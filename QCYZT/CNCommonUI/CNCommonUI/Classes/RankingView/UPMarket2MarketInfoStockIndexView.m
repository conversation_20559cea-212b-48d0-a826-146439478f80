//
//  UPMarket2MarketInfoStockIndexView.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2MarketInfoStockIndexView.h"
#import <UPMarket2/UPMarket2StockUIUtil.h>
#import "UPMarket2SimpleMinuteChartView.h"

@interface UPMarket2MarketInfoStockIndexView ()

@property (nonatomic, strong) UILabel *titleLabel;   // 标题

@property (nonatomic, strong) UILabel *pointLabel;   // 现价

@property (nonatomic, strong) UIButton *coverButton;

@property (nonatomic, strong) UILabel *leftLabel;   // 涨跌

@property (nonatomic, strong) UILabel *rightLabel;  // 涨跌幅

@property (nonatomic, strong) UIView *bottomView;

//@property (nonatomic, strong) CAGradientLayer *gradientLayer;

@end

@implementation UPMarket2MarketInfoStockIndexView
- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;

        self.coverButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [self.coverButton addTarget:self action:@selector(coverButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:self.coverButton];
        
        [self.coverButton addSubview:self.titleLabel];
        [self.coverButton addSubview:self.pointLabel];
        [self.coverButton addSubview:self.bottomView];
        [self.bottomView addSubview:self.leftLabel];
        [self.bottomView addSubview:self.rightLabel];
        self.layer.cornerRadius = UPWidth(5);
    }
    return self;
}

- (void)layoutSubviews {
    if (CGSizeEqualToSize(self.up_size, CGSizeZero)) {
        return;
    }
    
    [self.coverButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self).offset(UPHeight(10));
    }];
    [self.pointLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(UPHeight(6));
    }];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.pointLabel.mas_bottom).offset(UPHeight(7.5));
        make.height.equalTo(@(UPHeight(12)));
        make.centerX.equalTo(self);
        make.bottom.equalTo(self).offset(UPHeight(-8.5));
    }];
    [self.leftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bottomView);
        make.centerY.equalTo(self.bottomView);
    }];
    [self.rightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.leftLabel.mas_right).offset(2);
        make.right.equalTo(self.bottomView);
        make.centerY.equalTo(self.bottomView);
    }];
}

- (void)coverButtonClick:(UIButton *)button {
    if (self.indexModel.codeArray.count > 0 && self.indexModel.setCodeArray.count > 0) {
        NSMutableString * paramSetCode = [NSMutableString stringWithCapacity:64];
        NSMutableString * paramCode = [NSMutableString stringWithCapacity:64];
        
        for(NSInteger i = 0; i < self.indexModel.codeArray.count; i++) {
            if(paramSetCode.length > 0) {
                [paramSetCode appendString:@"_"];
            }
            
            if(paramCode.length > 0) {
                [paramCode appendString:@"_"];
            }
            NSString *code = self.indexModel.codeArray[i];
            NSNumber *setCode = self.indexModel.setCodeArray[i];
            [paramSetCode appendFormat:@"%@", setCode];
            [paramCode appendFormat:@"%@", code];
        }

        UPRouterNavigate([@"upchina://market/stock" up_buildURLWithQueryParams:@{
            @"setcode" : paramSetCode,
            @"code" : paramCode,
            @"current": [NSString stringWithFormat:@"%d", (int)(self.tag - kUPMarket2MarketInfoStockIndexViewBaseTag)],
        }]);
    }
}

- (void)updateGradientLayer {
    CGFloat value = self.indexModel.stockHq.changeValue;
    NSNumber *key;
    if (value > 0) {
        key = @1;
    } else if (value < 0) {
        key = @-1;
    } else {
        key = @0;
    }
    
    UIColor *gradient1Color;
    UIColor *gradient2Color;
    
    if (value > 0) {
        gradient1Color = UIColor.upmarket2_indexStock_rise_gradient1_bg_Color;
        gradient2Color = UIColor.upmarket2_indexStock_rise_gradient2_bg_Color;
    } else if (value < 0) {
        gradient1Color = UIColor.upmarket2_indexStock_fall_gradient1_bg_Color;
        gradient2Color = UIColor.upmarket2_indexStock_fall_gradient2_bg_Color;
    } else {
        gradient1Color = UIColor.upmarket2_indexStock_equal_gradient1_bg_Color;
        gradient2Color = UIColor.upmarket2_indexStock_equal_gradient2_bg_Color;
    }

//    if (!self.gradientLayer) {
//        self.gradientLayer = [CAGradientLayer layer];
//        self.gradientLayer.locations = @[@0,@1];
//        self.gradientLayer.startPoint = CGPointMake(0, 0);
//        self.gradientLayer.endPoint = CGPointMake(0, 1);
//        self.gradientLayer.zPosition = -100;
//    }
//
//    self.gradientLayer.colors = @[
//        (__bridge id)gradient1Color.CGColor,
////        (__bridge id)gradient2Color.CGColor
//    ];
//    self.gradientLayer.frame = CGRectMake(0, 0, self.up_width, self.up_height);
//    if (!self.gradientLayer.superlayer) {
//        [self.layer addSublayer:self.gradientLayer];
//    }
//    [self.gradientLayer layoutIfNeeded];
    self.backgroundColor = gradient1Color;
}

// MARK: - Getter & Setter
- (void)setIndexModel:(UPMarket2MarketInfoStockIndexModel *)messageModel {
    _indexModel = messageModel;

    self.titleLabel.text = messageModel.topHeaderName;
    self.pointLabel.text = messageModel.centerData;
    self.leftLabel.text = messageModel.bottomLeftData;
    self.rightLabel.text = messageModel.bottomRightData;
    
    [self updateGradientLayer];
    
    UIColor *textColor = [UPMarket2StockUIUtil getRiseFallTextColor:messageModel.stockHq.changeValue baseValue:0];
    self.pointLabel.textColor = textColor;
    self.leftLabel.textColor = textColor;
    self.rightLabel.textColor = textColor;
}

- (UILabel *)titleLabel {
    if (_titleLabel == nil) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont up_fontOfSize:UPHeight(14)];
        _titleLabel.textColor = UIColor.up_market2_title_fourth_color;
    }
    return _titleLabel;
}

- (UILabel *)pointLabel {
    if (_pointLabel == nil) {
        _pointLabel = [UILabel new];
        _pointLabel.font = [UIFont up_boldFontOfSize:UPHeight(18)];
        
    }
    return _pointLabel;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[UIView alloc] init];
    }
    return _bottomView;
}

- (UILabel *)leftLabel {
    if (!_leftLabel) {
        _leftLabel = [[UILabel alloc] init];
        _leftLabel.font = [UIFont up_boldFontOfSize:UPHeight(12)];
    }
    return _leftLabel;
}

- (UILabel *)rightLabel {
    if (!_rightLabel) {
        _rightLabel = [[UILabel alloc] init];
        _rightLabel.font = [UIFont up_boldFontOfSize:UPHeight(12)];
    }
    return _rightLabel;
}

@end


@interface UPMarket2MarketInfoStockIndexChartView()

@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UILabel * nameLabel;
@property (nonatomic, strong) UIImageView *iconImage;
@property (nonatomic, strong) UILabel * changeAmountLabel;
@property (nonatomic, strong) UILabel * percentLabel;
@property (nonatomic, strong) UILabel * zhishuNumLabel;


@property (nonatomic, strong) UPMarket2SimpleMinuteChartView * chartView;

@property (nonatomic, strong) UIButton * fullBtn;

@end


@implementation UPMarket2MarketInfoStockIndexChartView



#pragma mark 生命周期

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupViews];
        [self setConstraints];
        self.layer.cornerRadius = 5.f;
        self.layer.masksToBounds = YES;
    }
    return self;
}

- (void)setupViews{
    
    [self addSubview:self.stackView];
    [self.stackView addArrangedSubview:self.nameLabel];
    [self.stackView addArrangedSubview:self.iconImage];
    [self addSubview:self.zhishuNumLabel];
    [self addSubview:self.changeAmountLabel];
    [self addSubview:self.percentLabel];
    [self addSubview:self.chartView];
    [self addSubview:self.fullBtn];
}

- (void)setConstraints{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mas_centerX);
        make.top.equalTo(self).offset(UPWidth(7));
    }];
    
    [self.zhishuNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.nameLabel.mas_bottom).offset(UPHeight(5));
    }];
    
    [self.changeAmountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(UPWidth(8));
        make.top.equalTo(self.zhishuNumLabel.mas_bottom).offset(UPHeight(7));
    }];
    
    [self.percentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(UPWidth(-8));
//        make.left.equalTo(self.nameLabel.mas_right).offset(UPWidth(5));
//        make.width.equalTo(self.nameLabel.mas_width);
        make.centerY.equalTo(self.changeAmountLabel);
    }];
    
    [self.chartView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.percentLabel.mas_bottom).offset(UPWidth(5));
        make.left.right.bottom.equalTo(self);
    }];
    
    [self.fullBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(self);
    }];
}
#pragma mark 点击事件
- (void)fullBtnClick{
    if (self.indexModel.codeArray.count > 0 && self.indexModel.setCodeArray.count > 0) {
        NSMutableString * paramSetCode = [NSMutableString stringWithCapacity:64];
        NSMutableString * paramCode = [NSMutableString stringWithCapacity:64];
        
        for(NSInteger i = 0; i < self.indexModel.codeArray.count; i++) {
            if(paramSetCode.length > 0) {
                [paramSetCode appendString:@"_"];
            }
            
            if(paramCode.length > 0) {
                [paramCode appendString:@"_"];
            }
            NSString *code = self.indexModel.codeArray[i];
            NSNumber *setCode = self.indexModel.setCodeArray[i];
            [paramSetCode appendFormat:@"%@", setCode];
            [paramCode appendFormat:@"%@", code];
        }

        UPRouterNavigate([@"upchina://market/stock" up_buildURLWithQueryParams:@{
            @"setcode" : paramSetCode,
            @"code" : paramCode,
            @"current": [NSString stringWithFormat:@"%d", (int)(self.tag - kUPMarket2MarketInfoStockIndexViewBaseTag)],
        }]);
    }
}

#pragma mark setter&getter
- (UILabel *)nameLabel{
    if (!_nameLabel) {
        _nameLabel = [UILabel new];
        _nameLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _nameLabel.textColor = UIColor.up_market2_title_color;
        _nameLabel.text = @"-";
    }
    return _nameLabel;
}

- (UILabel *)changeAmountLabel{
    if (!_changeAmountLabel) {
        _changeAmountLabel = [UILabel new];
        _changeAmountLabel.font = [UIFont up_boldFontOfSize:UPWidth(11)];
        _changeAmountLabel.textColor = UIColor.up_market2_title_color;
        _changeAmountLabel.text = @"-";
    }
    return _changeAmountLabel;
}

- (UILabel *)zhishuNumLabel {
    if (!_zhishuNumLabel) {
        _zhishuNumLabel = [UILabel new];
        _zhishuNumLabel.font = [UIFont up_boldFontOfSize:UPWidth(14)];
        _zhishuNumLabel.textColor = UIColor.up_market2_title_color;
        _zhishuNumLabel.text = @"-";
    }
    return _zhishuNumLabel;
}

- (UIImageView *)iconImage {
    if (!_iconImage) {
        _iconImage = [[UIImageView alloc] init];
        
    }
    return _iconImage;
}

- (UILabel *)percentLabel{
    if (!_percentLabel) {
        _percentLabel = [UILabel new];
        _percentLabel.font = [UIFont up_fontOfSize:UPWidth(11) weight:UIFontWeightSemibold];
        _percentLabel.textColor = UIColor.up_equalColor;
        _percentLabel.text = @"-";
//        _percentLabel.layer.borderWidth = 0.5;
//        _percentLabel.layer.borderColor = UIColor.upmarketui_equalColor.CGColor;
//        _percentLabel.layer.cornerRadius = 2;
        _percentLabel.textAlignment = NSTextAlignmentRight;
    }
    return _percentLabel;
}

- (UPMarket2SimpleMinuteChartView *)chartView{
    if (!_chartView) {
        _chartView = [UPMarket2SimpleMinuteChartView new];
        _chartView.layer.cornerRadius = 5.f;
        _chartView.layer.masksToBounds = YES;
    }
    return _chartView;
}

- (void)setIndexModel:(UPMarket2MarketInfoStockIndexModel *)indexModel{
    _indexModel = indexModel;
    UIColor *textColor = [UPMarket2StockUIUtil getRiseFallTextColor:indexModel.stockHq.changeValue baseValue:0];
    self.nameLabel.text = indexModel.stockHq.name;
    self.percentLabel.text = indexModel.bottomRightData;
//    self.percentLabel.text = [indexModel.bottomRightData stringByReplacingOccurrencesOfString:@"+" withString:@""];
    self.percentLabel.textColor = textColor;
    self.percentLabel.layer.borderColor = textColor.CGColor;
    self.zhishuNumLabel.text = [NSString stringWithFormat:@"%.2f",indexModel.stockHq.nowPrice];
    self.zhishuNumLabel.textColor = textColor;
    if (indexModel.stockHq.changeValue > 0) {
        self.changeAmountLabel.text = [NSString stringWithFormat:@"+%.2f",indexModel.stockHq.changeValue];
    }else{
        self.changeAmountLabel.text = [NSString stringWithFormat:@"%.2f",indexModel.stockHq.changeValue];
    }
    
    self.changeAmountLabel.textColor = textColor;
    
    self.chartView.model = indexModel;
    self.layer.borderWidth = 0.5;
    if (indexModel.stockHq.changeValue > 0 ) {
        self.layer.borderColor = [UIColor up_colorFromHexString:@"#F55459"].CGColor;
        self.iconImage.image = UPTImg(@"行情/解盘icon_up");
        self.iconImage.hidden = NO;
    } else if (indexModel.stockHq.changeValue < 0 ) {
        self.iconImage.image = UPTImg(@"行情/解盘icon_down");
        self.iconImage.hidden = NO;
        self.layer.borderColor = [UIColor up_colorFromHexString:@"#1BA180"].CGColor;
    } else {
        self.iconImage.hidden = YES;
        self.layer.borderColor = [UIColor up_colorFromHexString:@"#D9D9D9"].CGColor;
    }
//    if (indexModel.stockHq.changeValue > 0 ) {
//        self.chartView.backgroundColor = [UIColor up_colorFromHexString:@"#0FF55459"];
//    } else if (indexModel.stockHq.changeValue < 0 ) {
//
//        self.chartView.backgroundColor = [UIColor up_colorFromHexString:@"#0F1BA180"];
//    } else {
//        self.chartView.backgroundColor = [UIColor up_colorFromHexString:@"#FFF9FAFB"];
//    }
    
    [self.percentLabel sizeToFit];
    [self.percentLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(UPWidth(self.percentLabel.up_width + 7)));
    }];
}

- (UIButton *)fullBtn{
    if (!_fullBtn) {
        _fullBtn = [UIButton new];
        [_fullBtn addTarget:self action:@selector(fullBtnClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _fullBtn;
}


- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [UIStackView new];
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.distribution = UIStackViewDistributionFill;
        _stackView.alignment = UIStackViewAlignmentLeading;
        _stackView.spacing = 2;
    }
    return _stackView;
}

@end
