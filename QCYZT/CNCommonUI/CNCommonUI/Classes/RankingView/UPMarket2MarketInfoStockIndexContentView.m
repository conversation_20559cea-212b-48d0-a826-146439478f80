//
//  UPMarket2MarketInfoStockIndexContentView.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2MarketInfoStockIndexContentView.h"
#import <CNCommonUI/UPMarket2IndexManagerView.h>
#import "UPMarket2MarketInfoStockIndexView.h"
#import "UPMarket2MarketInfoStockIndexPageControl.h"

#import "UPMarket2IndexManagerController.h"

#import "UPGestureScrollView.h"
#import "UPMarket2MarketInfoDefine.h"
#import <UPMarketUISDK/UPMarketUIDataTool.h>

@interface UPMarket2MarketInfoStockIndexContentView () <UIScrollViewDelegate> {
    MASConstraint *_messageViewBottomViewConstraint;
}

@property (nonatomic, strong) UPGestureScrollView *scrollView;
@property (nonatomic, strong) UPMarket2MarketInfoStockIndexPageControl *pageControl;
@property (nonatomic, strong) NSArray *indexViewArray;
@property (nonatomic, assign) CGFloat messageViewBottomOffset;
@property (nonatomic, strong) NSMutableDictionary *stockDic;

@property (nonatomic, strong) UPMarketMonitor *monitor;

@end

@implementation UPMarket2MarketInfoStockIndexContentView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.stockDic = [NSMutableDictionary dictionary];
        _messageViewBottomOffset = 20;
        
        self.backgroundColor = UIColor.up_contentBgColor;
        [self addSubview:self.scrollView];
//        [self addSubview:self.pageControl];
    }
    return self;
}

- (void)layoutSubviews {
    if (CGSizeEqualToSize(self.up_size, CGSizeZero)) {
        return;
    }
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(5);
        make.left.equalTo(self.mas_left).offset(10);
        make.bottom.right.equalTo(self);
    }];
//    [self.pageControl mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerX.equalTo(self);
//        make.top.equalTo(self.scrollView.mas_bottom).offset(7.5);
//    }];
    
    NSArray *dataArray = [self getIndexManagerModel];
    if (!dataArray) return;
    
    {
        /// 布局 scrollView
        NSUInteger count = dataArray.count;
        
        CGFloat padding = UPWidth(9);
        CGFloat edge = UPWidth(8);
        CGFloat ScreenWidth = self.up_width;
        CGFloat width = UPWidth(105);
        
        CGFloat height = UPHeight(105);
        
        for (int i = 0; i < count; i++) {
            CGFloat x;
            NSInteger pageCount = i / 3;
            NSInteger column = i % 3;
//            if (column > 0) {
//                x = ScreenWidth * pageCount + width * column + edge + padding * column;
//            } else {
                x = width * i + edge * (i+1);
//            }

            UPMarket2MarketInfoStockIndexView *indexView = [self.scrollView viewWithTag:i + kUPMarket2MarketInfoStockIndexViewBaseTag];
            
            indexView.frame = CGRectMake(x, 0, width, height);
        }
        
        if (UPMarket2MarketInfoStockIndexTypeAStock == _indexType
            || dataArray.count > 3) {
//            self.pageControl.hidden = NO;
            self.scrollView.scrollEnabled = YES;
            self.scrollView.contentSize = CGSizeMake(ScreenWidth * 2, height);
            self.messageViewBottomOffset = 20;
        } else {
            self.pageControl.hidden = YES;
            self.scrollView.scrollEnabled = NO;
            self.scrollView.contentSize = CGSizeMake(ScreenWidth, height);
            self.messageViewBottomOffset = 10;
        }
        
        UIButton *setBtn = [self.scrollView viewWithTag:100];
        
        if (setBtn) {
            CGFloat padding = 9;
            CGFloat edge = UPWidth(8);
            CGFloat ScreenWidth = self.up_width;
            CGFloat width = UPWidth(105);
            
            CGFloat btn_width = 109;
            CGFloat height = 105;
            // 添加设置按钮
            NSInteger count1 = count / 3;
            NSInteger column = count % 3;
            CGFloat btn_x;
//            if (count == 3) {
//                btn_x = ScreenWidth * count1 + width * column + edge;
//            } else {
//                btn_x = ScreenWidth * count1 + width * column + edge + padding * column;
//            }
            btn_x = width * count + edge * (count+1) + 15;
            
            setBtn.frame = CGRectMake(btn_x, 0, 50, height);
            
            CGSize imageSize = setBtn.imageView.frame.size;
            CGSize titleSize = setBtn.titleLabel.frame.size;
            CGFloat totalHeight = (imageSize.height + titleSize.height + 5);
            setBtn.imageEdgeInsets = UIEdgeInsetsMake(- (totalHeight - imageSize.height), 0.0, 0.0, - titleSize.width);
            setBtn.titleEdgeInsets = UIEdgeInsetsMake(0, - imageSize.width, - (totalHeight - titleSize.height), 0);
        }
    }
    
}

// MARK: - Public

- (void)viewDidAppear {
    [self updateIndexView];
}

- (void)viewDidDisappear {
    [self stopReloadData];
}


- (void)viewWillAppear{};
- (void)viewWillDisappear{};

// MARK: - Private

/// 更新界面数据
- (void)updateViewData:(NSArray *)dataArray {
    // 同步stockHq
    [dataArray enumerateObjectsUsingBlock:^(UPHqStockHq  * _Nonnull  obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *preKey = [NSString stringWithFormat:@"%@_%@",@(obj.setCode),obj.code];
        if (self.isUseChartStyle) {
            for (UPMarket2MarketInfoStockIndexChartView *indexView in self.indexViewArray) {
                UPMarket2MarketInfoStockIndexModel *model = indexView.indexModel;
                NSString *key = [NSString stringWithFormat:@"%@_%@",@(model.setCode),model.code];
                
                if ([preKey isEqualToString:key]) {
                    model.stockHq = obj;
                    indexView.indexModel = model;
                    break;
                }
            }
        }else{
            for (UPMarket2MarketInfoStockIndexView *indexView in self.indexViewArray) {
                UPMarket2MarketInfoStockIndexModel *model = indexView.indexModel;
                NSString *key = [NSString stringWithFormat:@"%@_%@",@(model.setCode),model.code];
                
                if ([preKey isEqualToString:key]) {
                    model.stockHq = obj;
                    indexView.indexModel = model;
                    break;
                }
            }
        }
        
    }];
}

/// 跳转指数管理
- (void)gotoIndexManager {
    UPMarket2IndexManagerController *vc = [[UPMarket2IndexManagerController alloc] init];
    vc.hidesNavigationBarWhenPush = NO;
    [UPRouter showViewController:vc];
}

- (NSArray *)getIndexManagerModel {
    if (UPMarket2MarketInfoStockIndexTypeAStock == _indexType) {
        return [UPMarket2IndexManagerManager getAStockIndexModels];
    } else if (UPMarket2MarketInfoStockIndexTypeHongKong == _indexType) {
        return [UPMarket2IndexManagerManager getHongKongIndexModels];
    } else if (UPMarket2MarketInfoStockIndexTypeStockTransfer == _indexType) {
        return [UPMarket2IndexManagerManager getStockTransferIndexModels];
    } else {
        return nil;
    }
}

// MARK: - UIScrollViewDelegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat UPScreenWidth = scrollView.up_width;
    NSInteger index = (scrollView.contentOffset.x + 0.5 * UPScreenWidth) / UPScreenWidth;
    self.pageControl.currentPage = index;
}

// MARK: - Getter & Setter

- (UPMarket2MarketInfoStockIndexPageControl *)pageControl {
    if (_pageControl == nil) {
        _pageControl = [UPMarket2MarketInfoStockIndexPageControl pageControl];
        _pageControl.numberOfPages = 2;
    }
    return _pageControl;
}

- (UPGestureScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UPGestureScrollView alloc] init];
        _scrollView.pagingEnabled = YES;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.delegate = self;
        _scrollView.bounces = NO;
    }
    return _scrollView;
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [[UPMarketMonitor alloc] init];
    }
    return _monitor;
}

- (void)setMessageViewBottomOffset:(CGFloat)messageViewBottomOffset {
    if (_messageViewBottomOffset == messageViewBottomOffset) {
        return;
    }
    
    _messageViewBottomOffset = messageViewBottomOffset;
    
    [self.scrollView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(10);
        make.left.right.equalTo(self);
        make.height.equalTo(@80).priorityLow();
        _messageViewBottomViewConstraint = make.bottom.equalTo(self).offset(-_messageViewBottomOffset);
    }];
}

- (void)setIndexType:(UPMarket2MarketInfoStockIndexType)indexType {
    _indexType = indexType;
    
    NSArray *dataArray = [self getIndexManagerModel];
    if (!dataArray) return;
    
    [self createIndexView:dataArray.count];
    
    if (UPMarket2MarketInfoStockIndexTypeAStock == _indexType) { // 有设置按钮
        [self addSetBtn];
    }
    
    [self setNeedsLayout];
}

// MARK: - Private
// 创建子视图
- (void)createIndexView:(NSInteger)count {
    /// 移除原有的
    for (UIView *subView in self.scrollView.subviews) {
        [subView removeFromSuperview];
    }
        
    NSMutableArray *arrM = [NSMutableArray array];

    if (self.isUseChartStyle) {
        for (int i = 0; i < count; i++) {
            UPMarket2MarketInfoStockIndexChartView *indexView = [[UPMarket2MarketInfoStockIndexChartView alloc] init];
            indexView.tag = i + kUPMarket2MarketInfoStockIndexViewBaseTag;
            [arrM addObject:indexView];
            [self.scrollView addSubview:indexView];
        }
        self.indexViewArray = arrM.copy;
    }else{
        for (int i = 0; i < count; i++) {
            UPMarket2MarketInfoStockIndexView *indexView = [[UPMarket2MarketInfoStockIndexView alloc] init];
            indexView.tag = i + kUPMarket2MarketInfoStockIndexViewBaseTag;
            [arrM addObject:indexView];
            [self.scrollView addSubview:indexView];
        }
        self.indexViewArray = arrM.copy;
    }
    
    [self updateViewIndexModel];
}

// 检查视图indexmodel，如果没有创建新的
- (UPMarket2MarketInfoStockIndexModel *)checkIndexModel:(UPMarket2MarketInfoStockIndexModel *)oldIndexModel {
    UPMarket2MarketInfoStockIndexModel *newIndexModel = oldIndexModel;
    if (!newIndexModel) {
        newIndexModel = [UPMarket2MarketInfoStockIndexModel new];
        newIndexModel.centerData = @"--";
        newIndexModel.bottomLeftData = @"--";
        newIndexModel.bottomRightData = @"--";
    }

    return newIndexModel;
}

// 更新视图indexmodel
- (void)updateViewIndexModel {
    NSArray *dataArray = [self getIndexManagerModel];
    NSMutableArray *codeArray = [dataArray valueForKeyPath:@"code"];
    NSMutableArray *setCodeArray = [dataArray valueForKeyPath:@"setCode"];
    NSInteger count = MIN(self.indexViewArray.count, dataArray.count);
    
    if (self.isUseChartStyle) {
        for (NSInteger i = 0; i < count; i++) {
            UPMarket2MarketInfoStockIndexChartView *indexView = self.indexViewArray[i];
            UPMarket2MarketInfoStockIndexModel *indexModel = [self checkIndexModel:indexView.indexModel];
            UPMarket2IndexManagerModel *indexManagerModel = dataArray[i];
            
            indexModel.codeArray = codeArray;
            indexModel.setCodeArray = setCodeArray;
            indexModel.setCode = indexManagerModel.setCode;
            indexModel.code = indexManagerModel.code;
            indexModel.topHeaderName = indexManagerModel.name;

            indexView.indexModel = indexModel;
        }
    }else{
        for (NSInteger i = 0; i < count; i++) {
            UPMarket2MarketInfoStockIndexView *indexView = self.indexViewArray[i];
            UPMarket2MarketInfoStockIndexModel *indexModel = [self checkIndexModel:indexView.indexModel];
            UPMarket2IndexManagerModel *indexManagerModel = dataArray[i];
            
            indexModel.codeArray = codeArray;
            indexModel.setCodeArray = setCodeArray;
            indexModel.setCode = indexManagerModel.setCode;
            indexModel.code = indexManagerModel.code;
            indexModel.topHeaderName = indexManagerModel.name;

            indexView.indexModel = indexModel;
        }
    }
    
}

/// 更新指数视图（指数个数或位置修改时）
- (void)updateIndexView {
    NSArray *dataArray = [self getIndexManagerModel];
    if (!dataArray) return;
        
    if (self.indexViewArray.count == dataArray.count) {
        [self updateViewIndexModel];
        
        [self reloadData];
        return;
    }

    [self createIndexView:dataArray.count];
    
    if (UPMarket2MarketInfoStockIndexTypeAStock == _indexType) { // 有设置按钮
        [self addSetBtn];
    }
    
    [self setNeedsLayout];
    
    [self reloadData];
}

- (void)addSetBtn {
    UIButton *setBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    setBtn.tag = 100;
    
    [setBtn setTitle:@"设置" forState:UIControlStateNormal];
    [setBtn setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateNormal];
    setBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    [setBtn setImage:UPTImg(@"行情/大盘指数-设置") forState:UIControlStateNormal];
    [setBtn addTarget:self action:@selector(gotoIndexManager) forControlEvents:UIControlEventTouchUpInside];
    
    [self.scrollView addSubview:setBtn];
}
/// 刷新数据
- (void)reloadData {
    if (self.isUseChartStyle) {
        [self reloadChartStyleData];
        return;
    }
    NSArray *dataArray = [self getIndexManagerModel];
    if (!dataArray) return;
    // 无网络
    if (!UPTAFNetworkReachable) {
        if (self.indexDelegate) {
            [self.indexDelegate reloadEnd];
        }
        return;
    }
    
    NSMutableArray *codeArray = [dataArray valueForKeyPath:@"code"];
    NSMutableArray *setCodeArray = [dataArray valueForKeyPath:@"setCode"];
    
    __block NSMutableArray *stocksM = [NSMutableArray array];
    [codeArray enumerateObjectsUsingBlock:^(NSString *obj, NSUInteger idx, BOOL *_Nonnull stop) {
        UPHqStockUnique *stock = [UPHqStockUnique new];
        stock.setCode = [setCodeArray[idx] intValue];
        stock.code = obj;
        [stocksM addObject:stock];
    }];
    
    UPMarketStockHqReq *hqReq = [[UPMarketStockHqReq alloc] initWithStockArray:stocksM.copy];
    hqReq.simpleData = YES;
    WeakSelf(weakSelf);
    [self.monitor stopMonitorWithTag:UPMarket2MarketInfoHeaderIndexTag];
    [self.monitor startMonitorStockHq:hqReq tag:UPMarket2MarketInfoHeaderIndexTag completionHandler:^(UPMarketStockHqRsp *rsp, NSError *error) {
        if (rsp.dataArray.count) {
            [weakSelf updateViewData:rsp.dataArray];
        }
        if (weakSelf.indexDelegate) {
            [weakSelf.indexDelegate reloadEnd];
        }
    }];
}

- (void)reloadChartStyleData{
    NSArray *dataArray = [self getIndexManagerModel];
    if (!dataArray) return;
    // 无网络
    if (!UPTAFNetworkReachable) {
        if (self.indexDelegate) {
            [self.indexDelegate reloadEnd];
        }
        return;
    }
    UPMarketOptStockHqReq *req = [[UPMarketOptStockHqReq alloc] init];
    NSMutableArray *codeArray = [dataArray valueForKeyPath:@"code"];
    NSMutableArray *setCodeArray = [dataArray valueForKeyPath:@"setCode"];
    
    __block NSMutableArray *stocksM = [NSMutableArray array];
    [codeArray enumerateObjectsUsingBlock:^(NSString *obj, NSUInteger idx, BOOL *_Nonnull stop) {
        UPHqStockUnique *stock = [UPHqStockUnique new];
        stock.setCode = [setCodeArray[idx] intValue];
        stock.code = obj;
        [stocksM addObject:stock];
        [req add:stock.setCode code:stock.code];
    }];
    
    
    
    req.rtMinInOpt = YES;
    req.rtMinDataType = 1;
    WeakSelf(weakSelf);
    [self.monitor stopMonitorWithTag:UPMarket2MarketInfoHeaderIndexTag];
    [self.monitor startMonitorOptStockHq:req tag:UPMarket2MarketInfoHeaderIndexTag completionHandler:^(UPMarketOptStockHqRsp *rsp, NSError *error) {
        if (rsp.dataArray.count) {
            [weakSelf updateViewData:rsp.dataArray];
        }
        if (weakSelf.indexDelegate) {
            [weakSelf.indexDelegate reloadEnd];
        }
    }];
}

- (void)stopReloadData {
    [self.monitor stopMonitorWithTag:UPMarket2MarketInfoHeaderIndexTag];
}

@end

@interface UPMarket2MarketInfoStockNowTimeContentView ()

@property (nonatomic, strong) UIImageView *iconImage;
@property (nonatomic, strong) UILabel *strongLabel;
@property (nonatomic, strong) UPMarketMonitor *monitor;


@end

@implementation UPMarket2MarketInfoStockNowTimeContentView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;
        [self addSubUI];
    }
    return self;
}

- (void)addSubUI {
    [self addSubview:self.namelabel];
    [self addSubview:self.detaiLabel];
    [self addSubview:self.strongLabel];
    [self addSubview:self.iconImage];
    
    [self.namelabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self.mas_left).offset(UPWidth(15));
    }];
    
    [self.detaiLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self.namelabel.mas_right).offset(UPWidth(8));
    }];
    
    [self.iconImage mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.mas_right).offset(UPWidth(-15));
    }];
    
    [self.strongLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.iconImage.mas_left).offset(UPWidth(-5));
    }];
}


#pragma mark getter && setter

- (UIImageView *)iconImage {
    if (!_iconImage) {
        _iconImage = [[UIImageView alloc] initWithImage:UPTImg(@"个股/查看更多")];
        
    }
    return _iconImage;
}

- (UILabel *)namelabel{
    if (!_namelabel) {
        _namelabel = [UILabel new];
        _namelabel.font = [UIFont up_fontOfSize:UPWidth(18) weight:UIFontWeightSemibold];
        _namelabel.textColor = UIColor.up_textPrimaryColor;
        _namelabel.text = @"-";
        _namelabel.textAlignment = NSTextAlignmentLeft;
    }
    return _namelabel;
}


- (UILabel *)detaiLabel{
    if (!_detaiLabel) {
        _detaiLabel = [UILabel new];
        _detaiLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _detaiLabel.textColor = [UIColor up_colorFromHexString:@"#9A9EAD"];
        _detaiLabel.text = @"-";
        _detaiLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _detaiLabel;
}


- (UILabel *)strongLabel{
    if (!_strongLabel) {
        _strongLabel = [UILabel new];
        _strongLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
        _strongLabel.textColor = [UIColor up_colorFromHexString:@"#787D87"];
        _strongLabel.text = @"-";

        _strongLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _strongLabel;
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [[UPMarketMonitor alloc] init];
    }
    return _monitor;
}


-(void)reoloaViewData:(UPHqStockHq *)hq {
    NSMutableString *mstr = [[NSMutableString alloc] initWithString:[NSString stringWithFormat:@"%d",hq.tradeDate]];
    if (mstr.length > 4) {
        [mstr insertString:@"-" atIndex:4];
    }
    if (mstr.length > 7) {
        [mstr insertString:@"-" atIndex:7];
    }
    self.detaiLabel.text = [mstr copy];
    self.strongLabel.text = @"市场强度";
//    if (hq.tradeStatus == UPMarketTradeStatusNotOpen) {
//        self.namelabel.text = @"未开盘";
//    } else if (hq.tradeStatus == UPMarketTradeStatusClosed) {
//        self.namelabel.text = @"已收盘";
//    } else {
//        self.namelabel.text = @"开盘中";
//    }
    self.namelabel.text = [UPMarketUIDataTool getStateWithTradeStatus:hq.tradeStatus];
    if (hq.tradeStatus == UPMarketTradeStatusTrading) {
        self.namelabel.text = @"开盘中";
    }
}
// MARK: - Public

- (void)viewDidAppear {
    [self reloadData];
}

- (void)viewDidDisappear {
    [self stopReloadData];
}

- (void)stopReloadData {
    [self.monitor stopMonitorWithTag:88];
}
/// 刷新数据
- (void)reloadData {
    if (!UPTAFNetworkReachable) {
        return;
    }

    
    UPMarketStockHqReq *hqReq = [[UPMarketStockHqReq alloc] initWithSetCode:1 code:@"000001"];
    hqReq.simpleData = YES;
    WeakSelf(weakSelf);
    [self.monitor stopMonitorWithTag:88];
    [self.monitor startMonitorStockHq:hqReq tag:88 completionHandler:^(UPMarketStockHqRsp *rsp, NSError *error) {
        if (rsp.dataArray.count) {
            [weakSelf reoloaViewData:rsp.dataArray.firstObject];
        } else {
            [weakSelf stopReloadData];
        }
        
    }];
}

- (void)viewWillAppear{};
- (void)viewWillDisappear{};
@end
