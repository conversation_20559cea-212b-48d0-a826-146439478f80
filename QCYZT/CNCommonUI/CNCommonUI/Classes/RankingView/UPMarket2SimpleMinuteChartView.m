//
//  UPMarket2SimpleMinuteChartView.m
//  UPMarket2
//
//  Created by kds on 2022/9/5.
//

#import "UPMarket2SimpleMinuteChartView.h"
#import "UPMarket2SimpleMinuteChartLayer.h"
#import <UPMarketUISDK/UPMarketUITransformTool.h>
#import <UPMarketUISDK/UPMarketUICompareTool.h>

@interface UPMarket2SimpleMinuteChartView()

@property (nonatomic, strong) NSArray<NSArray<NSNumber *>*> *tradeTimeArray;
@property (nonatomic, assign) NSInteger totalTradeMinutes;

@property (nonatomic, strong) UPMarket2SimpleMinuteChartLayer *minuteLayer;  // 分时层

@end

@implementation UPMarket2SimpleMinuteChartView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.maxValue = -CGFLOAT_MAX;
        self.minValue = CGFLOAT_MAX;
        self.backgroundColor = UIColor.clearColor;
        
        [self.layer addSublayer:self.minuteLayer];
    }
    return self;
}

- (void)layoutSubviews {
    CGFloat height = 40;
    self.drawingRect = CGRectMake(0, (self.frame.size.height - height)/2, self.frame.size.width, height + (self.frame.size.height - height)/2);
    [self.minuteLayer updateLayout:self.drawingRect];
    [self.minuteLayer drawMinuteChart:self];
}

- (void)initTradeTime:(NSUInteger)setCode code:(NSString *)code origCategory:(NSUInteger)origCategory origSubCategory:(NSUInteger)origSubCategory{
    
    self.tradeTimeArray = [UPMarketManager getMarketTradePeriodWithSetCode:setCode code:code origCategory:origCategory origSubCategory:origSubCategory];
    
    self.totalTradeMinutes = [UPMarketUITransformTool getTotolTradeMinutesFromTradeArray:self.tradeTimeArray containAfter:1] + 1;
}

- (void)calculateMaxMin {
    self.maxValue = -CGFLOAT_MAX;
    self.minValue = CGFLOAT_MAX;
    
    NSArray *dataList = self.model.stockHq.minuteData.minuteArray;
    
    double closeValue = self.model.stockHq.yClosePrice;
    
    self.maxValue = MAX(self.maxValue, [[dataList valueForKeyPath:@"@max.nowPrice"] doubleValue]);
    self.minValue = MIN(self.minValue, [[dataList valueForKeyPath:@"@min.nowPrice"] doubleValue]);
    
    if (self.maxValue == -CGFLOAT_MAX || self.minValue == CGFLOAT_MAX) {
        self.maxValue = closeValue;
        self.minValue = closeValue;
    }
    
    self.maxValue = MAX(self.maxValue, closeValue);
    self.minValue = MIN(self.minValue, closeValue);
    
    if ([UPMarketUICompareTool compareWithDoubleValue:self.maxValue andAnotherDoubleValue:self.minValue precise:self.model.stockHq.precise] == NSOrderedSame) {
        double maxOffset = MAX(ABS(self.maxValue - closeValue), ABS(closeValue - self.minValue)) + 0.05f;
        self.maxValue = closeValue + maxOffset;
        self.minValue = closeValue - maxOffset;
    }
    
}

// MARK: - Getter & Setter
- (UPMarket2SimpleMinuteChartLayer *)minuteLayer {
    if (!_minuteLayer) {
        _minuteLayer = [UPMarket2SimpleMinuteChartLayer new];
    }
    return _minuteLayer;
}
- (CGFloat)itemWidth {
    
    CGFloat itemWidth = 0.f;
    
    if (CGRectGetWidth(self.drawingRect) && self.totalTradeMinutes) {
        NSInteger totalMinutes = self.totalTradeMinutes;
        
        itemWidth = CGRectGetWidth(self.drawingRect) / (CGFloat)(totalMinutes - 1);
    }
    
    return itemWidth;
    
}

- (CGFloat)unitHeight {
    CGFloat valueSum = self.maxValue - self.minValue;

    return valueSum != 0 ? CGRectGetHeight(self.drawingRect) / valueSum : 0;
}

- (void)setModel:(UPMarket2MarketInfoStockIndexModel *)model {
    if (!(model.setCode == _model.stockHq.setCode
        && [model.code isEqualToString:_model.code])) {
        self.maxValue = -CGFLOAT_MAX;
        self.minValue = CGFLOAT_MAX;
    }
    if(!_model || _model.stockHq.setCode != model.stockHq.setCode
       || _model.stockHq.origCategory != model.stockHq.origCategory
       || _model.stockHq.origSubCategory != model.stockHq.origSubCategory) {
        [self initTradeTime:model.stockHq.setCode code:model.code origCategory:model.stockHq.origCategory origSubCategory:model.stockHq.origSubCategory];
    }
    _model = model;
    
    [self calculateMaxMin];
    [self.minuteLayer removeFromSuperlayer];
    self.minuteLayer = nil;
    
    [self.layer addSublayer:self.minuteLayer];
//    self.minuteLayer.yClosePriceLineLayer.hiden = YES;
    [self.minuteLayer updateLayout:self.drawingRect];
    [self.minuteLayer drawMinuteChart:self];
}

@end
