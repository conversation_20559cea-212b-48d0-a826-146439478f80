//
//  UPMarketGrailZdfbView.m
//  UPMarket
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/4/3.
//  Copyright © 2019 UpChina. All rights reserved.
//

#import "UPMarketGrailZdfbView.h"
#import "UPMarketBasicZdfbView.h"
#import <UPMarketUISDK/UPMarketUIDrawTool.h>
#import <UPMarketUISDK/UPMarketUITextLayer.h>
#import "UPMarket2StockUIUtil.h"
#import "UPMarketGrailZdfbRightContentView.h"
#import <UPMarketUISDK/UPMarketUICalculateUtil.h>

#define UPMarket2MarketInfoZDStatisticsStaticTag 62
const static CGFloat drawContentHeight = 90;

@interface UPMarketGrailZdfbView ()

@property (nonatomic, assign) NSInteger maxNumber;
@property (nonatomic, assign) NSInteger upSum;
@property (nonatomic, assign) NSInteger downSum;

@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UPMarketBasicZdfbView *zdfbBasicView;
@property (nonatomic, strong) UPMarketGrailZdfbRightContentView *rightContentView;
@property (nonatomic, strong) UILabel *todaynumLabel;
@property (nonatomic, strong) UILabel *yesterdatnumLabel;


@end

@implementation UPMarketGrailZdfbView


- (void)setupViews {
    [super setupViews];
    self.backgroundColor = UIColor.up_contentBgColor;
    
    [self addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(15);
        make.right.equalTo(self).offset(-12);
        make.bottom.equalTo(self).offset(-12);
        make.top.equalTo(self).offset(15);
    }];
    
    [self.stackView addArrangedSubview:self.zdfbBasicView];
    [self.zdfbBasicView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.stackView);
        make.top.equalTo(self.stackView.mas_top);
//        make.height.mas_equalTo(@(UPWidth(100)));
    }];
    
    UIView *vv = [[UIView alloc] init];
    
    [self.stackView addArrangedSubview:vv];
    [vv mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(@(UPWidth(20)));
        make.left.right.equalTo(self.stackView);
    }];
    
    self.todaynumLabel = [[UILabel alloc] init];
    self.todaynumLabel.textAlignment = NSTextAlignmentLeft;
//    self.todaynumLabel.text = @"今日实时成交金额";
    [vv addSubview:self.todaynumLabel];
    self.yesterdatnumLabel = [[UILabel alloc] init];
//    self.yesterdatnumLabel.text = @"今日实时成交金额";
    self.yesterdatnumLabel.textAlignment = NSTextAlignmentRight;
    [vv addSubview:self.yesterdatnumLabel];
    [self.todaynumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(vv);
    }];
    [self.yesterdatnumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.top.bottom.equalTo(vv);
    }];
    
    
    [self.stackView addArrangedSubview:self.rightContentView];
    [self.rightContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(@(UPWidth(40)));
        make.left.right.equalTo(self.stackView);
    }];
}




- (void)setConstraints {
    [super setConstraints];
}


- (void)drawRect:(CGRect)rect {
    [super drawRect:rect];
}

- (void)setZdlNum:(UPMarketFuPanFZDNumTrend *)zdlNum {
    _zdlNum = zdlNum;
    
    self.rightContentView.zdlNum = zdlNum;
    
}


- (void)setLnData:(UPMarketTrendLN *)lnData {
    _lnData = lnData;
    
    NSString *tpdaystr = [NSString stringWithFormat:@"今日实时成交额 %@",[UPMarketUICalculateUtil transStringWithUnitOfValue:@(lnData.dRealSHSZAmout) afterPoint:0]];
    NSString *yesterdaySttr = [NSString stringWithFormat:@"较上一日此时 %@",[UPMarketUICalculateUtil transStringWithUnitOfValue:@(lnData.dRealSHSZAmout - lnData.dPreSHSZAmout) afterPoint:0]];
    
    self.todaynumLabel.attributedText = [self getattributedStr:tpdaystr withSelStr:[UPMarketUICalculateUtil transStringWithUnitOfValue:@(lnData.dRealSHSZAmout) afterPoint:0] withColor:[UIColor up_textPrimaryColor]];
    self.yesterdatnumLabel.attributedText = [self getattributedStr:yesterdaySttr withSelStr:[UPMarketUICalculateUtil transStringWithUnitOfValue:@(lnData.dRealSHSZAmout - lnData.dPreSHSZAmout) afterPoint:0] withColor: [UPMarket2StockUIUtil getRiseFallTextColor:lnData.dRealSHSZAmout - lnData.dPreSHSZAmout baseValue:0]];
}


- (NSAttributedString *)getattributedStr:(NSString *)str withSelStr:(NSString *)slestr withColor:(UIColor *)color{

    NSRange unChangePartRange = [str rangeOfString:slestr];
    
    NSMutableAttributedString *string2 = [[NSMutableAttributedString alloc] initWithString:str attributes: @{NSForegroundColorAttributeName: [UIColor up_colorFromHexString:@"#657180"],NSFontAttributeName: [UIFont fontWithName:@"PingFangTC-Regular" size: 11]}];
    
    [string2 addAttributes:@{NSFontAttributeName: [UIFont fontWithName:@"PingFangTC-Medium" size: 11], NSForegroundColorAttributeName: color} range:unChangePartRange];
    
    return string2;
}



- (void)setUpDownArray:(NSArray<UPMarketUpDownTrendDetail *> *)upDownArray {
    _upDownArray = upDownArray;
    
    self.zdfbBasicView.upDownArray = upDownArray;
}

- (void)setHiddenRightInfoView:(BOOL)hiddenRightInfoView
{
    _hiddenRightInfoView = hiddenRightInfoView;
    
    self.rightContentView.hidden = hiddenRightInfoView;
}


- (UPMarketBasicZdfbView *)zdfbBasicView {
    if (!_zdfbBasicView) {
        _zdfbBasicView = [[UPMarketBasicZdfbView alloc] init];
    }
    return _zdfbBasicView;
}


- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [UIStackView new];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.distribution = UIStackViewDistributionFill;
        _stackView.alignment = UIStackViewAlignmentCenter;
        _stackView.spacing = 0;
    }
    return _stackView;
}


- (UPMarketGrailZdfbRightContentView *)rightContentView {
    if (!_rightContentView) {
        _rightContentView = [[UPMarketGrailZdfbRightContentView alloc] init];
        _rightContentView.backgroundColor = UIColor.up_contentBgColor;
    }
    return _rightContentView;
}

@end
