//
//  UPMarketBasicZdfbView.m
//  UPMarket2
//
//  Created by 彭继宗 on 2023/5/6.
//

#import "UPMarketBasicZdfbView.h"
#import <UPMarketUISDK/UPMarketUIDrawTool.h>
#import <UPMarketUISDK/UPMarketUITextLayer.h>
#import <CNCommonUI/UPMarket2MarketInfoDefine.h>

const static CGFloat drawContentHeightRatio = 0.45;//柱状图+文字区域比例

static NSString * const kTitleKey = @"kTitleKey";

static NSString * const kTagKey = @"kTagKey";

@interface UPMarketBasicZdfbView ()

@property (nonatomic, assign) NSInteger maxNumber;
@property (nonatomic, assign) NSInteger upSum;
@property (nonatomic, assign) NSInteger downSum;

@property (nonatomic, strong) NSMutableArray *riseShapeLayers;
@property (nonatomic, strong) NSMutableArray *riseTextLayers;
@property (nonatomic, strong) NSMutableArray *fallShapeLayers;
@property (nonatomic, strong) NSMutableArray *fallTextLayers;

@property (nonatomic, strong) UIProgressView *progressView;
@property (nonatomic, strong) UIImageView *proImg;
@property (nonatomic, strong) UILabel *szLableText;
@property (nonatomic, strong) UILabel *xdLableText;

@property (nonatomic, strong) NSArray<NSDictionary*> * titleDictDataArray;

@property (nonatomic, strong) CAShapeLayer *z_layer;   // 涨层
@property (nonatomic, strong) CAShapeLayer *pp_layer;  // 平层
@property (nonatomic, strong) CAShapeLayer *d_layer;   // 跌层

@end

@implementation UPMarketBasicZdfbView


- (void)setupViews {
    [super setupViews];
    self.backgroundColor = UIColor.up_contentBgColor;
    
    
    [self.layer addSublayer:self.z_layer];
    [self.layer addSublayer:self.pp_layer];
    [self.layer addSublayer:self.d_layer];
    
//    [self addSubview:self.progressView];
//    [self.progressView addSubview:self.proImg];
//    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.equalTo(self).offset(8);
//        make.right.equalTo(self).offset(-8);
//        make.height.equalTo(@(UPHeight(5)));
//        make.bottom.equalTo(self.mas_bottom).offset(-15);
//    }];
//    [self.proImg mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerX.equalTo(self.progressView.mas_left);
//        make.centerY.top.bottom.equalTo(self.progressView);
//        make.width.mas_equalTo(@(UPWidth(4)));
//    }];
    self.szLableText.text = @"上涨--家";
    self.xdLableText.text = @"下跌--家";
    [self addSubview:self.szLableText];
    [self.szLableText mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(-8);
        make.bottom.equalTo(self.mas_bottom).offset(-2);
    }];
    
    
    [self addSubview:self.xdLableText];
    [self.xdLableText mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(8);
        make.centerY.equalTo(self.szLableText);
    }];
    
}


- (void)setConstraints {
    [super setConstraints];
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
//    [self.progressView mas_updateConstraints:^(MASConstraintMaker *make) {
//        make.top.equalTo(self).offset(self.up_height*(drawContentHeightRatio + 0.16));
//    }];
    [self setNeedsDisplay];
}

- (void)drawRect:(CGRect)rect {
    [super drawRect:rect];
    [self drawView];
}

- (void)drawView {
    
    //每次重绘移除layer
    if (IsValidateArray(self.riseShapeLayers)) {
        [self.riseShapeLayers makeObjectsPerformSelector:@selector(removeFromSuperlayer)];
        [self.riseShapeLayers removeAllObjects];
    }
    if (IsValidateArray(self.riseTextLayers)) {
        [self.riseTextLayers makeObjectsPerformSelector:@selector(removeFromSuperlayer)];
        [self.riseTextLayers removeAllObjects];
    }
    if (IsValidateArray(self.fallShapeLayers)) {
        [self.fallShapeLayers makeObjectsPerformSelector:@selector(removeFromSuperlayer)];
        [self.fallShapeLayers removeAllObjects];
    }
    if (IsValidateArray(self.fallTextLayers)) {
        [self.fallTextLayers makeObjectsPerformSelector:@selector(removeFromSuperlayer)];
        [self.fallTextLayers removeAllObjects];
    }
    
    
    CGFloat startY = self.up_height * drawContentHeightRatio;
    CGFloat leftRightPadding = 10;
    CGFloat lineWidth = UPWidth(14);
    CGFloat margin = ((self.up_width) - 9 * lineWidth - leftRightPadding * 2) / 8;
    CGFloat lineStartX = leftRightPadding;
    
    NSInteger level = self.maxNumber / 500.5 + 1;
    CGFloat unit = 100 / (level *1500.0);
    
    NSInteger totalSum = self.upSum + self.downSum;
    double upApart;
    if (totalSum > 0) {
        upApart = self.upSum / (double) totalSum;
    } else {
        upApart = 0;
    }
    CGFloat lineLength = lineWidth * 10 + margin * 9;
    
    //画横线
    //起点X
    CGFloat x = lineStartX + lineWidth + margin;
    CGFloat y = startY ;
    
    CGPoint riseOne;
    CGPoint riseTwo;
    CGPoint riseThree;
    CGPoint riseFour;
    CGPoint fallOne;
    CGPoint fallTwo;
    CGPoint fallThree;
    CGPoint fallFour;
    if (upApart == 0) {
        riseOne = CGPointMake(x + 3, y);
        riseTwo = CGPointMake(x + 0.5 * lineLength + 3, y);
        riseThree = CGPointMake(x + 0.5 * lineLength, y - 5);
        riseFour = CGPointMake(x + 3, y - 5);
        
        fallOne = CGPointMake(x + 0.5 * lineLength + 1 + 3, y);
        fallTwo = CGPointMake(x + lineLength + 3, y);
        fallThree = CGPointMake(x + lineLength + 3, y - 5);
        fallFour = CGPointMake(x + 0.5 * lineLength - 2 + 3, y - 5);
    } else {
        riseOne = CGPointMake(x + 3, y);
        riseTwo = CGPointMake(x + upApart * lineLength + 3, y);
        riseThree = CGPointMake(x + upApart * lineLength - 3 + 3, y - 5);
        riseFour = CGPointMake(x + 3, y - 5);
        
        fallOne = CGPointMake(x + upApart * lineLength + 1 + 3, y);
        fallTwo = CGPointMake(x + upApart * lineLength  + (lineLength - upApart * lineLength) + 3, y);
        fallThree = CGPointMake(x + upApart * lineLength  + (lineLength - upApart * lineLength) + 3, y - 5);
        fallFour = CGPointMake(x + upApart * lineLength - 2 + 3, y - 5);
    }

    
    CGFloat hisStartX = lineStartX+3;
    
    
    for (int i = 0; i < 5; i++) {
        int stockNumber = 0;
        NSString *titleString = @"";
        switch (i) {
//            case 0:
//                stockNumber = self.upDownArray.firstObject.dtNum;
//                titleString = @"跌停";
//                break;
            case 0:
                stockNumber = self.upDownArray.firstObject.dPer7Num + self.upDownArray.firstObject.dPer8Num + self.upDownArray.firstObject.dPer9Num + self.upDownArray.firstObject.dtNum;
                titleString = @"≥7%";
                break;
            case 1:
                
                stockNumber = self.upDownArray.firstObject.dPer5Num + self.upDownArray.firstObject.dPer6Num;
                titleString = @"7-5%";
                break;
            case 2:
                stockNumber = self.upDownArray.firstObject.dPer3Num + self.upDownArray.firstObject.dPer4Num;
                titleString = @"5-3%";
                
                break;
            case 3:
                stockNumber = self.upDownArray.firstObject.dPer0Num + self.upDownArray.firstObject.dPer1Num + self.upDownArray.firstObject.dPer2Num;
                titleString = @"3-0%";
                
                break;
            case 4:
                stockNumber = self.upDownArray.firstObject.ppNum;
                titleString = @"0%";
                
                break;
            default:
                break;
        }
        
        CGPoint hisStartPoint = CGPointMake(hisStartX, startY - stockNumber * unit);
        
        UIBezierPath *path = [UIBezierPath bezierPath];
        [path moveToPoint:CGPointMake(hisStartPoint.x + lineWidth * 0.5, startY)];
        [path addLineToPoint:CGPointMake(hisStartPoint.x + lineWidth * 0.5, startY - (stockNumber * unit))];
        // 柱形图layer
        CAShapeLayer *cLayer = [CAShapeLayer layer];
        if ([titleString isEqualToString:@"0%"]) {
            cLayer.strokeColor = [UIColor up_colorFromHexString:@"#C3CBD6"].CGColor;
            cLayer.fillColor = [UIColor up_colorFromHexString:@"#C3CBD6"].CGColor;
        } else {
            cLayer.strokeColor = [UIColor up_colorFromHexString:@"#01AF70"].CGColor;
            cLayer.fillColor = [UIColor up_colorFromHexString:@"#01AF70"].CGColor;

        }
        cLayer.lineWidth = lineWidth;
        cLayer.lineCap = kCALineCapButt;
        cLayer.path = path.CGPath;
        cLayer.strokeEnd = 1.0;
        [self.layer addSublayer:cLayer];
        [self.fallShapeLayers addObject:cLayer];
        
        // 文字layer
        UPMarketUITextLayer *layer = [UPMarketUITextLayer layer];
        NSString *text = [NSString stringWithFormat:@"%d", stockNumber];
        CGRect rect = [UPMarketUIDrawTool rectOfDigitalNSString:text fontSize:12.f];
        layer.bounds = CGRectMake(0, 0, rect.size.width, rect.size.height);
        layer.alignmentMode = kCAAlignmentCenter;
        if (stockNumber == 0 || [titleString isEqualToString:@"0%"]) {
            [layer drawTextWithTextString:text fontSize:12.f color:[UIColor up_textSecondary1Color] point:CGPointMake(hisStartPoint.x + lineWidth * 0.5, startY - (stockNumber * unit) - 8)];
        } else {
            [layer drawTextWithTextString:text fontSize:12.f color:UIColor.up_fallColor point:CGPointMake(hisStartPoint.x + lineWidth * 0.5, startY - (stockNumber * unit) - 8)];
        }
       

        [self.layer addSublayer:layer];
        [self.fallTextLayers addObject:layer];
        
        if (titleString.length) {
            if (![titleString isEqualToString:@"0%"]) {
                [UPMarketUIDrawTool drawDigitalWithTextString:titleString
                                                     fontSize:12
                                                        color:[UIColor up_colorFromHexString:@"#1BA180"]
                                                  centerPoint:CGPointMake(hisStartPoint.x + lineWidth / 2, startY + 10)];

            } else {
                [UPMarketUIDrawTool drawDigitalWithTextString:titleString
                                                     fontSize:12
                                                        color:[UIColor up_colorFromHexString:@"#787D87"]
                                                  centerPoint:CGPointMake(hisStartPoint.x + lineWidth / 2, startY + 10)];
                }
        }
        hisStartX += (margin + lineWidth);
    }
    
    //柱状图
    for (int i = 0; i < 4; i++) {
        int stockNumber = 0;
        NSString *titleString = @"";
        switch (i) {
            case 0:
                stockNumber = self.upDownArray.firstObject.zPer0Num + self.upDownArray.firstObject.zPer1Num + self.upDownArray.firstObject.zPer2Num;
                titleString = @"0-3%";
                
                break;
            case 1:
                stockNumber = self.upDownArray.firstObject.zPer3Num + self.upDownArray.firstObject.zPer4Num;
                titleString = @"3-5%";
                
                break;
            case 2:
                stockNumber = self.upDownArray.firstObject.zPer5Num + self.upDownArray.firstObject.zPer6Num;
                titleString = @"5-7%";
                break;
            case 3:
                stockNumber = self.upDownArray.firstObject.ztNum + self.upDownArray.firstObject.zPer7Num + self.upDownArray.firstObject.zPer8Num + self.upDownArray.firstObject.zPer9Num;
                titleString = @"≥7%";
                break;
            default:
                break;
        }
        
        CGPoint hisStartPoint = CGPointMake(hisStartX, startY - stockNumber * unit);
        UIBezierPath *path = [UIBezierPath bezierPath];
        [path moveToPoint:CGPointMake(hisStartPoint.x + lineWidth * 0.5, startY)];
        [path addLineToPoint:CGPointMake(hisStartPoint.x + lineWidth * 0.5, startY - (stockNumber * unit))];
        // 柱形图layer
        CAShapeLayer *cLayer = [CAShapeLayer layer];
        cLayer.strokeColor = UIColor.up_riseColor.CGColor;
        cLayer.fillColor = UIColor.up_riseColor.CGColor;
        cLayer.lineWidth = lineWidth;
        cLayer.lineCap = kCALineCapButt;
        cLayer.path = path.CGPath;
        cLayer.strokeEnd = 1.0;
        [self.layer addSublayer:cLayer];
        [self.riseShapeLayers addObject:cLayer];
        
        // 文字layer
        UPMarketUITextLayer *layer = [UPMarketUITextLayer layer];
        NSString *text = [NSString stringWithFormat:@"%d", stockNumber];
        CGRect rect = [UPMarketUIDrawTool rectOfDigitalNSString:text fontSize:12.f];
        layer.bounds = CGRectMake(0, 0, rect.size.width, rect.size.height);
        layer.alignmentMode = kCAAlignmentCenter;
        if (stockNumber == 0) {
            [layer drawTextWithTextString:text fontSize:12.f color:[UIColor up_textSecondary1Color] point:CGPointMake(hisStartPoint.x + lineWidth * 0.5, startY - (stockNumber * unit) - 8)];
        } else {
            [layer drawTextWithTextString:text fontSize:12.f color:UIColor.up_riseColor point:CGPointMake(hisStartPoint.x + lineWidth * 0.5, startY - (stockNumber * unit) - 8)];
        }
        
        [self.layer addSublayer:layer];
        [self.riseTextLayers addObject:layer];
        if (titleString.length) {
            [UPMarketUIDrawTool drawDigitalWithTextString:titleString
                                                 fontSize:12
                                                    color:UIColor.up_riseColor
                                              centerPoint:CGPointMake(hisStartPoint.x + lineWidth / 2, startY + 10)];
        }
        hisStartX += (margin + lineWidth);
    }
    
    
}

//MARK: - setter/getter
/// 涨跌统计非空时绘制
- (void)drawZDStatistics:(UPMarketUpDownTrendDetail *)trendDetail {
    
    [CATransaction setDisableActions:YES];
    
    // 涨跌可能隐藏，手动打开
    self.z_layer.hidden = NO;
    self.d_layer.hidden = NO;
    
    // 涨家数
    int zNum = trendDetail.zNum;
//    _leftLabel.text = [NSString stringWithFormat:@"%d", zNum];
    // 跌家数
    int dNum = trendDetail.dNum;
//    _rightLabel.text = [NSString stringWithFormat:@"%d", dNum];
    
    self.szLableText.text = [NSString stringWithFormat:@"上涨%d家",zNum];
    self.xdLableText.text = [NSString stringWithFormat:@"下跌%d家",dNum];
    // 平家数
    int ppNum = trendDetail.ppNum;
    
    CGFloat totalNum = zNum + dNum + ppNum; // 总家数
    CGFloat total_width = self.up_width  - 16;  // 统计的总宽度
    
    CGFloat z_scale = 0;
    CGFloat d_scale = 0;
    if (totalNum > 0) {
        z_scale = zNum/totalNum;
        d_scale = dNum/totalNum;
    }
    
    CGFloat z_width = z_scale * total_width;   // 涨线的宽
    CGFloat d_width = d_scale * total_width;   // 跌线的宽
    CGFloat pp_width = total_width - z_width - d_width;   // 平线的宽
    CGFloat height = 5;   // 高
    CGFloat y = 56;   // y起点
    CGFloat start_x = 8;   // 涨的x起点
    CGFloat radius = 2.5;   // 两边弧度
    CGFloat slope = 5;   // 倾斜度
    CGFloat slope_width = 3;   // 倾斜度重叠宽
    
    /// 涨
    UIBezierPath *z_path = [UIBezierPath bezierPath];
    [z_path moveToPoint:CGPointMake(radius/2, 0)];
    [z_path addLineToPoint:CGPointMake(d_width, 0)];
    [z_path addLineToPoint:CGPointMake(d_width - slope, height)];
    [z_path addLineToPoint:CGPointMake(radius/2, height)];
    [z_path addArcWithCenter:CGPointMake(radius, radius)
                      radius:radius
                  startAngle:7/4*M_PI
                    endAngle:5/4*M_PI
                   clockwise:YES];
    [z_path closePath];
    [z_path fill];
    
    _d_layer.frame = CGRectMake(start_x, y, d_width, height);
    _d_layer.path = z_path.CGPath;
    
    /// 平
    CGFloat pp_w = pp_width + slope;
    _pp_layer.fillColor = UIColor.up_equalColor.CGColor;
    _pp_layer.frame = CGRectMake(start_x+d_width - slope_width, y, pp_w, height);
    
    UIBezierPath *pp_path = [UIBezierPath bezierPath];
    [pp_path moveToPoint:CGPointMake(slope, 0)];
    [pp_path addLineToPoint:CGPointMake(pp_w, 0)];
    [pp_path addLineToPoint:CGPointMake(pp_w - slope, height)];
    [pp_path addLineToPoint:CGPointMake(0, height)];
    [pp_path closePath];
    [pp_path fill];
    
    _pp_layer.path = pp_path.CGPath;

    /// 跌
    CGFloat d_w = z_width + slope;
    slope_width = 1;
    _z_layer.frame = CGRectMake(start_x+d_width+pp_w - slope_width - slope, y, d_w, height);
    
    UIBezierPath *d_path = [UIBezierPath bezierPath];
    [d_path moveToPoint:CGPointMake(d_w - radius/2, 0)];
    [d_path addLineToPoint:CGPointMake(slope, 0)];
    [d_path addLineToPoint:CGPointMake(0, height)];
    [d_path addLineToPoint:CGPointMake(d_w - radius/2, height)];
    [d_path addArcWithCenter:CGPointMake(d_w - radius, radius)
                      radius:radius
                  startAngle:1/4*M_PI
                    endAngle:3/4*M_PI
                   clockwise:NO];
    [d_path closePath];
    [d_path fill];
    
    _z_layer.path = d_path.CGPath;
    
    [CATransaction setDisableActions:NO];
}

- (CAShapeLayer *)z_layer {
    if (!_z_layer) {
        _z_layer = [[CAShapeLayer alloc] init];
        _z_layer.fillColor = UIColor.upmarketui_riseColor.CGColor;
    }
    return _z_layer;
}
- (CAShapeLayer *)pp_layer {
    if (!_pp_layer) {
        _pp_layer = [[CAShapeLayer alloc] init];
        _pp_layer.fillColor = UIColor.up_equalColor.CGColor;
    }
    return _pp_layer;
}
- (CAShapeLayer *)d_layer {
    if (!_d_layer) {
        _d_layer = [[CAShapeLayer alloc] init];
        _d_layer.fillColor = UIColor.upmarketui_fallColor.CGColor;
    }
    
    return _d_layer;
}



- (UIProgressView *)progressView {
    if (!_progressView) {
        _progressView = [[UIProgressView alloc] initWithProgressViewStyle:UIProgressViewStyleDefault];
//        _progressView.progress = 0.5;
        _progressView.progressTintColor = UIColor.up_fallColor;
        _progressView.trackTintColor = UIColor.up_riseColor;
        _progressView.layer.cornerRadius = 3.f;
        _progressView.layer.masksToBounds = YES;
    }
    return _progressView;
}

- (UIImageView *)proImg {
    if (!_proImg) {
        _proImg = [[UIImageView alloc] initWithImage:UPTImg(@"行情/zdfb_pro")];
//        _proImg.backgroundColor = UIColor.whiteColor;
    }
    return _proImg;
}

- (UILabel *)szLableText{
    if (!_szLableText) {
        _szLableText = [UILabel new];
        _szLableText.textColor = [UIColor up_colorFromHexString:@"#F54949"];
        _szLableText.font = [UIFont up_fontOfSize:UPWidth(10)];
    }
    return _szLableText;
}


- (UILabel *)xdLableText{
    if (!_xdLableText) {
        _xdLableText = [UILabel new];
        _xdLableText.textColor = [UIColor up_colorFromHexString:@"#1BA180"];
        _xdLableText.font = [UIFont up_fontOfSize:UPWidth(10)];
    }
    return _xdLableText;
}

- (void)setUpDownArray:(NSArray<UPMarketUpDownTrendDetail *> *)upDownArray {
    _upDownArray = upDownArray;
    
    NSInteger upMax = MAX4(upDownArray.firstObject.zPer0Num + upDownArray.firstObject.zPer1Num + upDownArray.firstObject.zPer2Num,
                           upDownArray.firstObject.zPer3Num + upDownArray.firstObject.zPer4Num,
                           upDownArray.firstObject.zPer5Num + upDownArray.firstObject.zPer6Num,
                           upDownArray.firstObject.ztNum);
    NSInteger downMax = MAX5(upDownArray.firstObject.dPer0Num + upDownArray.firstObject.dPer1Num + upDownArray.firstObject.dPer2Num,
                             upDownArray.firstObject.dPer3Num + upDownArray.firstObject.dPer4Num,
                             upDownArray.firstObject.dPer5Num + upDownArray.firstObject.dPer6Num,
                             upDownArray.firstObject.dPer7Num + upDownArray.firstObject.dPer8Num + upDownArray.firstObject.dPer9Num,
                             upDownArray.firstObject.dtNum
                             );
    
    self.maxNumber = MAX(upMax, downMax);
    
    self.upSum = upDownArray.firstObject.zNum;
    self.downSum = upDownArray.firstObject.dNum;
    
    self.szLableText.text = [NSString stringWithFormat:@"上涨%d家",upDownArray.firstObject.zNum];
    self.xdLableText.text = [NSString stringWithFormat:@"下跌%d家",upDownArray.firstObject.dNum];
    
//    self.progressView.progress = (float)upDownArray.firstObject.dNum / (upDownArray.firstObject.zNum + upDownArray.firstObject.dNum);
//    [self.proImg mas_updateConstraints:^(MASConstraintMaker *make) {
//        make.centerX.equalTo(self.progressView.mas_left).offset((self.progressView.up_width * (self.progressView.progress)));
//    }];
    
    [self drawZDStatistics:upDownArray.firstObject];
    
    [self setNeedsDisplay];
}

- (NSMutableArray *)riseShapeLayers {
    if (!_riseShapeLayers) {
        _riseShapeLayers = [NSMutableArray array];
    }
    return _riseShapeLayers;
}

- (NSMutableArray *)riseTextLayers {
    if (!_riseTextLayers) {
        _riseTextLayers = [NSMutableArray array];
    }
    return _riseTextLayers;
}

- (NSMutableArray *)fallShapeLayers {
    if (!_fallShapeLayers) {
        _fallShapeLayers = [NSMutableArray array];
    }
    return _fallShapeLayers;
}

- (NSMutableArray *)fallTextLayers {
    if (!_fallTextLayers) {
        _fallTextLayers = [NSMutableArray array];
    }
    return _fallTextLayers;
}

- (void)viewDidAppear {
    
};
- (void)viewDidDisappear {
    
};

@end
