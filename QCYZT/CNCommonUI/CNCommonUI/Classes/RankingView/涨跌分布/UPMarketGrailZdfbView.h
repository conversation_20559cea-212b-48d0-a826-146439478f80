//
//  UPMarketGrailZdfbView.h
//  UPMarket
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/4/3.
//  Copyright © 2019 UpChina. All rights reserved.
//

#import <UPBaseUI/UPBaseUI.h>

//NS_ASSUME_NONNULL_BEGIN

#define MAX3(A,B,C) MAX(MAX(A, B), C)
#define MIN3(A,B,C) MIN(MIN(A, B), C)
#define MAX4(A,B,C,D) MAX(MAX(MAX(A, B), C), D)
#define MIN4(A,B,C,D) MIN(MIN(MIN(A, B), C), D)
#define MAX5(A,B,C,D,E) MAX(MAX(MAX(MAX(A, B), C), D), E)
#define MIN5(A,B,C,D,E) MIN(MIN(MIN(MIN(A, B), C), D), E)
#define MAX6(A,B,C,D,E,F) MAX(MAX(MAX(MAX(MAX(A, B), C), D), E), F)
#define MIN6(A,B,C,D,E,F) MIN(MIN(MIN(MIN(MIN(A, B), C), D), E), F)
#define MAX7(A,B,C,D,E,F,H) MAX(MAX(MAX(MAX(MAX(MAX(A, B), C), D), E), F),H)
#define MIN7(A,B,C,D,E,F,H) MIN(MIN(MIN(MIN(MIN(MIN(A, B), C), D), E), F), H)

@interface UPMarketGrailZdfbView : UPBaseView

@property(nonatomic, strong) NSArray <UPMarketUpDownTrendDetail *> *upDownArray;

@property(nonatomic, strong) UPMarketFuPanFZDNumTrend *zdlNum;

@property(nonatomic, strong) UPMarketTrendLN *lnData;


@property (nonatomic, assign) BOOL hiddenRightInfoView;//default is NO

@end


//NS_ASSUME_NONNULL_END
