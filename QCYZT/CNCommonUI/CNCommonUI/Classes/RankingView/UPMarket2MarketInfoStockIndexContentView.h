//
//  UPMarket2MarketInfoStockIndexContentView.h
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//
/// 大盘指数
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSUInteger, UPMarket2MarketInfoStockIndexType) {
    UPMarket2MarketInfoStockIndexTypeAStock,   // A股
    UPMarket2MarketInfoStockIndexTypeHongKong, // 港股
    UPMarket2MarketInfoStockIndexTypeStockTransfer, // 股转
};

@protocol UPMarket2MarketInfoStockIndexContentViewDelegate <NSObject>

- (void)reloadEnd;

@end

@interface UPMarket2MarketInfoStockIndexContentView : UPBaseView

@property (nonatomic, weak) id<UPMarket2MarketInfoStockIndexContentViewDelegate> indexDelegate;

@property (nonatomic, assign) UPMarket2MarketInfoStockIndexType indexType;  // 指数类型（使用的指数数据不同）

/**
 是否使用图标样式
 */
@property (nonatomic, assign) BOOL isUseChartStyle;

- (void)viewWillAppear;
- (void)viewWillDisappear;
- (void)viewDidAppear;
- (void)viewDidDisappear;
@end




@interface UPMarket2MarketInfoStockNowTimeContentView : UPBaseView

@property (nonatomic, strong) UILabel *namelabel;
@property (nonatomic, strong) UILabel *detaiLabel;


- (void)viewWillAppear;
- (void)viewWillDisappear;
- (void)viewDidAppear;
- (void)viewDidDisappear;

@end

NS_ASSUME_NONNULL_END
