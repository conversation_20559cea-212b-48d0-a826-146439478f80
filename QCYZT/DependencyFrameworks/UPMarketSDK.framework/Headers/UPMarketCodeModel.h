//
//  UPMarketCodeModel.h
//  UPGPT
//
//  Created by Nic on 2017/3/8.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketDefine.h"
#import "UPMarketReqAndRsp.h"

extern NSString *DUOYIN_SPLIT; // 分隔股票名称的多种读法 -- 会入库，不要随意改动！！
extern NSString *PINYIN_SPLIT; // 分隔股票名称的每个字 -- 与后台保持一致，不要随意改动！！
extern NSInteger LONG_USED_NAME_ID; // 长名称使用的曾用名id -- 不要随意改动！！

@interface UPMarketCodeModel : NSObject

@property (nonatomic, assign) NSInteger setCode;                   // 股票市场

@property (nonatomic, strong) NSString *code;                      // 股票代码

@property (nonatomic, strong) NSString *name;                      // 股票名称

@property (nonatomic, strong) NSString *normalizedCode;            // 规范化的股票代码

@property (nonatomic, strong) NSString *normalizedName;            // 规范化的股票名称

@property (nonatomic, strong) NSString *pinyin;                    // 股票拼音

@property (nonatomic, assign) UPMarketStockCategory category;      // 股票类别

@property (nonatomic, assign) NSInteger origCategory;               // 股票原始类别

@property (nonatomic, assign) UPMarketStockSubCategory subCategory; // 股票原始类别

@property (nonatomic, assign) NSInteger origSubCategory;           // 股票原始类别

@property (nonatomic, assign) short unit;                          // 最小成交单元

@property (nonatomic, assign) NSInteger precise;                   // 股票精度

@property (nonatomic, assign) UPMarketTradeStatus status;          // 状态

@property (nonatomic, assign) NSInteger usedNameId;                // 曾用名id -- 0 代表 现用名；10000 代表 长名称

-(instancetype)initWithCodeModel:(UPMarketCodeModel *)model;

@end
