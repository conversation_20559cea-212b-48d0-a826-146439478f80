//
//  UPMarketMonitor.h
//  Pods
//
//  Created by <PERSON><PERSON> on 2017/3/6.
//
//

#import "UPMarketDefine.h"
#import "UPMarketReqAndRsp.h"

/**
 处理数据监控
 */
@interface UPMarketMonitor : NSObject

+ (instancetype)monitorWithInterval:(CGFloat)interval;

+ (instancetype)monitorWithInterval:(CGFloat)interval ignoreMarketStatus:(BOOL)ignoreMarketStatus;

/**
 根据标记介绍监控任务

 @param tag 监控标记
 */
- (void)stopMonitorWithTag:(int)tag;

/**
 * 停止所有monitor任务
 * */
- (void)stopMonitor;

/**
 * 某个tag的刷新任务是否正在进行
 * @param tag 标记
 * @return 是否正在进行
 */
- (BOOL)isMonitoringWithTag:(int)tag;

/**
 * 修改tag对应的req
 * @param tag tag标记
 * @param request req
 */
- (void)modifyMonitorWithTag:(int)tag request:(UPHqReq *)request;

/*
 刷新更新周期 秒数 默认3秒（单位：秒）
 */
@property (nonatomic, assign) float monitorTimerInterval;

/**
 监控股票行情数据

 @param req UPMarketStockHqReq
 @param tag 请求标记
 @param handler (UPMarketStockHqRsp、NSError)
 */
- (void)startMonitorStockHq:(UPMarketStockHqReq *)req tag:(int)tag completionHandler:(upMarketStockHqCompletionHandler)handler;

/**
 监控股票十档行情数据-走上证云SDK

 @param req UPMarketStockHqReq
 @param tag 请求标记
 @param handler (UPMarketStockHqRsp、NSError)
 */
- (void)startMonitorSHYTenQuote:(UPMarketStockHqReq *)req tag:(int)tag completionHandler:(upMarketStockHqCompletionHandler)handler;

/**
 根据类型请求股票数据刷新

 @param req UPMarketType2StockReq
 @param tag 请求标记
 @param handler (UPMarketType2StockRsp，NSError)
 */
- (void)startMonitorStockByType:(UPMarketType2StockReq *)req tag:(int)tag completionHandler:(upMarketStockTypeCompletionHandler)handler;

/**
 请求休市数据

 @param req UPMarketCloseInfoReq
 @param tag 请求标记
 @param handler (UPMarketCloseInfoRsp，NSError)
 */
- (void)startMonitorCloseInfo:(UPMarketCloseInfoReq *)req tag:(int)tag completionHandler:(upMarketCloseInfoCompletionHandler)handler;

/**
 * 根据板块请求股票数据刷新
 *
 *  @param req UPMarketBlock2StockReq
 *  @param tag 请求标记
 *  @param handler (UPMarketBlock2StockRsp，NSError)
 * */
- (void)startMonitorStockByBlock:(UPMarketBlock2StockReq *)req tag:(int)tag completionHandler:(upMarketStockBlockCompletionHandler)handler;

/**
 * 根据股票请求板块数据刷新
 *
 *  @param req UPMarketStock2BlockReq
 *  @param tag 请求标记
 *  @param handler (UPMarketStock2BlockRsp，NSError)
 * */
- (void)startMonitorBlockByStock:(UPMarketStock2BlockReq *)req tag:(int)tag completionHandler:(upMarketBlockStockCompletionHandler)handler;

/**
 监控股票明细数据

 @param req UPMarketTickDataReq
 @param tag 请求标记
 @param handler (UPMarketTickDataRsp,NSError)
 */
- (void)startMonitorStockTickData:(UPMarketTickDataReq *)req tag:(int)tag completionHandler:(upMarketStockTickCompletionHandler)handler;

/**
 请求股票逐笔数据

 @param req UPMarketTransReq
 @param handler (UPMarketTransRsp, NSError)
 */
- (void)startMonitorTransData:(UPMarketTransReq *)req tag:(int)tag completionHanlder:(upMarketStockTransCompletionHandler)handler;

/**
 监控分时数据

 @param req UPMarketMinDataReq
 @param tag 请求标记
 @param handler (UPMarketMinDataRsp、NSError)
 */
- (void)startMonitorStockMinuteData:(UPMarketMinDataReq *)req tag:(int)tag completionHandler:(upMarketStockMinuteCompletionHandler)handler;

/**
 监控K线数据

 @param req UPMarketKLineDataReq
 @param tag 监控标记
 @param handler (UPMarketKLineDataRsp、NSError)
 */
- (void)startMonitorStockKLineData:(UPMarketKLineDataReq *)req tag:(int)tag completionHandler:(upMarketStockKlineCompletionHandler)handler;

/**
 监控除权除息数据

 @param req UPMarketQXDataReq
 @param tag 监控标记
 @param handler (UPMarketQXDataRsp、NSError)
 */
- (void)startMonitorStockQXData:(UPMarketQXDataReq *)req tag:(int)tag completionHandler:(upMarketStockQXCompletionHandler)handler;

/**
 监控实时资金流数据
 
 @param req UPMarketCapitalFlowDataReq
 @param tag 监控标记
 @param handler (UPMarketCapitalFlowDataRsp,NSError)
 */
- (void)startMonitorStockCapitalFlowData:(UPMarketCapitalFlowDataReq *)req tag:(int)tag completionHandler:(upMarketCapitalFlowCompletionHandler)handler;

/**
 监控南向资金数据
 
 @param req UPMarketSouthMoneyFlowDataReq
 @param tag 监控标记
 @param handler (UPMarketSouthMoneyFlowDataRsp,NSError)
 */
- (void)startMonitorStockSouthMoneyFlowData:(UPMarketSouthMoneyFlowDataReq *)req tag:(int)tag completionHandler:(upMarketSouthMoneyFlowCompletionHandler)handler;


/**
 请求股票委托队列数据
 
 @param req UPMarketOrderQueueReq
 @param tag 监控标记
 @param handler (UPMarketOrderQueueRsp, NSError)
 */
- (void)startMonitorStockOrderQueueData:(UPMarketOrderQueueReq *)req tag:(int)tag completionHandler:(upMarketOrderQueueCompletionHandler)handler;

/**
 请求股票价格委托数据(千档)
 
 @param req UPMarketPriceOrderReq
 @param tag 监控标记
 @param handler (UPMarketPriceOrderRsp, NSError)
 */
- (void)startMonitorStockPriceOrderData:(UPMarketPriceOrderReq *)req tag:(int)tag completionHandler:(upMarketPriceOrderCompletionHandler)handler;

/**
 请求股票L2价量分布数据

 @param req UPMarketPriceVolReq
 @param tag 监控标记
 @param handler (UPMarketPriceVolRsp, NSError)
 */
- (void)startMonitorStockPriceVolData:(UPMarketPriceVolReq *)req tag:(int)tag completionHandler:(upMarketPriceVolCompletionHandler)handler;

/**
 请求股票筹码分布数据

 @param req UPMarketChipReq
 @param tag 监控标记
 @param handler (UPMarketChipRsp, NSError)
 */
- (void)startMonitorStockChipDistributionData:(UPMarketChipReq *)req tag:(int)tag completionHandler:(upMarketChipCompletionHandler)handler;

/**
 * 请求股票DDE系列数据
 * @param req UPMarketDDEReq
 * @param handler (UPMarketDDERsp,NSError)
 * */
- (void)startMonitorStockDDE:(UPMarketDDEReq *)req tag:(int)tag completionHandler:(upMarketDDECompletionHandler)handler;

/**
 * 请求股票指标数据
 * @param req UPMarketIndexReq
 * @param tag  监控标记
 * @param handler (UPMarketIndexRsp, NSError)
 */
- (void)startMonitorStockIndex:(UPMarketIndexReq *)req tag:(int)tag completionHandler:(upMarketIndexCompletionHandler)handler;

/**
 * 请求指标对应股票数据
 * @param req UPMarketIndexStockListReq
 * @param tag 监控标记
 * @param handler (UPMarketIndexStockListRsp, NSError)
 */
- (void)startMonitorIndexStockList:(UPMarketIndexStockListReq *)req tag:(int)tag completionHandler:(upMarketIndexStockListCompletionHandler)handler;


/**
 * 请求Level2选股池数据 (拖拉机单，顶级挂单，主力撤单)
 * @param req UPMarketLevel2PoolReq
 * @param handler (UPMarketLevel2PoolRsp, NSError)
 * */
- (void)startMonitorLevel2Pool:(UPMarketLevel2PoolReq *)req tag:(int)tag completionHandler:(upMarketLevel2CompletionHandler)handler;

/**
 * 请求资金排名数据
 * @param req UPMarketFlowRankReq
 * @param tag 监控标记
 * @param handler (UPMarketFlowRankRsp,NSError)
 */
- (void)startMonitorFlowRank:(UPMarketFlowRankReq *)req tag:(int)tag completionHandler:(upMarketFlowRankCompletionHandler)handler;

/**
 * 请求市场状态数据
 * @param req UPMarketStatusReq
 * @param handler (UPMarketStatusRsp,NSError)
 * */
- (void)startMonitorMarketStatus:(UPMarketStatusReq *)req tag:(int)tag completionHandler:(upMarketStatusCompletionHandler)handler;


/**
 * 请求港股经纪数据（L2）
 * @param req UPMarketBrokerQueueReq
 * @param tag 监控标记
 * @param handler (UPMarketBrokerQueueRsp,NSError)
 */
- (void)startMonitorBrokerQueueData:(UPMarketBrokerQueueReq *)req tag:(int)tag completionHandler:(upMarketBrokerQueueCompletionHandler)handler;

/**
 * 请求AH股票信息
 * @param req UPMarketAHStockReq
 * @param tag 监控标记
 * @param handler (UPMarketAHStockRsp, NSError)
 */
- (void)startMonitorAHStock:(UPMarketAHStockReq *)req tag:(int)tag completionHandler:(upMarketAHStockCompletionHandler)handler;

/**
 * 请求对应的AH股票
 * @param req UPMarketRelateAHStockReq
 * @param tag 监控标记
 * @param handler (UPMarketRelateAHStockRsp, NSError)
 */
- (void)startMonitorRelateAHStock:(UPMarketRelateAHStockReq *)req tag:(int)tag completionHandler:(upMarketRelateAHStockCompletionHandler)handler;

/**
 * 请求对应的AH股票(支持AMType传ALL,旧接口无法传All)
 * @param req UPMarketRelateStockReq
 * @param tag 监控标记
 * @param handler (UPMarketRelateAHStockRsp, NSError)
 */
- (void)startMonitorRelateAHStockBatch:(UPMarketRelateAHStockBatchReq *)req tag:(int)tag completionHandler:(upMarketRelateAHStockBatchCompletionHandler)handler;


/**
 * 监控自选股票行情数据
 *
 * @param req UPMarketOptStockHqReq
 * @param tag 请求标记
 * @param handler (UPMarketOptStockHqRsp、NSError)
 */
- (void)startMonitorOptStockHq:(UPMarketOptStockHqReq *)req tag:(int)tag completionHandler:(upMarketOptStockHqCompletionHandler)handler;

/**
 * 监控港股权证数据
 *
 * @param req UPMarketHKWarrantReq
 * @param tag 请求标记
 * @param handler (UPMarketHKWarrantRsp、NSError)
 */
- (void)startMonitorHKWarrant:(UPMarketHKWarrantReq *)req tag:(int)tag completionHandler:(upMarketHKWarrantCompletionHandler)handler;

/**
 * 监控股票或板块资金排名净流入和净流出的Top数据列表
 * @param tag 请求标记
 * @param req     - UPMarketMoneyRankTopDataReq
 *                  type:目前支持所有板块和沪深A股 所有板块传入:UPMarketBlockTypeAll 不传默认沪深A
 *                  wantNum:需要排名前几的数据  如：请求前三，返回净流入和净流出各3条共6条数据
 * @param handler - (UPMarketMoneyRankTopDataRsp、NSError)
 */
- (void)startMonitorMoneyRankTopData:(UPMarketMoneyRankTopDataReq *)req tag:(int)tag completionHandler:(upMarketMoneyRankTopDataCompletionHandler)handler;


/**
 * 监控港股权证所属的港股数据
 *
 * @param req UPMarketWarrantRelatedStockReq
 * @param tag 请求标记
 * @param handler (UPMarketWarrantRelatedStockRsp、NSError)
 */
- (void)startMonitorWarrantRelatedStock:(UPMarketWarrantRelatedStockReq *)req tag:(int)tag completionHandler:(upMarketWarrantRelatedStockCompletionHandler)handler;

/**
 * 监控股票因子库指标数据
 * @param req   UPMarketStockFactorDataReq
 *              setCode -- 市场码
 *              code -- 股票码
 *              type -- 因子类型
 *              startDate -- 起始日期
 *              endDate -- 结束日期
 * @param handler - (UPMarketStockFactorDataRsp、NSError)
*/
- (void)startMonitorStockFactorData:(UPMarketStockFactorDataReq *)req tag:(int)tag completionHandler:(upMarketStockFactorDataCompletionHandler)handler;

/// 批量因子库指标数据
- (void)startMonitorStockFactorBatchData:(UPMarketStockFactorBatchDataReq *)req tag:(int)tag completionHandler:(upMarketStockFactorDataCompletionHandler)handler;
/**
 * 监控股池统计数据
 * @param req   UPMarketStockPoolStatReq
 *              type -- 股池类型 - UPMarketIndexTypeGNNTop
 * @param handler - (UPMarketStockPoolStatRsp、NSError)
*/
- (void)startMonitorStockPoolStatData:(UPMarketStockPoolStatReq *)req tag:(int)tag completionHandler:(upMarketStockPoolStatCompletionHandler)handler;

/**
 * 监控股牛牛领涨题材数据
 * @param req   UPMarketLeadSubjectReq

 * @param handler - (UPMarketLeadSubjectRsp、NSError)
*/
- (void)startMonitorGNNLeadSubjectData:(UPMarketLeadSubjectReq *)req tag:(int)tag completionHandler:(upMarketLeadSubjectBlockCompletionHandler)handler;

/**
 * 监控股牛牛题材异动数据
 * @param req   UPMarketSubjectChangeListReq

 * @param handler - (UPMarketSubjectChangeListRsp、NSError)
*/
- (void)startMonitorGNNSubjectChangeListData:(UPMarketSubjectChangeListReq *)req tag:(int)tag completionHandler:(upMarketSubjectChangeListCompletionHandler)handler;

/**
 *  监控DDE排名数据
 * @param req   UPMarketDDERankDataReq
 
 * @param handler - (UPMarketDDERankDataRsp、NSError)
 */
- (void)startMonitorStockDDERankData:(UPMarketDDERankDataReq *)req tag:(int)tag completionHandler:(upMarketDDERankListCompletionHandler)handler;

/**
 * 请求指标股池 - 按指定日期 通过指标主站获取
 * @param req   UPMarketIndexStocksReq - UPMarketIndexTypeLTJJ

 * @param handler - (UPMarketIndexStocksRsp、NSError)
 */
- (void)startMonitorIndexStockListByDate:(UPMarketIndexStocksReq *)req tag:(int)tag completionHandler:(upMarketIndexStocksPushCompletionHandler)handler;

/// 趋势龙头
- (void)startMonitorRegStockListByDate:(UPMarketIndexStocksReq *)req tag:(int)tag completionHandler:(upMarketRegStockListCompletionHandler)handler;

/**
 * 请求股票指标数据 - 按指定日期 通过指标主站获取
 * @param req   UPMarketStockIndexReq - UPMarketIndexTypeLTJJGG

 * @param handler - (UPMarketStockIndexRsp、NSError)
 */
- (void)startMonitorStockIndexByDate:(UPMarketStockIndexReq *)req tag:(int)tag completionHandler:(upMarketStockIndexPushCompletionHandler)handler;


/**
 * 期权列表和T型报价列表
 * @param req UPMarketOptionListReq
 *              setCode -- 市场码 UPMarketSetCodeSH
 *              code -- 股票码 '510050'
 * @param handler - (UPMarketOptionListRsp、NSError)
 */
- (void)startMonitorOptionList:(UPMarketOptionListReq *)req tag:(int)tag completionHandler:(upMarketOptionListCompletionHandler)handler;

/**
 * 监控最新的个股异动数据
 *
 * @param req    必填参数：
 *                 type: -- 异动类型 UPMarketStockChangeType
 *                 wantNum:请求条数，缺省返回默认3条
 * @param handler - (UPMarketLatestStockChangeRsp、NSError)
 */
- (void)startMonitorLatestStockChanges:(UPMarketLatestStockChangeReq *)req tag:(int)tag completionHandler:(upMarketStockChangeCompletionHandler)handler;

/**
 * 监控短线精灵数据
 *
 * @param req    setCode:
 *                  1. 不传获取全市场
 *                  2. 传入 UPMarketSetCodeSH 上海市场
 *                  3. 传入 UPMarketSetCodeSZ 深圳市场
 *                 dxjlTypes:
 *                  1.不传默认取所有类型，具体类型见UPMarketDXJLType
 *                  2.传入具体类型  UPMarketDXJLType
 *                 wantNum:请求条数，缺省返回默认3条
 * @param handler - (UPMarketDXJLRsp、NSError)
 */
- (void)startMonitorDXJLStockList:(UPMarketDXJLReq *)req tag:(int)tag completionHandler:(upMarketDXJLCompletionHandler)handler;

/**
 * 监控集合竞价行情快照数据
 *
 * @param req    setCode: 市场代码
 *               code:股票代码
 * @param handler - (UPMarketStockAuctionRsp、NSError)
 */
- (void)startMonitorStockAuctionData:(UPMarketStockAuctionReq *)req tag:(int)tag completionHandler:(upMarketStockAuctionCompletionHandler)handler;

/**
 * 监控主站集合竞价分时
 *
 * @param req    setCode: 市场代码
 *               code:股票代码
 * @param handler - (UPMarketStockAuctionV2Rsp、NSError)
 */
- (void)startMonitorStockAuctionDataV2:(UPMarketStockAuctionReq *)req tag:(int)tag completionHandler:(upMarketStockAuctionV2CompletionHandler)handler;

/**
 * 监控可转债数据(为上证云SDK添加,补充上证云SDK stockHq接口没有可转债数据的问题)
 *
 * @param req    setCode: 市场代码
 *               code:股票代码
 * @param handler - (UPMarketKZZDataRsp、NSError)
 */
- (void)startMonitorKZZStockData:(UPMarketKZZDataReq *)req tag:(int)tag completionHandler:(upMarketStockKZZDataCompletionHandler)handler;

/**
 * 监控涨跌分布-主站接口
 *
 * @param req    zdfbType: 涨跌分布类型
 *               zdfbWidth:涨跌分布-区间宽度,如2%~4%宽度为2
 * @param handler - (UPMarketCurrencyInfoRsp、NSError)
 */
- (void)startMonitorZDFBData:(UPMarketZDFBInfoReq *)req tag:(int)tag completionHandler:(upMarketZDFBCompletionHandler)handler;

/// 获取L2中拖拉机单/顶级挂单/主力撤单
/// @param req type 0-拖拉机单 1-顶级挂单 2-主力撤单
/// @param handler upMarketL2SzfyComCompletionHandler
- (void)startMonitorL2SzfyCom:(UPMarketL2SzfyComReq *)req tag:(int)tag completionHandler:(upMarketL2SzfyComCompletionHandler)handler;

/// 区间统计(懂牛需求)
- (void)startMonitorRangeStatsByDate:(UPMarketRangeStatsByDateReq *)req tag:(int)tag completionHandler:(upMarketRangeStatsByDateCompletionHandler)handler;

/**
 * 股票异动
 *  @param req      必填参数：
 *                  wantNum:请求条数，缺省返回默认3条
 *                  anomalyType: -- 异动类型 UPMarketStockChangeType
 *                  typeList: -- NSArray<NSNumber*>类型
 */
- (void)startMonitorAnomalyBroadcast:(UPMarketAnomalyBroadcastReq *)req tag:(int)tag completionHandler:(upMarketAnormalyBroadcastCompletionHandler)handler;

// 南北向资金
- (void)startMonitorSNFundsData:(UPMarketSNFundsDataReq *)req tag:(int)tag completionHandler:(upMarketSNFundsDataCompletionHandler)handler;

// 南北向资金净流入
- (void)startMonitorSNNetData:(UPMarketSNNetDataReq *)req tag:(int)tag completionHandler:(upMarketSNNetDataCompletionHandler)handler;

// 南北向资金净流入
- (void)startMonitorHisSNNetData:(UPMarketHisSNNetDataReq *)req tag:(int)tag completionHandler:(upMarketHisSNNetDataCompletionHandler)handler;

@end
