//  UPMarketDefine.h
//  UPMarketSDK
//
//  Created by <PERSON><PERSON> on 2017/2/28.
//  Copyright © 2017年 UPChina. All rights reserved.
//

/**
 * 行情对外常量
 */

#ifndef UPMarketDefine_h
#define UPMarketDefine_h

#import <Foundation/Foundation.h>

// MARK: - Notification定义

extern NSString *const UPMarketNotifLevel2LoginSuccess;      // level2 登录成功
extern NSString *const UPMarketNotifLevel2LoginFailed;       // level2 登录失败
extern NSString *const UPMarketNotifLevel2KickedOff;         // level2 被踢下线

extern NSString *const UPMarketNotifSHYL2Offline;           // 上证云L2行情离线
extern NSString *const UPMarketNotifSHYL2Online;            // 上证云L2行情恢复
extern NSString *const UPMarketNotifClearSettingAddress;    // 设置地址不可用,清除


//---------南北向资金流代码定义--------------//
// 北向资金流-深
extern NSString *const UPMarketNorthSZFlowCode;
// 北向资金余额-深
extern NSString *const UPMarketNorthSZBalanceCode;
// 北向资金流-沪
extern NSString *const UPMarketNorthSHFlowCode;
// 北向资金余额-沪
extern NSString *const UPMarketNorthSHBalanceCode;

// 南向资金流-深
extern NSString *const UPMarketSouthSZFlowCode;
// 南向资金余额-深
extern NSString *const UPMarketSouthSZBalanceCode;
// 南向资金流-沪
extern NSString *const UPMarketSouthSHFlowCode;
// 南向资金余额-沪
extern NSString *const UPMarketSouthSHBalanceCode;



// MARK: - L2类型
typedef NS_ENUM(NSUInteger, UPMarketL2Type) {
   UPMarketL2TypeNone = 0,          // 非L2
    UPMarketL2TypeSHSZ = 1,         // 沪深L2
    UPMarketL2TypeHK = 1 << 1,      // 港股L2
    UPMarketL2TypeUS = 1 << 2,      // 美股L2
    UPMarketL2TypeHKUS = UPMarketL2TypeHK | UPMarketL2TypeUS,    // 港美股L2 -- 目前只支持单市场L2主站
    UPMarketL2TypeALL = UPMarketL2TypeSHSZ | UPMarketL2TypeHKUS, // 全类型L2 -- 目前只支持单市场L2主站
};

// MARK: - L2切换原因

typedef NS_ENUM(NSUInteger, UPMarketL2SwitchReason) {
    UPMarketL2SwitchReasonUserRightChange = 0,      // 用户权限发生变化
    UPMarketL2SwitchReasonL2Offline                 // 行情L2离线--上证云SDK
};

typedef NS_ENUM(NSUInteger, UPSNDataType) {
    UPSNDataTypeSZNet = 0,  //深股通 净买入
    UPSNDataTypeSHNet,      //沪股通 净买入
    UPSNDataTypeSZInflow,   //深股通 净流入
    UPSNDataTypeSHInflow,   //沪股通 净流入
};

// MARK: - UPMarketDB常量定义 - 内部使用

#define UPMarketDBColumnID  @"_id"
#define UPMarketDBColumnCode  @"code"
#define UPMarketDBColumnSetCode  @"setcode"
#define UPMarketDBColumnName  @"name"
#define UPMarketDBColumnNormalizedCode  @"normalized_code"
#define UPMarketDBColumnNormalizedName  @"normalized_name"
#define UPMarketDBColumnNormalizedPinyin  @"normalized_pinyin"
#define UPMarketDBColumnPinyin  @"pinyin"
#define UPMarketDBColumnSimplePinyin  @"simple_pinyin"
#define UPMarketDBColumnCategory  @"category"
#define UPMarketDBColumnUnit  @"unit"
#define UPMarketDBColumnPrecise  @"precise"
#define UPMarketDBColumnStatus  @"status"
#define UPMarketDBColumnWeight  @"weight"
#define UPMarketDBColumnUsedNameID  @"used_name_id"
#define UPMarketDBColumnOrigCategory  @"orig_category"
#define UPMarketDBColumnSubCategory  @"sub_category"
#define UPMarketDBColumnOrigSubCategory  @"orig_sub_category"

#define UPMarketSortColumnOtherBaseValue 10000

// MARK: - 错误码
typedef NSInteger UPMarketErrorType;

NS_ENUM(UPMarketErrorType) {
    UPMarketErrorTypeBuildError = 1001,         // 参数错误
    UPMarketErrorTypeConnectError = 1002,       // 网络连接错误
    UPMarketErrorTypeTimeOut = 1003,            // 请求超时
    UPMarketErrorTypeServerResponse = 1004,     // 服务器响应错误
    UPMarketErrorTypeNotSupportMarket = 1005,   // 不支持的市场类型

    UPMarketErrorTypeUnknownError = 1000        // 未知错误
};

// MARK: - 市场代码

typedef NSUInteger UPMarketSetCode;

NS_ENUM(UPMarketSetCode) {
    UPMarketSetCodeSZ = 0,                     // 深圳证券交易所
    UPMarketSetCodeSH = 1,                     // 上海证券交易所
    UPMarketSetCodeHK = 2,                     // 香港股票交易所
    UPMarketSetCodeZJS = 3,                    // 中金所市场
    UPMarketSetCodeBJ = 7,                     // 北京交易所
    UPMarketSetCodeSHJ = 8,                    // 上海黄金交易所
    UPMarketSetCodeLDJ = 9,                    // 伦敦金(国际金)
    UPMarketSetCodeSHOption = 10,              // 上海期权
    UPMarketSetCodeSZOption = 12,              // 深圳期权
    UPMarketSetCodeNSDK = 13,                  // 纳斯达克证券交易所
    UPMarketSetCodeNY = 14,                    // 纽约证券交易所
    UPMarketSetCodeUS = 15,                    // 美国证券交易所
    UPMarketSetCodeHKI = 16,                   // 港股指数
    UPMarketSetCodeUSI = 17,                   // 美股指数
    UPMarketSetCodeNK255 = 18,                 // 日经255
    UPMarketSetCodeKOSPI = 19,                 // 韩国综合指数
    UPMarketSetCodeTWII = 20,                  // 台湾加权
    UPMarketSetCodeKLSE = 22,                  // 马来综指
    UPMarketSetCodeSETI = 23,                  // 泰国综指
    UPMarketSetCodeAORD = 25,                  // 澳大利亚综合指数
    UPMarketSetCodeSENSEX = 27,                // 印度SENSEX
    UPMarketSetCodeUSD = 29,                   // 外汇
    UPMarketSetCodeCAC = 30,                   // 法国CAC40
    UPMarketSetCodeDAX = 31,                   // 德国dax指数
    UPMarketSetCodeAEX = 32,                   // 荷兰AEX
    UPMarketSetCodeKFX = 33,                   // 丹麦KFX
    UPMarketSetCodeBFX = 34,                   // 比利时BFX
    UPMarketSetCodeSSMI = 35,                  // 瑞士SSMI
    UPMarketSetCodeMIB = 38,                   // 意大利MIB
    UPMarketSetCodeFX = 39,                    // 交叉汇率
    UPMarketSetCodeFTSE = 40,                  // 英国富士指数
    UPMarketSetCodeXSB = 47,                   // 新三板
    UPMarketSetCodeCNY = 48,                   // 人民币外汇
    UPMarketSetCodeDSE = 60,                   // 孟加拉
    UPMarketSetCodeSouth = 61,                 // 南向资金
    UPMarketSetCodeNorth = 62,                 // 北向资金
    UPMarketSetCodeGGT = 64,                   // 港股通
    UPMarketSetCodeHKDark = 65,                // 港股暗盘
    UPMarketSetCodeHSGT = 66,                  // 沪深港通
    UPMarketSetCodeBH = 69,                    // B转H股
    UPMarketSetCodeHKBLOCK = 70,               // 香港板块
    UPMarketSetCodeABlock = 71,                // A股板块
    UPMarketSetCodeUSBLOCK = 72,               // 美股板块
    UPMarketSetCodeNASDAQPRE = 73,             // 纳斯达克交易所盘前
    UPMarketSetCodeNYSEPRE = 74,               // 纽约交易所盘前
    UPMarketSetCodeAMEXPRE = 75,               // 美国证券交易所盘前
    UPMarketSetCodeNASDAQPOST = 76,            // 纳斯达克交易所盘后
    UPMarketSetCodeNYSEPOST = 77,              // 纽约交易所盘后
    UPMarketSetCodeAMEXPOST = 78,              // 美国证券交易所盘后
};

// MARK:股票名称类型
typedef NSUInteger UPMarketNameFlag;

NS_ENUM(UPMarketNameFlag) {
    UPMarketNameFlagNow = 0,         // 现用名
    UPMarketNameFlagOld,             // 曾用名
    UPMarketNameFlagLong = 10000,   // 长名称
};

// MARK: - 排序列

typedef NSUInteger UPMarketSortColumn;

NS_ENUM(UPMarketSortColumn) {
    UPMarketSortColumnNone = 0,                // 默认排序
    UPMarketSortColumnChangeRatio,             // 涨跌幅
    UPMarketSortColumnSwingRatio,              // 振幅
    UPMarketSortColumnVolRatio,                // 量比
    UPMarketSortColumnTurnoverRate,            // 换手率
    UPMarketSortColumnDealAmount,              // 成交量
    UPMarketSortColumnNowPrice,                // 现价
    UPMarketSortColumnPremiumRate,             // 溢价率
    UPMarketSortColumnUpSpeed,                 // 涨速
    UPMarketSortColumnStockCode,               // 个股名称代码
    UPMarketSortColumnChangeValue,             // 涨跌值
    UPMarketSortColumnDealVol,                 // 成交量，也称总手
    UPMarketSortColumnNowVol,                  // 现手
    UPMarketSortColumnPERatio,                 // 市盈率
    UPMarketSortColumnPBRatio,                 // 市净率
    UPMarketSortColumnCircleMarketValue,       // 流通值
    UPMarketSortColumnTotalMarketValue,        // 总市值
    UPMarketSortColumnCommittee,               // 委比
    UPMarketSortColumnMGSY,                    // 每股收益
    UPMarketSortColumnMGJZC,                   // 每股净资产
    UPMarketSortColumnJZCSYL,                  // 净资产收益率
    UPMarketSortColumnYSZZL3Y,                 // 3年营收增长率
    UPMarketSortColumnJLSZZL3Y,                // 3年净利润增长率
    UPMarketSortColumnMainNetBuy,              // 主力净买
    UPMarketSortColumnMainNetRatio,            // 主力净占比
    UPMarketSortColumnDDX,                     // DDX
    UPMarketSortColumnDDY,                     // DDY
    UPMarketSortColumnDDZ,                     // DDZ
    UPMarketSortColumnDDF,                     // DDF
    UPMarketSortColumnDDX5D,                   // 5日DDX
    UPMarketSortColumnDDY5D,                   // 5日DDY
    UPMarketSortColumnDDX60D,                  // 60日DDX
    UPMarketSortColumnDDY60D,                  // 60日DDY
    UPMarketSortColumnYClose,                  // 昨收
    UPMarketSortColumnOpenPrice,               // 开盘价
    UPMarketSortColumnHighPrice,               // 最高价
    UPMarketSortColumnLowPrice,                // 最低价
    UPMarketSortColumn5DayChangeRatio,         // 5日涨跌幅
    UPMarketSortColumn10DayChangeRatio,        // 10日涨跌幅
    UPMarketSortColumn20DayChangeRatio,        // 20日涨跌幅
    UPMarketSortColumnMonthChangeRatio,        // 月涨跌幅
    UPMarketSortColumnSeasonChangeRatio,       // 季涨跌幅
    UPMarketSortColumnYearChangeRatio,         // 年涨跌幅
    UPMarketSortColumnThisYearChangeRatio,     // 年初至今涨跌幅
    UPMarketSortColumnZGJZ,                    // 转股价值
    UPMarketSortColumnTotalChg,                // 上市以来涨跌幅
    UPMarketSortColumnOptionalDate,            // 自选日期
    UPMarketSortColumnOptionalPrice,           // 自选价
    UPMarketSortColumnOptionalIncome,          // 自选收益
    
    UPMarketSortColumnDayMainIn = 100,         // 当日主力资金净流入
    UPMarketSortColumnDayMainRatio,            // 当日主力资金净占比
    UPMarketSortColumn3DayMainIn,              // 三日主力资金净流入
    UPMarketSortColumn3DayMainRatio,           // 三日主力资金净占比
    UPMarketSortColumn5DayMainIn,              // 五日主力资金净流入
    UPMarketSortColumn5DayMainRatio,           // 五日主力资金净占比
    UPMarketSortColumn10DayMainIn,             // 十日主力资金净流入
    UPMarketSortColumn10DayMainRatio,          // 十日主力资金净占比
    UPMarketSortColumn3MinMainIn,              // 3分钟主力资金净流入
    UPMarketSortColumn3MinMainRatio,           // 3分钟主力资金净占比
    UPMarketSortColumn5MinMainIn,              // 5分钟主力资金净流入
    UPMarketSortColumn5MinMainRatio,           // 5分钟主力资金净占比
    UPMarketSortColumn10MinMainIn,             // 10分钟主力资金净流入
    UPMarketSortColumn10MinMainRatio,          // 10分钟主力资金净占比
    UPMarketSortColumn30MinMainIn,             // 30分钟主力资金净流入
    UPMarketSortColumn30MinMainRatio,          // 30分钟主力资金净占比
    UPMarketSortColumn60MinMainIn,             // 60分钟主力资金净流入
    UPMarketSortColumn60MinMainRatio,          // 60分钟主力资金净占比

    //区间统计rangeStatsByDate排序类型
    UPMarketSortColumnERS_CODE,                 // code
    UPMarketSortColumnERS_NAME,                 // name
    UPMarketSortColumnERS_CHANGE,               //涨跌幅
    UPMarketSortColumnERS_MAINMONEYINFLOW,      //主力流入
    UPMarketSortColumnERS_MAINMONEYOUTFLOW,     //主力流出
    UPMarketSortColumnERS_MAINMONEYFLOW,        //主力净额
    UPMarketSortColumnERS_AMOUNT,               //总成交额
    UPMarketSortColumnERS_CIRCULAMARKETVALUE,   //流通市值
    UPMarketSortColumnERS_TUNOVERRATE,          //换手率
    UPMarketSortColumnERS_INFLOWDAYS,           //净流入天数
    UPMarketSortColumnERS_OUTFLOWDAYs,          //净流出天数

    UPMarketSortColumnWXGPCMAXRise = 1000,     // 五星股票池最大涨幅
    UPMarketSortColumnWXGPCNowRise,            // 五星股票池当日涨幅
    UPMarketSortColumnRGTJCNowRise,            // 热股推荐当日涨幅

    UPMarketSortColumnOther = UPMarketSortColumnOtherBaseValue, // 外部自定义排序定义基数
};

// MARK: - 排序顺序

typedef NSUInteger UPMarketSortOrder;

NS_ENUM(UPMarketSortOrder) {
    UPMarketSortOrderDefault = 0,              // 默认排序
    UPMarketSortOrderAscend,                   // 升序
    UPMarketSortOrderDescend,                  // 降序
};

// MARK: - 数据级别

typedef NSUInteger UPMarketDataLevel;

NS_ENUM(UPMarketDataLevel) {
    UPMarketDataLevelAll = 0,                  // 全量数据
    UPMarketDataLevelSimple,                   // 简版数据
    UPMarketDataLevelSimplest,                 // 最简版数据
};

// MARK: - Push标志

typedef NSUInteger UPMarketPushFlag;

NS_ENUM(UPMarketPushFlag) {
    UPMarketPushFlagNone = 0,
    UPMarketPushFlagRegister,
    UPMarketPushFlagUnregister,
    UPMarketPushFlagDisable,
    UPmarketPushFlagMonitor,
};

// MARK: - 股票类别  ！！！值已入库，不可更改！！！

typedef NSUInteger UPMarketStockCategory;

NS_ENUM(UPMarketStockCategory) {
    UPMarketStockCategory_NONE = 0,            // 默认类别
    UPMarketStockCategory_SZ_A,                // 深A        1
    UPMarketStockCategory_SZ_B,                // 深B        2
    UPMarketStockCategory_SH_A,                // 沪A        3
    UPMarketStockCategory_SH_B,                // 沪B        4
    UPMarketStockCategory_INDEX,               // 指数        5
    UPMarketStockCategory_Industry,            // 行业板块     6
    UPMarketStockCategory_Region,              // 地区板块      7
    UPMarketStockCategory_Concept,             // 题材板块      8
    UPMarketStockCategory_BOND,                // 债券        9
    UPMarketStockCategory_FUTURE,              // 期货        10
    UPMarketStockCategory_FUND,                // 基金        11
    UPMarketStockCategory_AH,                  // AH        12
    UPMarketStockCategory_HK,                  // 港股        13
    UPMarketStockCategory_US,                  // 美股        14
    UPMarketStockCategory_METAL,               // 现货        15
    UPMarketStockCategory_FOREIGN,             // 外汇        16
    //@Deprecated 使用setCode == UPMarketSetCodeXSB替代 category == UPMarketStockCategory_XSB的判断
    //之前新三板的市场所有的股票都是CATEGORY_XSB,无法使用category区分出对应的类型,
    //后续新增的新三板类型会进行区分,其category不再是CATEGORY_XSB
    UPMarketStockCategory_XSB,                 // 新三板       17
    UPMarketStockCategory_COIN,                // 数字币       18
    UPMarketStockCategory_HLTCDR,              // 沪伦通CDR    19
    UPMarketStockCategory_GGT_SH,              // 港股通沪      20
    UPMarketStockCategory_GGT_SZ,              // 港股通深      21
    UPMarketStockCategory_GGT_SHSZ,            // 港股通(深 + 沪) 22
    UPMarketStockCategory_OPTION,              // 个股期权      23
    UPMarketStockCategory_HK_Dark,             // 港股暗盘      24
    UPMarketStockCategory_SH_SG,               // 深圳证券申购   25
    UPMarketStockCategory_SZ_SG,               // 上海证券申     26
    UPMarketStockCategory_BJS,                 // 北京交易所     27
    UPMarketStockCategory_BH,                  // BH           28
};

// MARK: - 股票子类别

typedef NSUInteger UPMarketStockSubCategory;

NS_ENUM(UPMarketStockSubCategory) {
    UPMarketStockSubCategory_NONE = 0,
    /// 港股子类别
    UPMarketStockSubCategory_HK_MAIN = 1001,    // 港股主板
    UPMarketStockSubCategory_HK_CYB,            // 创业板
    UPMarketStockSubCategory_HK_FUND,           // 基金
    UPMarketStockSubCategory_HK_BOND,           // 债券
    UPMarketStockSubCategory_HK_W,              // 涡轮
    UPMarketStockSubCategory_HK_CBBC,           // 牛熊证
    /// 美股子类别
    UPMarketStockSubCategory_US_S = 2001,       // 普通股票
    UPMarketStockSubCategory_US_FUND,           // 基金
    UPMarketStockSubCategory_US_BOND,           // 债券
    UPMarketStockSubCategory_US_OTHER,          // 其他

    /// 新三板子类别
    UPMarketStockSubCategory_XSB_LW = 3001,         //两网及退市
    UPMarketStockSubCategory_XSB_XYZR_A,            //协议转让
    UPMarketStockSubCategory_XSB_ZSZR_A,            //做市转让
    UPMarketStockSubCategory_XSB_XYZR_B,            //协议转让
    UPMarketStockSubCategory_XSB_ZSZR_B,            //做市转让
    UPMarketStockSubCategory_XSB_ZS,                //三板指数
    UPMarketStockSubCategory_XSB_YZB,               //已转板
    UPMarketStockSubCategory_XSB_DSC,               //待审查
    UPMarketStockSubCategory_XSB_DGP,               //待挂牌
    UPMarketStockSubCategory_XSB_YXG,               //优先股
    UPMarketStockSubCategory_XSB_JJZRGP,            //挂牌股票，交易类型：竞价转让
    UPMarketStockSubCategory_XSB_LXJJZRGP,          //挂牌股票，交易类型：集合竞价+连续竞价转让
    UPMarketStockSubCategory_XSB_JJZRQQ,            //挂牌期权，交易类型：竞价转让
    UPMarketStockSubCategory_XSB_ZXJJZRQQ,          //挂牌期权，交易类型：集合竞价+连续竞价转让
    UPMarketStockSubCategory_XSB_YYSG,              //要约收购
    UPMarketStockSubCategory_XSB_YYHG,              //要约回购
    UPMarketStockSubCategory_XSB_FX,                //发行（询价）
    UPMarketStockSubCategory_XSB_FXSG,              //发行（申购）
    UPMarketStockSubCategory_XSB_KZZ,               //可转债
    UPMarketStockSubCategory_XSB_TSKZZ,             //退市可转债
    
    // 债券子类别
    UPMarketStockSubCategory_ZQ_GZ = 4001,          // 国债
    UPMarketStockSubCategory_ZQ_WXGZ,               // 无息国债
    UPMarketStockSubCategory_ZQ_GZFX,               // 国债分销
    UPMarketStockSubCategory_ZQ_GSZQFX,             // 公司债券分销
    UPMarketStockSubCategory_ZQ_QYZ,                // 企业债
    UPMarketStockSubCategory_ZQ_KZZ,                // 可转债
    UPMarketStockSubCategory_ZQ_GSZ,                // 公司债
    UPMarketStockSubCategory_ZQ_JRJGFXZ,            // 金融机构发行债
    UPMarketStockSubCategory_ZQ_ZYSQYHG,            // 质押式企业桂狗
    UPMarketStockSubCategory_ZQ_MDSZQHG,            // 买断式债券回购
    UPMarketStockSubCategory_ZQ_FLSKZZ,             // 分离式可转债
    UPMarketStockSubCategory_ZQ_SMZ,                // 私募债
    UPMarketStockSubCategory_ZQ_KJHSMZ,             // 可交换私募债
    UPMarketStockSubCategory_ZQ_ZQGSCJZ,            // 证券公司次级债
    UPMarketStockSubCategory_ZQ_OTHER,              // 其他债券
    
    // 港股暗盘子类别
    UPMarketStockSubCategory_HK_DARK_NORMAL = 5001, // 正常
    UPMarketStockSubCategory_HK_DARK_TS,            // 退市
    UPMarketStockSubCategory_HK_DARK_JJSSL,         // 即将上市
    
    // 创业板子类别
    UPMarketStockSubCategory_CYB_REG = 6001,       // 注册制

    // 基金子类别
    UPMarketStockSubCategory_Fund_ETF = 7001,       // ETF
    UPMarketStockSubCategory_Fund_LOF,              // LOF
    UPMarketStockSubCategory_Fund_REITs_SH,         // 上证REITs
    UPMarketStockSubCategory_Fund_REITs_SZ,         // 深证REITs
    UPMarketStockSubCategory_Fund_GGT_TRST          // 港股通ETF
};

// MARK: - 沪深港通类型  ！！！值已入库，不可更改！！！

typedef NSUInteger UPMarketHsgtType;

NS_ENUM(UPMarketHsgtType) {
    UPMarketHsgtTypeNone,                         // 默认值
    UPMarketHsgtTypeHgt,                          // 沪股通
    UPMarketHsgtTypeSgt,                          // 深股通
};

// MARK: - 币种

typedef NSUInteger UPMarketCoinType;

NS_ENUM(UPMarketCoinType) {
    UPMarketCoinTypeRMB,                       // 人民币
    UPMarketCoinTypeHKD,                       // 港元
    UPMarketCoinTypeUSD,                       // 美元
    UPMarketCoinTypeJPY,                       // 日元
    UPMarketCoinTypeGBP,                       // 英镑
    UPMarketCoinTypeEUR,                       // 欧元
    UPMarketCoinTypeCHF,                       // 瑞士法郎
    UPMarketCoinTypeCAD,                       // 加元
    UPMarketCoinTypeNone,                      // 未知类型
};

// MARK: - 匹配类型

typedef NSUInteger UPMarketCodeMatchType;

NS_ENUM(UPMarketCodeMatchType) {
    UPMarketCodeMatchTypeCode = 0,             // 股票代码
    UPMarketCodeMatchTypeName = 1,             // 股票名称
    UPMarketCodeMatchTypePinyin = 2            // 股票拼音
};

// MARK: - 交易状态

typedef NSUInteger UPMarketTradeStatus;

NS_ENUM(UPMarketTradeStatus) {
    UPMarketTradeStatusUnknown = 0,            // 未知状态
    UPMarketTradeStatusClosed,                 // 已收盘
    UPMarketTradeStatusAuction,                // 集合竞价
    UPMarketTradeStatusStopped,                // 停牌
    UPMarketTradeStatusNotOpen,                // 未开盘
    UPMarketTradeStatusWaiting,                // 等待开盘
    UPMarketTradeStatusTrading,                // 交易中
    UPMarketTradeStatusAMTrading,              // 早盘
    UPMarketTradeStatusPMTrading,              // 午盘
    UPMarketTradeStatusNoonBreak,              // 午休
    UPMarketTradeStatusTempStop,               // 临时停牌
    UPMarketTradeStatusPreOrder,               // 盘前下单（港股特有 原始状态 -1- 输入买卖盘数段:9：00~9：15）
    UPMarketTradeStatusPreNoCancel,            // 盘前不可撤单（港股特有 原始状态 -101- 不可取消时段 9：15~9：20）
    UPMarketTradeStatusPaused,                 // 暂停状态（港股特有 原始状态 -7- 9：28~9：30或者9：22~9：30）
    UPMarketTradeStatusIntervention,           // 交易所干预（港股特有 原始状态 -102- 通常是半日交易时用于收盘12：00~12：05）
    UPMarketTradeStatusCancelAble,             // 订单可取消（港股特有 原始状态 -104- 订单可取消12：30~13：00）
    UPMarketTradeStatusPriceFixing,            // 价格纠正（港股特有 原始状态 -105-  盘后价格纠正16：00~16：01）
    UPMarketTradeStatusPostOrder,              // 盘后下单（港股特有 原始状态 -5-   盘后下单 16：01~16：06）
    UPMarketTradeStatusPostNoCancel,           // 盘后不可撤单（港股特有 原始状态 -106-  盘后不可撤销 16：06~收盘）
    UPMarketTradeStatusRandomClosed,           // 随机收盘（港股特有 原始状态 -107- 随机收盘16：08~16：10
    UPMarketTradeStatusPreAuction,             // 开盘集合竞价(港股原始状态 -2-  9：20~9：28或者9：20~9：22)
    UPMarketTradeStatusPostAuction,            // 收盘集合竞价(港股原始状态 -4- )
    UPMarketTradeStatusClosing,                // 收盘中
    UPMarketTradeStatusAfterTrade,             // 盘后交易(科创板)
    UPMarketTradeStatusPosRandomMatching,      // 港股POS: 原始状态 -108- 随机对盘:09:20~09:22

    // ！！！以下数值已入库，不可更改！！！
    UPMarketTradeStatusStockExited = 100,      // 退市
    UPMarketTradeStatusStockSuspended = 101,   // 暂停上市
    UPMarketTradeStatusStockUpcoming = 102,    // 待上市
    UPMarketTradeStatusStockDeleted = 103,     // 删除
};

// MARK: - 委托单状态

typedef NSUInteger UPMarketOrderStatus;

NS_ENUM(UPMarketOrderStatus) {
    UPMarketOrderStatusNone = 0,               // 普通状态
    UPMarketOrderStatusTrade,                  // 交易状态
    UPMarketOrderStatusTradePart,              // 部分交易状态
    UPMarketOrderStatusCancel,                 // 撤销交易状态
    UPMarketOrderStatusBig,                    // 大单交易状态
    UPMarketOrderStatusTractor                 // 拖拉机单状态
};

//MARK: - 涨停尖兵状态

typedef NSUInteger UPMarketZTJBStatus;

NS_ENUM(UPMarketZTJBStatus) {
    UPMarketZTJBStatusNone,                    // 默认
    UPMarketZTJBStatusXN,                      // 蓄能
    UPMarketZTJBStatusCC,                      // 冲刺
    UPMarketZTJBStatusZT,                      // 涨停
};

// MARK: - 板块类型

typedef NSUInteger UPMarketBlockType;

NS_ENUM(UPMarketBlockType) {
    UPMarketBlockTypeNone = 0,                 // 默认板块
    UPMarketBlockTypeIndustry,                 // 行业板块
    UPMarketBlockTypeConcept,                  // 概念板块
    UPMarketBlockTypeRegion,                   // 区域板块
    UPMarketBlockTypeAll,                      // 所有板块
    UPMarketBlockTypeHKZB,                     // 港股主版
    UPMarketBlockTypeHKCYB,                    // 港股创业板
    UPMarketBlockTypeUSZGG,                    // 美股中概股
    UPMarketBlockTypeUSBP500,                  // 美股标普500
    UPMarketBlockTypeUSFamous,                 // 知名美股
    UPMarketBlockTypeUSMain,                   // 主要美股
    UPMarketBlockTypeZJS,                      // 中金所
    UPMarketBlockTypeSQS,                      // 上期所
    UPMarketBlockTypeDSS,                      // 大商所
    UPMarketBlockTypeZSS,                      // 郑商所
    UPMarketBlockTypeHSF,                      // 沪深基金
    UPMarketBlockTypeSHF,                      // 上证基金
    UPMarketBlockTypeSZF,                      // 深证基金
    UPMarketBlockTypeETF,                      // ETF
    UPMarketBlockTypeLOF,                      // LOF
    UPMarketBlockTypeFJF,                      // 分级基金
    UPMarketBlockTypeFBF,                      // 封闭基金
    UPMarketBlockTypeHBF,                      // 货币基金
    UPMarketBlockTypeBTB,                      // 比特币
    UPMarketBlockTypeYTF,                      // 以太坊
    UPMarketBlockTypeRBB,                      // 瑞波币
    UPMarketBlockTypeLTB,                      // 莱特币
    UPMarketBlockTypeHKRED,                    // 红筹股
    UPMarketBlockTypeHKBLUE,                   // 蓝筹股
    UPMarketBlockTypeHKGOG,                    // 国企股
    UPMarketBlockTypeHSGTHGT,                  // 沪股通
    UPMarketBlockTypeHSGTSGT,                  // 深股通
    UPMarketBlockTypeHSGTGGTH,                 // 港股通(沪) 优品主站, 走板块获取
    UPMarketBlockTypeHGGTGGTS,                 // 港股通(深) 优品主站, 走板块获取
    UPMarketBlockTypeHKETF,                    // 港股ETF
    UPMarketBlockTypeUSETF,                    // 美股ETF
    UPMarketBlockTypeUSDPZS,                   // 美股大盘指数
    UPMarketBlockTypeUSHYZS,                   // 美股行业指数
    UPMarketBlockTypeUSDZSP,                   // 美股大宗商品
    UPMarketBlockTypeUSWH,                     // 美股外汇
    UPMarketBlockTypeUSZQ,                     // 美股债券
    UPMarketBlockTypeUSCHETF,                  // 美股中国ETF
    UPMarketBlockTypeWhiteHorse,               // 白马股
    UPMarketBlockTypeNationalTeam,             // 国家队
    UPMarketBlockTypeHeavyWeight,              // 权重股
    UPMarketBlockTypeShSzA,                    // 沪深A股
    UPMarketBlockTypeShA,                      // 上证A股
    UPMarketBlockTypeSzA,                      // 深证A股
    UPMarketBlockTypeShSzB,                    // 沪深B股
    UPMarketBlockTypeShB,                      // 上证B股
    UPMarketBlockTypeSzB,                      // 深证B股
    UPMarketBlockTypeZXB,                      // 中小板
    UPMarketBlockTypeCYB,                      // 创业板
    UPMarketBlockTypeKCB,                      // 科创板
    UPMarketBlockTypeShSzIndex,                // 沪深指数
    UPMarketBlockTypeCDR,                      // CDR
    UPMarketBlockTypeHLT,                      // 沪伦通
    UPMarketBlockTypeFXJS,                     // 风险警示
    UPMarketBlockTypeTSZL,                     // 退市整理
    UPMarketBlockTypeBondShSz,                 // 沪深债券
    UPMarketBlockTypeBondSh,                   // 上证债券
    UPMarketBlockTypeBondSz,                   // 深证债券
    UPMarketBlockTypeBondCountry,              // 国债
    UPMarketBlockTypeBondRegion,               // 地方债
    UPMarketBlockTypeBondEnterprise,           // 企业债
    UPMarketBlockTypeBondFinance,              // 金融债
    UPMarketBlockTypeBondCompany,              // 公司债
    UPMarketBlockTypeBondTransable,            // 可转债
    UPMarketBlockTypeXsbIndex,                 // 三板指数
    UPMarketBlockTypeXsbGPGS,                  // 挂牌公司
    UPMarketBlockTypeXsbZSZR,                  // 做市转让
    UPMarketBlockTypeXsbJJZR,                  // 集合竞价转让
    UPMarketBlockTypeXsbCXC,                   // 创新层
    UPMarketBlockTypeXsbJCC,                   // 基础层
    UPMarketBlockTypeXsbYXG,                   // 优先股
    UPMarketBlockTypeXsbLwTs,                  // 两网及退市
    UPMarketBlockTypeZLHY,                     // 主力合约
    UPMarketBlockTypeYPQH,                     // 夜盘期货
    UPMarketBlockTypeShZQHG,                   // 上海债券逆回购
    UPMarketBlockTypeSZZQHG,                   // 深圳债券逆回购
    UPMarketBlockTypeKCBFXJS,                  // 科创板风险警示
    UPMarketBlockTypeKCBTSZL,                  // 科创板退市整理
    UPMarketBlockTypeGDR,                      // GDR
    UPMarketBlockTypeKCBCDR,                   // 科创板CDR
    UPMarketBlockTypeGGTHOEM,                  // 港股通(沪) 三方券商, 走类型获取
    UPMarketBlockTypeGGTSOEM,                  // 港股通(深) 三方券商, 走类型获取
    UPMarketBlockTypeGGTETF,                   // 港股通ETF
    UPMarketBlockTypeIndexSH,                  // 上证指数成分股
    UPMarketBlockTypeIndexSZ,                  // 深圳成指成分股
    UPMarketBlockTypeIndexSH50,                // 上证50成分股
    UPMarketBlockTypeIndexZZ500,               // 中证500成分股
    UPMarketBlockTypeIndexHS300,               // 沪深300成分股
    UPMarketBlockTypeIndexZXB,                 // 中小板成分股
    UPMarketBlockTypeIndexCYB,                 // 创业板成分股
    UPMarketBlockTypeIndexHK,                  // 港股指数
    UPMarketBlockTypeJXC,                      // 新三板精选层
    UPMarketBlockTypeLXJJ,                     // 新三板连续竞价
    UPMarketBlockTypeFX,                       // 新三板发行股票,不支持type2stock接口查询
    UPMarketBlockTypeYY,                       // 新三板要约股票,不支持type2stock接口查询
    UPMarketBlockTypeSHCXQY,                   // 上海创新企业
    UPMarketBlockTypeXSBAll,                   // 新三板所有分类
    UPMarketBlockTypeXSBSRGP,                  // 新三板首日挂牌
    UPMarketBlockTypeXSBZFGP,                  // 新三板增发股票
    UPMarketBlockTypeHSSubNew,                 // 次新股
    UPMarketBlockTypeHSNew,                    // 新股
    UPMarketBlockTypeDSE,                      // 孟加拉
    UPMarketBlockTypeMainHY,                   // 实时主力合约
    UPMarketBlockTypeXSBXYZR,                  // 新三板协议转让
    UPMarketBlockTypeSHEnergy,                 // 上期能源
    UPMarketBlockTypeHLFX,                     // 交叉汇率
    UPMarketBlockTypeHLCNY,                    // 人民币汇率
    UPMarketBlockTypeCYBCDR,                   // 创业板CDR
    UPMarketBlockTypeMainBoardCDR,             // 主板CDR
    UPMarketBlockTypeREITsSH,                  // 上证REITs
    UPMarketBlockTypeREITsSZ,                  // 深证REITs
    UPMarketBlockTypeBJS,                      // 北交所挂牌公司股票
    UPMarketBlockTypeBJ_ZS,                    // 北交所指数
    UPMarketBlockTypeHSB_ALL,                  // 上海Ａ股+深圳Ａ股+深圳中小企业板+创业板+北交所挂牌公司股票
    UPMarketBlockTypeGlobalIndex,              // 全球指数
    UPMarketBlockTypeBondBJ,                   // 北交所债券
    UPMarketBlockTypeBondBJ_KZZ,               // 北交所可转债
    UPMarketBlockTypeBondHSJ,                  // 沪深京债券
    UPMarketBlockTypeBONDHSJ_KZZ,              // 沪深京可转债
    UPMarketBlockTypeBONDXSB,                  // 新三板债券
    UPMarketBlockTypeBONDXSB_KZZ,              // 新三板可转债
    UPMarketBlockTypeBondTS,                   // 退市整理可转债
    UPMarketBlockTypeHK,                       // 港股板块
    UPMarketBlockTypeREITsALL,                 // 全部REITs
    UPMarketBlockTypeCommonWH,                 // 常用外汇
    UPMarketBlockTypeHLUS,                     // 美元外汇
    UPMarketBlockTypeHLALL,                    // 全部汇率：交叉、人民币和美元外汇
    UPMarketBlockTypeQQZS,                     // 全球指数，百度app解决崩溃问题，用于映射新版本需要的全球指数
    UPMarketBlockTypeBKTS,                     // 特色板块
    
    /// 沪深指数成分股
    UPMarketBlockTypeIndex_000001 = 300,        // 上证指数 000001
    UPMarketBlockTypeIndex_000002,              // A股指数 000002
    UPMarketBlockTypeIndex_000003,              // B股指数 000003
    UPMarketBlockTypeIndex_399001,              // 深证成指 399001
    UPMarketBlockTypeIndex_399002,              // 深成指R 399002
    UPMarketBlockTypeIndex_399003,              // 成分B指 399003
    UPMarketBlockTypeIndex_399005,              // 中小板指 399005
    UPMarketBlockTypeIndex_399006,              // 创业板指 399006
    UPMarketBlockTypeIndex_399107,              // 深圳A指 399107
    UPMarketBlockTypeIndex_399108,              // 深圳B指 399108

    /// 新三板指数成分股
    UPMarketBlockTypeXSBIndexSBZS = 400,         // 三板做市
    UPMarketBlockTypeXSBIndexSBCZ,               // 三板成指
    UPMarketBlockTypeXSBIndexSBYY,               // 三板医药
    UPMarketBlockTypeXSBIndexCXCZ,               // 创新成指
    UPMarketBlockTypeXSBIndexSBLT,               // 三板龙头
    UPMarketBlockTypeXSBIndexSBZZ,               // 三板制造
    UPMarketBlockTypeXSBIndexSBFW,               // 三板服务
    UPMarketBlockTypeXSBIndexSBXF,               // 三板消费
    UPMarketBlockTypeXSBIndexSBYF,               // 三板研发
    UPMarketBlockTypeXSBIndexSBHY,               // 三板活跃
    UPMarketBlockTypeXSBIndexJCZSZR,               // 三板基础层做市转让
    UPMarketBlockTypeXSBIndexJCJJZR,               // 基础层集合竞价
    UPMarketBlockTypeXSBIndexCXZSZR,               // 创新层做市转让
    UPMarketBlockTypeXSBIndexCXJJZR,               // 创新层集合竞价
};

// MARK: - K线类型

typedef NSUInteger UPMarketKlineType;

NS_ENUM(UPMarketKlineType) {
    UPMarketKlineTypeDay = 0,                  // 日K
    UPMarketKlineTypeWeek,                     // 周k
    UPMarketKlineTypeMonth,                    // 月K
    UPMarketKlineTypeMinute1,                  // 1分钟K线
    UPMarketKlineTypeMinute5,                  // 5分钟K线
    UPMarketKlineTypeMinute15,                 // 15分钟K线
    UPMarketKlineTypeMinute30,                 // 30分钟K线
    UPMarketKlineTypeMinute60,                 // 60分钟K线
    UPMarketKlineTypeSeason,                   // 季K
    UPMarketKlineTypeYear,                     // 年K
    UPMarketKlineTypeMinute120,                // 120分钟K线
};

// MARK: - 分时类型

/**
 * ！！！Note:周期类型，值不能同UPMarketKlineType的值相同 ！！！
 */
typedef NSUInteger UPMarketMinuteType;

NS_ENUM(UPMarketMinuteType) {
    UPMarketMinuteRTMin = 100,                  // 当日分时
};


// MARK: - 资金类型

typedef NSUInteger UPMarketMoneyType;

NS_ENUM(UPMarketMoneyType) {
    UPMarketMoneyTypeDay_1,                    // 1日资金
    UPMarketMoneyTypeDay_3,                    // 3日资金
    UPMarketMoneyTypeDay_5,                    // 5日资金
    UPMarketMoneyTypeDay_10,                   // 10日资金
    UPMarketMoneyTypeMin_10,                   // 10分钟资金
    UPMarketMoneyTypeMin_30,                   // 30分钟资金
    UPMarketMoneyTypeMin_60,                   // 60分钟资金
};

// MARK: - L2选股池类型

typedef NSUInteger UPMarketLevel2PoolType;

NS_ENUM(UPMarketLevel2PoolType) {
    UPMarketLevel2PoolTypeTractors,            // 拖拉机单
    UPMarketLevel2PoolTypeUpstage,             // 顶级挂单
    UPMarketLevel2PoolTypeMainForce,           // 主力撤单
};

// MARK: - DDE 类型

typedef NSUInteger UPMarketDDEType;

NS_ENUM(UPMarketDDEType) {
    UPMarketDDETypeDay,                        // 日DDE
    UPMarketDDETypeWeek,                       // 周DDE
    UPMarketDDETypeMonth,                      // 月DDE
    UPMarketDDETypeMinute1,                    // 1分钟DDE
    UPMarketDDETypeMinute5,                    // 5分钟DDE
    UPMarketDDETypeMinute15,                   // 15分钟DDE
    UPMarketDDETypeMinute30,                   // 30分钟DDE
    UPMarketDDETypeMinute60,                   // 60分钟DDE
    UPMarketDDETypeRealTime,                   // 实时分时
    UPMarketDDETypeSeason,                     // 季DDE
    UPMarketDDETypeYear,                       // 年DDE
    UPMarketDDETypeMinute120,                  // 120分钟DDE
};

// MARK: - 指标类型

typedef NSUInteger UPMarketIndexType;

NS_ENUM(UPMarketIndexType) {
    UPMarketIndexTypeWXCP,                      // 五星操盘
    UPMarketIndexTypeCPXHD,                     // 操盘信号灯
    UPMarketIndexTypeDJWTD,                     // 点金王通道
    UPMarketIndexTypeZTJB,                      // 涨停尖兵
    UPMarketIndexTypeXSBDW,                     // 晓胜波段王
    UPMarketIndexTypeZLT0,                      // 主力T+0
    UPMarketIndexTypeQKJCY,                     // 乾坤决策眼
    UPMarketIndexTypeZJDL,                      // 资金动力
    UPMarketIndexTypeXSTP,                      // 晓盛分时图谱
    UPMarketIndexTypeWXGPC,                     // 五星股票池
    UPMarketIndexTypeDPYLZC,                    // 大盘K线压力支撑
    UPMarketIndexTypeJZXL,                      // 九转序列
    UPMarketIndexTypeRGTJ,                      // 热股推荐
    UPMarketIndexTypeDDLDJ,                     // 顶底雷达机
    UPMarketIndexTypeZLGJ,                      // 慧眼识庄:主力攻击
    UPMarketIndexTypeZLCT,                      // 慧眼识庄:主力撤退
    UPMarketIndexTypeHJJC,                      // 资金先锋:黄金交叉
    UPMarketIndexTypeSWJC,                      // 资金先锋:死亡交叉
    UPMarketIndexTypeLS,                        // 闪电突击:拉升
    UPMarketIndexTypeTS,                        // 闪电突击:跳水
    UPMarketIndexTypeDYYH,                      // L2策略风云：多赢一号
    UPMarketIndexTypeDYEH,                      // L2策略风云：多赢二号
    UPMarketIndexTypeHJY,                       // L2策略风云：黄金眼
    UPMarketIndexTypePKS,                       // L2策略风云：盘口杀
    UPMarketIndexTypeDXTJ,                      // 超级短打：短线突击
    UPMarketIndexTypeDDCM,                      // 超级短打：短打筹码
    UPMarketIndexTypeDDDN,                      // 超级短打：短打动能
    UPMarketIndexTypeZJWW,                      // 精选股票：资金为王
    UPMarketIndexTypeZTXN,                      // 精选股票：涨停蓄能
    UPMarketIndexTypeZJBB,                      // 战绩播报
    UPMarketIndexTypeZJB,                       // 战绩榜
    UPMarketIndexTypeHisDBDX,                   // 打板低吸历史数据
    UPMarketIndexTypeGNNTop,                    // 股牛牛成功率和平均收益
    UPMarketIndexTypeYQRG,                      // 舆情热股
    UPMarketIndexTypeQSRG,                      // 强势热股
    UPMarketIndexTypeJZRG,                      // 价值热股
    UPMarketIndexTypeXBLD,                      // 谐波雷达
    UPMarketIndexTypeFJLD,                      // 伏击雷达
    UPMarketIndexTypeZLZD,                      // 主力阵地
    UPMarketIndexTypeMainDDLD,                  // 主图顶底雷达机
    UPMarketIndexTypeLTJJ,                      // 龙头狙击股池
    UPMarketIndexTypeLTJJGG,                    // 龙头狙击个股
    UPMarketIndexTypeLTJJZJB,                   // 龙头狙击战绩榜
    UPMarketIndexTypeLTJJHis,                   // 龙头狙击历史股池
    UPMarketIndexTypeLTJJStat,                  // 龙头狙击统计数据
    UPMarketIndexTypeHHBGC,                     // 红黄白股池
    UPMarketIndexTypeHHBGG,                     // 红黄白个股指标
    UPMarketIndexTypeHHBZTYZZJB,                // 红黄白涨停因子战绩榜
    UPMarketIndexTypeHHBZTYZHis,                // 红黄白涨停因子历史数据
    UPMarketIndexTypeHHBZTYZStatis,             // 红黄白涨停因子统计数据
    UPMarketIndexTypeCDJJ,                      // 超短掘金
    UPMarketIndexTypeCDJJZJB,                   // 超短掘金战绩榜
    UPMarketIndexTypeCDJJHis,                   // 超短掘金历史数据
    UPMarketIndexTypeCDJJStatis,                // 超短掘金统计数据
    UPMarketIndexTypeZTKline,                   // 涨停K线
    UPMarketIndexTypeBDKX,                      // 波段K线
    UPMarketIndexTypeSJNX,                      // 四季牛熊
    UPMarketIndexTypeFGPH,                      // 风格偏好
    UPMarketIndexTypeCWPZ,                      // 仓位配置
    UPMarketIndexTypeLTQDXH,                    // 龙头信号池
    UPMarketIndexTypeLTQDJK,                    // 启动监控池
    UPMarketIndexTypeLTZSXH,                    // 主升信号池
    UPMarketIndexTypeLTZSJK,                    // 主升监控池
    UPMarketIndexTypeEBDL,                      // 龙头二波股池
};

// MARK: - L2策略风云指标子类型

typedef NSUInteger UPMarketIndexSubType;

NS_ENUM(UPMarketIndexSubType) {
    // 策略风云:多赢一号
    UPMarketCLFYIndexSubTypeDXHN = 1,           // 短线红牛
    UPMarketCLFYIndexSubTypeCXZJ,               // 长线资金
    UPMarketCLFYIndexSubTypeDXZJ,               // 短线资金
    UPMarketCLFYIndexSubTypeYDSM,               // 异动扫描
    UPMarketCLFYIndexSubTypeRDJJ,               // 热点狙击
    // 策略风云:多赢二号
    UPMarketCLFYIndexSubTypeFLZT,               // 飞龙在天(主力出击)
    UPMarketCLFYIndexSubTypeZLZD,               // 主力阵地
    UPMarketCLFYIndexSubTypeDBJJ,               // 底部狙击
    UPMarketCLFYIndexSubTypeZJZX,               // 资金转向
    UPMarketCLFYIndexSubTypeJCDL,               // 金叉动力
    // 策略风云:黄金眼
    UPMarketCLFYIndexSubTypeYYQZ,               // 鹰眼擒庄(与庄共舞)
    UPMarketCLFYIndexSubTypeSZLD,               // 神庄雷达
    UPMarketCLFYIndexSubTypeFJLD,               // 伏击雷达
    UPMarketCLFYIndexSubTypeDBLD,               // 顶部雷达
    UPMarketCLFYIndexSubTypeZZLD,               // 追涨雷达
    // 策略风云:盘口杀
    UPMarketCLFYIndexSubTypeHMLS,               // 黑马猎杀(操盘赢家)
    UPMarketCLFYIndexSubTypeJSCP,               // 绝杀操盘
    UPMarketCLFYIndexSubTypeFZJJ,               // 反转狙击
    UPMarketCLFYIndexSubTypeZJBD,               // 资金波动
    // 龙头信号池
    UPMarketCLFYIndexSubTypeQBB,                // 全部版
    UPMarketCLFYIndexSubTypeCDB,                // 超跌板
    UPMarketCLFYIndexSubTypeQSB,                // 趋势版
    UPMarketCLFYIndexSubTypeFLB                 // 放量版
};

// MARK: - 日志输出类型
typedef NSUInteger UPMarketLogLevel;

NS_ENUM(UPMarketLogLevel) {
    UPMarketLogLevelDefault,                   // log输出到console，并保存到文件
    UPMarketLogLevelConsole,                   // log输出到console
    UPMarketLogLevelFile,                      // log保存到文件(/Documents/Log/UPMarketSDKLog.txt)
    UPMarketLogLevelNone,                      // 不输出log
};

// MARK: - 个股标签类型
typedef NSUInteger UPMarketStockLabelType;

NS_ENUM(UPMarketStockLabelType) {
    // 静态标签
    UPMarketStockLabelTypeRZRQ = 1,   // 融资融券
    UPMarketStockLabelTypeXG,         // 新股
    UPMarketStockLabelTypeCXG,        // 次新股
    UPMarketStockLabelTypeGZY,        // 高质押

    // 动态标签
    UPMarketStockLabelTypeZT = 100,   // 涨停
};

// MARK: - 热股类型
typedef NSUInteger UPMarketHotStockType;

NS_ENUM(UPMarketHotStockType) {
    UPMarketHotStockTypeYQRG = 190,   // 舆情热股
    UPMarketHotStockTypeQSRG,         // 强势热股
    UPMarketHotStockTypeJZRG,         // 价值热股
};

// MARK: - 热股类型
typedef NSUInteger UPMarketHKWarrantType;

NS_ENUM(UPMarketHKWarrantType) {
    UPMarketHKWarrantTypeNone = 0,  // 未知
    UPMarketHKWarrantTypeWL,        // 涡轮
    UPMarketHKWarrantTypeNX,        // 牛熊证
    UPMarketHKWarrantTypeWLNX,        //涡轮+牛熊
};


// MARK: - 股票因子库类型

typedef NSUInteger UPMarketStockFactorType;

NS_ENUM(UPMarketStockFactorType) {
    UPMarketStockFactorTypeLHB = 1,     // 龙虎榜
    UPMarketStockFactorTypeFZT,         // 封涨停（游资）
    UPMarketStockFactorTypeBX,          // 北向持仓变动
    UPMarketStockFactorTypeGGTZJ,       // 港股通资金
};



typedef NSUInteger UPMarketSubjectChangeType;

NS_ENUM(UPMarketSubjectChangeType) {
    UPMarketSubjectChangeTypeAll,               // 全部
    UPMarketSubjectChangeTypeRapidUP,           // 急速拉升
    UPMarketSubjectChangeTypeRapidBounce,       // 快速反弹
    UPMarketSubjectChangeTypeRapidDown,         // 加速下跌
    UPMarketSubjectChangeTypeStockRapidUP       // 个股极速拉升封涨停
};

// MARK: - 新三板转让类型
typedef NSUInteger UPMarketXSBZRType;

NS_ENUM(UPMarketXSBZRType) {
    UPMarketXSBZRTypeXY = 1,   // 协议转让
    UPMarketXSBZRTypeZS,       // 做市转让
    UPMarketXSBZRTypeJJAndLXJJ, // 集合竞价+连续集合竞价转让
    UPMarketXSBZRTypeJJ,        // 集合竞价转让
};

// MARK: - 新三板转让状态
typedef NSUInteger UPMarketXSBZRStatus;
    
NS_ENUM(UPMarketXSBZRStatus) {
    UPMarketXSBZRStatusNormal = 1, // 正常状态
    UPMarketXSBZRStatusSRGP,       // 首日挂牌
    UPMarketXSBZRStatusXZGP,       // 新增股票挂牌转让
    UPMarketXSBZRStatusXJ,         // 询价
    UPMarketXSBZRStatusSG,         // 申购
};
    
// MARK: - 新三板分层信息
typedef NSUInteger UPMarketXSBFCType;

NS_ENUM(UPMarketXSBFCType) {
    UPMarketXSBFCTypeCXC = 1,   // 创新层
    UPMarketXSBFCTypeJCC,       // 基础层
    UPMarketXSBFCTypeJXC,       // 精选层
};

// MARK: - 新三板停牌状态
typedef NSUInteger UPMarketXSBTPStatus;

NS_ENUM(UPMarketXSBTPStatus) {
    UPMarketXSBTPStatusUnKnown = 0,     // 未知
    UPMarketXSBTPStatusTrading,         // 正常交易
    UPMarketXSBTPStatusStop,            // 停牌，不接受申报
    UPMarketXSBTPStatusTempStop,        // 临时停牌，接受申报
};

// MARK: - 新三板除权除息状态
typedef NSUInteger UPMarketXSBCQCXStatus;

NS_ENUM(UPMarketXSBCQCXStatus) {
    UPMarketXSBCQCXStatusUnKnown = 0,   // 未知
    UPMarketXSBCQCXStatusNormal,        // 正常状态
    UPMarketXSBCQCXStatusCQ,            // 除权
    UPMarketXSBCQCXStatusCX,            // 除息
    UPMarketXSBCQCXStatusCQCX,          // 除权除息
};

// MARK: - 新三板表决权差异
typedef NSUInteger UPMarketXSBRight;

NS_ENUM(UPMarketXSBRight) {
    UPMarketXSBRightUnKnown = 0,    // 未知
    UPMarketXSBRightSame,           // 表决权相同
    UPMarketXSBRightDiff,           // 表决权差异
};

// MARK: - 新三板发行方式
typedef NSUInteger UPMarketXSBFXMethod;

NS_ENUM(UPMarketXSBFXMethod) {
    UPMarketXSBFXMethodUnKnown = 0,    // 未知
    UPMarketXSBFXMethodXJ,             // 询价
    UPMarketXSBFXMethodDJ,             // 定价
    UPMarketXSBFXMethodJJ,             // 竞价
};

// MARK: - 打板擒牛股池
typedef NSUInteger UPMarketQNStockType;

NS_ENUM(UPMarketQNStockType) {
    UPMarketQNStockTypeDB = 1,   // 打板
    UPMarketQNStockTypeDX,       // 低吸
};

// MARK: - 涨停K线类型
typedef NSUInteger UPMarketZTKLineType;

NS_ENUM(UPMarketZTKLineType) {
    UPMarketZTKLineTypeNone = 0,    // 未知
    UPMarketZTKLineTypeZT,          // 涨停
    UPMarketZTKLineTypeDT,          // 跌停
    UPMarketZTKLineTypeZTKB,        // 涨停开板(炸板)
    UPMarketZTKLineTypeTPFB,        // 突破反包
    UPMarketZTKLineTypeZDFB,        // 震荡反包
    UPMarketZTKLineTypeFTFB,        // 反弹反包

};

// MARK: - 期权认购认沽类型
typedef NSUInteger UPMarketOptionCPType;

NS_ENUM(UPMarketOptionCPType) {
    UPMarketOptionCPTypeCall = 0,   // 认购
    UPMarketOptionCPTypePut,        // 认沽
};

// MARK: - 期权类型
typedef NSUInteger UPMarketOptionType;

NS_ENUM(UPMarketOptionType) {
    UPMarketOptionTypeEU,           // 欧式期权
    UPMarketOptionTypeAM,           // 美式期权
};

// MARK: - 新三板发行股票发行类型
typedef NSUInteger UPMarketFXType;

NS_ENUM(UPMarketFXType) {
    UPMarketFXTypeNone = 0,     // 未知类型
    UPMarketFXTypeXJ,           // 询价
    UPMarketFXTypeSG,           // 申购
    UPMarketFXTypeAll,          // 询价 + 申购
};

// MARK: - 新三板要约股票要约类型
typedef NSUInteger UPMarketYYType;

NS_ENUM(UPMarketYYType) {
    UPMarketYYTypeSG = 1,       // 收购
    UPMarketYYTypeHG,           // 回购
};

// MARK: - 新三板发行股票发行类型
typedef NSUInteger UPMarketYYStatus;

NS_ENUM(UPMarketYYStatus) {
    UPMarketYYStatusYYSB = 1,       // 要约申报
    UPMarketYYStatusZZZX,           // 终止执行
};


// MARK: - 复权模式
typedef NSUInteger UPMarketFQMode;

NS_ENUM(UPMarketFQMode) {
    UPMarketFQModeNone = 0,         // 不复权
    UPMarketFQModeFront,            // 前复权
    UPMarketFQModeBack,             // 后复权
};

// MARK: - 竞价方向
typedef NSUInteger UPMarketActBSFlag;

NS_ENUM(UPMarketActBSFlag) {
    UPMarketActBSFlagUnKnown = 0,   // 未知
    UPMarketActBSFlagB,             // 买盘多
    UPMarketActBSFlagS,             // 卖盘多
    UPMarketActBSFlagN,             // 相等
};

// MARK: - 新三板股票关联发行要约股票类型
typedef NSUInteger UPMarketRelationStockType;

NS_ENUM(UPMarketRelationStockType) {
    UPMarketRelationStockTypeUnKnown = 0,    // 未知
    UPMarketRelationStockTypeYY,             // 要约
    UPMarketRelationStockTypeFX,             // 发行
    UPMarketRelationStockTypeKZZ,            // 可转债
    UPMarketRelationStockTypeAH,             // AH股
    UPMarketRelationStockTypeBH,             // BH股
    UPMarketRelationStockTypeUH,             // 美股ADR
};

// MARK: - 个股异动类型
typedef NSUInteger UPMarketStockChangeType;
NS_ENUM(UPMarketStockChangeType) {
    UPMarketStockChangeTypeHigh = 0,    // 创新高
    UPMarketStockChangeTypeLow,         // 创新低
    UPMarketStockChangeTypeMA,          // 上坡均线
};

// MARK: 短线精灵挂单类型
typedef NSUInteger UPMarketDXJLType;
NS_ENUM(UPMarketDXJLType) {
    UPMarketDXJLType_SUPER_BUY = 0,        //顶级买单
    UPMarketDXJLType_SUPER_SELL,           //顶级卖单
    UPMarketDXJLType_UP_STOP,              //封涨停板
    UPMarketDXJLType_DOWN_STOP,            //封跌停板
    UPMarketDXJLType_OPEN_UP_STOP,         //打开涨停板
    UPMarketDXJLType_OPEN_DOWN_STOP,       //打开跌停板
    UPMarketDXJLType_POST_LARGE_BUY,       //大买盘
    UPMarketDXJLType_POST_LARGE_SELL,      //大卖盘
    UPMarketDXJLType_SUPER_LARGE_BUY,      //超大买单
    UPMarketDXJLType_SUPER_LARGE_SELL,     //超大卖单
    UPMarketDXJLType_LARGE_BUY_IN,         //大笔买入
    UPMarketDXJLType_LAGRGE_SELL_OUT,      //大笔卖出
    UPMarketDXJLType_BUY_FEN_DAN,          //拖拉机买
    UPMarketDXJLType_SELL_FEN_DAN,         //拖拉机卖
    UPMarketDXJLType_ROCKET_RUSH,          //火箭发射
    UPMarketDXJLType_QUICK_BUCKUP,         //快速反弹
    UPMarketDXJLType_HIGH_DIVING,          //高台跳水
    UPMarketDXJLType_QUICK_DOWN,           //加速下跌
    UPMarketDXJLType_CANCEL_BUY,           //大笔撤买
    UPMarketDXJLType_CANCEL_SELL,          //大笔撤卖

    UPMarketDXJLType_ALL,  // 内部使用标志，外部勿用
};

// MARK: - 上证云SDK
typedef NSInteger UPMarketQueryDir;
NS_ENUM(UPMarketQueryDir) {
    UPMarketQueryDirNewest = 0,             // 最新
    UPMarketQueryDirNewer,                  // 相对较新
    UPMarketQueryDirOlder,                  // 查询相对较老
};

// MARK: - AH股请求类型
typedef NS_ENUM(NSInteger, UPMarketAMType) {
    UPMarketAMTypeAH = 0,       // 默认AH
    UPMarketAMTypeBH,       // BH
    UPMarketAMTypeALL,      // ALL
    UPMarketAMTypeUH,       // 美股ADR
};

typedef NS_ENUM(NSInteger, UPMarketPremiumType) {
    UPMarketPremiumTypeDefault = 0,       // 无效数据,默认AH
    UPMarketPremiumTypeAH,                // AH溢价，即(A股价-港股价)/港股价
    UPMarketPremiumTypeHA,                // HA溢价，即(港股价-A股价)/A股价
};

// MARK: - 港股 参考平衡方向
typedef NSInteger UPMarketHKBalanceDir;
NS_ENUM(UPMarketHKBalanceDir) {
    UPMarketHKBalanceDirUnavailable = 0,          // 不可用
    UPMarketHKBalanceDirBSEqual,                  // 买=卖
    UPMarketHKBalanceDirBProfit,                  // 买入盈余
    UPMarketHKBalanceDirSProfit,                  // 卖出盈余
};

// MARK: - 除权除息状态
typedef NSUInteger UPMarketCQCXStatus;

NS_ENUM(UPMarketCQCXStatus) {
    UPMarketCQCXStatusUnKnown = 0,   // 未知
    UPMarketCQCXStatusNormal,        // 正常状态
    UPMarketCQCXStatusCQ,            // 除权
    UPMarketCQCXStatusCX,            // 除息
    UPMarketCQCXStatusCQCX,          // 除权除息
};

// 北交所新三板交易类型
typedef NSUInteger UPMarketBJXSBTradeType;

NS_ENUM(UPMarketBJXSBTradeType) {
    UPMarketBJXSBTradeTypeUNKNOWN = 0,       // 未知转让类型
    UPMarketBJXSBTradeTypeXYZR = 1,          // 协议转让
    UPMarketBJXSBTradeTypeZSZR = 2,          // 做市转让
    UPMarketBJXSBTradeTypeLXJHJJZR = 3,      // 集合竞价+连续集合竞价转让
    UPMarketBJXSBTradeTypeJHJJZR = 4,        // 集合竞价转让
    UPMarketBJXSBTradeTypeFX = 5             // 发行方式
};


// 北交所新三板交易类型
typedef NSUInteger UPMarketZDFBType;

NS_ENUM(UPMarketZDFBType) {
    UPMarketZDFBTypeSZA = 1,        //深圳A股
    UPMarketZDFBTypeSHA = 2,        //上海A股
    UPMarketZDFBTypeBJ = 3,         //北交所股票
    UPMarketZDFBTypeSZSHA = 4,      //沪深A股
    UPMarketZDFBTypeSHJ = 5,        //沪深京
    UPMarketZDFBTypeHK = 6,         //港股
    UPMarketZDFBTypeAMEX = 7,       //美股
};

//---------区间统计请求类型（个股、指数、板块）------------//
typedef NSUInteger UPMarketRangeType;

NS_ENUM(UPMarketRangeType) {
    UPMarketRangeTypeStock = 0,
    UPMarketRangeTypeIndex = 1,
    UPMarketRangeTypeBlock = 2,
   
};

typedef NSUInteger UPMarketQXModelType;

NS_ENUM(UPMarketRangeType) {
    UPMarketQXModelTypeNone = 0,
    UPMarketQXModelTypeFront = 1,
    UPMarketQXModelTypeBack = 2,
   
};

/// ---------排序过滤类型------------
typedef NSUInteger UPMarketRankFilterType;

NS_ENUM(UPMarketRangeType) {
    UPMarketRangeTypeST = 1,        //过滤ST
    UPMarketRangeTypeTS = 2,        //过滤退市整理
    UPMarketRangeTypeNEWSTOCK = 3,      //过滤新股
    UPMarketRangeTypeSUB_NEWSTOCK = 4,  //过滤次新股
    UPMarketRangeTypeKCB = 5,           //过滤科创板
    UPMarketRangeTypeCYB = 6,           //过滤创业板
    UPMarketRangeTypeBJ = 7,            //过滤北证
    UPMarketRangeTypeSZSH = 8,          //过滤沪深股票(只返回北证)
};



#endif /* UPMarketDefine_h */
