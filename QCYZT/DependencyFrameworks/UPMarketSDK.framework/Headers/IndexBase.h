// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `IndexBase.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>

enum {
    IndicatorSys_BUS_TYPE_BT_FUPAN = 1,
    IndicatorSys_BUS_TYPE_BT_INDEX = 2
};
#define IndicatorSys_BUS_TYPE NSInteger


enum {
    IndicatorSys_DATA_COMPACT_MODE_DCM_UNKNOWN = 0,
    IndicatorSys_DATA_COMPACT_MODE_DCM_JCE = 1,
    IndicatorSys_DATA_COMPACT_MODE_DCM_LZ4 = 2,
    IndicatorSys_DATA_COMPACT_MODE_DCM_SNP = 3
};
#define IndicatorSys_DATA_COMPACT_MODE NSInteger


enum {
    IndicatorSys_INDEX_PUSH_TYPE_INDEX_PUSH_NONE = 0,
    IndicatorSys_INDEX_PUSH_TYPE_INDEX_PUSH_REQUIRE = 1,
    IndicatorSys_INDEX_PUSH_TYPE_INDEX_PUSH_CANCEL = 2
};
#define IndicatorSys_INDEX_PUSH_TYPE NSInteger


enum {
    IndicatorSys_INDEX_DATA_FLAG_IDF_INDEX = 1,
    IndicatorSys_INDEX_DATA_FLAG_IDF_STOCK_LIST = 2,
    IndicatorSys_INDEX_DATA_FLAG_IDF_STOCK_INDEX = 4,
    IndicatorSys_INDEX_DATA_FLAG_IDF_INDEX_FIELD = 8
};
#define IndicatorSys_INDEX_DATA_FLAG NSInteger


enum {
    IndicatorSys_PERIOD_TYPE_E_TYPE_NONE = 0,
    IndicatorSys_PERIOD_TYPE_E_TYPE_RTMIN = 1,
    IndicatorSys_PERIOD_TYPE_E_TYPE_MIN1 = 2,
    IndicatorSys_PERIOD_TYPE_E_TYPE_MIN5 = 3,
    IndicatorSys_PERIOD_TYPE_E_TYPE_MIN15 = 4,
    IndicatorSys_PERIOD_TYPE_E_TYPE_MIN30 = 5,
    IndicatorSys_PERIOD_TYPE_E_TYPE_MIN60 = 6,
    IndicatorSys_PERIOD_TYPE_E_TYPE_DAY = 7,
    IndicatorSys_PERIOD_TYPE_E_TYPE_WEEK = 8,
    IndicatorSys_PERIOD_TYPE_E_TYPE_MONTH = 9,
    IndicatorSys_PERIOD_TYPE_E_TYPE_SEASON = 10,
    IndicatorSys_PERIOD_TYPE_E_TYPE_YEAR = 11,
    IndicatorSys_PERIOD_TYPE_E_TYPE_DYNAMIC = 12,
    IndicatorSys_PERIOD_TYPE_E_TYPE_MIN120 = 13
};
#define IndicatorSys_PERIOD_TYPE NSInteger


enum {
    IndicatorSys_IndexSerialDataType_ISDT_SIndexNew = 0,
    IndicatorSys_IndexSerialDataType_ISDT_SIndexInc = 1,
    IndicatorSys_IndexSerialDataType_ISDT_SIndexIncUpdate = 2
};
#define IndicatorSys_IndexSerialDataType NSInteger

@interface IndicatorSysIndexClientInfo : UPTAFJceObject
@property (nonatomic, strong) NSData* jce_vGuid;
@property (nonatomic, strong) NSString* jce_sXua;
@end

@interface IndicatorSysIndexHeaderInfo : UPTAFJceObject
@property (nonatomic, strong) IndicatorSysIndexClientInfo* jce_stClient;
@end

@interface IndicatorSysIndexBusTypeInfo : UPTAFJceObject
@property (nonatomic, assign) IndicatorSys_BUS_TYPE jce_eBusType;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) IndicatorSys_PERIOD_TYPE jce_ePeriodType;
@end

@interface IndicatorSysIndexHeartReq : UPTAFJceObject
@property (nonatomic, strong) IndicatorSysIndexHeaderInfo* jce_stHeader;
@end

@interface IndicatorSysRegIndexReq : UPTAFJceObject
@property (nonatomic, strong) IndicatorSysIndexHeaderInfo* jce_stHeader;
@property (nonatomic, assign) IndicatorSys_BUS_TYPE jce_eBusType;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) IndicatorSys_DATA_COMPACT_MODE jce_mode;
@property (nonatomic, assign) IndicatorSys_INDEX_PUSH_TYPE jce_ePushFlag;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) IndicatorSys_PERIOD_TYPE jce_ePeriodType;
@property (nonatomic, assign) JceBool jce_bDiffUpdate;
@end

@interface IndicatorSysIndexSerialData : UPTAFJceObject
@property (nonatomic, assign) IndicatorSys_BUS_TYPE jce_eBusType;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) IndicatorSys_DATA_COMPACT_MODE jce_mode;
@property (nonatomic, strong) NSData* jce_data;
@property (nonatomic, assign) JceBool jce_bReset;
@property (nonatomic, assign) JceInt32 jce_iDataFlag;
@property (nonatomic, assign) IndicatorSys_PERIOD_TYPE jce_ePeriodType;
@property (nonatomic, assign) IndicatorSys_IndexSerialDataType jce_iSerialType;
@property (nonatomic, assign) JceInt32 jce_nResetIndex;
@end

@interface IndicatorSysIndexStatsData : UPTAFJceObject
@property (nonatomic, assign) IndicatorSys_BUS_TYPE jce_eBusType;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSString*>* jce_mData;
@end

@interface IndicatorSysSIndexDataNew : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, strong) NSDictionary<NSString*, NSNumber*>* jce_mField;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mTagField;
@property (nonatomic, strong) NSDictionary<NSString*, NSArray<NSNumber*>*>* jce_mExtData;
@end

@interface IndicatorSysSIndexNew : UPTAFJceObject
@property (nonatomic, strong) NSArray<IndicatorSysSIndexDataNew*>* jce_vData;
@end

@interface IndicatorSysSStockIndexInc : UPTAFJceObject
@property (nonatomic, strong) NSArray<IndicatorSysSIndexDataNew*>* jce_vData;
@end

@interface IndicatorSysSIndexInc : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSString*, IndicatorSysSStockIndexInc*>* jce_mapStockIndexData;
@end

@interface IndicatorSysSIndexIncUpdate : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSString*, IndicatorSysSStockIndexInc*>* jce_mapDataUpdate;
@property (nonatomic, strong) NSDictionary<NSString*, IndicatorSysSStockIndexInc*>* jce_mapDataDelete;
@property (nonatomic, strong) NSDictionary<NSString*, IndicatorSysSStockIndexInc*>* jce_mapDataInsert;
@property (nonatomic, strong) NSDictionary<NSString*, IndicatorSysSStockIndexInc*>* jce_mapDataNewStk;
@property (nonatomic, strong) NSDictionary<NSString*, NSNumber*>* jce_mapDataDelStk;
@end



