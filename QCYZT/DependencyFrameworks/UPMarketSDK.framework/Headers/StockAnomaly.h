// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `StockAnomaly.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>

enum {
    HQExtend_ANOMALY_TYPE_EAT_THROUGHT_HIGH = 1,
    HQExtend_ANOMALY_TYPE_EAT_THROUGHT_LOW = 2,
    HQExtend_ANOMALY_TYPE_EAT_THROUGHT_MA = 3
};
#define HQExtend_ANOMALY_TYPE NSInteger

@interface HQExtendStockPoolReq : UPTAFJceObject
@property (nonatomic, assign) HQExtend_ANOMALY_TYPE jce_eType;
@end

@interface HQExtendStock : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@end

@interface HQExtendStockHq : UPTAFJceObject
@property (nonatomic, strong) HQExtendStock* jce_stStock;
@property (nonatomic, assign) JceFloat jce_fPrice;
@property (nonatomic, assign) JceFloat jce_fZdf;
@end

@interface HQExtendStockPoolRsp : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, NSArray<HQExtendStockHq*>*>* jce_mData;
@end

@interface HQExtendHisHighLowReq : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) HQExtend_ANOMALY_TYPE jce_eAnomalyType;
@end

@interface HQExtendHisHighLowRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQExtendStockHq*>* jce_vData;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSNumber*>* jce_mHisData;
@end

@interface HQExtendLatestDataReq : UPTAFJceObject
@property (nonatomic, assign) HQExtend_ANOMALY_TYPE jce_eType;
@property (nonatomic, assign) JceInt32 jce_iWantNum;
@end

@interface HQExtendLatestData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) HQExtend_ANOMALY_TYPE jce_eAnomalyType;
@property (nonatomic, strong) HQExtendStockHq* jce_hq;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt32 jce_iTime;
@end

@interface HQExtendLatestDataRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQExtendLatestData*>* jce_vData;
@end

@interface HQExtendGetStockAnomalyBroadcastReq : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iWantNum;
@property (nonatomic, assign) HQExtend_ANOMALY_TYPE jce_eAnomalyType;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_iTypeList;
@end



