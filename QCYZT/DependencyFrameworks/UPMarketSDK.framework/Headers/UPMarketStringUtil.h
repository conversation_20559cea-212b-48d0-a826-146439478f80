//
//  UPMarketStringUtil.h
//  UPGPT
//
//  Created by Nic on 2017/3/8.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketModel.h"

/*
 字符串处理
 */
@interface UPMarketStringUtil : NSObject

/*
 是否是纯数字
 */
+ (BOOL)isNumeric:(NSString *)string;

/*
 是否是纯中文字符串
 */
+ (BOOL)isChineseString:(NSString *)string;

/*
 是否含有中文字符
 */
+ (BOOL)hasChineseCharacter:(NSString *)string;

/*
 全角转半角 大写转小些 去掉空格
 */
+ (NSString *)getNormalizeString:(NSString *)string;

/*
 将数组中的字符串通过连接字符拼接
 */
+ (NSString *)combineStringArray:(NSArray *)array withConnector:(NSString *)connector;

/*
 获取简拼

 @param pinyin wan ke
 @return wk
 */
+ (NSString *)getSimplePinyin:(NSString *)pinyin;

/*
 将特殊字符转义
 */
+ (NSString *)getSQLString:(NSString *)string;

+ (NSDictionary *)getRealPositionWithTarget:(NSString *)target start:(NSInteger)start end:(NSInteger)end;

+ (NSString *)hexStringWithData:(NSData *)data;

+ (NSString *)fclBlowfishEncrypt:(NSString *)string withKey:(const NSString *)key;

+ (NSData *)base64StringToData:(NSString *)string;

+ (void)sortMarkets:(NSMutableOrderedSet *)orderedSet;

+ (NSString *)set2String:(NSOrderedSet<NSNumber *> *)markets;

+ (NSOrderedSet *)string2Set:(NSString *)marketStr;

+ (int)getMarketKey:(NSOrderedSet<NSNumber *> *)set l2Markets:(NSOrderedSet<NSNumber *> *)markets;

+ (NSString *)getDataListStr:(NSArray <UPHqStockHq *> *)dataArray;

@end
