// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `BasicData.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>
#import "RadarData.h"
#import "CommonData.h"

enum {
    HQSys_E_SIMPLE_MF_TYPE_E_MF_TODAY = 0,
    HQSys_E_SIMPLE_MF_TYPE_E_MF_3DAY = 1,
    HQSys_E_SIMPLE_MF_TYPE_E_MF_5DAY = 2,
    HQSys_E_SIMPLE_MF_TYPE_E_MF_10DAY = 3,
    HQSys_E_SIMPLE_MF_TYPE_E_MF_3MIN = 4,
    HQSys_E_SIMPLE_MF_TYPE_E_MF_5MIN = 5,
    HQSys_E_SIMPLE_MF_TYPE_E_MF_10MIN = 6,
    HQSys_E_SIMPLE_MF_TYPE_E_MF_30MIN = 7,
    HQSys_E_SIMPLE_MF_TYPE_E_MF_60MIN = 8
};
#define HQSys_E_SIMPLE_MF_TYPE NSInteger


enum {
    HQSys_E_FINANCING_TARGET_E_FT_NULL,
    HQSys_E_FINANCING_TARGET_E_FT_FINANCING
};
#define HQSys_E_FINANCING_TARGET NSInteger


enum {
    HQSys_E_STOCK_FLAG_TYPE_E_FLAG_TYPE_UNKNOWN = 0,
    HQSys_E_STOCK_FLAG_TYPE_E_FLAG_TYPE_ZTG = 1
};
#define HQSys_E_STOCK_FLAG_TYPE NSInteger


enum {
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_UNKOWN = 0,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_CLOSED = 1,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_NOT_OPEN = 2,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_AUTION = 3,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_UPCOMING = 4,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_TRADE = 5,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_AM_TRADE = 6,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_NOONTIME = 7,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_PM_TRADE = 8,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_STOPPED = 9,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_TEMP_STOPPED = 10,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_AFTER_TRADE = 11,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_VOLATILITY_STOPPED = 12,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_RECOVERABLE_FUSING = 13,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_NON_RECOVERABLE_FUSING = 14,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_PM_AUCTION = 20,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_ATP_TRADE = 23,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_JJSS = 24,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_TS = 25,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_SUSPEND = 26,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_PRE_TRADE_ORDER = 31,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_NO_CANCAL = 32,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_RANDOM_MATCHING = 33,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_AM_AUCTION = 34,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_BLOCKING = 35,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_INTERVERTION = 36,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_REST = 37,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_CANCAL = 38,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_CAS_FIXING = 39,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_CAS_OI = 40,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_CAS_NO_CANCAL = 41,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_CAS_RANDOM_CLOSE = 42,
    HQSys_E_STOCK_TRADE_STATUS_E_STATUS_HK_PM_AUCTION = 43
};
#define HQSys_E_STOCK_TRADE_STATUS NSInteger


enum {
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_UNKOWN = 0,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_CLOSED = 1,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_NOT_OPEN = 2,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_AUCTION = 3,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_UPCOMING = 4,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_TRADE = 5,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_AM_TRADE = 6,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_NOONTIME = 7,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_PM_TRADE = 8,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_PM_AUCTION = 20,
    HQSys_E_MARKET_TRADE_STATUS_E_MARKET_STATUS_ATP_TRADE = 23
};
#define HQSys_E_MARKET_TRADE_STATUS NSInteger


enum {
    HQSys_E_STOCK_HQ_DATA_E_SHD_NONE = 0,
    HQSys_E_STOCK_HQ_DATA_E_SHD_SIMPLE = 1,
    HQSys_E_STOCK_HQ_DATA_E_SHD_ORDER = 2,
    HQSys_E_STOCK_HQ_DATA_E_SHD_BLOCK = 4,
    HQSys_E_STOCK_HQ_DATA_E_SHD_DERIVE = 8,
    HQSys_E_STOCK_HQ_DATA_E_SHD_1DAY_ZJ = 32,
    HQSys_E_STOCK_HQ_DATA_E_SHD_3DAY_ZJ = 64,
    HQSys_E_STOCK_HQ_DATA_E_SHD_5DAY_ZJ = 128,
    HQSys_E_STOCK_HQ_DATA_E_SHD_10DAY_ZJ = 256,
    HQSys_E_STOCK_HQ_DATA_E_SHD_DDE = 512,
    HQSys_E_STOCK_HQ_DATA_E_SHD_CW = 1024,
    HQSys_E_STOCK_HQ_DATA_E_SHD_3MIN_ZJ = 2048,
    HQSys_E_STOCK_HQ_DATA_E_SHD_5MIN_ZJ = 4096,
    HQSys_E_STOCK_HQ_DATA_E_SHD_10MIN_ZJ = 8192,
    HQSys_E_STOCK_HQ_DATA_E_SHD_30MIN_ZJ = 16384,
    HQSys_E_STOCK_HQ_DATA_E_SHD_60MIN_ZJ = 32768,
    HQSys_E_STOCK_HQ_DATA_E_SHD_DAY_MF = 65536,
    HQSys_E_STOCK_HQ_DATA_E_SHD_OPTION = 131072,
    HQSys_E_STOCK_HQ_DATA_E_SHD_AUCTION = 262144
};
#define HQSys_E_STOCK_HQ_DATA NSInteger


enum {
    HQSys_E_STOCK_DATA_ENV_E_SHDE_NORMAL = 0,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_PRICE = 1,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_QT = 2,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_1DAYZJ = 3,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_3DAYZJ = 4,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_5DAYZJ = 5,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_10DAYZJ = 6,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_SIMPLE_STATUS = 7,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_3MINZJ = 8,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_5MINZJ = 9,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_10MINZJ = 10,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_30MINZJ = 11,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_60MINZJ = 12,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_QT_MF = 13,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_NEWQT = 14,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_ZXG = 15,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_CDDP = 16,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_CDFP = 17,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_JDCX = 18,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_FUND = 19,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_BOND = 20,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_HK = 21,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_US = 22,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_FX = 23,
    HQSys_E_STOCK_DATA_ENV_E_SHDE_LIST_GPC = 24
};
#define HQSys_E_STOCK_DATA_ENV NSInteger


enum {
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_OPEN = 0,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_HIGH = 1,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_LOW = 2,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_CLOSE = 3,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_VOLUME = 4,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_AMOUNT = 5,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_CHGVALUE = 6,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_CHGRATIO = 7,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_ZHENFU = 8,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_ZT = 9,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_LEADBLOCK = 10,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_OPENAMONNT = 11,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_OPENVOLUME = 12,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_LEAD_CODE = 14,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_LEAD_NAME = 15,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_NOWVOL = 16,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_INSIDE = 17,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_OUTSIDE = 18,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_VBSELL = 19,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_AVGPRICE = 20,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_ZTPRICE = 21,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_DTPRICE = 22,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_TURNOVERRATE = 23,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_TRADEMIN = 24,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_TRADEDATE = 25,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_TRADETIME = 26,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_ZDMARK = 27,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_TRADESTATUS = 28,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_VBSNUM = 29,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_BSFLAG = 30,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_PERATIO = 31,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_PRICERATIO = 32,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_ZSZ = 33,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_LTSZ = 34,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_QH_SETTLEMENTPRICE = 35,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_QH_PRESETTLEMENTPRICE = 36,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_QH_OPENINTEREST = 37,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_QH_PREOPENINTEREST = 38,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_QH_EVERYHAND = 39,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_QH_DAYINCREASE = 40,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_MF_BASE = 41,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DDE_DDX = 42,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DDE_DDY = 43,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DDE_DDZ = 44,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DDE_DDF = 45,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DDE_DDX5 = 46,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DDE_DDY5 = 47,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DDE_DDX60 = 48,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DDE_DDY60 = 49,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_HEADMARKET = 50,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_HEADCODE = 51,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_HEADNAME = 52,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_HEADNOW = 53,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_HEADCLOSE = 54,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_STOCKNUM = 55,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_EQUALNUM = 56,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_ZTNUM = 57,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_UPNUM = 58,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_DOWNNUM = 59,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_ZSZ = 60,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_LTSZ = 61,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_UPNDAY = 62,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_3DAYCHG = 63,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_5DAYCHG = 64,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_10DAYCHG = 65,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_LIANGBI = 66,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_UPSPEED = 67,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_TRADENUM = 68,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_BSVOL = 69,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_STATUS = 70,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_5MINMAININFLOW = 71,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_TICKNUM = 72,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_ATPVOL = 73,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_ATPAMOUNT = 74,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_AVGBP = 75,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_BP = 76,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_IOPV = 77,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_10DAYHBL = 78,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_52WEEKSTAT = 79,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_HISTORYSTAT = 80,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_3DAYNETINFLOW = 81,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_5DAYNETINFLOW = 82,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_10DAYNETINFLOW = 83,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_20DAYNETINFLOW = 84,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_MATCHPRICE = 85,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_5DAYCHG = 86,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_10DAYCHG = 87,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_20DAYCHG = 88,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_MONTHCHG = 89,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_SEASONCHG = 90,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_YEARCHG = 91,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_THISYEARCHG = 92,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_REFPRICE = 93,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_LYBRANK = 94,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_CW_MGSY = 97,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_CW_MGJZC = 98,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_CW_JZCSYL = 99,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_CW_YSZZL3Y = 100,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_CW_JLSZZL3Y = 101,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_ZQ_BASE = 102,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_3MINMF_BASE = 103,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_5MINMF_BASE = 104,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_10MINMF_BASE = 105,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_30MINMF_BASE = 106,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_60MINMF_BASE = 107,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_TB_BASE = 108,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DAYMF_AMT_BASE = 109,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DAYMF_VOL_BASE = 110,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DAYMF_NUM_BASE = 111,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_OPTION_BASE = 112,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_SIM_MF_1DAY = 113,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_LZDTZJ = 114,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_WEIBI = 115,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_LASTCHG = 116,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_BLOCKTRADE = 117,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_TOTALCHG = 118,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_JT_PE = 119,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_EX_TTM_PE = 120,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_LEAD = 121,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_HK_BASE = 122,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_OPENCHG = 123,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_AUCLAST = 124,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_AUCVOL = 125,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_DERIVE_AUCTURN = 126,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_HEADCHGRATIO = 127,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_UPNUM_RATIO = 128,
    HQSys_E_HSTOCKHQ_BITMAP_EBIT_BLOCKINDEX_20DAYCHG = 129
};
#define HQSys_E_HSTOCKHQ_BITMAP NSInteger


enum {
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_NOW = 0,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_OPEN = 1,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_HIGH = 2,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_LOW = 3,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_CLOSE = 4,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_VOLUME = 5,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_AMOUNT = 6,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_CHGVALUE = 7,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_CHGRATIO = 8,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZHENFU = 9,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_OPEN_AMT = 10,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_OPEN_VOL = 11,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_NOW_VOL = 12,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_INSIDE = 13,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_OUTSIDE = 14,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BUY_PRICE = 15,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BUY_VOL = 16,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SELL_PRICE = 17,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SELL_VOL = 18,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_AVG_PRICE = 19,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZT_PRICE = 20,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_DT_PRICE = 21,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TURNOVER_RATE = 22,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_DATE = 23,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TIME = 24,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZT_MARK = 25,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_WP_STATUS = 26,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BS_FLAG = 27,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_DT_PE = 28,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_JT_PE = 29,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TTM_PE = 30,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZSZ = 31,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_LTSZ = 32,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZGB = 33,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_LTGB = 34,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_LIANGBI = 35,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_UP_SPEED = 36,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TRADE_NUM = 37,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_NP_STATUS = 38,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_5MIN_MAIN_INFLOW = 39,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ATP_VOL = 40,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ATP_AMT = 41,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_10DAY_RATE = 42,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_52WEEK_HIGH = 43,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_52WEEK_LOW = 44,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_HISTORY_HIGH = 45,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_HISTORY_LOW = 46,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_3DAY_NET_INFLOW = 47,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_5DAY_NET_INFLOW = 48,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_10DAY_NET_INFLOW = 49,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_20DAY_NET_INFLOW = 50,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_MATCH_PRICE = 51,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_MATCH_VOL = 52,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_NOT_MATCH_VOL = 53,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_5DAY_CHG = 54,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_10DAY_CHG = 55,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_20DAY_CHG = 56,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_MONTH_CHG = 57,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SEASON_CHG = 58,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_YEAR_CHG = 59,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_THIS_YEAR_CHG = 60,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TOTAL_CHG = 61,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_WEIBI = 62,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BIG_TRADE = 63,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_LYB_RANK = 64,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_LYB_PRE_RANK = 65,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_OPEN_CHG = 66,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_LAST_AUC = 67,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_AUC_VOL = 68,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_AUC_TRUN = 69,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_MGSY = 70,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_MGJZC = 71,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BUY_AVG = 72,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SELL_AVG = 73,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BUY_PRICE_NUM = 74,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SELL_PRICE_NUM = 75,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BUY_VOLUME = 76,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SELL_VOLUME = 77,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_PRICE_RATIO = 78,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TYPE = 79,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_FL_MAIN_BUY = 80,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_FL_MAIN_RATIO = 81,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_1DAY_FL = 82,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_3DAY_FL = 83,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_5DAY_FL = 84,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_10DAY_FL = 85,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_LEAD_MARKET = 90,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_LEAD_CODE = 91,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_LEAD_NAME = 92,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_LEAD_NOW_PRICE = 93,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_LEAD_HEAD_PRICE = 94,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_TOTOAL = 95,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_EQUAL = 96,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_UP = 97,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_DOWN = 98,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_ZT = 99,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_ZSZ = 100,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_LTSZ = 101,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_UP_DAYS = 102,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_3DAYS_CHG = 103,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_5DAYS_CHG = 104,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BLOCK_10DAYS_CHG = 105,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_YEAR_ROR = 110,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_10W_RATE = 111,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_1K_RATE = 112,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_DAYS = 113,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_ZK_DAYS = 114,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_JX_FROM = 115,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_JX_TO = 116,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_TODAY_BUY = 117,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_ZJ_USE_DAYS = 118,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_ZJ_FETCH_DAYS = 119,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_GZJZ = 120,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_ZQ_YJL = 121,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_ZR_TYPE = 130,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_ZR_STATUS = 131,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_FC = 132,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_TYPE = 133,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_TP_STATUS = 134,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_CQCX_STATUS = 135,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_DIFF_RIGHT = 136,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_ZSS_COUNT = 137,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_TB_FX_TYPE = 138,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_HK_BALANCE_PRICE = 145,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_HK_BALANCE_VOL = 146,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_HK_REFER_PRICE = 147,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_HK_LOW_LIMIT_PRICE = 148,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_HK_HIGH_LIMIT_PRICE = 149,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_DIRECTION = 150,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_IMBALANCE_VOL = 151,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BUY_LOW_LIMIT_PRICE = 152,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_BUY_HIGHT_LIMIT_PRICE = 153,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SELL_LOW_LIMIT_PRICE = 154,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SELL_HIGH_LIMIT_PRICE = 155,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SPQH_SETTLEMENT_PRICE = 160,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SPQH_PRE_SETTLEMENT_PRICE = 161,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SPQH_OPEN_INTEREST = 162,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SPQH_PRE_OPEN_INTEREST = 163,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SPQH_EVERY_HAND = 164,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_SPQH_DAY_INCREASE = 165,
    HQSys_E_STOCK_HQ_SIMPLE_BITMAP_EBIT_LASTCHG = 166
};
#define HQSys_E_STOCK_HQ_SIMPLE_BITMAP NSInteger


enum {
    HQSys_E_KLINE_BITMAP_EBIT_KLINE_VOL = 0,
    HQSys_E_KLINE_BITMAP_EBIT_KLINE_AMT = 1,
    HQSys_E_KLINE_BITMAP_EBIT_KLINE_DAY = 2,
    HQSys_E_KLINE_BITMAP_EBIT_KLINE_TIME = 3,
    HQSys_E_KLINE_BITMAP_EBIT_KLINE_UP = 4,
    HQSys_E_KLINE_BITMAP_EBIT_KLINE_DOWN = 5,
    HQSys_E_KLINE_BITMAP_EBIT_KLINE_ZF = 6,
    HQSys_E_KLINE_BITMAP_EBIT_KLINE_TUR = 7
};
#define HQSys_E_KLINE_BITMAP NSInteger


enum {
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_AVG = 0,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_NOW_VOL = 1,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_BUY_VOL = 2,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_SELL_VOL = 3,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_AMT = 4,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_VOL_IN = 5,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_LEAD = 6,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_FLAG = 7,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_IOPV = 8,
    HQSys_E_RTMIN_BITMAP_EBIT_RTMIN_DATE = 9
};
#define HQSys_E_RTMIN_BITMAP NSInteger


enum {
    HQSys_E_DICT_BITMAP_EBIT_DICT_UNIT1 = 0,
    HQSys_E_DICT_BITMAP_EBIT_DICT_VOL_BASE = 1,
    HQSys_E_DICT_BITMAP_EBIT_DICT_PRECISE = 2,
    HQSys_E_DICT_BITMAP_EBIT_DICT_CHG_HIS = 3,
    HQSys_E_DICT_BITMAP_EBIT_DICT_DIFF_RIGHT = 4,
    HQSys_E_DICT_BITMAP_EBIT_DICT_CDR = 5,
    HQSys_E_DICT_BITMAP_EBIT_DICT_GDR = 6,
    HQSys_E_DICT_BITMAP_EBIT_DICT_UNIT2 = 7,
    HQSys_E_DICT_BITMAP_EBIT_DICT_NAME_EX = 8,
    HQSys_E_DICT_BITMAP_EBIT_DICT_DEFICIT = 9,
    HQSys_E_DICT_BITMAP_EBIT_DICT_PRO_CONTROL = 10
};
#define HQSys_E_DICT_BITMAP NSInteger


enum {
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_NAME = 0,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_PRECISE = 1,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_SUB_TYPE = 2,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_COIN_TYPE = 3,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_LTGB = 4,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_ZGB = 5,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_RZ_MARK = 6,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_RQ_MARK = 7,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_NET_VALUE = 8,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_CLOSE = 9,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_ZT = 10,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_DT = 11,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_DIFF_RIGHT = 12,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_CDR = 13,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_GDR = 14,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_IPO_DATE = 15,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_FXJ = 16,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_UNIT = 17,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_JTMGSY = 18,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_DTMGSY_BASE = 19,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_JTMGSY_BASE = 20,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_TTMMGSY_BASE = 21,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_NAMEEX = 22,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_DEFICIT = 23,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_PRO_CONTROL = 24,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_ZD_LIMIT = 25,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_IPO_FLAG = 26,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_IPO_PRIME = 27,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_BLOCK_CODE = 28,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_BLOCK_NAME = 29,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_FLAG = 30,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_END_DATE = 31,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_RELATE_BLOCK_ID = 32,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_JZC = 33,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_TSZLSR = 34,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_CQCX_STATUS = 35,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_ZSS_COUNT = 36,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_RELATION = 37,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_TAGS = 38,
    HQSys_E_STOCKBASE_BITMAP_EBIT_STOCKBASE_ZCZB = 39
};
#define HQSys_E_STOCKBASE_BITMAP NSInteger


enum {
    HQSys_E_ORDER_ITEM_TYPE_E_ORDER_ITEM_NO = 0,
    HQSys_E_ORDER_ITEM_TYPE_E_ORDER_ITEM_TRADE = 1,
    HQSys_E_ORDER_ITEM_TYPE_E_ORDER_ITEM_CANCEL = 2,
    HQSys_E_ORDER_ITEM_TYPE_E_ORDER_ITEM_BIG = 4,
    HQSys_E_ORDER_ITEM_TYPE_E_ORDER_ITEM_ADD = 8,
    HQSys_E_ORDER_ITEM_TYPE_E_ORDER_ITEM_PARTTRADE = 16
};
#define HQSys_E_ORDER_ITEM_TYPE NSInteger


enum {
    HQSys_E_ORDER_OPERATE_TYPE_E_BUY_ORDER = 0,
    HQSys_E_ORDER_OPERATE_TYPE_E_SELL_ORDER = 1,
    HQSys_E_ORDER_OPERATE_TYPE_E_CANCAL_ORDER = 2
};
#define HQSys_E_ORDER_OPERATE_TYPE NSInteger


enum {
    HQSys_E_ORDER_TRADE_KINDE_E_ORDER_MARKET_PRICE = 0,
    HQSys_E_ORDER_TRADE_KINDE_E_ORDER_FIXED_PRICE = 1,
    HQSys_E_ORDER_TRADE_KINDE_E_ORDER_BEST_SCHEME = 2
};
#define HQSys_E_ORDER_TRADE_KINDE NSInteger


enum {
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_0_1 = 0,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_1_3 = 1,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_3_7 = 2,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_7_10 = 3,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_10_15 = 4,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_15_20 = 5,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_20_30 = 6,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_30_50 = 7,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_50_70 = 8,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_70_100 = 9,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_100_200 = 10,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_200_500 = 11,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_500_700 = 12,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_700_1000 = 13,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_1000_1500 = 14,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_1500_2000 = 15,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_2000_3000 = 16,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_3000_5000 = 17,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_5000_7000 = 18,
    HQSys_TRANS_VOL_RANGE_E_TRANS_VOL_7000_MAX = 19
};
#define HQSys_TRANS_VOL_RANGE NSInteger


enum {
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_UP = 1,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_DROP = 2,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_SPEED_TOP = 4,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_SPEED_LAST = 8,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_WEIBI_TOP = 16,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_WEIBI_LAST = 32,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_LIANGBI = 64,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_AMOUNT = 128,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_ZHENFU = 256,
    HQSys_H_ZH_RANK_TYPE_E_ZH_RANK_ALL = 4095
};
#define HQSys_H_ZH_RANK_TYPE NSInteger


enum {
    HQSys_E_MARKET_TYPE_E_TYPE_SZ_AB = 0,
    HQSys_E_MARKET_TYPE_E_TYPE_SH_AB = 1,
    HQSys_E_MARKET_TYPE_E_TYPE_BK_ZS = 2,
    HQSys_E_MARKET_TYPE_E_TYPE_QH_GZ = 3,
    HQSys_E_MARKET_TYPE_E_TYPE_QH_TB = 4,
    HQSys_E_MARKET_TYPE_E_TYPE_SH_ZQ_HG = 5,
    HQSys_E_MARKET_TYPE_E_TYPE_SZ_ZQ_HG = 6,
    HQSys_E_MARKET_TYPE_E_TYPE_GGT = 7,
    HQSys_E_MARKET_TYPE_E_TYPE_SH_KCB = 8,
    HQSys_E_MARKET_TYPE_E_TYPE_SH_OPTION = 9,
    HQSys_E_MARKET_TYPE_E_TYPE_SZ_OPTION = 10,
    HQSys_E_MARKET_TYPE_E_TYPE_SZ_GZGG = 11,
    HQSys_E_MARKET_TYPE_E_TYPE_SZ_CYREG = 12,
    HQSys_E_MARKET_TYPE_E_TYPE_SF_OPTION = 13,
    HQSys_E_MARKET_TYPE_E_TYPE_SZ_ZQXQ = 14,
    HQSys_E_MARKET_TYPE_E_TYPE_BJ_AB = 15,
    HQSys_E_MARKET_TYPE_E_TYPE_NONE = 99
};
#define HQSys_E_MARKET_TYPE NSInteger


enum {
    HQSys_E_LOGIN_CLIENT_TYPE_EPC_LOGIN = 0,
    HQSys_E_LOGIN_CLIENT_TYPE_EMOBILE_LOGIN = 1,
    HQSys_E_LOGIN_CLIENT_TYPE_EWEB_LOGIN = 2
};
#define HQSys_E_LOGIN_CLIENT_TYPE NSInteger


enum {
    HQSys_E_KICK_OUT_STATUS_E_NOT_KICK_OUT = 0,
    HQSys_E_KICK_OUT_STATUS_E_DO_KICK_OUT = 1
};
#define HQSys_E_KICK_OUT_STATUS NSInteger


enum {
    HQSys_E_KICK_OUT_REASON_E_KICK_REPEAT = 0,
    HQSys_E_KICK_OUT_REASON_E_KICK_OVERDUE = 1
};
#define HQSys_E_KICK_OUT_REASON NSInteger


enum {
    HQSys_E_LOGIN_AUTH_TYPE_E_LOGIN_AUTH_SZ_SH = 1,
    HQSys_E_LOGIN_AUTH_TYPE_E_LOGIN_AUTH_HK_US = 2,
    HQSys_E_LOGIN_AUTH_TYPE_E_LOGIN_AUTH_LST = 3,
    HQSys_E_LOGIN_AUTH_TYPE_E_LOGIN_AUTH_XW = 4,
    HQSys_E_LOGIN_AUTH_TYPE_E_LOGIN_AUTH_AC = 5,
    HQSys_E_LOGIN_AUTH_TYPE_E_LOGIN_AUTH_CLS = 6,
    HQSys_E_LOGIN_AUTH_TYPE_E_LOGIN_AUTH_GNN = 7,
    HQSys_E_LOGIN_AUTH_TYPE_E_LOGIN_AUTH_GNN_DL = 8
};
#define HQSys_E_LOGIN_AUTH_TYPE NSInteger


enum {
    HQSys_E_AUTH_TYPE_E_AUTH_OEM = 1,
    HQSys_E_AUTH_TYPE_E_AUTH_LOGIN = 2,
    HQSys_E_AUTH_TYPE_E_AUTH_ALL_LOGIN = 3,
    HQSys_E_AUTH_TYPE_E_AUTH_KICK_USER = 4
};
#define HQSys_E_AUTH_TYPE NSInteger


enum {
    HQSys_E_HK_WARRANT_TYPE_E_HWT_NONE = 0,
    HQSys_E_HK_WARRANT_TYPE_E_HWT_WL = 1,
    HQSys_E_HK_WARRANT_TYPE_E_HWT_NX = 2,
    HQSys_E_HK_WARRANT_TYPE_E_HWT_WL_NX = 3
};
#define HQSys_E_HK_WARRANT_TYPE NSInteger


enum {
    HQSys_E_SN_DATA_TYPE_E_SN_SZ_NET = 0,
    HQSys_E_SN_DATA_TYPE_E_SN_SH_NET = 1,
    HQSys_E_SN_DATA_TYPE_E_SN_SZ_INFLOW = 2,
    HQSys_E_SN_DATA_TYPE_E_SN_SH_INFLOW = 3
};
#define HQSys_E_SN_DATA_TYPE NSInteger


enum {
    HQSys_E_ZDFENBU_TYPE_E_ZD_SZ_AG = 1,
    HQSys_E_ZDFENBU_TYPE_E_ZD_SH_AG = 2,
    HQSys_E_ZDFENBU_TYPE_E_ZD_BJ_GP = 3,
    HQSys_E_ZDFENBU_TYPE_E_ZD_SZSH_AG = 4,
    HQSys_E_ZDFENBU_TYPE_E_ZD_SZSHBJ = 5,
    HQSys_E_ZDFENBU_TYPE_E_ZD_HK = 6,
    HQSys_E_ZDFENBU_TYPE_E_ZD_AMEX = 7
};
#define HQSys_E_ZDFENBU_TYPE NSInteger

@interface HQSysHTolMoneyFlow : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceInt16 jce_shtsetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceDouble jce_fSuperIn;
@property (nonatomic, assign) JceDouble jce_fSuperOut;
@property (nonatomic, assign) JceDouble jce_fBigIn;
@property (nonatomic, assign) JceDouble jce_fBigOut;
@property (nonatomic, assign) JceDouble jce_fMidIn;
@property (nonatomic, assign) JceDouble jce_fMidOut;
@property (nonatomic, assign) JceDouble jce_fSmallIn;
@property (nonatomic, assign) JceDouble jce_fSmallOut;
@property (nonatomic, assign) JceDouble jce_dPrevClose;
@end

@interface HQSysHSimpleMoneyFlow : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_fMainBuy;
@property (nonatomic, assign) JceFloat jce_fMainRatio;
@end

@interface HQSysHTolMoneyFlowSet : UPTAFJceObject
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_amt;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_vol;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_num;
@end

@interface HQSysHRTMinData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMinute;
@property (nonatomic, assign) JceDouble jce_fNow;
@property (nonatomic, assign) JceDouble jce_fAverage;
@property (nonatomic, assign) JceUInt32 jce_uiNowVol;
@property (nonatomic, assign) JceUInt32 jce_uiBuyv;
@property (nonatomic, assign) JceUInt32 jce_uiSellv;
@property (nonatomic, assign) JceDouble jce_dAmount;
@property (nonatomic, assign) JceUInt32 jce_uiVolInStock;
@property (nonatomic, assign) JceDouble jce_fLead;
@property (nonatomic, assign) JceInt16 jce_shtFlag;
@property (nonatomic, assign) JceDouble jce_dIOPV;
@property (nonatomic, assign) JceInt32 jce_iDate;
@end

@interface HQSysHRTMinDataSimp : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMinute;
@property (nonatomic, assign) JceFloat jce_fNow;
@property (nonatomic, assign) JceFloat jce_fAverage;
@end

@interface HQSysHRTMinDataAuc : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_shtTime;
@property (nonatomic, assign) JceFloat jce_fNow;
@property (nonatomic, assign) JceInt64 jce_lVol;
@property (nonatomic, assign) JceInt64 jce_lUmVol;
@property (nonatomic, assign) JceInt16 jce_shtDir;
@end

@interface HQSysSZTData : UPTAFJceObject
@property (nonatomic, assign) JceInt8 jce_bZT;
@property (nonatomic, assign) JceInt8 jce_bPreZT;
@property (nonatomic, assign) JceInt32 jce_iBoardDays;
@property (nonatomic, assign) JceInt32 jce_iStrongDays;
@property (nonatomic, assign) JceInt32 jce_iZDTDays;
@end

@interface HQSysBlockBasicInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@end

@interface HQSysHDateTime : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt16 jce_shtDay;
@property (nonatomic, assign) JceInt16 jce_shtTime;
@end

@interface HQSysHTogetherZhiShu : UPTAFJceObject
@property (nonatomic, assign) JceUInt32 jce_uiVolInStock;
@property (nonatomic, assign) JceDouble jce_fYClose;
@property (nonatomic, assign) JceUInt16 jce_usUp;
@property (nonatomic, assign) JceUInt16 jce_usDown;
@end

@interface HQSysHAnalyData : UPTAFJceObject
@property (nonatomic, strong) HQSysHDateTime* jce_sttDateTime;
@property (nonatomic, assign) JceDouble jce_fOpen;
@property (nonatomic, assign) JceDouble jce_fHigh;
@property (nonatomic, assign) JceDouble jce_fLow;
@property (nonatomic, assign) JceDouble jce_fClose;
@property (nonatomic, assign) JceDouble jce_fAmount;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceDouble jce_dSettlementPrice;
@property (nonatomic, strong) HQSysHTogetherZhiShu* jce_sttZhiShu;
@property (nonatomic, assign) JceUInt32 jce_uiAtpVolume;
@property (nonatomic, assign) JceDouble jce_dAtpAmount;
@property (nonatomic, assign) JceUInt32 jce_uiAtpTradeNum;
@property (nonatomic, assign) JceFloat jce_fZhenfu;
@property (nonatomic, assign) JceFloat jce_fTurnoverRate;
@end

@interface HQSysHCQAnalyData : UPTAFJceObject
@property (nonatomic, strong) HQSysHAnalyData* jce_stLine;
@property (nonatomic, assign) JceInt64 jce_lOpenDate;
@property (nonatomic, assign) JceInt64 jce_lCloseDate;
@property (nonatomic, assign) JceInt64 jce_lHighDate;
@property (nonatomic, assign) JceInt64 jce_lLowDate;
@property (nonatomic, assign) JceDouble jce_dHigh;
@property (nonatomic, assign) JceDouble jce_dLow;
@end

@interface HQSysHTickData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMinute;
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceUInt32 jce_uiNowVol;
@property (nonatomic, assign) JceInt16 jce_shtInOutFlag;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceInt32 jce_iTradeNum;
@property (nonatomic, assign) JceDouble jce_dAvgPrice;
@property (nonatomic, assign) JceUInt32 jce_uiFrontTrans;
@property (nonatomic, assign) JceInt32 jce_iVolInStockDif;
@property (nonatomic, assign) JceInt16 jce_shtType;
@end

@interface HQSysHMarketTradePeriod : UPTAFJceObject
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vTradePeriod;
@end

@interface HQSysHStock : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@end

@interface HQSysHMarketTypeData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, assign) JceInt16 jce_shtType;
@end

@interface HQSysHStockDatePeriod : UPTAFJceObject
@property (nonatomic, strong) HQSysHStockUnique* jce_stStock;
@property (nonatomic, assign) JceInt32 jce_iStartDate;
@property (nonatomic, assign) JceInt32 jce_iEndDate;
@property (nonatomic, assign) JceInt32 jce_iNum;
@property (nonatomic, assign) JceInt64 jce_iStartDateTime;
@property (nonatomic, assign) JceInt64 jce_iEndDateTime;
@end

@interface HQSysHStockAnalyData : UPTAFJceObject
@property (nonatomic, strong) HQSysHStockUnique* jce_stStock;
@property (nonatomic, strong) NSArray<HQSysHAnalyData*>* jce_vAnalayData;
@property (nonatomic, assign) JceInt32 jce_iSumCount;
@end

@interface HQSysHMFlowTrend : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_fMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_fMainMoneyRatio;
@property (nonatomic, assign) JceDouble jce_fRetailMoneyInflow;
@property (nonatomic, assign) JceDouble jce_fRetailMoneyRatio;
@property (nonatomic, assign) JceDouble jce_fSuperLargeInflow;
@property (nonatomic, assign) JceDouble jce_fSuperLargeRatio;
@property (nonatomic, assign) JceDouble jce_fLargeInflow;
@property (nonatomic, assign) JceDouble jce_fLargeRatio;
@property (nonatomic, assign) JceDouble jce_fMiddleInflow;
@property (nonatomic, assign) JceDouble jce_fMiddleRatio;
@property (nonatomic, assign) JceDouble jce_fSmallInflow;
@property (nonatomic, assign) JceDouble jce_fSmallRatio;
@end

@interface HQSysHMFlowRank : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceDouble jce_fChg;
@property (nonatomic, assign) JceDouble jce_fChange;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_fDayMFlowTrend;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_f3DayMFlowTrend;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_f5DayMFlowTrend;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_f10DayMFlowTrend;
@property (nonatomic, assign) JceInt8 jce_bTransactionStatus;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_f3MinMFlowTrend;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_f5MinMFlowTrend;
@property (nonatomic, strong) HQSysSZTData* jce_ztData;
@property (nonatomic, strong) HQSysBlockBasicInfo* jce_leadBlock;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_f10MinMFlowTrend;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_f30MinMFlowTrend;
@property (nonatomic, strong) HQSysHMFlowTrend* jce_f60MinMFlowTrend;
@end

@interface HQSysHDDERank : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceDouble jce_fChg;
@property (nonatomic, assign) JceDouble jce_fTurnoverRate;
@property (nonatomic, assign) JceDouble jce_fDDX;
@property (nonatomic, assign) JceDouble jce_fDDY;
@property (nonatomic, assign) JceDouble jce_fDDZ;
@property (nonatomic, assign) JceDouble jce_fDDF;
@property (nonatomic, assign) JceDouble jce_fDDX5;
@property (nonatomic, assign) JceDouble jce_fDDY5;
@property (nonatomic, assign) JceDouble jce_fDDX60;
@property (nonatomic, assign) JceDouble jce_fDDY60;
@end

@interface HQSysHDxjl : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_iOrderTime;
@property (nonatomic, assign) JceInt32 jce_iShtType;
@property (nonatomic, assign) JceDouble jce_dOrderVol;
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceInt32 jce_iNum;
@property (nonatomic, assign) JceDouble jce_fChg;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vExts;
@end

@interface HQSysHOrderUnit : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dPadOrderPrice;
@property (nonatomic, assign) JceInt32 jce_iPadOrderNum;
@property (nonatomic, assign) JceDouble jce_dPressOrderPrice;
@property (nonatomic, assign) JceInt32 jce_iPressOrderNum;
@property (nonatomic, assign) JceDouble jce_dNowPrice;
@property (nonatomic, assign) JceInt32 jce_iVolume;
@property (nonatomic, assign) JceDouble jce_dBuyAmt;
@property (nonatomic, assign) JceDouble jce_dSellAmt;
@end

@interface HQSysHOrderClassify : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_iOrderTime;
@property (nonatomic, assign) JceInt32 jce_iShtType;
@property (nonatomic, strong) HQSysHOrderUnit* jce_stOrderUnit;
@end

@interface HQSysTagInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, strong) NSString* jce_sDesc;
@end

@interface HQSysRelationStock : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, strong) HQSysHYYInfo* jce_sYYInfo;
@property (nonatomic, strong) HQSysHFXInfo* jce_sFXInfo;
@property (nonatomic, strong) HQSysHKZZInfo* jce_sKZZInfo;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt16 jce_shtMainType;
@property (nonatomic, assign) JceInt16 jce_shtSubType;
@end

@interface HQSysBdStock : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, strong) HQSysHKZZInfo* jce_sKZZInfo;
@property (nonatomic, strong) NSString* jce_sName;
@end

@interface HQSysHStockBaseInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_fPeRatio;
@property (nonatomic, assign) JceInt8 jce_cCoinType;
@property (nonatomic, assign) JceDouble jce_fCirculationStocks;
@property (nonatomic, assign) JceDouble jce_fTotalMarketValue;
@property (nonatomic, assign) JceDouble jce_fCirculationMarketValue;
@property (nonatomic, assign) JceInt8 jce_bMarginMark;
@property (nonatomic, assign) JceInt8 jce_bSecuritiesMark;
@property (nonatomic, assign) JceDouble jce_fPriceRatio;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceDouble jce_dNetValue;
@property (nonatomic, assign) JceDouble jce_dZGB;
@property (nonatomic, assign) JceDouble jce_dDTSY;
@property (nonatomic, assign) HQSys_E_FINANCING_TARGET jce_eTarget;
@property (nonatomic, strong) NSArray<HQSysTagInfo*>* jce_vTags;
@property (nonatomic, assign) JceDouble jce_dPreClose;
@property (nonatomic, assign) JceDouble jce_dZTPrice;
@property (nonatomic, assign) JceDouble jce_dDTPrice;
@property (nonatomic, assign) JceBool jce_bDiffRight;
@property (nonatomic, assign) JceBool jce_bCDR;
@property (nonatomic, assign) JceBool jce_bGDR;
@property (nonatomic, assign) JceInt32 jce_iIPODate;
@property (nonatomic, assign) JceDouble jce_dFXJ;
@property (nonatomic, assign) JceDouble jce_dPE_Static;
@property (nonatomic, assign) JceDouble jce_dPE_TTM;
@property (nonatomic, assign) JceDouble jce_dDivide_TTM;
@property (nonatomic, assign) JceDouble jce_dDivide_LFY;
@property (nonatomic, assign) JceDouble jce_dDivideRate_TTM;
@property (nonatomic, assign) JceDouble jce_dDivideRate_LFY;
@property (nonatomic, assign) JceInt32 jce_iUnit;
@property (nonatomic, assign) JceInt32 jce_iSubType;
@property (nonatomic, assign) JceDouble jce_dMGSY;
@property (nonatomic, assign) JceDouble jce_dMGSYTTM;
@property (nonatomic, assign) JceDouble jce_dFXGB;
@property (nonatomic, strong) NSArray<HQSysRelationStock*>* jce_vRelationStock;
@property (nonatomic, strong) NSString* jce_sNameEx;
@property (nonatomic, strong) NSArray<HQSysBdStock*>* jce_vBdStock;
@property (nonatomic, assign) JceBool jce_bDeficit;
@property (nonatomic, assign) JceBool jce_bProControl;
@property (nonatomic, assign) JceInt8 jce_cPrecise;
@property (nonatomic, assign) JceFloat jce_fZDLimit;
@property (nonatomic, assign) JceBool jce_bIpoFlag;
@property (nonatomic, assign) JceBool jce_bIpoPrime;
@property (nonatomic, assign) JceInt32 jce_iSwitch;
@property (nonatomic, assign) JceDouble jce_dZCZB;
@property (nonatomic, strong) NSString* jce_sHyBlockCode;
@property (nonatomic, strong) NSString* jce_sHyBlockName;
@property (nonatomic, assign) JceInt8 jce_cFlag;
@property (nonatomic, assign) JceUInt32 jce_iEndDate;
@property (nonatomic, strong) NSString* jce_sRelationBlockId;
@property (nonatomic, assign) JceFloat jce_fMGSYBase;
@property (nonatomic, assign) JceFloat jce_fJZCSYL;
@property (nonatomic, assign) JceFloat jce_fYSZZL3Y;
@property (nonatomic, assign) JceFloat jce_fJLRZZL3Y;
@property (nonatomic, strong) HQSysHStaticDataHK* jce_stHK;
@property (nonatomic, assign) JceDouble jce_dJZC;
@property (nonatomic, assign) JceBool jce_bLXBD;
@property (nonatomic, strong) NSString* jce_sCodeExt;
@property (nonatomic, assign) JceBool jce_bTSZLSR;
@property (nonatomic, assign) JceInt16 jce_shtCQCXStatus;
@property (nonatomic, assign) JceInt16 jce_shtMarketMakeCount;
@property (nonatomic, assign) JceBool jce_bSellBack;
@property (nonatomic, assign) JceBool jce_bTransferShare;
@property (nonatomic, assign) JceDouble jce_fConvStockPrice;
@property (nonatomic, assign) JceInt32 jce_iConvStockEndDate;
@property (nonatomic, assign) JceInt16 jce_shtTradeType;
@property (nonatomic, assign) JceDouble jce_fSellBackPrice;
@property (nonatomic, assign) JceDouble jce_fRedemptionPrice;
@property (nonatomic, assign) JceDouble jce_fIssueAmount;
@property (nonatomic, assign) JceDouble jce_fRemainAmount;
@property (nonatomic, strong) NSString* jce_sBondRating;
@property (nonatomic, assign) JceBool jce_bIsZQXQ;
@property (nonatomic, assign) JceInt8 jce_cTradePrecise;
@property (nonatomic, assign) JceDouble jce_fSellBackTriggerPrice;
@property (nonatomic, assign) JceDouble jce_fForceRedemptionTriggerPrice;
@property (nonatomic, assign) JceBool jce_bRegist;
@property (nonatomic, assign) JceFloat jce_fMinPriceChange;
@end

@interface HQSysHMarketBaseInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSArray<HQSysHStockBaseInfo*>* jce_vStockInfo;
@end

@interface HQSysHMarketBaseBuf : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSData* jce_vBuf;
@property (nonatomic, assign) JceInt16 jce_shtCompress;
@property (nonatomic, strong) NSString* jce_sMD5;
@end

@interface HQSysStockStaticData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, assign) JceDouble jce_dLtg;
@property (nonatomic, assign) JceDouble jce_dZgb;
@property (nonatomic, assign) JceDouble jce_dJzc;
@property (nonatomic, assign) JceDouble jce_dNetValue;
@property (nonatomic, assign) JceDouble jce_dDTSY;
@property (nonatomic, assign) JceDouble jce_d5SumVol;
@property (nonatomic, assign) JceDouble jce_dZTPrice;
@property (nonatomic, assign) JceDouble jce_dDTPrice;
@property (nonatomic, assign) JceDouble jce_dPreClose;
@property (nonatomic, assign) JceInt8 jce_bMarginMark;
@property (nonatomic, assign) JceInt8 jce_bSecuritiesMark;
@property (nonatomic, assign) JceInt8 jce_cCoinType;
@property (nonatomic, assign) HQSys_E_FINANCING_TARGET jce_eTarget;
@property (nonatomic, strong) NSArray<HQSysTagInfo*>* jce_vTags;
@property (nonatomic, assign) JceInt32 jce_iIPODate;
@property (nonatomic, assign) JceDouble jce_dFXJ;
@end

@interface HQSysHStockSimHq : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceDouble jce_fOpen;
@property (nonatomic, assign) JceDouble jce_fHigh;
@property (nonatomic, assign) JceDouble jce_fLow;
@property (nonatomic, assign) JceDouble jce_fClose;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceDouble jce_fAmount;
@property (nonatomic, assign) JceDouble jce_fChgValue;
@property (nonatomic, assign) JceDouble jce_fChgRatio;
@property (nonatomic, assign) JceDouble jce_fZhenfu;
@property (nonatomic, strong) HQSysSZTData* jce_ztData;
@property (nonatomic, strong) HQSysBlockBasicInfo* jce_leadBlock;
@property (nonatomic, assign) JceDouble jce_dOpenAmount;
@property (nonatomic, assign) JceInt64 jce_lOpenVolume;
@end

@interface HQSysHStockQhHq : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dSettlementPrice;
@property (nonatomic, assign) JceDouble jce_dPreSettlementPrice;
@property (nonatomic, assign) JceDouble jce_dOpenInterest;
@property (nonatomic, assign) JceDouble jce_dPreOpenInterest;
@property (nonatomic, assign) JceInt32 jce_iEveryHand;
@property (nonatomic, assign) JceDouble jce_dDayIncrease;
@end

@interface HQSysHStockTB : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtZRType;
@property (nonatomic, assign) JceInt16 jce_shtZRStatus;
@property (nonatomic, assign) JceInt16 jce_shtFC;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, assign) JceInt16 jce_shtTpStatus;
@property (nonatomic, assign) JceInt16 jce_shtCQCXStatus;
@property (nonatomic, assign) JceInt8 jce_cDiffRight;
@property (nonatomic, assign) JceUInt16 jce_shtMarketMakeCount;
@property (nonatomic, assign) JceInt16 jce_shtFXMethod;
@end

@interface HQSysHStockHK : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dBalancePrice;
@property (nonatomic, assign) JceInt64 jce_lBalanceVol;
@property (nonatomic, assign) JceDouble jce_dReferencePrice;
@property (nonatomic, assign) JceDouble jce_dLowLimitPrice;
@property (nonatomic, assign) JceDouble jce_dHighLimitPrice;
@property (nonatomic, assign) JceInt8 jce_cDirection;
@property (nonatomic, assign) JceInt64 jce_lImbalanceVol;
@property (nonatomic, assign) JceDouble jce_dBuyLowLimitPrice;
@property (nonatomic, assign) JceDouble jce_dBuyHighLimitPrice;
@property (nonatomic, assign) JceDouble jce_dSellLowLimitPrice;
@property (nonatomic, assign) JceDouble jce_dSellHighLimitPrice;
@end

@interface HQSysHStockZQHq : UPTAFJceObject
@property (nonatomic, assign) JceFloat jce_fRoRPerYear;
@property (nonatomic, assign) JceDouble jce_dRatePer10w;
@property (nonatomic, assign) JceDouble jce_dRatePer1k;
@property (nonatomic, assign) JceInt16 jce_shtDays;
@property (nonatomic, assign) JceInt16 jce_shtZkDays;
@property (nonatomic, assign) JceInt32 jce_iJxFrom;
@property (nonatomic, assign) JceInt32 jce_iJxTo;
@property (nonatomic, assign) JceInt32 jce_iTodayBuy;
@property (nonatomic, assign) JceInt32 jce_iZjUserDay;
@property (nonatomic, assign) JceInt32 jce_iZjFetchDay;
@property (nonatomic, assign) JceFloat jce_fGZJZ;
@property (nonatomic, assign) JceFloat jce_fYJL;
@property (nonatomic, assign) JceDouble jce_dBdPrice;
@end

@interface HQSysHStockExHq : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lNowVol;
@property (nonatomic, assign) JceInt64 jce_lInside;
@property (nonatomic, assign) JceInt64 jce_lOutside;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vBuyp;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vBuyv;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vSellp;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vSellv;
@property (nonatomic, assign) JceDouble jce_fAveragePrice;
@property (nonatomic, assign) JceDouble jce_fZTPrice;
@property (nonatomic, assign) JceDouble jce_fDTPrice;
@property (nonatomic, assign) JceDouble jce_fTurnoverRate;
@property (nonatomic, assign) JceInt32 jce_iTradeMin;
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iTradeTime;
@property (nonatomic, assign) JceInt8 jce_bZDMark;
@property (nonatomic, assign) JceInt8 jce_bTransactionStatus;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vBuyNum;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vSellNum;
@property (nonatomic, assign) JceInt32 jce_iBSFlag;
@property (nonatomic, assign) JceDouble jce_dPeRatio;
@property (nonatomic, assign) JceDouble jce_dPriceRatio;
@property (nonatomic, assign) JceDouble jce_dZSZ;
@property (nonatomic, assign) JceDouble jce_dLTZS;
@property (nonatomic, assign) JceFloat jce_fJTPe;
@property (nonatomic, assign) JceFloat jce_fTTMPe;
@end

@interface HQSysHStockDeriveHq : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dLiangBi;
@property (nonatomic, assign) JceDouble jce_dUpSpeed;
@property (nonatomic, assign) JceInt64 jce_lTradeNum;
@property (nonatomic, assign) JceDouble jce_dBuyAvg;
@property (nonatomic, assign) JceDouble jce_dSellAvg;
@property (nonatomic, assign) JceInt64 jce_lBuyPriceNum;
@property (nonatomic, assign) JceInt64 jce_lSellPriceNum;
@property (nonatomic, assign) JceInt64 jce_lBuyVol;
@property (nonatomic, assign) JceInt64 jce_lSellVol;
@property (nonatomic, assign) HQSys_E_STOCK_TRADE_STATUS jce_eStatus;
@property (nonatomic, assign) JceDouble jce_dMainMoneyInflow5Min;
@property (nonatomic, assign) JceUInt32 jce_uiItemNum;
@property (nonatomic, assign) JceInt64 jce_lAtpVolume;
@property (nonatomic, assign) JceDouble jce_dAtpAmount;
@property (nonatomic, assign) JceInt32 jce_iGzhgAvgBP;
@property (nonatomic, assign) JceInt32 jce_iGzhgBP;
@property (nonatomic, assign) JceDouble jce_dIOPV;
@property (nonatomic, assign) JceDouble jce_d10DayReturnRate;
@property (nonatomic, assign) JceDouble jce_d52WeekMax;
@property (nonatomic, assign) JceDouble jce_d52WeekMin;
@property (nonatomic, assign) JceDouble jce_dHistoryMax;
@property (nonatomic, assign) JceDouble jce_dHistoryMin;
@property (nonatomic, assign) JceDouble jce_d3DayNetInflow;
@property (nonatomic, assign) JceDouble jce_d5DayNetInflow;
@property (nonatomic, assign) JceDouble jce_d10DayNetInflow;
@property (nonatomic, assign) JceDouble jce_d20DayNetInflow;
@property (nonatomic, assign) JceDouble jce_dMatchPrice;
@property (nonatomic, assign) JceInt64 jce_lMatchVol;
@property (nonatomic, assign) JceInt64 jce_lNoMatchVol;
@property (nonatomic, assign) JceDouble jce_d5DayChg;
@property (nonatomic, assign) JceDouble jce_d10DayChg;
@property (nonatomic, assign) JceDouble jce_d20DayChg;
@property (nonatomic, assign) JceDouble jce_dMonthChg;
@property (nonatomic, assign) JceDouble jce_dSeasonChg;
@property (nonatomic, assign) JceDouble jce_dYearChg;
@property (nonatomic, assign) JceInt8 jce_bActBSFlag;
@property (nonatomic, assign) JceDouble jce_dRefBeginPrice;
@property (nonatomic, assign) JceDouble jce_dRefEndPrice;
@property (nonatomic, assign) JceDouble jce_dThisYearChg;
@property (nonatomic, assign) JceDouble jce_dLztzj;
@property (nonatomic, assign) JceDouble jce_dldtzj;
@property (nonatomic, assign) JceFloat jce_fLastChg;
@property (nonatomic, assign) JceFloat jce_fWeiBi;
@property (nonatomic, assign) JceBool jce_bBlockTrade;
@property (nonatomic, assign) JceDouble jce_dTotalChg;
@property (nonatomic, assign) JceDouble jce_dCfgYjSz;
@property (nonatomic, assign) JceDouble jce_dCfgZjSz;
@property (nonatomic, assign) JceDouble jce_dCfgSz;
@property (nonatomic, assign) JceUInt32 jce_uiLYBCurrDayRank;
@property (nonatomic, assign) JceUInt32 jce_uiLYBPreDayRank;
@property (nonatomic, assign) JceFloat jce_fOpenChg;
@property (nonatomic, assign) JceFloat jce_fAucLast;
@property (nonatomic, assign) JceInt32 jce_iAucVol;
@property (nonatomic, assign) JceFloat jce_fAucTurn;
@end

@interface HQSysHBlockIndexHq : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_sHeadMarket;
@property (nonatomic, strong) NSString* jce_sHeadCode;
@property (nonatomic, strong) NSString* jce_sHeadName;
@property (nonatomic, assign) JceDouble jce_fHeadNow;
@property (nonatomic, assign) JceDouble jce_fHeadClose;
@property (nonatomic, assign) JceInt32 jce_iStockNum;
@property (nonatomic, assign) JceInt32 jce_iEqualNum;
@property (nonatomic, assign) JceInt32 jce_iZTNum;
@property (nonatomic, assign) JceInt32 jce_iUpNum;
@property (nonatomic, assign) JceInt32 jce_iDownNum;
@property (nonatomic, assign) JceDouble jce_dTotalMarketValue;
@property (nonatomic, assign) JceDouble jce_dCirculationMarketValue;
@property (nonatomic, assign) JceInt32 jce_iUpNDay;
@property (nonatomic, assign) JceDouble jce_d3DayChg;
@property (nonatomic, assign) JceDouble jce_d5DayChg;
@property (nonatomic, assign) JceDouble jce_d10DayChg;
@property (nonatomic, assign) JceDouble jce_dLead;
@end

@interface HQSysHStockCwHq : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dMGSY;
@property (nonatomic, assign) JceDouble jce_dMGJZC;
@property (nonatomic, assign) JceDouble jce_dJZCSYL;
@property (nonatomic, assign) JceDouble jce_dYSZZL3Y;
@property (nonatomic, assign) JceDouble jce_dJLSZZL3Y;
@end

@interface HQSysHOptionHq : UPTAFJceObject
@property (nonatomic, assign) JceFloat jce_fImpliedVolatility;
@property (nonatomic, assign) JceFloat jce_fDelta;
@property (nonatomic, assign) JceFloat jce_fGamma;
@property (nonatomic, assign) JceFloat jce_fVega;
@property (nonatomic, assign) JceFloat jce_fTheta;
@property (nonatomic, assign) JceFloat jce_fRho;
@property (nonatomic, assign) JceFloat jce_fLeverageRatio;
@property (nonatomic, assign) JceFloat jce_fRealLeverageRatio;
@property (nonatomic, assign) JceFloat jce_fInstrinsicValue;
@property (nonatomic, assign) JceFloat jce_fPremiumRatio;
@property (nonatomic, assign) JceFloat jce_fXushiDu;
@property (nonatomic, assign) JceFloat jce_fHisVol;
@end

@interface HQSysHStockHq : UPTAFJceObject
@property (nonatomic, assign) JceUInt16 jce_shtPrecise;
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) HQSysHStockSimHq* jce_stSimHq;
@property (nonatomic, strong) HQSysHStockExHq* jce_stExHq;
@property (nonatomic, strong) HQSysHStockQhHq* jce_stQhHq;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_stMF;
@property (nonatomic, strong) HQSysHDDERank* jce_stDDE;
@property (nonatomic, strong) HQSysHBlockIndexHq* jce_stBlockHq;
@property (nonatomic, strong) HQSysHStockDeriveHq* jce_stDeriveHq;
@property (nonatomic, strong) HQSysHStockCwHq* jce_stCwHq;
@property (nonatomic, strong) HQSysHStockZQHq* jce_stZQhq;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_st3MinMF;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_st5MinMF;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_st10MinMF;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_st30MinMF;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_st60MinMF;
@property (nonatomic, strong) HQSysHStockTB* jce_stTB;
@property (nonatomic, strong) HQSysHTolMoneyFlowSet* jce_stDayMF;
@property (nonatomic, strong) HQSysHOptionHq* jce_stOpt;
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysHSimpleMoneyFlow*>* jce_mapSimMf;
@property (nonatomic, strong) HQSysHStockHK* jce_stHK;
@end

@interface HQSysHStockHqSimple : UPTAFJceObject
@property (nonatomic, assign) JceUInt16 jce_precise;
@property (nonatomic, assign) JceInt16 jce_market;
@property (nonatomic, strong) NSString* jce_code;
@property (nonatomic, strong) NSString* jce_name;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSNumber*>* jce_m1;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSArray<NSNumber*>*>* jce_m2;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSString*>* jce_m3;
@end

@interface HQSysHStockSimHqNew : UPTAFJceObject
@property (nonatomic, assign) JceUInt16 jce_shtPrecise;
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceDouble jce_fOpen;
@property (nonatomic, assign) JceDouble jce_fHigh;
@property (nonatomic, assign) JceDouble jce_fLow;
@property (nonatomic, assign) JceDouble jce_fClose;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceDouble jce_fAmount;
@property (nonatomic, assign) JceDouble jce_fChgValue;
@property (nonatomic, assign) JceDouble jce_fChgRatio;
@property (nonatomic, assign) JceDouble jce_fZhenfu;
@property (nonatomic, strong) HQSysSZTData* jce_ztData;
@property (nonatomic, strong) HQSysBlockBasicInfo* jce_leadBlock;
@property (nonatomic, assign) JceDouble jce_dOpenAmount;
@property (nonatomic, assign) JceInt64 jce_lOpenVolume;
@property (nonatomic, assign) JceInt64 jce_lNowVol;
@property (nonatomic, assign) JceInt64 jce_lInside;
@property (nonatomic, assign) JceInt64 jce_lOutside;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vBuyp;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vBuyv;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vSellp;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vSellv;
@property (nonatomic, assign) JceDouble jce_fAveragePrice;
@property (nonatomic, assign) JceDouble jce_fZTPrice;
@property (nonatomic, assign) JceDouble jce_fDTPrice;
@property (nonatomic, assign) JceDouble jce_fTurnoverRate;
@property (nonatomic, assign) JceInt32 jce_iTradeMin;
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iTradeTime;
@property (nonatomic, assign) JceInt8 jce_bZDMark;
@property (nonatomic, assign) JceInt8 jce_bTransactionStatus;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vBuyNum;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vSellNum;
@property (nonatomic, assign) JceInt32 jce_iBSFlag;
@property (nonatomic, assign) JceDouble jce_dPeRatio;
@property (nonatomic, assign) JceDouble jce_dPriceRatio;
@property (nonatomic, assign) JceDouble jce_dZSZ;
@property (nonatomic, assign) JceDouble jce_dLTZS;
@property (nonatomic, assign) JceFloat jce_fJTPe;
@property (nonatomic, assign) JceFloat jce_fTTMPe;
@property (nonatomic, assign) JceDouble jce_dSettlementPrice;
@property (nonatomic, assign) JceDouble jce_dPreSettlementPrice;
@property (nonatomic, assign) JceDouble jce_dOpenInterest;
@property (nonatomic, assign) JceDouble jce_dPreOpenInterest;
@property (nonatomic, assign) JceInt32 jce_iEveryHand;
@property (nonatomic, assign) JceDouble jce_dDayIncrease;
@property (nonatomic, assign) JceInt16 jce_sHeadMarket;
@property (nonatomic, strong) NSString* jce_sHeadCode;
@property (nonatomic, strong) NSString* jce_sHeadName;
@property (nonatomic, assign) JceDouble jce_fHeadNow;
@property (nonatomic, assign) JceDouble jce_fHeadClose;
@property (nonatomic, assign) JceInt32 jce_iStockNum;
@property (nonatomic, assign) JceInt32 jce_iEqualNum;
@property (nonatomic, assign) JceInt32 jce_iZTNum;
@property (nonatomic, assign) JceInt32 jce_iUpNum;
@property (nonatomic, assign) JceInt32 jce_iDownNum;
@property (nonatomic, assign) JceDouble jce_dTotalMarketValue;
@property (nonatomic, assign) JceDouble jce_dCirculationMarketValue;
@property (nonatomic, assign) JceInt32 jce_iUpNDay;
@property (nonatomic, assign) JceDouble jce_d3DayChg;
@property (nonatomic, assign) JceDouble jce_d5DayChg;
@property (nonatomic, assign) JceDouble jce_d10DayChg;
@property (nonatomic, assign) JceDouble jce_dLead;
@property (nonatomic, assign) JceDouble jce_dMGSY;
@property (nonatomic, assign) JceDouble jce_dMGJZC;
@property (nonatomic, assign) JceDouble jce_dJZCSYL;
@property (nonatomic, assign) JceDouble jce_dYSZZL3Y;
@property (nonatomic, assign) JceDouble jce_dJLSZZL3Y;
@property (nonatomic, assign) JceFloat jce_fRoRPerYear;
@property (nonatomic, assign) JceDouble jce_dRatePer10w;
@property (nonatomic, assign) JceDouble jce_dRatePer1k;
@property (nonatomic, assign) JceInt16 jce_shtDays;
@property (nonatomic, assign) JceInt16 jce_shtZkDays;
@property (nonatomic, assign) JceInt32 jce_iJxFrom;
@property (nonatomic, assign) JceInt32 jce_iJxTo;
@property (nonatomic, assign) JceInt32 jce_iTodayBuy;
@property (nonatomic, assign) JceInt32 jce_iZjUserDay;
@property (nonatomic, assign) JceInt32 jce_iZjFetchDay;
@property (nonatomic, assign) JceFloat jce_fGZJZ;
@property (nonatomic, assign) JceFloat jce_fYJL;
@property (nonatomic, assign) JceInt16 jce_shtZRType;
@property (nonatomic, assign) JceInt16 jce_shtZRStatus;
@property (nonatomic, assign) JceInt16 jce_shtFC;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, assign) JceInt16 jce_shtTpStatus;
@property (nonatomic, assign) JceInt16 jce_shtCQCXStatus;
@property (nonatomic, assign) JceInt8 jce_cDiffRight;
@property (nonatomic, assign) JceUInt16 jce_shtMarketMakeCount;
@property (nonatomic, assign) JceFloat jce_fImpliedVolatility;
@property (nonatomic, assign) JceFloat jce_fDelta;
@property (nonatomic, assign) JceFloat jce_fGamma;
@property (nonatomic, assign) JceFloat jce_fVega;
@property (nonatomic, assign) JceFloat jce_fTheta;
@property (nonatomic, assign) JceFloat jce_fRho;
@property (nonatomic, assign) JceFloat jce_fLeverageRatio;
@property (nonatomic, assign) JceFloat jce_fRealLeverageRatio;
@property (nonatomic, assign) JceFloat jce_fInstrinsicValue;
@property (nonatomic, assign) JceFloat jce_fPremiumRatio;
@property (nonatomic, assign) JceFloat jce_fXushiDu;
@property (nonatomic, assign) JceFloat jce_fHisVol;
@property (nonatomic, assign) JceDouble jce_dBalancePrice;
@property (nonatomic, assign) JceInt64 jce_lBalanceVol;
@property (nonatomic, assign) JceDouble jce_dReferencePrice;
@property (nonatomic, assign) JceDouble jce_dLowLimitPrice;
@property (nonatomic, assign) JceDouble jce_dHighLimitPrice;
@property (nonatomic, assign) JceInt8 jce_cDirection;
@property (nonatomic, assign) JceInt64 jce_lImbalanceVol;
@property (nonatomic, assign) JceDouble jce_dBuyLowLimitPrice;
@property (nonatomic, assign) JceDouble jce_dBuyHighLimitPrice;
@property (nonatomic, assign) JceDouble jce_dSellLowLimitPrice;
@property (nonatomic, assign) JceDouble jce_dSellHighLimitPrice;
@property (nonatomic, assign) JceDouble jce_fDDX;
@property (nonatomic, assign) JceDouble jce_fDDY;
@property (nonatomic, assign) JceDouble jce_fDDZ;
@property (nonatomic, assign) JceDouble jce_fDDF;
@property (nonatomic, assign) JceDouble jce_fDDX5;
@property (nonatomic, assign) JceDouble jce_fDDY5;
@property (nonatomic, assign) JceDouble jce_fDDX60;
@property (nonatomic, assign) JceDouble jce_fDDY60;
@end

@interface HQSysHStockDeriveHqNew : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dLiangBi;
@property (nonatomic, assign) JceDouble jce_dUpSpeed;
@property (nonatomic, assign) JceInt64 jce_lTradeNum;
@property (nonatomic, assign) JceDouble jce_dBuyAvg;
@property (nonatomic, assign) JceDouble jce_dSellAvg;
@property (nonatomic, assign) JceInt64 jce_lBuyPriceNum;
@property (nonatomic, assign) JceInt64 jce_lSellPriceNum;
@property (nonatomic, assign) JceInt64 jce_lBuyVol;
@property (nonatomic, assign) JceInt64 jce_lSellVol;
@property (nonatomic, assign) HQSys_E_STOCK_TRADE_STATUS jce_eStatus;
@property (nonatomic, assign) JceDouble jce_dMainMoneyInflow5Min;
@property (nonatomic, assign) JceUInt32 jce_uiItemNum;
@property (nonatomic, assign) JceInt64 jce_lAtpVolume;
@property (nonatomic, assign) JceDouble jce_dAtpAmount;
@property (nonatomic, assign) JceInt32 jce_iGzhgAvgBP;
@property (nonatomic, assign) JceInt32 jce_iGzhgBP;
@property (nonatomic, assign) JceDouble jce_dIOPV;
@property (nonatomic, assign) JceDouble jce_d10DayReturnRate;
@property (nonatomic, assign) JceDouble jce_d52WeekMax;
@property (nonatomic, assign) JceDouble jce_d52WeekMin;
@property (nonatomic, assign) JceDouble jce_dHistoryMax;
@property (nonatomic, assign) JceDouble jce_dHistoryMin;
@property (nonatomic, assign) JceDouble jce_d3DayNetInflow;
@property (nonatomic, assign) JceDouble jce_d5DayNetInflow;
@property (nonatomic, assign) JceDouble jce_d10DayNetInflow;
@property (nonatomic, assign) JceDouble jce_d20DayNetInflow;
@property (nonatomic, assign) JceDouble jce_dMatchPrice;
@property (nonatomic, assign) JceInt64 jce_lMatchVol;
@property (nonatomic, assign) JceInt64 jce_lNoMatchVol;
@property (nonatomic, assign) JceDouble jce_d5DayChg;
@property (nonatomic, assign) JceDouble jce_d10DayChg;
@property (nonatomic, assign) JceDouble jce_d20DayChg;
@property (nonatomic, assign) JceDouble jce_dMonthChg;
@property (nonatomic, assign) JceDouble jce_dSeasonChg;
@property (nonatomic, assign) JceDouble jce_dYearChg;
@property (nonatomic, assign) JceInt8 jce_bActBSFlag;
@property (nonatomic, assign) JceDouble jce_dRefBeginPrice;
@property (nonatomic, assign) JceDouble jce_dRefEndPrice;
@property (nonatomic, assign) JceDouble jce_dThisYearChg;
@property (nonatomic, assign) JceDouble jce_dLztzj;
@property (nonatomic, assign) JceDouble jce_dldtzj;
@property (nonatomic, assign) JceFloat jce_fLastChg;
@property (nonatomic, assign) JceFloat jce_fWeiBi;
@property (nonatomic, assign) JceBool jce_bBlockTrade;
@property (nonatomic, assign) JceDouble jce_dTotalChg;
@property (nonatomic, assign) JceDouble jce_dCfgYjSz;
@property (nonatomic, assign) JceDouble jce_dCfgZjSz;
@property (nonatomic, assign) JceDouble jce_dCfgSz;
@property (nonatomic, assign) JceUInt32 jce_uiLYBCurrDayRank;
@property (nonatomic, assign) JceUInt32 jce_uiLYBPreDayRank;
@end

@interface HQSysHStockHqNew : UPTAFJceObject
@property (nonatomic, strong) HQSysHStockSimHqNew* jce_stSimHq;
@property (nonatomic, strong) HQSysHStockDeriveHqNew* jce_stDeriveHq;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_stMF;
@property (nonatomic, strong) HQSysHTolMoneyFlowSet* jce_stDayMF;
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysHSimpleMoneyFlow*>* jce_mapSimMf;
@end

@interface HQSysOptStockPrePostInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtStatus;
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceDouble jce_fChg;
@property (nonatomic, assign) JceDouble jce_fChgValue;
@end

@interface HQSysOptStockHqEx : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceDouble jce_fChg;
@property (nonatomic, assign) JceDouble jce_fChgValue;
@property (nonatomic, assign) JceDouble jce_dUpSpeed;
@property (nonatomic, assign) JceDouble jce_fTurnoverRate;
@property (nonatomic, assign) JceDouble jce_fPeRatio;
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_fDayMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_fDayMainMoneyRatio;
@property (nonatomic, assign) JceDouble jce_f3DayMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_f3DayMainMoneyRatio;
@property (nonatomic, assign) JceDouble jce_f5DayMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_f5DayMainMoneyRatio;
@property (nonatomic, assign) JceInt8 jce_bTransactionStatus;
@property (nonatomic, assign) JceInt8 jce_precise;
@property (nonatomic, assign) JceDouble jce_f3MinMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_f3MinMainMoneyRatio;
@property (nonatomic, assign) JceDouble jce_f5MinMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_f5MinMainMoneyRatio;
@property (nonatomic, strong) HQSysSZTData* jce_ztData;
@property (nonatomic, strong) HQSysBlockBasicInfo* jce_leadBlock;
@property (nonatomic, assign) JceDouble jce_f10MinMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_f10MinMainMoneyRatio;
@property (nonatomic, assign) JceDouble jce_f30MinMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_f30MinMainMoneyRatio;
@property (nonatomic, assign) JceDouble jce_f60MinMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_f60MinMainMoneyRatio;
@property (nonatomic, assign) JceDouble jce_dOpen;
@property (nonatomic, assign) JceDouble jce_dHigh;
@property (nonatomic, assign) JceDouble jce_dLow;
@property (nonatomic, assign) JceDouble jce_dPreClose;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceInt64 jce_lTradeNum;
@property (nonatomic, assign) JceDouble jce_dAmount;
@property (nonatomic, strong) HQSysHStockTB* jce_stTB;
@property (nonatomic, strong) NSArray<HQSysHRTMinDataSimp*>* jce_vRTMinData;
@property (nonatomic, assign) JceFloat jce_fZhenfu;
@property (nonatomic, assign) JceFloat jce_fLiangBi;
@property (nonatomic, assign) JceFloat jce_fWeibi;
@property (nonatomic, assign) JceUInt32 jce_uiLYBCurrDayRank;
@property (nonatomic, assign) JceUInt32 jce_uiLYBPreDayRank;
@property (nonatomic, assign) JceFloat jce_f5DayChg;
@property (nonatomic, assign) JceFloat jce_f10DayChg;
@property (nonatomic, assign) JceFloat jce_f20DayChg;
@property (nonatomic, assign) JceFloat jce_fMonthChg;
@property (nonatomic, assign) JceFloat jce_fSeasonChg;
@property (nonatomic, assign) JceFloat jce_fThisYearChg;
@property (nonatomic, assign) JceFloat jce_fYearChg;
@property (nonatomic, strong) HQSysOptStockPrePostInfo* jce_stPrePostHq;
@property (nonatomic, assign) JceInt32 jce_iStockNum;
@property (nonatomic, assign) JceDouble jce_fTotalMarketValue;
@property (nonatomic, assign) JceDouble jce_fCirculationMarketValue;
@property (nonatomic, assign) JceInt32 jce_iUpNDay;
@property (nonatomic, assign) JceInt16 jce_shtThisYearUpTotalDay;
@end

@interface HQSysHCQCX : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt16 jce_shtSetCode;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceDouble jce_fDividend;
@property (nonatomic, assign) JceDouble jce_fAllotmentPrice;
@property (nonatomic, assign) JceDouble jce_fBonusStock;
@property (nonatomic, assign) JceDouble jce_fAllotmentStock;
@end

@interface HQSysHAHItem : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_fNowPrice;
@property (nonatomic, assign) JceDouble jce_fChg;
@property (nonatomic, assign) JceDouble jce_fCurMarketValue;
@property (nonatomic, assign) JceDouble jce_fTurnoverRate;
@property (nonatomic, assign) JceInt32 jce_iTotalHand;
@property (nonatomic, assign) JceInt16 jce_shtOwnIndustry;
@property (nonatomic, assign) JceDouble jce_dChangeValue;
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iTradeTime;
@end

@interface HQSysHAHStock : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_fPremiumRate;
@property (nonatomic, strong) HQSysHAHItem* jce_stAItem;
@property (nonatomic, strong) HQSysHAHItem* jce_stHItem;
@end

@interface HQSysHTradePeriod : UPTAFJceObject
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vTradePeriod;
@end

@interface HQSysHHotPlateStock : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceDouble jce_fClose;
@property (nonatomic, assign) JceDouble jce_fPrice;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceInt64 jce_lAmount;
@property (nonatomic, assign) JceDouble jce_fOpen;
@property (nonatomic, assign) JceDouble jce_fHigh;
@property (nonatomic, assign) JceDouble jce_fLow;
@property (nonatomic, assign) JceInt8 jce_cDecimal;
@end

@interface HQSysHL2Monit : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_fPrice;
@property (nonatomic, assign) JceFloat jce_fChg;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceDouble jce_djb;
@property (nonatomic, assign) JceDouble jce_djs;
@property (nonatomic, assign) JceDouble jce_tljb;
@property (nonatomic, assign) JceDouble jce_tljs;
@property (nonatomic, assign) JceDouble jce_dbcb;
@property (nonatomic, assign) JceDouble jce_dbcs;
@property (nonatomic, assign) JceDouble jce_cdb;
@property (nonatomic, assign) JceDouble jce_cds;
@property (nonatomic, assign) JceDouble jce_dbb;
@property (nonatomic, assign) JceDouble jce_dbs;
@property (nonatomic, assign) JceInt32 jce_fzt;
@property (nonatomic, assign) JceInt32 jce_dkzt;
@property (nonatomic, assign) JceInt32 jce_fdt;
@property (nonatomic, assign) JceInt32 jce_dkdt;
@property (nonatomic, assign) JceDouble jce_ydb;
@property (nonatomic, assign) JceDouble jce_yds;
@property (nonatomic, assign) JceInt32 jce_hjfs;
@property (nonatomic, assign) JceInt32 jce_jsxd;
@property (nonatomic, assign) JceInt32 jce_ksft;
@property (nonatomic, assign) JceInt32 jce_gtts;
@end

@interface HQSysHSZDetail : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceInt32 jce_iNum;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceDouble jce_dVol;
@end

@interface HQSysHSZFY : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_fPrice;
@property (nonatomic, assign) JceFloat jce_fChg;
@property (nonatomic, assign) JceInt32 jce_iNum;
@property (nonatomic, assign) JceDouble jce_vol;
@property (nonatomic, assign) JceDouble jce_value;
@property (nonatomic, strong) NSArray<HQSysHSZDetail*>* jce_vDetail;
@end

@interface HQSysDxjlUnit : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iCount;
@property (nonatomic, assign) JceDouble jce_dVol;
@property (nonatomic, assign) JceDouble jce_dAmt;
@end

@interface HQSysHSZFYCom : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_fPrice;
@property (nonatomic, assign) JceFloat jce_fChg;
@property (nonatomic, strong) HQSysDxjlUnit* jce_stBuyUnit;
@property (nonatomic, strong) HQSysDxjlUnit* jce_stSellUnit;
@end

@interface HQSysHCPTMin : UPTAFJceObject
@property (nonatomic, assign) JceUInt32 jce_uiTradeSeq;
@property (nonatomic, assign) JceUInt32 jce_uiTradeDate;
@property (nonatomic, assign) JceUInt32 jce_uiTradeTime;
@end

@interface HQSysHTradeTime : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iBegTime;
@property (nonatomic, assign) JceInt32 jce_iEndTime;
@property (nonatomic, assign) JceInt32 jce_iFlag;
@end

@interface HQSysHSubTypeTradeTime : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_iSubType;
@property (nonatomic, strong) NSArray<HQSysHTradeTime*>* jce_vPeriod;
@end

@interface HQSysHTradePeriodTime : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysHTradeTime*>* jce_vPeriod;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSArray<HQSysHTradeTime*>*>* jce_mPeriod;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSDictionary<NSNumber*, NSArray<HQSysHTradeTime*>*>*>* jce_mSubPeriod;
@end

@interface HQSysHFinData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetCode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceDouble jce_dFlowStock;
@property (nonatomic, assign) JceInt16 jce_shtAddr;
@property (nonatomic, assign) JceInt16 jce_shtHy;
@property (nonatomic, assign) JceInt64 jce_lNewDate;
@property (nonatomic, assign) JceInt64 jce_lStartDate;
@property (nonatomic, assign) JceDouble jce_dTotalStock;
@property (nonatomic, assign) JceDouble jce_dNatinalStock;
@property (nonatomic, assign) JceDouble jce_dFounderStock;
@property (nonatomic, assign) JceDouble jce_dBStock;
@property (nonatomic, assign) JceDouble jce_dHStock;
@property (nonatomic, assign) JceDouble jce_dWorkerStock;
@property (nonatomic, assign) JceDouble jce_dTotalValue;
@property (nonatomic, assign) JceDouble jce_dFixedValue;
@property (nonatomic, assign) JceDouble jce_dFlowValue;
@property (nonatomic, assign) JceDouble jce_dWxValue;
@property (nonatomic, assign) JceDouble jce_dLongValue;
@property (nonatomic, assign) JceDouble jce_dFlowLoad;
@property (nonatomic, assign) JceDouble jce_dLongLoad;
@property (nonatomic, assign) JceDouble jce_dCapitalValue;
@property (nonatomic, assign) JceDouble jce_dRightValue;
@property (nonatomic, assign) JceDouble jce_dMainValue;
@property (nonatomic, assign) JceDouble jce_dMainInterest;
@property (nonatomic, assign) JceDouble jce_dOtherInterest;
@property (nonatomic, assign) JceDouble jce_dBusInterest;
@property (nonatomic, assign) JceDouble jce_dInvestInterest;
@property (nonatomic, assign) JceDouble jce_dBuTieValue;
@property (nonatomic, assign) JceDouble jce_dOutValue;
@property (nonatomic, assign) JceDouble jce_dLoseAdjust;
@property (nonatomic, assign) JceDouble jce_dProfitValue;
@property (nonatomic, assign) JceDouble jce_dAfterTaxValue;
@property (nonatomic, assign) JceDouble jce_dNetValue;
@property (nonatomic, assign) JceDouble jce_dUnDistibuteValue;
@property (nonatomic, assign) JceDouble jce_dAdjustValue;
@property (nonatomic, assign) JceDouble jce_dHalfYearFlag;
@end

@interface HQSysHMarketInit : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetCode;
@property (nonatomic, assign) JceUInt32 jce_uiTradeSeq;
@property (nonatomic, assign) JceUInt32 jce_uiTradeDate;
@end

@interface HQSysHStockIndustry : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_dNowPrice;
@property (nonatomic, assign) JceDouble jce_dChgValue;
@property (nonatomic, assign) JceDouble jce_dChgRatio;
@property (nonatomic, assign) JceInt8 jce_bTransactionStatus;
@property (nonatomic, strong) NSString* jce_sIndustryName;
@property (nonatomic, strong) NSString* jce_sIndustryCode;
@end

@interface HQSysHStockBlock : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_dChgRatio;
@end

@interface HQSysHStockDictInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_Code;
@property (nonatomic, assign) JceInt16 jce_Unit;
@property (nonatomic, strong) NSString* jce_Name;
@property (nonatomic, assign) JceInt32 jce_VolBase;
@property (nonatomic, assign) JceInt8 jce_precise;
@property (nonatomic, assign) JceFloat jce_Close;
@property (nonatomic, assign) JceInt16 jce_Market;
@property (nonatomic, assign) JceInt16 jce_BaseFreshCount;
@property (nonatomic, assign) JceInt16 jce_GbbqFreshCount;
@property (nonatomic, assign) JceInt16 jce_iType;
@property (nonatomic, assign) JceBool jce_bDiffRight;
@property (nonatomic, assign) JceBool jce_bCDR;
@property (nonatomic, assign) JceBool jce_bGDR;
@property (nonatomic, assign) JceInt32 jce_iUnit;
@property (nonatomic, assign) JceBool jce_bDeficit;
@property (nonatomic, assign) JceBool jce_bProControl;
@end

@interface HQSysHOrderItem : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) HQSys_E_ORDER_ITEM_TYPE jce_eStatus;
@end

@interface HQSysHOrderQueue : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lTime;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceInt8 jce_bBuySell;
@property (nonatomic, assign) JceInt64 jce_lOrderNum;
@property (nonatomic, strong) NSArray<HQSysHOrderItem*>* jce_vOrder;
@end

@interface HQSysHTransaction : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lTime;
@property (nonatomic, assign) JceInt64 jce_lTradeNo;
@property (nonatomic, assign) JceInt8 jce_bType;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceInt64 jce_lBuyNo;
@property (nonatomic, assign) JceInt64 jce_lSellNo;
@property (nonatomic, assign) JceInt64 jce_lBargainNo;
@property (nonatomic, assign) JceInt16 jce_shtTradeType;
@end

@interface HQSysHOrderRec : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lTime;
@property (nonatomic, assign) JceInt64 jce_lTradeNo;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) HQSys_E_ORDER_TRADE_KINDE jce_eTradeKind;
@property (nonatomic, assign) HQSys_E_ORDER_OPERATE_TYPE jce_eOPType;
@property (nonatomic, assign) JceInt64 jce_lOrderNo;
@end

@interface HQSysHPriceVolInfo : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceInt64 jce_lVol;
@property (nonatomic, assign) JceInt64 jce_lBuyVol;
@property (nonatomic, assign) JceInt64 jce_lSellVol;
@property (nonatomic, assign) JceInt64 jce_lBuyNum;
@property (nonatomic, assign) JceInt64 jce_lSellNum;
@end

@interface HQSysHTransVolNum : UPTAFJceObject
@property (nonatomic, assign) HQSys_TRANS_VOL_RANGE jce_eVolRange;
@property (nonatomic, assign) JceInt64 jce_lBuyNum;
@property (nonatomic, assign) JceInt64 jce_lSellNum;
@end

@interface HQSysHOrderSumStat : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dBuyAvgPrice;
@property (nonatomic, assign) JceInt64 jce_lBuyVol;
@property (nonatomic, assign) JceInt64 jce_lBuyOrderCount;
@property (nonatomic, assign) JceInt64 jce_lBuyNum;
@property (nonatomic, assign) JceInt64 jce_lBigBuyVol;
@property (nonatomic, assign) JceInt64 jce_lAllBuyNum;
@property (nonatomic, assign) JceInt64 jce_lBuyCancelNum;
@property (nonatomic, assign) JceInt64 jce_lBigBuyCancelNum;
@property (nonatomic, assign) JceDouble jce_dSellAvgPrice;
@property (nonatomic, assign) JceInt64 jce_lSellVol;
@property (nonatomic, assign) JceInt64 jce_lSellOrderCount;
@property (nonatomic, assign) JceInt64 jce_lSellNum;
@property (nonatomic, assign) JceInt64 jce_lBigSellVol;
@property (nonatomic, assign) JceInt64 jce_lAllSellNum;
@property (nonatomic, assign) JceInt64 jce_lSellCancelNum;
@property (nonatomic, assign) JceInt64 jce_lBigSellCancelNum;
@property (nonatomic, strong) HQSysHPriceVolInfo* jce_obstruction;
@property (nonatomic, strong) HQSysHPriceVolInfo* jce_support;
@end

@interface HQSysHOrderQueuePrice : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceInt64 jce_lSumVol;
@property (nonatomic, assign) JceInt64 jce_lBigVol;
@property (nonatomic, assign) JceInt64 jce_lTotalOrder;
@property (nonatomic, assign) HQSys_E_ORDER_ITEM_TYPE jce_eStatus;
@end

@interface HQSysHPriceAmount : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lOrderNo;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) HQSys_E_ORDER_ITEM_TYPE jce_eStatus;
@end

@interface HQSysHZHRankData : UPTAFJceObject
@property (nonatomic, assign) HQSys_H_ZH_RANK_TYPE jce_eRankType;
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceDouble jce_dNowPrice;
@property (nonatomic, assign) JceDouble jce_dRankValue;
@property (nonatomic, assign) JceDouble jce_dPrevClose;
@end

@interface HQSysHStockRankData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetCode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceDouble jce_dRankValue;
@end

@interface HQSysHBKLedData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetCode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceDouble jce_dRankValue;
@property (nonatomic, assign) JceInt32 jce_iUpNum;
@property (nonatomic, assign) JceInt32 jce_iEqualNum;
@property (nonatomic, assign) JceInt32 jce_iDownNum;
@property (nonatomic, strong) NSArray<HQSysHStockRankData*>* jce_vStock;
@end

@interface HQSysHDDZData : UPTAFJceObject
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_stMfAmt;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_stMfVol;
@property (nonatomic, strong) HQSysHTolMoneyFlow* jce_stMfNum;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceInt32 jce_iDate;
@end

@interface HQSysHTypeStatusData : UPTAFJceObject
@property (nonatomic, assign) HQSys_E_MARKET_TYPE jce_eType;
@property (nonatomic, assign) JceInt32 jce_iStatus;
@end

@interface HQSysHMarketStatusData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, assign) JceInt32 jce_iStatus;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSNumber*>* jce_mapTypeStaus;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSDictionary<NSNumber*, NSNumber*>*>* jce_mapSubTypeStatus;
@end

@interface HQSysHFileInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sFileName;
@property (nonatomic, assign) JceInt32 jce_iPos;
@property (nonatomic, strong) NSString* jce_sCheckSum;
@property (nonatomic, assign) JceInt32 jce_iLen;
@property (nonatomic, assign) JceBool jce_bCompress;
@end

@interface HQSysHFileData : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sFileName;
@property (nonatomic, assign) JceBool jce_bCompress;
@property (nonatomic, assign) JceBool jce_bChg;
@property (nonatomic, strong) NSData* jce_vBuf;
@property (nonatomic, assign) JceBool jce_bRemain;
@property (nonatomic, strong) NSString* jce_sCheckSum;
@end

@interface HQSysHRigthUnit : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sRightId;
@property (nonatomic, strong) NSString* jce_sBeginDate;
@property (nonatomic, strong) NSString* jce_sEndDate;
@end

@interface HQSysHUserRight : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sUserId;
@property (nonatomic, strong) NSArray<HQSysHRigthUnit*>* jce_vRight;
@property (nonatomic, strong) NSString* jce_sRd;
@property (nonatomic, strong) NSString* jce_sSsoDate;
@property (nonatomic, assign) JceBool jce_bAllowRepeatLogin;
@end

@interface HQSysHLoginInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sUserId;
@property (nonatomic, strong) NSString* jce_sRd;
@property (nonatomic, strong) NSString* jce_sPermission;
@property (nonatomic, strong) NSString* jce_sSsoTime;
@property (nonatomic, strong) NSString* jce_sCltTime;
@property (nonatomic, assign) JceInt64 jce_lCltConId;
@property (nonatomic, assign) HQSys_E_LOGIN_CLIENT_TYPE jce_eCltType;
@property (nonatomic, assign) JceInt16 jce_shtDelFlag;
@property (nonatomic, strong) HQSysHUserRight* jce_stUserRight;
@property (nonatomic, assign) HQSys_E_LOGIN_AUTH_TYPE jce_eAuthType;
@property (nonatomic, strong) NSString* jce_sSvrId;
@property (nonatomic, strong) NSString* jce_sGuid;
@end

@interface HQSysHKickOutInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysHLoginInfo* jce_stInfo;
@property (nonatomic, strong) NSString* jce_sSvrId;
@property (nonatomic, assign) HQSys_E_KICK_OUT_STATUS jce_eStatus;
@property (nonatomic, assign) HQSys_E_KICK_OUT_REASON jce_eReason;
@end

@interface HQSysHAuthServerInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sSvrId;
@end

@interface HQSysHHeartBeatReq : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sSvrId;
@end

@interface HQSysHHeartBeatRsp : UPTAFJceObject
@property (nonatomic, assign) JceBool jce_bNeedReg;
@property (nonatomic, strong) NSString* jce_sMdsId;
@end

@interface HQSysHOEMAuthResult : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iRet;
@property (nonatomic, strong) NSString* jce_sMsg;
@property (nonatomic, assign) JceBool jce_bHSL2;
@property (nonatomic, assign) JceBool jce_bHKL2;
@property (nonatomic, assign) JceBool jce_bUSL2;
@property (nonatomic, strong) NSString* jce_sSerialNo;
@end

@interface HQSysHAuthServerPacket : UPTAFJceObject
@property (nonatomic, assign) HQSys_E_AUTH_TYPE jce_eAuthType;
@property (nonatomic, strong) NSData* jce_vGuid;
@property (nonatomic, strong) NSString* jce_sXua;
@property (nonatomic, strong) NSString* jce_sToken;
@property (nonatomic, strong) NSString* jce_sSvrId;
@property (nonatomic, strong) NSString* jce_sAuthAddr;
@property (nonatomic, assign) JceInt16 jce_shtClearFlag;
@property (nonatomic, strong) NSArray<HQSysHLoginInfo*>* jce_vInfo;
@property (nonatomic, strong) HQSysHLoginInfo* jce_stInfo;
@property (nonatomic, strong) NSString* jce_sSerialNo;
@property (nonatomic, strong) HQSysHKickOutInfo* jce_stKickOutInfo;
@end

@interface HQSysHNewStockPerform : UPTAFJceObject
@property (nonatomic, strong) HQSysHStock* jce_stStock;
@property (nonatomic, assign) JceDouble jce_dSignProfit;
@property (nonatomic, assign) JceInt32 jce_iZtNDay;
@property (nonatomic, assign) JceBool jce_bBreakPlate;
@property (nonatomic, strong) NSString* jce_sListDate;
@end

@interface HQSysHRadarQt : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) RadarDataRadarQt* jce_stQt;
@end

@interface HQSysHRadarChg : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) RadarDataRadarChg* jce_stChg;
@end

@interface HQSysHSyntInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceDouble jce_dZdf;
@property (nonatomic, assign) JceDouble jce_dltsz;
@property (nonatomic, assign) JceDouble jce_dMainBuy;
@property (nonatomic, assign) JceFloat jce_fMainBuyRatio;
@property (nonatomic, assign) JceFloat jce_fMainSellRatio;
@property (nonatomic, assign) JceFloat jce_fDDX;
@property (nonatomic, assign) JceFloat jce_fDDY;
@property (nonatomic, assign) JceFloat jce_fDDZ;
@property (nonatomic, assign) JceInt32 jce_intVol500;
@property (nonatomic, assign) JceInt32 jce_intVol1000;
@property (nonatomic, assign) JceInt32 jce_intVol5000;
@property (nonatomic, assign) JceInt32 jce_intVol9999;
@property (nonatomic, assign) JceInt32 jce_intAmt100;
@property (nonatomic, assign) JceInt32 jce_intAmt200;
@property (nonatomic, assign) JceInt32 jce_intAmt500;
@property (nonatomic, assign) JceDouble jce_dMainBuy3;
@property (nonatomic, assign) JceDouble jce_dMainBuy5;
@property (nonatomic, assign) JceDouble jce_dMainBuy10;
@property (nonatomic, assign) JceInt32 jce_iMainBuyRedDay;
@property (nonatomic, assign) JceInt32 jce_iMainBuyRedDay5;
@property (nonatomic, assign) JceInt32 jce_iMainBuyRedDay10;
@property (nonatomic, assign) JceFloat jce_fDDX3;
@property (nonatomic, assign) JceFloat jce_fDDX5;
@property (nonatomic, assign) JceFloat jce_fDDX10;
@property (nonatomic, assign) JceInt32 jce_iDDXRedDay;
@property (nonatomic, assign) JceInt32 jce_iDDXRedDay5;
@property (nonatomic, assign) JceInt32 jce_iDDXRedDay10;
@property (nonatomic, assign) JceFloat jce_fMainFlowTrend;
@end

@interface HQSysHPankouRadio : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iOrderLevel;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceDouble jce_dVolume;
@end

@interface HQSysHWarrantData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_dNowPrice;
@property (nonatomic, assign) JceDouble jce_dAmount;
@property (nonatomic, assign) JceDouble jce_dChg;
@property (nonatomic, assign) JceInt32 jce_iMaturityDate;
@property (nonatomic, assign) JceInt8 jce_cPrecise;
@end

@interface HQSysHWarrantHkData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_dNowPrice;
@property (nonatomic, assign) JceDouble jce_dChgValue;
@property (nonatomic, assign) JceDouble jce_dChg;
@end

@interface HQSysHOptionBaseInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_market;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSString* jce_sUnderlyingCode;
@property (nonatomic, strong) NSString* jce_sUnerlyingName;
@property (nonatomic, assign) JceInt8 jce_cOptionType;
@property (nonatomic, assign) JceInt8 jce_cCallOrPut;
@property (nonatomic, assign) JceInt32 jce_iContractMutiplierUnit;
@property (nonatomic, assign) JceDouble jce_dExercisePrice;
@property (nonatomic, assign) JceInt32 jce_iEndDate;
@property (nonatomic, assign) JceInt32 jce_iExerciseDate;
@property (nonatomic, assign) JceDouble jce_dPreClose;
@property (nonatomic, assign) JceDouble jce_dPreSettlPrice;
@property (nonatomic, assign) JceDouble jce_dUpLimit;
@property (nonatomic, assign) JceDouble jce_dDownLimit;
@property (nonatomic, assign) JceInt32 jce_iRoundLot;
@property (nonatomic, assign) JceFloat jce_fTickSize;
@property (nonatomic, assign) JceInt8 jce_cTradeFlag;
@property (nonatomic, assign) JceInt8 jce_cTradePhase;
@property (nonatomic, assign) JceInt8 jce_cExpireStatus;
@property (nonatomic, assign) JceInt8 jce_cChangeStatus;
@property (nonatomic, assign) JceInt32 jce_iDaysLeft;
@property (nonatomic, strong) NSString* jce_sContractType;
@property (nonatomic, assign) JceUInt16 jce_usUnderlyingMarket;
@end

@interface HQSysHGGTMarketRate : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dSettlementBuy;
@property (nonatomic, assign) JceDouble jce_dSettlementSell;
@property (nonatomic, assign) JceDouble jce_dReferenceBuy;
@property (nonatomic, assign) JceDouble jce_dReferenceSell;
@end

@interface HQSysHGGTRate : UPTAFJceObject
@property (nonatomic, strong) HQSysHGGTMarketRate* jce_shRate;
@property (nonatomic, strong) HQSysHGGTMarketRate* jce_szRate;
@end

@interface HQSysHGGTDividendShare : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_nRecordDate;
@property (nonatomic, assign) JceDouble jce_dDivPs_HK;
@property (nonatomic, assign) JceDouble jce_dSpeDivPer_HK;
@property (nonatomic, assign) JceDouble jce_dSwapRate;
@property (nonatomic, assign) JceDouble jce_dBonusSHR_X;
@property (nonatomic, assign) JceDouble jce_dBonusSHR_Y;
@property (nonatomic, assign) JceDouble jce_dTurnAdd_X;
@property (nonatomic, assign) JceDouble jce_dTurnAdd_Y;
@end

@interface HQSysHGGTSpinOffMerger : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dCOMB_X;
@property (nonatomic, assign) JceDouble jce_dCOMB_Y;
@property (nonatomic, assign) JceDouble jce_dSPLI_X;
@property (nonatomic, assign) JceDouble jce_dSPLI_Y;
@end

@interface HQSysHGGTQXInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysHGGTDividendShare* jce_stShare;
@property (nonatomic, strong) HQSysHGGTSpinOffMerger* jce_stSpinOffMerge;
@end

@interface HQSysHGGTStockQXInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysHStockUnique* jce_stock;
@property (nonatomic, strong) HQSysHGGTQXInfo* jce_qxData;
@end

@interface HQSysHCheckData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt8 jce_cCheckVal;
@property (nonatomic, assign) JceInt32 jce_iTime;
@end

@interface HQSysHSectionCheckData : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lBeginDate;
@property (nonatomic, assign) JceInt64 jce_lEndDate;
@property (nonatomic, strong) NSString* jce_sMD5;
@end

@interface HQSysHEtfComponentInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_qty;
@property (nonatomic, assign) JceInt8 jce_subFlag;
@property (nonatomic, assign) JceFloat jce_preminumRatio;
@property (nonatomic, assign) JceDouble jce_dSubCashAmount;
@end

@interface HQSysHEtfBasicInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradingDay;
@property (nonatomic, assign) JceInt32 jce_iPreTradingDay;
@property (nonatomic, assign) JceDouble jce_dEstimateCashComponent;
@property (nonatomic, assign) JceFloat jce_fMaxCashRatio;
@property (nonatomic, assign) JceBool jce_bPublish;
@property (nonatomic, assign) JceInt32 jce_iCreationRedemptionUnit;
@property (nonatomic, assign) JceInt32 jce_iTotalRecordNum;
@property (nonatomic, assign) JceBool jce_bCreation;
@property (nonatomic, assign) JceBool jce_bRedemption;
@property (nonatomic, assign) JceDouble jce_dCashComponent;
@property (nonatomic, assign) JceDouble jce_dNAVPreCU;
@property (nonatomic, assign) JceDouble jce_dNAV;
@end

@interface HQSysHEtfDescInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSString* jce_sFundId1;
@property (nonatomic, strong) NSString* jce_sUnderlyingCode;
@property (nonatomic, strong) HQSysHEtfBasicInfo* jce_stBaseInfo;
@property (nonatomic, strong) NSArray<HQSysHEtfComponentInfo*>* jce_vComponents;
@end

@interface HQSysHTypeSubType : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vecSubType;
@end

@interface HQSysH5AverageVolData : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_d5AveVol;
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@end

@interface HQSysHStockAuction : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceUInt32 jce_uiDate;
@property (nonatomic, assign) JceUInt32 jce_uiTime;
@property (nonatomic, assign) JceFloat jce_fPreClose;
@property (nonatomic, assign) JceFloat jce_fOpen;
@property (nonatomic, assign) JceFloat jce_fNow;
@property (nonatomic, assign) JceUInt32 jce_uiVol;
@property (nonatomic, assign) JceUInt32 jce_uiUnmatchVol;
@property (nonatomic, assign) JceFloat jce_fUmatchValue;
@property (nonatomic, assign) JceInt16 jce_shtDirection;
@end

@interface HQSysHStockAHUnique : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) HQSys_E_AM_STK_TYPE jce_eAMType;
@end

@interface HQSysHSNFundsData : UPTAFJceObject
@property (nonatomic, assign) JceFloat jce_fNet;
@property (nonatomic, assign) JceFloat jce_fBalance;
@property (nonatomic, assign) JceFloat jce_fTotal;
@property (nonatomic, assign) JceInt16 jce_shtType;
@end

@interface HQSysHSNNetData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceDouble jce_dSZNet;
@property (nonatomic, assign) JceDouble jce_dSHNet;
@end

@interface HQSysHTypeFilt : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vecSubType;
@end

@interface HQSysHMarketTypeFilt : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vecType;
@end

@interface HQSysHJPJLStock : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSString* jce_sCodeEx;
@property (nonatomic, assign) HQSys_E_JPJL_MATCH_TYPE jce_eMatchType;
@property (nonatomic, assign) HQSys_E_STOCK_STATUS_TYPE jce_eStatus;
@property (nonatomic, assign) JceInt16 jce_sthType;
@property (nonatomic, assign) JceInt16 jce_shtSubType;
@end

@interface HQSysHZDFenBuInfo : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, NSNumber*>* jce_mapFenbu;
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iZChg5Num;
@property (nonatomic, assign) JceInt32 jce_iDChg5Num;
@property (nonatomic, assign) JceInt32 jce_iZTNum;
@property (nonatomic, assign) JceInt32 jce_iDTNum;
@property (nonatomic, assign) JceInt32 jce_iSuspendNum;
@property (nonatomic, assign) JceInt32 jce_iRiseNum;
@property (nonatomic, assign) JceInt32 jce_iFallNum;
@property (nonatomic, assign) JceInt32 jce_iFlatNum;
@end

@interface HQSysHCurrencyInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@end



