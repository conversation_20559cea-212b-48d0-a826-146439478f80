// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `Common.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>

enum {
    HQSys_E_CLI_COMP_TYPE_E_CLI_NO_COMP = 0,
    HQSys_E_CLI_COMP_TYPE_E_CLI_COMPRESS = 1,
    HQSys_E_CLI_COMP_TYPE_E_CLT_LZMA_COMPRESS = 2,
    HQSys_E_CLI_COMP_TYPE_E_CLT_LZ4_COMPRESS = 3,
    HQSys_E_CLI_COMP_TYPE_E_CLT_SNAPPY_COMPRESS = 4
};
#define HQSys_E_CLI_COMP_TYPE NSInteger


enum {
    HQSys_E_HQ_PACKET_TYPE_E_HQ_NORMAL = 0,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_DIFF_PACKET = 100,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_LZ4_PACKET = 101,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_LZ4_DIFF = 102,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_LZMA_PACKET = 103,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_LZMA_DIFF = 104,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_SNAPPY_PACKET = 105,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_SNAPPY_DIFF = 106,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_GAP_DIFF_PACKET = 110,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_LZ4_GAP_DIFF = 112,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_LZMA_GAP_DIFF = 114,
    HQSys_E_HQ_PACKET_TYPE_E_HQ_SNAPPY_GAP_DIFF = 116
};
#define HQSys_E_HQ_PACKET_TYPE NSInteger


enum {
    HQSys_HISTORY_DATA_TYPE_HDT_DAY_KLINE = 0,
    HQSys_HISTORY_DATA_TYPE_HDT_MIN1_KLINE = 1,
    HQSys_HISTORY_DATA_TYPE_HDT_MIN5_KLINE = 2,
    HQSys_HISTORY_DATA_TYPE_HDT_MIN15_KLINE = 3,
    HQSys_HISTORY_DATA_TYPE_HDT_MIN30_KLINE = 4,
    HQSys_HISTORY_DATA_TYPE_HDT_MIN60_KLINE = 5,
    HQSys_HISTORY_DATA_TYPE_HDT_RTMIN = 6,
    HQSys_HISTORY_DATA_TYPE_HDT_WEEK_KLINE = 7,
    HQSys_HISTORY_DATA_TYPE_HDT_MONTH_KLINE = 8,
    HQSys_HISTORY_DATA_TYPE_HDT_SEASON_KLINE = 9,
    HQSys_HISTORY_DATA_TYPE_HDT_YEAR_KLINE = 10,
    HQSys_HISTORY_DATA_TYPE_HDT_DAY_MONEYFLOW = 11,
    HQSys_HISTORY_DATA_TYPE_HDT_TICK = 12,
    HQSys_HISTORY_DATA_TYPE_HDT_WEEK_MONEYFLOW = 13,
    HQSys_HISTORY_DATA_TYPE_HDT_MONTH_MONEYFLOW = 14,
    HQSys_HISTORY_DATA_TYPE_HDT_SEASON_MONEYFLOW = 15,
    HQSys_HISTORY_DATA_TYPE_HDT_YEAR_MONEYFLOW = 16,
    HQSys_HISTORY_DATA_TYPE_HDT_FLOW_MIN1 = 17,
    HQSys_HISTORY_DATA_TYPE_HDT_FLOW_MIN5 = 18,
    HQSys_HISTORY_DATA_TYPE_HDT_FLOW_MIN15 = 19,
    HQSys_HISTORY_DATA_TYPE_HDT_FLOW_MIN30 = 20,
    HQSys_HISTORY_DATA_TYPE_HDT_FLOW_MIN60 = 21,
    HQSys_HISTORY_DATA_TYPE_HDT_MIN120_KLINE = 22,
    HQSys_HISTORY_DATA_TYPE_HDT_FLOW_MIN120 = 23
};
#define HQSys_HISTORY_DATA_TYPE NSInteger


enum {
    HQSys_E_MONEYFLOW_TYPE_E_MONEYFLOW_DOUBLE = 0,
    HQSys_E_MONEYFLOW_TYPE_E_MONEYFLOW_INITIATIVE = 1,
    HQSys_E_MONEYFLOW_TYPE_E_MONEYFLOW_PASSIVE = 2,
    HQSys_E_MONEYFLOW_TYPE_E_MONEYFLOW_DOUBLE_NEW = 3
};
#define HQSys_E_MONEYFLOW_TYPE NSInteger


enum {
    HQSys_E_QX_MODE_EQM_QX_NONE = 0,
    HQSys_E_QX_MODE_EQM_QX_FRONT = 1,
    HQSys_E_QX_MODE_EQM_QX_BACK = 2,
    HQSys_E_QX_MODE_EQM_QX_FRONT_VOL = 3,
    HQSys_E_QX_MODE_EQM_QX_BACK_VOL = 4
};
#define HQSys_E_QX_MODE NSInteger


enum {
    HQSys_E_STOCK_TYPE_SUMMARAY_ESTS_ALL = 0,
    HQSys_E_STOCK_TYPE_SUMMARAY_ESTS_STOCK = 1,
    HQSys_E_STOCK_TYPE_SUMMARAY_ESTS_BLOCK = 2,
    HQSys_E_STOCK_TYPE_SUMMARAY_ESTS_ZHISHU = 3
};
#define HQSys_E_STOCK_TYPE_SUMMARAY NSInteger


enum {
    HQSys_E_BLOCK_TYPE_EBT_BK_ALL = 0,
    HQSys_E_BLOCK_TYPE_EBT_BK_HY = 22,
    HQSys_E_BLOCK_TYPE_EBT_BK_DQ = 23,
    HQSys_E_BLOCK_TYPE_EBT_BK_GN = 24
};
#define HQSys_E_BLOCK_TYPE NSInteger


enum {
    HQSys_E_STOCK_CATEGORY_STKC_SH_ZS = 1,
    HQSys_E_STOCK_CATEGORY_STKC_SH_AG = 2,
    HQSys_E_STOCK_CATEGORY_STKC_SH_BG = 3,
    HQSys_E_STOCK_CATEGORY_STKC_SH_ZQ = 4,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_ZS = 5,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_AG = 6,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_BG = 7,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_ZQ = 8,
    HQSys_E_STOCK_CATEGORY_STKC_SH_JJ = 9,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_JJ = 10,
    HQSys_E_STOCK_CATEGORY_STKC_SH_QZ = 11,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_QZ = 12,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_SM = 13,
    HQSys_E_STOCK_CATEGORY_STKC_NO_LO = 14,
    HQSys_E_STOCK_CATEGORY_STKC_SH_ZS_IM9 = 15,
    HQSys_E_STOCK_CATEGORY_STKC_SH_ZS_IM6 = 16,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_ALL_AG = 17,
    HQSys_E_STOCK_CATEGORY_STKC_AH = 20,
    HQSys_E_STOCK_CATEGORY_STKC_SH_TS = 18,
    HQSys_E_STOCK_CATEGORY_STKC_SH_FX = 19,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_TS = 21,
    HQSys_E_STOCK_CATEGORY_STKC_BK_HY = 22,
    HQSys_E_STOCK_CATEGORY_STKC_BK_DQ = 23,
    HQSys_E_STOCK_CATEGORY_STKC_BK_GN = 24,
    HQSys_E_STOCK_CATEGORY_STKC_SH_HLTCDR = 25,
    HQSys_E_STOCK_CATEGORY_STKC_SH_CDR = 26,
    HQSys_E_STOCK_CATEGORY_STKC_SH_KCB = 27,
    HQSys_E_STOCK_CATEGORY_STKC_SH_ZQHG = 28,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_ZQHG = 29,
    HQSys_E_STOCK_CATEGORY_STKC_SH_PURC = 30,
    HQSys_E_STOCK_CATEGORY_STKC_QH = 31,
    HQSys_E_STOCK_CATEGORY_STKC_FOREX = 32,
    HQSys_E_STOCK_CATEGORY_STKC_GWQH = 33,
    HQSys_E_STOCK_CATEGORY_STKC_SH_ZB = 34,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_PURC = 35,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_GZAG = 36,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_GZGG = 37,
    HQSys_E_STOCK_CATEGORY_STKC_SH_OTZQ = 38,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_OTZQ = 39,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_AG = 40,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_QZ = 41,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_ZQ = 42,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_ZQXQ = 43,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_ZQXQZS = 44,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_TSAG = 45,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_TSBG = 46,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_GPZQ = 47,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_YXG = 48,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_YYSG = 49,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_GQJL = 50,
    HQSys_E_STOCK_CATEGORY_STKC_GGT_SH = 51,
    HQSys_E_STOCK_CATEGORY_STKC_GGT_SZ = 52,
    HQSys_E_STOCK_CATEGORY_STKC_GGT_SHSZ = 53,
    HQSys_E_STOCK_CATEGORY_STKC_GGT_NOT_VALID = 54,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_IE = 55,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_IS = 56,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_CV = 57,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_CR = 58,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_R1 = 59,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_R2 = 60,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_R3 = 61,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_R4 = 62,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_OS = 63,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_OCR = 64,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_OD = 66,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_OT = 67,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_OV = 68,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_ECR = 69,
    HQSys_E_STOCK_CATEGORY_STKC_HK_STOCK = 71,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_BDW = 72,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_RIGHT = 75,
    HQSys_E_STOCK_CATEGORY_STKC_HK_CY = 77,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_CY = 80,
    HQSys_E_STOCK_CATEGORY_STKC_SZ_SB = 81,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_FXYW = 83,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_ZS = 84,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_ZQ = 85,
    HQSys_E_STOCK_CATEGORY_STKC_BK_TS = 86,
    HQSys_E_STOCK_CATEGORY_STKC_BJ_KZZ = 87,
    HQSys_E_STOCK_CATEGORY_STKC_BH = 88,
    HQSys_E_STOCK_CATEGORY_STKC_UNKNOWN = 99,
    HQSys_E_STOCK_CATEGORY_STKC_HK = 101,
    HQSys_E_STOCK_CATEGORY_STKC_QH_ZSQQ = 165,
    HQSys_E_STOCK_CATEGORY_STKC_QH_ZSQQ_C = 166,
    HQSys_E_STOCK_CATEGORY_STKC_QH_ZSQQ_P = 167,
    HQSys_E_STOCK_CATEGORY_STKC_QH_GZQH = 168,
    HQSys_E_STOCK_CATEGORY_STKC_QH_TBQH = 169,
    HQSys_E_STOCK_CATEGORY_STKC_QH_ALL = 170,
    HQSys_E_STOCK_CATEGORY_STKC_SH_ISP = 171,
    HQSys_E_STOCK_CATEGORY_STKC_SH_OPTION = 172,
    HQSys_E_STOCK_CATEGORY_STKC_SH_OPTION_C = 173,
    HQSys_E_STOCK_CATEGORY_STKC_SH_OPTION_P = 174,
    HQSys_E_STOCK_CATEGORY_STKC_ALL_HSJ = 199,
    HQSys_E_STOCK_CATEGORY_STKC_M_OTHER = 200,
    HQSys_E_STOCK_CATEGORY_STKC_M_FOREX = 201,
    HQSys_E_STOCK_CATEGORY_STKC_M_FUTURES = 202,
    HQSys_E_STOCK_CATEGORY_STKC_M_METAL = 203,
    HQSys_E_STOCK_CATEGORY_STKC_M_INDEX = 204,
    HQSys_E_STOCK_CATEGORY_STKC_M_FUND = 205,
    HQSys_E_STOCK_CATEGORY_STKC_M_HK = 206,
    HQSys_E_STOCK_CATEGORY_STKC_M_US = 207,
    HQSys_E_STOCK_CATEGORY_STKC_M_XSB = 208,
    HQSys_E_STOCK_CATEGORY_STKC_M_COIN = 209,
    HQSys_E_STOCK_CATEGORY_STKC_M_HKDARK = 210,
    HQSys_E_STOCK_CATEGORY_STKC_M_FUTUREOPTION = 211,
    HQSys_E_STOCK_CATEGORY_STKC_M_FUTURECOMBI = 212
};
#define HQSys_E_STOCK_CATEGORY NSInteger


enum {
    HQSys_E_MARKET_CODE_E_MARKET_SZ = 0,
    HQSys_E_MARKET_CODE_E_MARKET_SH = 1,
    HQSys_E_MARKET_CODE_E_MARKET_HK = 2,
    HQSys_E_MARKET_CODE_E_MARKET_SF = 3,
    HQSys_E_MARKET_CODE_E_MARKET_SC = 4,
    HQSys_E_MARKET_CODE_E_MARKET_DC = 5,
    HQSys_E_MARKET_CODE_E_MARKET_ZC = 6,
    HQSys_E_MARKET_CODE_E_MARKET_BJ = 7,
    HQSys_E_MARKET_CODE_E_MARKET_SJ = 8,
    HQSys_E_MARKET_CODE_E_MARKET_LDJ = 9,
    HQSys_E_MARKET_CODE_E_MARKET_TJ = 10,
    HQSys_E_MARKET_CODE_E_MARKET_DY = 11,
    HQSys_E_MARKET_CODE_E_MARKET_GJ = 12,
    HQSys_E_MARKET_CODE_E_MARKET_NASDAQ = 13,
    HQSys_E_MARKET_CODE_E_MARKET_NYSE = 14,
    HQSys_E_MARKET_CODE_E_MARKET_AMEX = 15,
    HQSys_E_MARKET_CODE_E_MARKET_HSI = 16,
    HQSys_E_MARKET_CODE_E_MARKET_USI = 17,
    HQSys_E_MARKET_CODE_E_MARKET_NK225 = 18,
    HQSys_E_MARKET_CODE_E_MARKET_KOSPI = 19,
    HQSys_E_MARKET_CODE_E_MARKET_TWII = 20,
    HQSys_E_MARKET_CODE_E_MARKET_STI = 21,
    HQSys_E_MARKET_CODE_E_MARKET_KLSE = 22,
    HQSys_E_MARKET_CODE_E_MARKET_SETI = 23,
    HQSys_E_MARKET_CODE_E_MARKET_JKSE = 24,
    HQSys_E_MARKET_CODE_E_MARKET_AORD = 25,
    HQSys_E_MARKET_CODE_E_MARKET_NZSE = 26,
    HQSys_E_MARKET_CODE_E_MARKET_SENSEX = 27,
    HQSys_E_MARKET_CODE_E_MARKET_GSPTSE = 28,
    HQSys_E_MARKET_CODE_E_MARKET_USD = 29,
    HQSys_E_MARKET_CODE_E_MARKET_CAC = 30,
    HQSys_E_MARKET_CODE_E_MARKET_DAX = 31,
    HQSys_E_MARKET_CODE_E_MARKET_AEX = 32,
    HQSys_E_MARKET_CODE_E_MARKET_OMX20 = 33,
    HQSys_E_MARKET_CODE_E_MARKET_BFX = 34,
    HQSys_E_MARKET_CODE_E_MARKET_SSMI = 35,
    HQSys_E_MARKET_CODE_E_MARKET_IBOVESPA = 36,
    HQSys_E_MARKET_CODE_E_MARKET_RTS = 37,
    HQSys_E_MARKET_CODE_E_MARKET_MIB = 38,
    HQSys_E_MARKET_CODE_E_MARKET_FX = 39,
    HQSys_E_MARKET_CODE_E_MARKET_FTSE = 40,
    HQSys_E_MARKET_CODE_E_MARKET_COMEX = 41,
    HQSys_E_MARKET_CODE_E_MARKET_LME = 42,
    HQSys_E_MARKET_CODE_E_MARKET_NYMEX = 43,
    HQSys_E_MARKET_CODE_E_MARKET_CBOT = 44,
    HQSys_E_MARKET_CODE_E_MARKET_IPE = 45,
    HQSys_E_MARKET_CODE_E_MARKET_FUND = 46,
    HQSys_E_MARKET_CODE_E_MARKET_TB = 47,
    HQSys_E_MARKET_CODE_E_MARKET_CNY = 48,
    HQSys_E_MARKET_CODE_E_MARKET_XS = 49,
    HQSys_E_MARKET_CODE_E_MARKET_DELAY_HK = 50,
    HQSys_E_MARKET_CODE_E_MARKET_BS = 51,
    HQSys_E_MARKET_CODE_E_MARKET_HT = 52,
    HQSys_E_MARKET_CODE_E_MARKET_WT = 53,
    HQSys_E_MARKET_CODE_E_MARKET_XIN9 = 54,
    HQSys_E_MARKET_CODE_E_MARKET_DSE = 60,
    HQSys_E_MARKET_CODE_E_MARKET_SOUTH = 61,
    HQSys_E_MARKET_CODE_E_MARKET_NORTH = 62,
    HQSys_E_MARKET_CODE_E_MARKET_GGT = 64,
    HQSys_E_MARKET_CODE_E_MARKET_HKDARK = 65,
    HQSys_E_MARKET_CODE_E_MARKET_SGT = 66,
    HQSys_E_MARKET_CODE_E_MARKET_HGT = 67,
    HQSys_E_MARKET_CODE_E_MARKET_SZATP = 68,
    HQSys_E_MARKET_CODE_E_MARKET_BH = 69,
    HQSys_E_MARKET_CODE_E_MARKET_HKBLOCK = 70,
    HQSys_E_MARKET_CODE_E_MARKET_BLOCK = 71,
    HQSys_E_MARKET_CODE_E_MARKET_USBLOCK = 72,
    HQSys_E_MARKET_CODE_E_MARKET_NASDAQ_PRE = 73,
    HQSys_E_MARKET_CODE_E_MARKET_NYSE_PRE = 74,
    HQSys_E_MARKET_CODE_E_MARKET_AMEX_PRE = 75,
    HQSys_E_MARKET_CODE_E_MARKET_NASDAQ_POST = 76,
    HQSys_E_MARKET_CODE_E_MARKET_NYSE_POST = 77,
    HQSys_E_MARKET_CODE_E_MARKET_AMEX_POST = 78,
    HQSys_E_MARKET_CODE_E_MARKET_SHATP = 88,
    HQSys_E_MARKET_CODE_E_MARKET_SC1 = 90,
    HQSys_E_MARKET_CODE_E_MARKET_SC2 = 91,
    HQSys_E_MARKET_CODE_E_MARKET_SC3 = 92,
    HQSys_E_MARKET_CODE_E_MARKET_SC4 = 93,
    HQSys_E_MARKET_CODE_E_MARKET_DC1 = 94,
    HQSys_E_MARKET_CODE_E_MARKET_DC2 = 95,
    HQSys_E_MARKET_CODE_E_MARKET_ZC1 = 96,
    HQSys_E_MARKET_CODE_E_MARKET_ZC2 = 97,
    HQSys_E_MARKET_CODE_E_MARKET_DC1OPTION = 100,
    HQSys_E_MARKET_CODE_E_MARKET_ZC1OPTION = 101,
    HQSys_E_MARKET_CODE_E_MARKET_SC1OPTION = 102,
    HQSys_E_MARKET_CODE_E_MARKET_SC2OPTION = 103,
    HQSys_E_MARKET_CODE_E_MARKET_SC3OPTION = 104,
    HQSys_E_MARKET_CODE_E_MARKET_ZC1COMBI = 110,
    HQSys_E_MARKET_CODE_E_MARKET_ZC2COMBI = 111,
    HQSys_E_MARKET_CODE_E_MARKET_DC1COMBI = 112,
    HQSys_E_MARKET_CODE_E_MARKET_DC2COMBI = 113
};
#define HQSys_E_MARKET_CODE NSInteger


enum {
    HQSys_E_SC_CATEGORY_E_SC3_AU = 1,
    HQSys_E_SC_CATEGORY_E_SC3_AG = 2,
    HQSys_E_SC_CATEGORY_E_SC1_AL = 3,
    HQSys_E_SC_CATEGORY_E_SC2_BU = 4,
    HQSys_E_SC_CATEGORY_E_SC1_CU = 5,
    HQSys_E_SC_CATEGORY_E_SC2_FU = 6,
    HQSys_E_SC_CATEGORY_E_SC2_HC = 7,
    HQSys_E_SC_CATEGORY_E_SC1_PB = 8,
    HQSys_E_SC_CATEGORY_E_SC2_RB = 9,
    HQSys_E_SC_CATEGORY_E_SC2_RU = 10,
    HQSys_E_SC_CATEGORY_E_SC4_WR = 11,
    HQSys_E_SC_CATEGORY_E_SC1_ZN = 12,
    HQSys_E_SC_CATEGORY_E_SC1_NI = 13,
    HQSys_E_SC_CATEGORY_E_SC1_SN = 14,
    HQSys_E_SC_CATEGORY_E_SC3_SC = 15,
    HQSys_E_SC_CATEGORY_E_SC4_SS = 16,
    HQSys_E_SC_CATEGORY_E_SC2_NR = 17,
    HQSys_E_SC_CATEGORY_E_SC2_SP = 18,
    HQSys_E_SC_CATEGORY_E_SC2_LU = 19,
    HQSys_E_SC_CATEGORY_E_SC1_BC = 20
};
#define HQSys_E_SC_CATEGORY NSInteger


enum {
    HQSys_E_DC_CATEGORY_E_DC2_BB = 1,
    HQSys_E_DC_CATEGORY_E_DC2_FB = 2,
    HQSys_E_DC_CATEGORY_E_DC1_A = 3,
    HQSys_E_DC_CATEGORY_E_DC1_B = 4,
    HQSys_E_DC_CATEGORY_E_DC1_C = 5,
    HQSys_E_DC_CATEGORY_E_DC1_I = 6,
    HQSys_E_DC_CATEGORY_E_DC2_JD = 7,
    HQSys_E_DC_CATEGORY_E_DC1_JM = 8,
    HQSys_E_DC_CATEGORY_E_DC1_L = 9,
    HQSys_E_DC_CATEGORY_E_DC1_M = 10,
    HQSys_E_DC_CATEGORY_E_DC1_P = 11,
    HQSys_E_DC_CATEGORY_E_DC1_PP = 12,
    HQSys_E_DC_CATEGORY_E_DC1_V = 13,
    HQSys_E_DC_CATEGORY_E_DC1_Y = 14,
    HQSys_E_DC_CATEGORY_E_DC1_J = 17,
    HQSys_E_DC_CATEGORY_E_DC1_CS = 18,
    HQSys_E_DC_CATEGORY_E_DC1_EG = 19,
    HQSys_E_DC_CATEGORY_E_DC1_EB = 20,
    HQSys_E_DC_CATEGORY_E_DC1_RR = 21,
    HQSys_E_DC_CATEGORY_E_DC1_PG = 22,
    HQSys_E_DC_CATEGORY_E_DC2_LH = 23
};
#define HQSys_E_DC_CATEGORY NSInteger


enum {
    HQSys_E_ZC_CATEGORY_E_ZC1_CF = 2,
    HQSys_E_ZC_CATEGORY_E_ZC1_FG = 3,
    HQSys_E_ZC_CATEGORY_E_ZC2_JR = 4,
    HQSys_E_ZC_CATEGORY_E_ZC2_LR = 5,
    HQSys_E_ZC_CATEGORY_E_ZC1_MA = 6,
    HQSys_E_ZC_CATEGORY_E_ZC1_OI = 8,
    HQSys_E_ZC_CATEGORY_E_ZC2_PM = 9,
    HQSys_E_ZC_CATEGORY_E_ZC2_RI = 10,
    HQSys_E_ZC_CATEGORY_E_ZC1_RM = 11,
    HQSys_E_ZC_CATEGORY_E_ZC2_RS = 12,
    HQSys_E_ZC_CATEGORY_E_ZC2_SM = 14,
    HQSys_E_ZC_CATEGORY_E_ZC2_SF = 13,
    HQSys_E_ZC_CATEGORY_E_ZC1_SR = 15,
    HQSys_E_ZC_CATEGORY_E_ZC1_TA = 16,
    HQSys_E_ZC_CATEGORY_E_ZC2_WH = 18,
    HQSys_E_ZC_CATEGORY_E_ZC1_ZC = 19,
    HQSys_E_ZC_CATEGORY_E_ZC2_CY = 20,
    HQSys_E_ZC_CATEGORY_E_ZC2_AP = 21,
    HQSys_E_ZC_CATEGORY_E_ZC2_UR = 22,
    HQSys_E_ZC_CATEGORY_E_ZC2_CJ = 23,
    HQSys_E_ZC_CATEGORY_E_ZC1_PF = 24,
    HQSys_E_ZC_CATEGORY_E_ZC1_SA = 25,
    HQSys_E_ZC_CATEGORY_E_ZC2_PK = 26
};
#define HQSys_E_ZC_CATEGORY NSInteger


enum {
    HQSys_E_HK_CATEGORY_E_HK_ZQ = 66,
    HQSys_E_HK_CATEGORY_E_HK_NXZ = 67,
    HQSys_E_HK_CATEGORY_E_HK_JJ = 70,
    HQSys_E_HK_CATEGORY_E_HK_CYB = 71,
    HQSys_E_HK_CATEGORY_E_HK_ZB = 77,
    HQSys_E_HK_CATEGORY_E_HK_QZ = 87
};
#define HQSys_E_HK_CATEGORY NSInteger


enum {
    HQSys_E_US_CATEGORY_E_US_ZQ = 66,
    HQSys_E_US_CATEGORY_E_US_JJ = 70,
    HQSys_E_US_CATEGORY_E_US_OTHRE = 79,
    HQSys_E_US_CATEGORY_E_US_NORMAL = 83
};
#define HQSys_E_US_CATEGORY NSInteger


enum {
    HQSys_E_TB_CATEGORY_E_TB_LW = 1,
    HQSys_E_TB_CATEGORY_E_TB_XYZR_A = 2,
    HQSys_E_TB_CATEGORY_E_TB_ZSZR_A = 3,
    HQSys_E_TB_CATEGORY_E_TB_XYZR_B = 4,
    HQSys_E_TB_CATEGORY_E_TB_ZSZR_B = 5,
    HQSys_E_TB_CATEGORY_E_TB_ZS = 6,
    HQSys_E_TB_CATEGORY_E_TB_YZB = 7,
    HQSys_E_TB_CATEGORY_E_TB_DSC = 8,
    HQSys_E_TB_CATEGORY_E_TB_DGP = 9,
    HQSys_E_TB_CATEGORY_E_TB_YXG = 10,
    HQSys_E_TB_CATEGORY_E_TB_JJZRGP = 11,
    HQSys_E_TB_CATEGORY_E_TB_LXJJZRGP = 12,
    HQSys_E_TB_CATEGORY_E_TB_JJZRQQ = 13,
    HQSys_E_TB_CATEGORY_E_TB_ZXJJZRQQ = 14,
    HQSys_E_TB_CATEGORY_E_TB_YYSG = 15,
    HQSys_E_TB_CATEGORY_E_TB_YYHG = 16,
    HQSys_E_TB_CATEGORY_E_TB_FX = 17,
    HQSys_E_TB_CATEGORY_E_TB_LWB = 18,
    HQSys_E_TB_CATEGORY_E_TB_FXSG = 19,
    HQSys_E_TB_CATEGORY_E_TB_ZQ = 20,
    HQSys_E_TB_CATEGORY_E_TB_KZZ = 21,
    HQSys_E_TB_CATEGORY_E_TB_TSKZZ = 23,
    HQSys_E_TB_CATEGORY_E_TB_JXC = 33
};
#define HQSys_E_TB_CATEGORY NSInteger


enum {
    HQSys_E_HKDARK_CATEGORY_E_HKDARK_NORMAL = 1,
    HQSys_E_HKDARK_CATEGORY_E_HKDARK_TS = 2,
    HQSys_E_HKDARK_CATEGORY_E_HKDARK_JJSS = 3
};
#define HQSys_E_HKDARK_CATEGORY NSInteger


enum {
    HQSys_E_BUSS_TYPE_EBT_SH_A = 0,
    HQSys_E_BUSS_TYPE_EBT_SH_B = 1,
    HQSys_E_BUSS_TYPE_EBT_SZ_A = 2,
    HQSys_E_BUSS_TYPE_EBT_SZ_B = 3,
    HQSys_E_BUSS_TYPE_EBT_SH_ZQ = 4,
    HQSys_E_BUSS_TYPE_EBT_SZ_ZQ = 5,
    HQSys_E_BUSS_TYPE_EBT_A = 6,
    HQSys_E_BUSS_TYPE_EBT_B = 7,
    HQSys_E_BUSS_TYPE_EBT_ZQ = 8,
    HQSys_E_BUSS_TYPE_EBT_JJ = 9,
    HQSys_E_BUSS_TYPE_EBT_ZS_BK = 11,
    HQSys_E_BUSS_TYPE_EBT_SZ_SM = 12,
    HQSys_E_BUSS_TYPE_EBT_QZ = 17,
    HQSys_E_BUSS_TYPE_EBT_SZ_CY = 18,
    HQSys_E_BUSS_TYPE_EBT_M_BH = 19,
    HQSys_E_BUSS_TYPE_EBT_M_DS = 20,
    HQSys_E_BUSS_TYPE_EBT_M_ZS = 21,
    HQSys_E_BUSS_TYPE_EBT_M_SQ = 22,
    HQSys_E_BUSS_TYPE_EBT_ZJ = 23,
    HQSys_E_BUSS_TYPE_EBT_M_SHJ = 24,
    HQSys_E_BUSS_TYPE_EBT_M_GJJ = 25,
    HQSys_E_BUSS_TYPE_EBT_M_TG = 26,
    HQSys_E_BUSS_TYPE_EBT_SH_ZQHG = 28,
    HQSys_E_BUSS_TYPE_EBT_SZ_ZQHG = 29,
    HQSys_E_BUSS_TYPE_EBT_BLK_ALL = 30,
    HQSys_E_BUSS_TYPE_EBT_BLK_DY = 31,
    HQSys_E_BUSS_TYPE_EBT_BLK_HY = 32,
    HQSys_E_BUSS_TYPE_EBT_BLK_GN = 33,
    HQSys_E_BUSS_TYPE_EBT_M_DY = 34,
    HQSys_E_BUSS_TYPE_EBT_M_GJ = 35,
    HQSys_E_BUSS_TYPE_EBT_T_HK = 36,
    HQSys_E_BUSS_TYPE_EBT_T_HK_ZB = 37,
    HQSys_E_BUSS_TYPE_EBT_T_HK_CYB = 38,
    HQSys_E_BUSS_TYPE_EBT_T_HK_ZQ = 39,
    HQSys_E_BUSS_TYPE_EBT_T_HK_QZ = 41,
    HQSys_E_BUSS_TYPE_EBT_T_HK_NXZ = 42,
    HQSys_E_BUSS_TYPE_EBT_T_HK_JJ = 43,
    HQSys_E_BUSS_TYPE_EBT_M_HSI = 49,
    HQSys_E_BUSS_TYPE_EBT_M_US = 50,
    HQSys_E_BUSS_TYPE_EBT_M_NASDAQ = 51,
    HQSys_E_BUSS_TYPE_EBT_M_NYSE = 52,
    HQSys_E_BUSS_TYPE_EBT_M_AMEX = 53,
    HQSys_E_BUSS_TYPE_EBT_M_USI = 54,
    HQSys_E_BUSS_TYPE_EBT_BJ_A = 60,
    HQSys_E_BUSS_TYPE_EBT_BJ_ZS = 61,
    HQSys_E_BUSS_TYPE_EBT_A_ALL = 62,
    HQSys_E_BUSS_TYPE_EBT_BJ_ZQ = 63,
    HQSys_E_BUSS_TYPE_EBT_BK_TS = 70,
    HQSys_E_BUSS_TYPE_EBT_CDR_HLT = 125,
    HQSys_E_BUSS_TYPE_EBT_SH_GGT = 126,
    HQSys_E_BUSS_TYPE_EBT_SZ_GGT = 127,
    HQSys_E_BUSS_TYPE_EBT_SHSZ_GGT = 128,
    HQSys_E_BUSS_TYPE_EBT_QH_YP = 129,
    HQSys_E_BUSS_TYPE_EBT_CDR = 130,
    HQSys_E_BUSS_TYPE_EBT_KCB = 131,
    HQSys_E_BUSS_TYPE_EBT_ZQHG = 132,
    HQSys_E_BUSS_TYPE_EBT_A_KCB = 133,
    HQSys_E_BUSS_TYPE_EBT_SH_CXQYGP = 134,
    HQSys_E_BUSS_TYPE_EBT_OTZQ = 135,
    HQSys_E_BUSS_TYPE_EBT_SHSZ_ZQHG = 136,
    HQSys_E_BUSS_TYPE_EBT_ZQ_ALL = 137,
    HQSys_E_BUSS_TYPE_EBT_ZS_000001 = 2000,
    HQSys_E_BUSS_TYPE_EBT_ZS_000002 = 2001,
    HQSys_E_BUSS_TYPE_EBT_ZS_000003 = 2002,
    HQSys_E_BUSS_TYPE_EBT_ZS_399001 = 2003,
    HQSys_E_BUSS_TYPE_EBT_ZS_399002 = 2004,
    HQSys_E_BUSS_TYPE_EBT_ZS_399003 = 2005,
    HQSys_E_BUSS_TYPE_EBT_ZS_399005 = 2006,
    HQSys_E_BUSS_TYPE_EBT_ZS_399006 = 2007,
    HQSys_E_BUSS_TYPE_EBT_ZS_399107 = 2008,
    HQSys_E_BUSS_TYPE_EBT_ZS_399108 = 2009,
    HQSys_E_BUSS_TYPE_EBT_SH_JJ = 9100,
    HQSys_E_BUSS_TYPE_EBT_SZ_JJ = 9101,
    HQSys_E_BUSS_TYPE_EBT_T_SC_CU = 10000,
    HQSys_E_BUSS_TYPE_EBT_T_SC_AL = 10001,
    HQSys_E_BUSS_TYPE_EBT_T_SC_ZN = 10002,
    HQSys_E_BUSS_TYPE_EBT_T_SC_PB = 10003,
    HQSys_E_BUSS_TYPE_EBT_T_SC_AU = 10004,
    HQSys_E_BUSS_TYPE_EBT_T_SC_AG = 10005,
    HQSys_E_BUSS_TYPE_EBT_T_SC_RB = 10006,
    HQSys_E_BUSS_TYPE_EBT_T_SC_RU = 10007,
    HQSys_E_BUSS_TYPE_EBT_T_SC_FU = 10008,
    HQSys_E_BUSS_TYPE_EBT_T_SC_WR = 10009,
    HQSys_E_BUSS_TYPE_EBT_T_SC_BU = 10010,
    HQSys_E_BUSS_TYPE_EBT_T_SC_HC = 10011,
    HQSys_E_BUSS_TYPE_EBT_T_SC_NI = 10012,
    HQSys_E_BUSS_TYPE_EBT_T_SC_SN = 10013,
    HQSys_E_BUSS_TYPE_EBT_T_SC_NR = 10014,
    HQSys_E_BUSS_TYPE_EBT_T_SC_SP = 10015,
    HQSys_E_BUSS_TYPE_EBT_T_DC_A = 10100,
    HQSys_E_BUSS_TYPE_EBT_T_DC_M = 10101,
    HQSys_E_BUSS_TYPE_EBT_T_DC_Y = 10102,
    HQSys_E_BUSS_TYPE_EBT_T_DC_P = 10103,
    HQSys_E_BUSS_TYPE_EBT_T_DC_C = 10104,
    HQSys_E_BUSS_TYPE_EBT_T_DC_I = 10105,
    HQSys_E_BUSS_TYPE_EBT_T_DC_JM = 10106,
    HQSys_E_BUSS_TYPE_EBT_T_DC_J = 10107,
    HQSys_E_BUSS_TYPE_EBT_T_DC_L = 10108,
    HQSys_E_BUSS_TYPE_EBT_T_DC_V = 10109,
    HQSys_E_BUSS_TYPE_EBT_T_DC_B = 10110,
    HQSys_E_BUSS_TYPE_EBT_T_DC_JD = 10111,
    HQSys_E_BUSS_TYPE_EBT_T_DC_FB = 10112,
    HQSys_E_BUSS_TYPE_EBT_T_DC_BB = 10113,
    HQSys_E_BUSS_TYPE_EBT_T_DC_PP = 10114,
    HQSys_E_BUSS_TYPE_EBT_T_DC_CS = 10115,
    HQSys_E_BUSS_TYPE_EBT_T_DC_PG = 10116,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_WH = 10200,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_OI = 10201,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_CF = 10202,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_SR = 10203,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_RI = 10204,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_TC = 10205,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_TA = 10206,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_FG = 10207,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_MA = 10208,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_RM = 10209,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_RS = 10210,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_PM = 10211,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_JR = 10212,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_LR = 10213,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_SM = 10214,
    HQSys_E_BUSS_TYPE_EBT_T_ZC_SF = 10215,
    HQSys_E_BUSS_TYPE_EBT_M_FUND = 10350,
    HQSys_E_BUSS_TYPE_EBT_M_CBOT = 10400,
    HQSys_E_BUSS_TYPE_EBT_M_COMEX = 10401,
    HQSys_E_BUSS_TYPE_EBT_M_NYMEX = 10402,
    HQSys_E_BUSS_TYPE_EBT_M_LME = 10403,
    HQSys_E_BUSS_TYPE_EBT_M_FX = 10410,
    HQSys_E_BUSS_TYPE_EBT_M_USD = 10411,
    HQSys_E_BUSS_TYPE_EBT_M_CNY = 10412,
    HQSys_E_BUSS_TYPE_EBT_M_DSE = 10413,
    HQSys_E_BUSS_TYPE_EBT_ZS = 10422,
    HQSys_E_BUSS_TYPE_EBT_SH_ZS = 10423,
    HQSys_E_BUSS_TYPE_EBT_SZ_ZS = 10424,
    HQSys_E_BUSS_TYPE_EBT_TB = 10438,
    HQSys_E_BUSS_TYPE_EBT_TB_ZS = 10439,
    HQSys_E_BUSS_TYPE_EBT_TB_ZSZR = 10440,
    HQSys_E_BUSS_TYPE_EBT_TB_XYZR = 10441,
    HQSys_E_BUSS_TYPE_EBT_TB_LW = 10442,
    HQSys_E_BUSS_TYPE_EBT_TB_DSC = 10443,
    HQSys_E_BUSS_TYPE_EBT_TB_DGP = 10444,
    HQSys_E_BUSS_TYPE_EBT_TB_YZB = 10445,
    HQSys_E_BUSS_TYPE_EBT_TB_ZQ = 10446,
    HQSys_E_BUSS_TYPE_EBT_TB_KZZ = 10447,
    HQSys_E_BUSS_TYPE_EBT_M_XS = 10500,
    HQSys_E_BUSS_TYPE_EBT_M_DS_SUB = 10550,
    HQSys_E_BUSS_TYPE_EBT_M_ZS_SUB = 10551,
    HQSys_E_BUSS_TYPE_EBT_M_SQ_SUB = 10552,
    HQSys_E_BUSS_TYPE_EBT_M_INDEX = 10553,
    HQSys_E_BUSS_TYPE_EBT_M_SGT = 10554,
    HQSys_E_BUSS_TYPE_EBT_M_HGT = 10555,
    HQSys_E_BUSS_TYPE_EBT_M_INDEX_2 = 10556,
    HQSys_E_BUSS_TYPE_EBT_T_SC_ENERGY = 10580,
    HQSys_E_BUSS_TYPE_EBT_T_SUB = 12000,
    HQSys_E_BUSS_TYPE_EBT_T_A_SUB = 12001,
    HQSys_E_BUSS_TYPE_EBT_T_M_SUB = 12002
};
#define HQSys_E_BUSS_TYPE NSInteger


enum {
    HQSys_E_CUSTOM_TYPE_ECT_QH_ZLHY = 1,
    HQSys_E_CUSTOM_TYPE_ECT_QH_MAIN_HY = 2,
    HQSys_E_CUSTOM_TYPE_ECT_QH_MAIN_HY_YESTERDAY = 3
};
#define HQSys_E_CUSTOM_TYPE NSInteger


enum {
    HQSys_E_SORT_METHOD_E_SORT_DEFAULT = 0,
    HQSys_E_SORT_METHOD_E_SORT_ASCEND = 1,
    HQSys_E_SORT_METHOD_E_SORT_DESCEN = 2
};
#define HQSys_E_SORT_METHOD NSInteger


enum {
    HQSys_E_RANGE_TYPE_E_STOCK = 0,
    HQSys_E_RANGE_TYPE_E_INDEX = 1,
    HQSys_E_RANGE_TYPE_E_BLOCK = 2
};
#define HQSys_E_RANGE_TYPE NSInteger


enum {
    HQSys_E_RANGE_SORT_COLUMN_ERS_CODE = 1,
    HQSys_E_RANGE_SORT_COLUMN_ERS_NAME = 2,
    HQSys_E_RANGE_SORT_COLUMN_ERS_CHANGE = 3,
    HQSys_E_RANGE_SORT_COLUMN_ERS_MAINMONEYINFLOW = 4,
    HQSys_E_RANGE_SORT_COLUMN_ERS_MAINMONEYOUTFLOW = 5,
    HQSys_E_RANGE_SORT_COLUMN_ERS_MAINMONEYFLOW = 6,
    HQSys_E_RANGE_SORT_COLUMN_ERS_AMOUNT = 7,
    HQSys_E_RANGE_SORT_COLUMN_ERS_CIRCULAMARKETVALUE = 8,
    HQSys_E_RANGE_SORT_COLUMN_ERS_TUNOVERRATE = 9,
    HQSys_E_RANGE_SORT_COLUMN_ERS_INFLOWDAYS = 10,
    HQSys_E_RANGE_SORT_COLUMN_ERS_OUTFLOWDAYs = 11
};
#define HQSys_E_RANGE_SORT_COLUMN NSInteger


enum {
    HQSys_E_MF_RANK_DAY_EMR_DAY_1 = 1,
    HQSys_E_MF_RANK_DAY_EMR_DAY_3 = 3,
    HQSys_E_MF_RANK_DAY_EMR_DAY_5 = 5,
    HQSys_E_MF_RANK_DAY_EMR_DAY_10 = 10
};
#define HQSys_E_MF_RANK_DAY NSInteger


enum {
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_CODE = 1,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_NAME = 2,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_NOWPRICE = 3,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_CHG = 4,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_CHANGE = 5,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_ZLLR = 6,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_ZLZB = 7,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_SHLR = 8,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_SHZB = 9,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_CDDZJLR = 10,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_CDDZJZB = 11,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_DDZJLR = 12,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_DDZJZB = 13,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_ZDZJLR = 14,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_ZDZJZB = 15,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_XDZJLR = 16,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_DAY_XDZJZB = 17,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_ZLLR = 18,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_ZLZB = 19,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_SHLR = 20,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_SHZB = 21,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_CDDZJLR = 22,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_CDDZJZB = 23,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_DDZJLR = 24,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_DDZJZB = 25,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_ZDZJLR = 26,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_ZDZJZB = 27,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_XDZJLR = 28,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3DAY_XDZJZB = 29,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_ZLLR = 30,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_ZLZB = 31,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_SHLR = 32,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_SHZB = 33,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_CDDZJLR = 34,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_CDDZJZB = 35,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_DDZJLR = 36,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_DDZJZB = 37,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_ZDZJLR = 38,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_ZDZJZB = 39,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_XDZJLR = 40,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5DAY_XDZJZB = 41,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_ZLLR = 42,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_ZLZB = 43,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_SHLR = 44,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_SHZB = 45,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_CDDZJLR = 46,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_CDDZJZB = 47,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_DDZJLR = 48,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_DDZJZB = 49,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_ZDZJLR = 50,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_ZDZJZB = 51,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_XDZJLR = 52,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10DAY_XDZJZB = 53,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_ZLLR = 54,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_ZLZB = 55,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_SHLR = 56,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_SHZB = 57,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_CDDZJLR = 58,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_CDDZJZB = 59,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_DDZJLR = 60,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_DDZJZB = 61,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_ZDZJLR = 62,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_ZDZJZB = 63,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_XDZJLR = 64,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_3MIN_XDZJZB = 65,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_ZLLR = 66,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_ZLZB = 67,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_SHLR = 68,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_SHZB = 69,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_CDDZJLR = 70,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_CDDZJZB = 71,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_DDZJLR = 72,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_DDZJZB = 73,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_ZDZJLR = 74,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_ZDZJZB = 75,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_XDZJLR = 76,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_5MIN_XDZJZB = 77,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_ZLLR = 78,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_ZLZB = 79,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_SHLR = 80,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_SHZB = 81,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_CDDZJLR = 82,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_CDDZJZB = 83,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_DDZJLR = 84,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_DDZJZB = 85,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_ZDZJLR = 86,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_ZDZJZB = 87,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_XDZJLR = 88,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_10MIN_XDZJZB = 89,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_ZLLR = 90,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_ZLZB = 91,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_SHLR = 92,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_SHZB = 93,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_CDDZJLR = 94,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_CDDZJZB = 95,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_DDZJLR = 96,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_DDZJZB = 97,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_ZDZJLR = 98,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_ZDZJZB = 99,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_XDZJLR = 100,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_30MIN_XDZJZB = 101,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_ZLLR = 102,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_ZLZB = 103,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_SHLR = 104,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_SHZB = 105,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_CDDZJLR = 106,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_CDDZJZB = 107,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_DDZJLR = 108,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_DDZJZB = 109,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_ZDZJLR = 110,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_ZDZJZB = 111,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_XDZJLR = 112,
    HQSys_E_MF_RANK_SORT_COLUMN_EMRC_60MIN_XDZJZB = 113
};
#define HQSys_E_MF_RANK_SORT_COLUMN NSInteger


enum {
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_CODE = 1,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_NAME = 2,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_NOWPRICE = 3,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_CHG = 4,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_TURNOVERRATE = 5,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_DDX = 6,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_DDY = 7,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_DDZ = 8,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_DDF = 9,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_DDX5 = 10,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_DDY5 = 11,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_DDX60 = 12,
    HQSys_E_DDERANK_SORT_COLUMN_E_DDERANK_DDY60 = 13
};
#define HQSys_E_DDERANK_SORT_COLUMN NSInteger


enum {
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SUPER_BUY = 0,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SUPER_SELL = 1,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_UP_STOP = 2,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_DOWN_STOP = 3,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_OPEN_UP_STOP = 4,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_OPEN_DOWN_STOP = 5,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_POST_LARGE_BUY = 6,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_POST_LARGE_SELL = 7,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SUPER_LARGE_BUY = 8,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SUPER_LARGE_SELL = 9,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_LARGE_BUY_IN = 14,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_LAGRGE_SELL_OUT = 15,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_BUY_FEN_DAN = 16,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SELL_FEN_DAN = 17,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_ROCKET_RUSH = 22,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_QUICK_BUCKUP = 23,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_HIGH_DIVING = 24,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_QUICK_DOWN = 25,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_CANCEL_BUY = 26,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_CANCEL_SELL = 27,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_RADAR = 51,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_PAD_ORDER = 55,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_PRESS_ORDER = 56,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_CLIP_ORDER = 57,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_HEDGE_ORDER = 58,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_JUMP_PRICE_DOWN_ORDER = 59,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_JUMP_PRICE_UP_ORDER = 60,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_AUCTION_CANCEL_BUY = 61,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_AUCTION_CANCEL_SELL = 62,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SHADOWLESS_BUY = 63,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SHADOWLESS_SELL = 64,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_MAIN_TURNOVER_BUY = 65,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_MAIN_TURNOVER_SELL = 66,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_TAKEALL_BUY = 67,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_TAKEALL_SELL = 68,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SUPER_PICK = 69,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SUPER_CRACK = 70,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SUPER_MAIN_BUY = 71,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_SUPER_MAIN_SELL = 72,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_FRONTLINE_BUY = 73,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_FRONTLINE_SELL = 74,
    HQSys_E_STOCK_ORDER_TYPE_E_STOCK_ORDER_ALL = 99
};
#define HQSys_E_STOCK_ORDER_TYPE NSInteger


enum {
    HQSys_E_SZFY_ORDER_TYPE_E_SZFY_TIMES = 0,
    HQSys_E_SZFY_ORDER_TYPE_E_SZFY_VOL = 1,
    HQSys_E_SZFY_ORDER_TYPE_E_SZFY_AMT = 2
};
#define HQSys_E_SZFY_ORDER_TYPE NSInteger


enum {
    HQSys_E_COM_ORDER_TYPE_E_COT_TLJ = 0,
    HQSys_E_COM_ORDER_TYPE_E_COT_DJ = 1,
    HQSys_E_COM_ORDER_TYPE_E_COT_ZL = 2
};
#define HQSys_E_COM_ORDER_TYPE NSInteger


enum {
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_CODE = 0,
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_PRICE = 1,
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_CHG = 2,
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_BUY_COUNT = 3,
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_BUY_VOL = 4,
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_BUY_AMT = 5,
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_SELL_COUNT = 6,
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_SELL_VOL = 7,
    HQSys_E_SZFYCOM_ORDER_TYPE_E_SZFYCOM_SELL_AMT = 8
};
#define HQSys_E_SZFYCOM_ORDER_TYPE NSInteger


enum {
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_CODE = 1,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_NAME = 2,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_YCLOSE = 3,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_OPEN = 4,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_HIGH = 5,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_LOW = 6,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_NOWPRICE = 7,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BUY = 8,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SELL = 9,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_VOLUME = 10,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_AMOUNT = 11,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_CURVOL = 12,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_CHANGE = 13,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_CHG = 14,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_AMPLITUDE = 15,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_AVGPRICE = 16,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_PREOPENINTEREST = 17,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_PRESETTLEMENTPRICE = 18,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_OPENINTEREST = 19,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_TURNOVERRATE = 20,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_LIANGBI = 23,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_NET_BUY = 24,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_RATIO = 25,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_UP_SPEED = 26,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_WEIBI = 27,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_PERATIO = 28,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_PRICERATIO = 29,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_CURRENTMARKET = 30,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_TOTALMARKET = 31,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MGSY = 32,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MGJZC = 33,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_JZCSYL = 34,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_YSZZL3Y = 35,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_JLSZZL3Y = 36,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_5DAYCHG = 37,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_10DAYCHG = 38,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_20DAYCHG = 39,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MONTHCHG = 40,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SEASONCHG = 41,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_YEARCHG = 42,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_THISYEARCHG = 43,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_NET_BUY_5MIN = 44,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_LZTZJ = 45,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_LDTZJ = 46,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_ZGJZ = 47,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_PREMIUMRATE = 48,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_TOTALCHG = 49,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_INTERESTDAYINCREASE = 50,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_IOPV = 51,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_OPENCHG = 52,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_AUCLAST = 53,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_AUCVOL = 54,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_AUCTURN = 55,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SUPPER_NET_BUY = 56,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SUPPER_RATIO = 57,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BIG_NET_BUY = 58,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BIG_RATIO = 59,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MIDDLE_NET_BUY = 60,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MIDDLE_RATIO = 61,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SMALL_NET_BUY = 62,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SMALL_RATIO = 63,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_HEADCODE_CHGRATIO = 64,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BLOCK_UPNUM = 65,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BLOCK_DOWNNUM = 66,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BLOCK_EQUALNUM = 67,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BLOCK_ZTNUM = 68,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BLOCK_UPNUM_RATIO = 69,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BLOCK_UPDAY = 70,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_NET_BUY3 = 71,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_RATIO3 = 72,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SUPPER_NET_BUY3 = 73,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SUPPER_RATIO3 = 74,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BIG_NET_BUY3 = 75,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BIG_RATIO3 = 76,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MIDDLE_NET_BUY3 = 77,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MIDDLE_RATIO3 = 78,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SMALL_NET_BUY3 = 79,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SMALL_RATIO3 = 80,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_NET_BUY5 = 81,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_RATIO5 = 82,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SUPPER_NET_BUY5 = 83,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SUPPER_RATIO5 = 84,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BIG_NET_BUY5 = 85,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BIG_RATIO5 = 86,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MIDDLE_NET_BUY5 = 87,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MIDDLE_RATIO5 = 88,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SMALL_NET_BUY5 = 89,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SMALL_RATIO5 = 90,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_NET_BUY10 = 91,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MAIN_RATIO10 = 92,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SUPPER_NET_BUY10 = 93,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SUPPER_RATIO10 = 94,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BIG_NET_BUY10 = 95,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_BIG_RATIO10 = 96,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MIDDLE_NET_BUY10 = 97,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_MIDDLE_RATIO10 = 98,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SMALL_NET_BUY10 = 99,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_SMALL_RATIO10 = 100,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_HTB = 101,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_GJB = 102,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_10DAY_HIGH_CHG = 103,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_3DAYCHG = 104,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_OPENAMONNT = 105,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_OPENVOLUME = 106,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_DIVIDERATE = 107,
    HQSys_E_HQ_SORT_COLUMN_E_HQ_COLUMN_PERATIOTTM = 108
};
#define HQSys_E_HQ_SORT_COLUMN NSInteger


enum {
    HQSys_E_AH_SORT_COLUMN_E_AH_PREMIUMRATE = 1,
    HQSys_E_AH_SORT_COLUMN_E_AH_ACODE = 2,
    HQSys_E_AH_SORT_COLUMN_E_AH_ANAME = 3,
    HQSys_E_AH_SORT_COLUMN_E_AH_ANOWPRICE = 4,
    HQSys_E_AH_SORT_COLUMN_E_AH_ACHG = 5,
    HQSys_E_AH_SORT_COLUMN_E_AH_ACURMARKETVALUE = 6,
    HQSys_E_AH_SORT_COLUMN_E_AH_ATURNOVERRATE = 7,
    HQSys_E_AH_SORT_COLUMN_E_AH_ATOTALHAND = 8,
    HQSys_E_AH_SORT_COLUMN_E_AH_AOWNINDUSTRY = 9,
    HQSys_E_AH_SORT_COLUMN_E_AH_HCODE = 10,
    HQSys_E_AH_SORT_COLUMN_E_AH_HNAME = 11,
    HQSys_E_AH_SORT_COLUMN_E_AH_HNOWPRICE = 12,
    HQSys_E_AH_SORT_COLUMN_E_AH_HCHG = 13,
    HQSys_E_AH_SORT_COLUMN_E_AH_HCURMARKETVALUE = 14,
    HQSys_E_AH_SORT_COLUMN_E_AH_HTURNOVERRATE = 15,
    HQSys_E_AH_SORT_COLUMN_E_AH_HTOTALHAND = 16,
    HQSys_E_AH_SORT_COLUMN_E_AH_HOWNINDUSTRY = 17
};
#define HQSys_E_AH_SORT_COLUMN NSInteger


enum {
    HQSys_E_AM_STK_TYPE_E_AM_STK_ALL = 0,
    HQSys_E_AM_STK_TYPE_E_AM_STK_AH = 1,
    HQSys_E_AM_STK_TYPE_E_AM_STK_BH = 2,
    HQSys_E_AM_STK_TYPE_E_AM_STK_UH = 3
};
#define HQSys_E_AM_STK_TYPE NSInteger


enum {
    HQSys_E_SUGGEST_STOCK_TYPE_E_SUGGEST_ALL = 1,
    HQSys_E_SUGGEST_STOCK_TYPE_E_SUGGEST_STOCK_ONLY = 2,
    HQSys_E_SUGGEST_STOCK_TYPE_E_SUGGEST_INDEX_ONLY = 3,
    HQSys_E_SUGGEST_STOCK_TYPE_E_SUGGEST_STOCK_AB = 4,
    HQSys_E_SUGGEST_STOCK_TYPE_E_SUGGEST_STOCK_BLOCK = 5,
    HQSys_E_SUGGEST_STOCK_TYPE_E_SUGGEST_ZXJT_ST_ONLY = 6,
    HQSys_E_SUGGEST_STOCK_TYPE_E_SUGGEST_ZXJT_NO_ST = 7
};
#define HQSys_E_SUGGEST_STOCK_TYPE NSInteger


enum {
    HQSys_E_SUGGEST_STOCK_EXT_E_SUGGEST_EXT_INS = -1
};
#define HQSys_E_SUGGEST_STOCK_EXT NSInteger


enum {
    HQSys_E_JPJL_MATCH_TYPE_E_MATCH_DEFAULT = 0,
    HQSys_E_JPJL_MATCH_TYPE_E_MATCH_CODE_EX = 1,
    HQSys_E_JPJL_MATCH_TYPE_E_MATCH_NAME_USED = 2
};
#define HQSys_E_JPJL_MATCH_TYPE NSInteger


enum {
    HQSys_E_STOCK_STATUS_TYPE_E_NORMAL_STOCK = 0,
    HQSys_E_STOCK_STATUS_TYPE_E_EXIT_STOCK = 1,
    HQSys_E_STOCK_STATUS_TYPE_E_RISK_STOCK = 2,
    HQSys_E_STOCK_STATUS_TYPE_E_DELETED_STOCK = 3,
    HQSys_E_STOCK_STATUS_TYPE_E_SUSPEND_STOCK = 4,
    HQSys_E_STOCK_STATUS_TYPE_E_UPING_STOCK = 5
};
#define HQSys_E_STOCK_STATUS_TYPE NSInteger


enum {
    HQSys_E_RELATION_TYPE_E_RELATION_UNKNOWN = 0,
    HQSys_E_RELATION_TYPE_E_RELATION_YY = 1,
    HQSys_E_RELATION_TYPE_E_RELATION_FX = 2,
    HQSys_E_RELATION_TYPE_E_RELATION_KZZ = 3,
    HQSys_E_RELATION_TYPE_E_RELATION_AH = 4,
    HQSys_E_RELATION_TYPE_E_RELATION_PS = 5,
    HQSys_E_RELATION_TYPE_E_RELATION_BH = 6,
    HQSys_E_RELATION_TYPE_E_RELATION_UH = 7
};
#define HQSys_E_RELATION_TYPE NSInteger


enum {
    HQSys_E_CVT_DATA_CVT_NORMAL = 0,
    HQSys_E_CVT_DATA_CVT_MFL = 1,
    HQSys_E_CVT_DATA_INDEX_DATA = 4096
};
#define HQSys_E_CVT_DATA NSInteger


enum {
    HQSys_E_ZR_TYPE_E_ZR_TYPE_UNKNOWN = 0,
    HQSys_E_ZR_TYPE_E_ZR_TYPE_XYZR = 1,
    HQSys_E_ZR_TYPE_E_ZR_TYPE_ZSZR = 2,
    HQSys_E_ZR_TYPE_E_ZR_TYPE_LXJHJJZR = 3,
    HQSys_E_ZR_TYPE_E_ZR_TYPE_JHJJZR = 4,
    HQSys_E_ZR_TYPE_E_ZR_TYPE_FX = 5
};
#define HQSys_E_ZR_TYPE NSInteger


enum {
    HQSys_E_ZR_Status_E_ZR_STATUS_UNKNOWN = 0,
    HQSys_E_ZR_Status_E_ZR_STATUS_NORMAL = 1,
    HQSys_E_ZR_Status_E_ZR_STATUS_SRGP = 2,
    HQSys_E_ZR_Status_E_ZR_STATUS_XZGPGPZR = 3,
    HQSys_E_ZR_Status_E_ZR_STATUS_XJ = 4,
    HQSys_E_ZR_Status_E_ZR_STATUS_SG = 5
};
#define HQSys_E_ZR_Status NSInteger


enum {
    HQSys_E_FC_Type_E_FC_UNKNOWN = 0,
    HQSys_E_FC_Type_E_FC_CX = 1,
    HQSys_E_FC_Type_E_FC_JC = 2,
    HQSys_E_FC_Type_E_FC_JX = 3
};
#define HQSys_E_FC_Type NSInteger


enum {
    HQSys_E_TP_Type_E_TP_UNKNOWN = 0,
    HQSys_E_TP_Type_E_TP_NORMAL = 1,
    HQSys_E_TP_Type_E_TP_T = 2,
    HQSys_E_TP_Type_E_TP_H = 3
};
#define HQSys_E_TP_Type NSInteger


enum {
    HQSys_E_FX_Method_E_FX_METHOD_UNKNOWN = 0,
    HQSys_E_FX_Method_E_FX_METHOD_XJ = 1,
    HQSys_E_FX_Method_E_FX_METHOD_DJ = 2,
    HQSys_E_FX_Method_E_FX_METHOD_JJ = 3
};
#define HQSys_E_FX_Method NSInteger


enum {
    HQSys_E_CQCX_Status_E_CQCX_STATUS_UNKNOWN = 0,
    HQSys_E_CQCX_Status_E_CQCX_STATUS_NORMAL = 1,
    HQSys_E_CQCX_Status_E_CQCX_STATUS_CQ = 2,
    HQSys_E_CQCX_Status_E_CQCX_STATUS_CX = 3,
    HQSys_E_CQCX_Status_E_CQCX_STATUS_CQCX = 4
};
#define HQSys_E_CQCX_Status NSInteger


enum {
    HQSys_E_BZ_Status_E_BZ_UNKNOWN = 0,
    HQSys_E_BZ_Status_E_BZ_NODIFF = 1,
    HQSys_E_BZ_Status_E_BZ_DIFF = 2
};
#define HQSys_E_BZ_Status NSInteger


enum {
    HQSys_E_RANK_FILTER_TYPE_E_FILTER_ST = 1,
    HQSys_E_RANK_FILTER_TYPE_E_FILTER_TS = 2,
    HQSys_E_RANK_FILTER_TYPE_E_FILTER_NEWSTOCK = 3,
    HQSys_E_RANK_FILTER_TYPE_E_FILTER_SUB_NEWSTOCK = 4,
    HQSys_E_RANK_FILTER_TYPE_E_FILTER_KCB = 5,
    HQSys_E_RANK_FILTER_TYPE_E_FILTER_CYB = 6,
    HQSys_E_RANK_FILTER_TYPE_E_FILTER_BJ = 7,
    HQSys_E_RANK_FILTER_TYPE_E_FILTER_SZSH = 8
};
#define HQSys_E_RANK_FILTER_TYPE NSInteger


enum {
    HQSys_E_AH_PREMIUM_TYPE_E_PREMIUM_TYPE_NONE = 0,
    HQSys_E_AH_PREMIUM_TYPE_E_PREMIUM_TYPE_AH = 1,
    HQSys_E_AH_PREMIUM_TYPE_E_PREMIUM_TYPE_HA = 2
};
#define HQSys_E_AH_PREMIUM_TYPE NSInteger

@interface HQSysClientInfo : UPTAFJceObject
@property (nonatomic, strong) NSData* jce_vGuid;
@property (nonatomic, strong) NSString* jce_sXua;
@property (nonatomic, strong) NSString* jce_sMAC;
@end

@interface HQSysHeaderInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) HQSysClientInfo* jce_stClientInfo;
@property (nonatomic, assign) HQSys_E_CLI_COMP_TYPE jce_eCompress;
@property (nonatomic, assign) JceBool jce_bCancelWithReqId;
@end



