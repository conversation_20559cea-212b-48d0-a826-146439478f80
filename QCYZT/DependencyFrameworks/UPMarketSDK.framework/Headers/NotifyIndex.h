// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `NotifyIndex.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>

enum {
    HQSys_E_PERIOD_TYPE_E_PERIOD_NONE = 0,
    HQSys_E_PERIOD_TYPE_E_PERIOD_RTMIN = 1,
    HQSys_E_PERIOD_TYPE_E_PERIOD_MIN1 = 2,
    HQSys_E_PERIOD_TYPE_E_PERIOD_MIN5 = 3,
    HQSys_E_PERIOD_TYPE_E_PERIOD_MIN15 = 4,
    HQSys_E_PERIOD_TYPE_E_PERIOD_MIN30 = 5,
    HQSys_E_PERIOD_TYPE_E_PERIOD_MIN60 = 6,
    HQSys_E_PERIOD_TYPE_E_PERIOD_DAY = 7,
    HQSys_E_PERIOD_TYPE_E_PERIOD_WEEK = 8,
    HQSys_E_PERIOD_TYPE_E_PERIOD_MONTH = 9,
    HQSys_E_PERIOD_TYPE_E_PERIOD_SEASON = 10,
    HQSys_E_PERIOD_TYPE_E_PERIOD_YEAR = 11,
    HQSys_E_PERIOD_TYPE_E_PERIOD_DYNAMIC = 12
};
#define HQSys_E_PERIOD_TYPE NSInteger


enum {
    HQSys_E_HQDATA_TYPE_E_HQDATA_QT = 1
};
#define HQSys_E_HQDATA_TYPE NSInteger


enum {
    HQSys_E_DXJL_TYPE_E_DXJL_TLJD = 1,
    HQSys_E_DXJL_TYPE_E_DXJL_DJGD = 2,
    HQSys_E_DXJL_TYPE_E_DXJL_ZLCD = 3,
    HQSys_E_DXJL_TYPE_E_DXJL_DJGD_XG = 4
};
#define HQSys_E_DXJL_TYPE NSInteger


enum {
    HQSys_E_CATEGORY_TYPE_E_CATEGORY_YJXG = 1,
    HQSys_E_CATEGORY_TYPE_E_CATEGORY_CLZX = 2,
    HQSys_E_CATEGORY_TYPE_E_CATEGORY_CLFY = 3
};
#define HQSys_E_CATEGORY_TYPE NSInteger


enum {
    HQSys_E_INDEX_TYPE_E_INDEX_NONE = 0,
    HQSys_E_INDEX_TYPE_E_INDEX_WXCP = 1,
    HQSys_E_INDEX_TYPE_E_INDEX_QKJCY = 2,
    HQSys_E_INDEX_TYPE_E_INDEX_ZTJB = 3,
    HQSys_E_INDEX_TYPE_E_INDEX_DXJJ = 4,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLGJ = 5,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLCT = 6,
    HQSys_E_INDEX_TYPE_E_INDEX_HJJC = 7,
    HQSys_E_INDEX_TYPE_E_INDEX_SWJC = 8,
    HQSys_E_INDEX_TYPE_E_INDEX_LS_F = 9,
    HQSys_E_INDEX_TYPE_E_INDEX_TS_F = 10,
    HQSys_E_INDEX_TYPE_E_INDEX_BSJK = 11,
    HQSys_E_INDEX_TYPE_E_INDEX_DYGZMX = 12,
    HQSys_E_INDEX_TYPE_E_INDEX_DYZCMX = 13,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG = 14,
    HQSys_E_INDEX_TYPE_E_INDEX_TSXG = 15,
    HQSys_E_INDEX_TYPE_E_INDEX_JDXG = 16,
    HQSys_E_INDEX_TYPE_E_INDEX_YHXG = 17,
    HQSys_E_INDEX_TYPE_E_INDEX_MJXG = 18,
    HQSys_E_INDEX_TYPE_E_INDEX_MJ60 = 19,
    HQSys_E_INDEX_TYPE_E_INDEX_QSDXJGG = 20,
    HQSys_E_INDEX_TYPE_E_INDEX_MMRQJGG = 21,
    HQSys_E_INDEX_TYPE_E_INDEX_JYAQJGG = 22,
    HQSys_E_INDEX_TYPE_E_INDEX_CWPGJGG = 23,
    HQSys_E_INDEX_TYPE_E_INDEX_ZHPXJGG = 24,
    HQSys_E_INDEX_TYPE_E_INDEX_QSAQPF = 25,
    HQSys_E_INDEX_TYPE_E_INDEX_QSAQZCYL = 26,
    HQSys_E_INDEX_TYPE_E_INDEX_QSAQZT = 27,
    HQSys_E_INDEX_TYPE_E_INDEX_ZJDL = 28,
    HQSys_E_INDEX_TYPE_E_INDEX_ZJKP = 29,
    HQSys_E_INDEX_TYPE_E_INDEX_ZJLX = 30,
    HQSys_E_INDEX_TYPE_E_INDEX_DPFX = 31,
    HQSys_E_INDEX_TYPE_E_INDEX_CWML = 32,
    HQSys_E_INDEX_TYPE_E_INDEX_SSZF = 33,
    HQSys_E_INDEX_TYPE_E_INDEX_FLXG12 = 34,
    HQSys_E_INDEX_TYPE_E_INDEX_FLXG13 = 35,
    HQSys_E_INDEX_TYPE_E_INDEX_DDX_RED = 36,
    HQSys_E_INDEX_TYPE_E_INDEX_DYYH = 37,
    HQSys_E_INDEX_TYPE_E_INDEX_DYEH = 38,
    HQSys_E_INDEX_TYPE_E_INDEX_HJY = 39,
    HQSys_E_INDEX_TYPE_E_INDEX_PKS = 40,
    HQSys_E_INDEX_TYPE_E_INDEX_DZZJ = 41,
    HQSys_E_INDEX_TYPE_E_INDEX_GRZMR = 42,
    HQSys_E_INDEX_TYPE_E_INDEX_GSZG = 43,
    HQSys_E_INDEX_TYPE_E_INDEX_JGYZG = 44,
    HQSys_E_INDEX_TYPE_E_INDEX_MJDY = 45,
    HQSys_E_INDEX_TYPE_E_INDEX_PJDS = 46,
    HQSys_E_INDEX_TYPE_E_INDEX_SCRMGZ = 47,
    HQSys_E_INDEX_TYPE_E_INDEX_DYDZ = 48,
    HQSys_E_INDEX_TYPE_E_INDEX_YJYZ = 49,
    HQSys_E_INDEX_TYPE_E_INDEX_YPZTYC = 50,
    HQSys_E_INDEX_TYPE_E_INDEX_YQNG = 51,
    HQSys_E_INDEX_TYPE_E_INDEX_BDLT = 52,
    HQSys_E_INDEX_TYPE_E_INDEX_CDLT = 53,
    HQSys_E_INDEX_TYPE_E_INDEX_CZLT = 54,
    HQSys_E_INDEX_TYPE_E_INDEX_JZLT = 55,
    HQSys_E_INDEX_TYPE_E_INDEX_QSLT = 56,
    HQSys_E_INDEX_TYPE_E_INDEX_RDLT = 57,
    HQSys_E_INDEX_TYPE_E_INDEX_ZCLT = 58,
    HQSys_E_INDEX_TYPE_E_INDEX_ZTLT = 59,
    HQSys_E_INDEX_TYPE_E_INDEX_ZJLT = 60,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB1 = 61,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB2 = 62,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB3 = 63,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB4 = 64,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB5 = 65,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB6 = 66,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB7 = 67,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB8 = 68,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB9 = 69,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB10 = 70,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB11 = 71,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB12 = 72,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB13 = 73,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB14 = 74,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB15 = 75,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB16 = 76,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB17 = 77,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB18 = 78,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB19 = 79,
    HQSys_E_INDEX_TYPE_E_INDEX_XGZB20 = 80,
    HQSys_E_INDEX_TYPE_E_INDEX_BDGZ = 81,
    HQSys_E_INDEX_TYPE_E_INDEX_DXLS = 82,
    HQSys_E_INDEX_TYPE_E_INDEX_FZXZ = 83,
    HQSys_E_INDEX_TYPE_E_INDEX_FLZT = 84,
    HQSys_E_INDEX_TYPE_E_INDEX_LHXF = 85,
    HQSys_E_INDEX_TYPE_E_INDEX_WJJ = 86,
    HQSys_E_INDEX_TYPE_E_INDEX_WJCP = 87,
    HQSys_E_INDEX_TYPE_E_INDEX_YZQD = 88,
    HQSys_E_INDEX_TYPE_E_INDEX_FCT_BOLL = 89,
    HQSys_E_INDEX_TYPE_E_INDEX_FCT_KDJ = 90,
    HQSys_E_INDEX_TYPE_E_INDEX_FCT_MACD = 91,
    HQSys_E_INDEX_TYPE_E_INDEX_FCT_RSI = 92,
    HQSys_E_INDEX_TYPE_E_INDEX_FCT_DKX = 93,
    HQSys_E_INDEX_TYPE_E_INDEX_FCT_DYZCMX_JGYW = 94,
    HQSys_E_INDEX_TYPE_E_INDEX_KDJ_DSX = 95,
    HQSys_E_INDEX_TYPE_E_INDEX_MA_DSX = 96,
    HQSys_E_INDEX_TYPE_E_INDEX_MACD_DSX = 97,
    HQSys_E_INDEX_TYPE_E_INDEX_BSJK_BDJK = 98,
    HQSys_E_INDEX_TYPE_E_INDEX_BSJK_WXJK = 99,
    HQSys_E_INDEX_TYPE_E_INDEX_BSJK_SBFZJK = 100,
    HQSys_E_INDEX_TYPE_E_INDEX_TSXG_CPLS = 101,
    HQSys_E_INDEX_TYPE_E_INDEX_TSXG_ZJBY = 102,
    HQSys_E_INDEX_TYPE_E_INDEX_TSXG_MACDDZQGZ = 103,
    HQSys_E_INDEX_TYPE_E_INDEX_TSXG_GSBD = 104,
    HQSys_E_INDEX_TYPE_E_INDEX_TSXG_DKLC = 105,
    HQSys_E_INDEX_TYPE_E_INDEX_TSXG_HCNLBY = 106,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_MACDJX = 107,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_DBHSB = 108,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_SDHT = 109,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_JDXG = 110,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_DFP = 111,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_LNJB = 112,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_PZTP = 113,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_XBSY = 114,
    HQSys_E_INDEX_TYPE_E_INDEX_MJXG_DXLS = 115,
    HQSys_E_INDEX_TYPE_E_INDEX_MJXG_SSQL = 116,
    HQSys_E_INDEX_TYPE_E_INDEX_MJXG_YXQN = 117,
    HQSys_E_INDEX_TYPE_E_INDEX_YHXG_ZJXB = 118,
    HQSys_E_INDEX_TYPE_E_INDEX_YHXG_HJZ = 119,
    HQSys_E_INDEX_TYPE_E_INDEX_JDXG_BHLCT = 120,
    HQSys_E_INDEX_TYPE_E_INDEX_JDXG_MMPP = 121,
    HQSys_E_INDEX_TYPE_E_INDEX_JDXG_QSDD = 122,
    HQSys_E_INDEX_TYPE_E_INDEX_JDXG_CYHT = 123,
    HQSys_E_INDEX_TYPE_E_INDEX_JDXG_BTX = 124,
    HQSys_E_INDEX_TYPE_E_INDEX_JDXG_QLDX = 125,
    HQSys_E_INDEX_TYPE_E_INDEX_MHDZ = 126,
    HQSys_E_INDEX_TYPE_E_INDEX_CPXHD = 127,
    HQSys_E_INDEX_TYPE_E_INDEX_DXJJ_HIS = 128,
    HQSys_E_INDEX_TYPE_E_INDEX_DYYH_HIS = 129,
    HQSys_E_INDEX_TYPE_E_INDEX_DYEH_HIS = 130,
    HQSys_E_INDEX_TYPE_E_INDEX_HJY_HIS = 131,
    HQSys_E_INDEX_TYPE_E_INDEX_PKS_HIS = 132,
    HQSys_E_INDEX_TYPE_E_INDEX_DJPD = 133,
    HQSys_E_INDEX_TYPE_E_INDEX_WXCP_B = 134,
    HQSys_E_INDEX_TYPE_E_INDEX_WXCP_B_HIS = 135,
    HQSys_E_INDEX_TYPE_E_INDEX_WXCP_BT = 136,
    HQSys_E_INDEX_TYPE_E_INDEX_ZTJBNEW = 137,
    HQSys_E_INDEX_TYPE_E_INDEX_ZTJB_APP = 138,
    HQSys_E_INDEX_TYPE_E_INDEX_XSBDW = 139,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_HSB = 140,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_QMZX = 141,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_DWCZX = 142,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_KZTM = 143,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_SGCX = 144,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_DFP = 145,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_SSSF = 146,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_SKYX = 147,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_PTDB = 148,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_DWBPYX = 149,
    HQSys_E_INDEX_TYPE_E_INDEX_PYTHON_XSYJ = 150,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLGZ = 151,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLT0 = 152,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLT0_RTMIN = 153,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLT0_DAY = 154,
    HQSys_E_INDEX_TYPE_E_INDEX_LB = 155,
    HQSys_E_INDEX_TYPE_E_INDEX_LB_RTMIN = 156,
    HQSys_E_INDEX_TYPE_E_INDEX_XSBDXG = 157,
    HQSys_E_INDEX_TYPE_E_INDEX_XSBDXG_HIS = 158,
    HQSys_E_INDEX_TYPE_E_INDEX_QDYCL = 159,
    HQSys_E_INDEX_TYPE_E_INDEX_ZJDL_L2 = 160,
    HQSys_E_INDEX_TYPE_E_INDEX_XSBDW_YJ = 161,
    HQSys_E_INDEX_TYPE_E_INDEX_ZJDL_L2_RTMIN = 162,
    HQSys_E_INDEX_TYPE_E_INDEX_ZJDL_L2_DAY = 163,
    HQSys_E_INDEX_TYPE_E_INDEX_ZCYL = 164,
    HQSys_E_INDEX_TYPE_E_INDEX_ZCYL_DAY = 165,
    HQSys_E_INDEX_TYPE_E_INDEX_BSJK_WXJK_MRJG = 166,
    HQSys_E_INDEX_TYPE_E_INDEX_FCT_VOL = 167,
    HQSys_E_INDEX_TYPE_E_INDEX_ZPZY = 168,
    HQSys_E_INDEX_TYPE_E_INDEX_WPQL = 169,
    HQSys_E_INDEX_TYPE_E_INDEX_LZJJ = 170,
    HQSys_E_INDEX_TYPE_E_INDEX_LZJJ_HIS = 171,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLGJ_HIS = 172,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLCT_HIS = 173,
    HQSys_E_INDEX_TYPE_E_INDEX_HJJC_HIS = 174,
    HQSys_E_INDEX_TYPE_E_INDEX_SWJC_HIS = 175,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_MACDJX_HIS = 176,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_DBHSB_HIS = 177,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_SDHT_HIS = 178,
    HQSys_E_INDEX_TYPE_E_INDEX_XTXG_LNJB_HIS = 179,
    HQSys_E_INDEX_TYPE_E_INDEX_XSBDW_PZ = 180,
    HQSys_E_INDEX_TYPE_E_INDEX_LHB_JGWM = 181,
    HQSys_E_INDEX_TYPE_E_INDEX_LHB_YGTC = 182,
    HQSys_E_INDEX_TYPE_E_INDEX_LHB_CBQC = 183,
    HQSys_E_INDEX_TYPE_E_INDEX_DXJL_DJGD = 185,
    HQSys_E_INDEX_TYPE_E_INDEX_LHB_JGWM_HIS = 186,
    HQSys_E_INDEX_TYPE_E_INDEX_LHB_YGTC_HIS = 187,
    HQSys_E_INDEX_TYPE_E_INDEX_LHB_CBQC_HIS = 188,
    HQSys_E_INDEX_TYPE_E_INDEX_JZXL = 189,
    HQSys_E_INDEX_TYPE_E_INDEX_YQRG = 190,
    HQSys_E_INDEX_TYPE_E_INDEX_QSRG = 191,
    HQSys_E_INDEX_TYPE_E_INDEX_JZRG = 192,
    HQSys_E_INDEX_TYPE_E_INDEX_ALLRG = 193,
    HQSys_E_INDEX_TYPE_E_INDEX_HQQSG = 194,
    HQSys_E_INDEX_TYPE_E_INDEX_ZPZY_RQBS = 195,
    HQSys_E_INDEX_TYPE_E_INDEX_ZPZY_YZWM = 196,
    HQSys_E_INDEX_TYPE_E_INDEX_ZPZY_ZJQY = 197,
    HQSys_E_INDEX_TYPE_E_INDEX_QLGC_ZPZY = 198,
    HQSys_E_INDEX_TYPE_E_INDEX_WPQLNEW = 199,
    HQSys_E_INDEX_TYPE_E_INDEX_QLGC_WPQL = 202,
    HQSys_E_INDEX_TYPE_E_INDEX_WXQL_SQFZ = 203,
    HQSys_E_INDEX_TYPE_E_INDEX_WXQL_SZTD = 204,
    HQSys_E_INDEX_TYPE_E_INDEX_WXQL_DWCD = 205,
    HQSys_E_INDEX_TYPE_E_INDEX_QLGC_WXQL = 206,
    HQSys_E_INDEX_TYPE_E_INDEX_DDLDJ = 207,
    HQSys_E_INDEX_TYPE_E_INDEX_CDQB = 208,
    HQSys_E_INDEX_TYPE_E_INDEX_QLGC_ZTJB = 209,
    HQSys_E_INDEX_TYPE_E_INDEX_QSYHCD = 210,
    HQSys_E_INDEX_TYPE_E_INDEX_SDJJ = 211,
    HQSys_E_INDEX_TYPE_E_INDEX_QSQB = 212,
    HQSys_E_INDEX_TYPE_E_INDEX_GZZZ = 213,
    HQSys_E_INDEX_TYPE_E_INDEX_HJHC = 214,
    HQSys_E_INDEX_TYPE_E_INDEX_DN_QSQD = 215,
    HQSys_E_INDEX_TYPE_E_INDEX_ZJ_ZPZY = 216,
    HQSys_E_INDEX_TYPE_E_INDEX_CJDD = 217,
    HQSys_E_INDEX_TYPE_E_INDEX_QJCMN = 218,
    HQSys_E_INDEX_TYPE_E_INDEX_BLK_DDERANK = 219,
    HQSys_E_INDEX_TYPE_E_INDEX_BLK_QJCMN_DTGC = 220,
    HQSys_E_INDEX_TYPE_E_INDEX_BLK_QJCMN_CFGC = 221,
    HQSys_E_INDEX_TYPE_E_INDEX_LTJB = 222,
    HQSys_E_INDEX_TYPE_E_INDEX_EBDL = 223,
    HQSys_E_INDEX_TYPE_E_INDEX_HWKYY_CDDS = 224,
    HQSys_E_INDEX_TYPE_E_INDEX_HWKYY_ZZLS = 225,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_ZJWW = 226,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_ZTXN = 227,
    HQSys_E_INDEX_TYPE_E_INDEX_DDTJ = 228,
    HQSys_E_INDEX_TYPE_E_INDEX_DDCM = 229,
    HQSys_E_INDEX_TYPE_E_INDEX_DDDN = 230,
    HQSys_E_INDEX_TYPE_E_INDEX_QLGC_XSBDW = 231,
    HQSys_E_INDEX_TYPE_E_INDEX_QLGC_XSBDW_HIS = 232,
    HQSys_E_INDEX_TYPE_E_INDEX_ZGB_LZYH = 233,
    HQSys_E_INDEX_TYPE_E_INDEX_ZGB_LZYH_YZGC = 234,
    HQSys_E_INDEX_TYPE_E_INDEX_STJGC = 235,
    HQSys_E_INDEX_TYPE_E_INDEX_CDJJJGC = 236,
    HQSys_E_INDEX_TYPE_E_INDEX_YZJGC = 237,
    HQSys_E_INDEX_TYPE_E_INDEX_QSYBP = 238,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_ZJB = 239,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_ZJBB = 240,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_TOP = 241,
    HQSys_E_INDEX_TYPE_E_INDEX_DMGC = 242,
    HQSys_E_INDEX_TYPE_E_INDEX_CJDD_MARKET_HOT = 243,
    HQSys_E_INDEX_TYPE_E_INDEX_CJDD_ALL = 244,
    HQSys_E_INDEX_TYPE_E_INDEX_CJDD_TPDD = 245,
    HQSys_E_INDEX_TYPE_E_INDEX_CJDD_CDDD = 246,
    HQSys_E_INDEX_TYPE_E_INDEX_XBLD = 247,
    HQSys_E_INDEX_TYPE_E_INDEX_FJLD = 248,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLZD = 249,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_DBDX_HIS = 250,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_LBYCBB = 251,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_LBYCZJB = 252,
    HQSys_E_INDEX_TYPE_E_INDEX_GNN_LBYCCGL = 253,
    HQSys_E_INDEX_TYPE_E_INDEX_AX_WXCP = 254,
    HQSys_E_INDEX_TYPE_E_INDEX_AX_XYBJX = 255,
    HQSys_E_INDEX_TYPE_E_INDEX_AX_XYBJX_TENDAYS = 256,
    HQSys_E_INDEX_TYPE_E_INDEX_AX_XYBJX_HIS = 257,
    HQSys_E_INDEX_TYPE_E_INDEX_LTZSXH = 258,
    HQSys_E_INDEX_TYPE_E_INDEX_LTZSJK = 259,
    HQSys_E_INDEX_TYPE_E_INDEX_DBKX = 260,
    HQSys_E_INDEX_TYPE_E_INDEX_TPFB = 261,
    HQSys_E_INDEX_TYPE_E_INDEX_FTJK = 262,
    HQSys_E_INDEX_TYPE_E_INDEX_ZBTP = 263,
    HQSys_E_INDEX_TYPE_E_INDEX_YYFZ = 264,
    HQSys_E_INDEX_TYPE_E_INDEX_HHBZTYZ = 265,
    HQSys_E_INDEX_TYPE_E_INDEX_DDLD = 266,
    HQSys_E_INDEX_TYPE_E_INDEX_QBDH = 267,
    HQSys_E_INDEX_TYPE_E_INDEX_SHZS = 268,
    HQSys_E_INDEX_TYPE_E_INDEX_LTJJ = 269,
    HQSys_E_INDEX_TYPE_E_INDEX_LTJJ_ZJB = 270,
    HQSys_E_INDEX_TYPE_E_INDEX_LTJJ_HIS = 271,
    HQSys_E_INDEX_TYPE_E_INDEX_LTJJ_STATIS = 272,
    HQSys_E_INDEX_TYPE_E_INDEX_GDS_BT = 273,
    HQSys_E_INDEX_TYPE_E_INDEX_GDS_LTSYB = 274,
    HQSys_E_INDEX_TYPE_E_INDEX_GDS_YZGC = 275,
    HQSys_E_INDEX_TYPE_E_INDEX_LTJJ_GG = 276,
    HQSys_E_INDEX_TYPE_E_INDEX_LTQDJK = 277,
    HQSys_E_INDEX_TYPE_E_INDEX_LTQDXH = 278,
    HQSys_E_INDEX_TYPE_E_INDEX_HHBZTYZ_ZJB = 279,
    HQSys_E_INDEX_TYPE_E_INDEX_HHBZTYZ_HIS = 280,
    HQSys_E_INDEX_TYPE_E_INDEX_HHBZTYZ_STATIS = 281,
    HQSys_E_INDEX_TYPE_E_INDEX_QUANT_BDDD = 282,
    HQSys_E_INDEX_TYPE_E_INDEX_QUANT_TPDD = 283,
    HQSys_E_INDEX_TYPE_E_INDEX_QUANT_CDDD = 284,
    HQSys_E_INDEX_TYPE_E_INDEX_QUANT_CJDD_ALL = 285,
    HQSys_E_INDEX_TYPE_E_INDEX_QUANT_CJDD_FCT = 286,
    HQSys_E_INDEX_TYPE_E_INDEX_ZB_ZT = 287,
    HQSys_E_INDEX_TYPE_E_INDEX_NBXZJYD = 288,
    HQSys_E_INDEX_TYPE_E_INDEX_JZLT_DN = 289,
    HQSys_E_INDEX_TYPE_E_INDEX_LTQDXH_HTTP = 290,
    HQSys_E_INDEX_TYPE_E_INDEX_JZLT_XH = 291,
    HQSys_E_INDEX_TYPE_E_INDEX_ZXJT_LS_F = 292,
    HQSys_E_INDEX_TYPE_E_INDEX_ZXJT_TS_F = 293,
    HQSys_E_INDEX_TYPE_E_INDEX_YY_HHB = 294,
    HQSys_E_INDEX_TYPE_E_INDEX_YY_HB = 295,
    HQSys_E_INDEX_TYPE_E_INDEX_ZT_FORECAST = 296,
    HQSys_E_INDEX_TYPE_E_INDEX_SZZL = 297,
    HQSys_E_INDEX_TYPE_E_INDEX_ZLCB = 298,
    HQSys_E_INDEX_TYPE_E_INDEX_BDKX = 299,
    HQSys_E_INDEX_TYPE_E_INDEX_LTJJ_HIS_B = 300,
    HQSys_E_INDEX_TYPE_E_INDEX_HHBZTYZ_HIS_B = 301,
    HQSys_E_INDEX_TYPE_E_INDEX_LHZY_3D = 302,
    HQSys_E_INDEX_TYPE_E_INDEX_LHZY_5D = 308,
    HQSys_E_INDEX_TYPE_E_INDEX_LHZY_10D = 309,
    HQSys_E_INDEX_TYPE_E_INDEX_LHZY_20D = 310,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_CX = 311,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_ZX = 312,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_DX = 313,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_LH = 314,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_JGDY = 315,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_6 = 316,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_7 = 317,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_8 = 318,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_9 = 319,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_10 = 320,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_11 = 321,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_12 = 322,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_13 = 323,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_14 = 324,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_15 = 325,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_16 = 326,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_17 = 327,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_18 = 328,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_19 = 329,
    HQSys_E_INDEX_TYPE_E_INDEX_CF_20 = 330,
    HQSys_E_INDEX_TYPE_E_INDEX_HZZZ = 331,
    HQSys_E_INDEX_TYPE_E_INDEX_HZQB = 332,
    HQSys_E_INDEX_TYPE_E_INDEX_SHGD = 333,
    HQSys_E_INDEX_TYPE_E_INDEX_GKZZ = 334,
    HQSys_E_INDEX_TYPE_E_INDEX_GKQB = 335,
    HQSys_E_INDEX_TYPE_E_INDEX_GKSH = 336,
    HQSys_E_INDEX_TYPE_E_INDEX_KXCL = 337,
    HQSys_E_INDEX_TYPE_E_INDEX_LHZY_1D = 338,
    HQSys_E_INDEX_TYPE_E_INDEX_ZTGG = 339,
    HQSys_E_INDEX_TYPE_E_INDEX_GPT_CJDD = 340,
    HQSys_E_INDEX_TYPE_E_INDEX_NEWHHBGC = 341,
    HQSys_E_INDEX_TYPE_E_INDEX_NEWHHBGC_HIS = 342,
    HQSys_E_INDEX_TYPE_E_INDEX_NEWHHBGG = 343,
    HQSys_E_INDEX_TYPE_E_INDEX_SGXN = 354,
    HQSys_E_INDEX_TYPE_E_INDEX_ZCLD = 355,
    HQSys_E_INDEX_TYPE_E_INDEX_C_POINT = 356,
    HQSys_E_INDEX_TYPE_E_INDEX_QSQD = 357,
    HQSys_E_INDEX_TYPE_E_INDEX_WXGPC = 358,
    HQSys_E_INDEX_TYPE_E_INDEX_GSDGC = 359,
    HQSys_E_INDEX_TYPE_E_INDEX_QSQDGC = 360,
    HQSys_E_INDEX_TYPE_E_INDEX_QSQRGC = 361,
    HQSys_E_INDEX_TYPE_E_INDEX_QSABC = 362,
    HQSys_E_INDEX_TYPE_E_INDEX_GZKX = 370,
    HQSys_E_INDEX_TYPE_E_INDEX_WXCPX_NEW = 373,
    HQSys_E_INDEX_TYPE_E_INDEX_LTJJ_GG_TSTC = 1250,
    HQSys_E_INDEX_TYPE_E_INDEX_QLSBF = 1251,
    HQSys_E_INDEX_TYPE_E_INDEX_WXGPC_MMD = 1255,
    HQSys_E_INDEX_TYPE_E_INDEX_SJNX = 1400,
    HQSys_E_INDEX_TYPE_E_INDEX_FGPH = 1401,
    HQSys_E_INDEX_TYPE_E_INDEX_CWPZ = 1402
};
#define HQSys_E_INDEX_TYPE NSInteger


enum {
    HQSys_E_INDEX_REQ_TYPE_E_REQ_REQ = 1,
    HQSys_E_INDEX_REQ_TYPE_E_REQ_REG = 2,
    HQSys_E_INDEX_REQ_TYPE_E_REQ_UNREG = 3
};
#define HQSys_E_INDEX_REQ_TYPE NSInteger

@interface HQSysHMoneyFlowTrend : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_fMainMoneyInflow;
@property (nonatomic, assign) JceDouble jce_fMainMoneyAllInflow;
@end

@interface HQSysHMoneyFlow : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceInt32 jce_days;
@property (nonatomic, strong) HQSysHMoneyFlowTrend* jce_fDayMFlowTrend;
@end

@interface HQSysSIndexData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceDouble jce_f1;
@property (nonatomic, assign) JceDouble jce_f2;
@property (nonatomic, assign) JceDouble jce_f3;
@property (nonatomic, assign) JceDouble jce_f4;
@property (nonatomic, assign) JceDouble jce_f5;
@property (nonatomic, assign) JceDouble jce_f6;
@property (nonatomic, assign) JceDouble jce_f7;
@property (nonatomic, assign) JceDouble jce_f8;
@property (nonatomic, assign) JceDouble jce_f9;
@property (nonatomic, assign) JceDouble jce_f10;
@property (nonatomic, assign) JceDouble jce_f11;
@property (nonatomic, assign) JceDouble jce_f12;
@property (nonatomic, assign) JceDouble jce_f13;
@property (nonatomic, assign) JceDouble jce_f14;
@property (nonatomic, assign) JceDouble jce_f15;
@property (nonatomic, assign) JceDouble jce_f16;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, strong) NSString* jce_sName;
@end

@interface HQSysSIndexDataNew : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, strong) NSDictionary<NSString*, NSNumber*>* jce_mField;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mTagField;
@end

@interface HQSysSIndex : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysSIndexData*>* jce_vData;
@end

@interface HQSysSIndexNew : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysSIndexDataNew*>* jce_vData;
@end

@interface HQSysSIndexConf : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sCategory;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mField;
@end

@interface HQSysSCategoryConfList : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysSIndexConf*>* jce_vData;
@end

@interface HQSysSL2DynamicData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mField;
@end

@interface HQSysSL2Dynamic : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysSL2DynamicData*>* jce_vData;
@end

@interface HQSysSShortLineData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mField;
@property (nonatomic, strong) NSString* jce_sName;
@end

@interface HQSysSShortLine : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysSShortLineData*>* jce_vData;
@end

@interface HQSysSIndexBackTestData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mField;
@end

@interface HQSysSIndexBackTest : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysSIndexBackTestData*>* jce_vData;
@end

@interface HQSysSStkPoolBTData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDayNum;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mField;
@end

@interface HQSysSStkPoolBT : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysSStkPoolBTData*>* jce_vData;
@end

@interface HQSysSNotifyIndexReq : UPTAFJceObject
@property (nonatomic, assign) HQSys_E_INDEX_REQ_TYPE jce_eReqType;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_eType;
@end

@interface HQSysSNotifyIndexRsp : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysSIndex*>* jce_mData;
@end

@interface HQSysSPushIndexReq : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysSIndex*>* jce_mData;
@end

@interface HQSysSPushMFlowReq : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysHMoneyFlow*>* jce_vflow;
@end

@interface HQSysSPushDataReq : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSString*, HQSysSIndex*>* jce_mData;
@end



