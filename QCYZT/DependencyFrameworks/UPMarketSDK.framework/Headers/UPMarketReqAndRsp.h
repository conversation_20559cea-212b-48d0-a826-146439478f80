//
//  UPMarketReqAndRes.h
//  UP_GuPiaoTong
//
//  Created by <PERSON><PERSON> on 2017/2/28.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketModel.h"
#import "UPMarketDefine.h"

// ---------------------Handler-----------------------

@class UPHqRsp;
@class UPMarketStockHqRsp;
@class UPMarketType2StockRsp;
@class UPMarketBlock2StockRsp;
@class UPMarketStock2BlockRsp;
@class UPMarketTickDataRsp;
@class UPMarketTransRsp;
@class UPMarketMinDataRsp;
@class UPMarketKLineDataRsp;
@class UPMarketQXDataRsp;
@class UPMarketCapitalFlowDataRsp;
@class UPMarketOrderQueueRsp;
@class UPMarketPriceOrderRsp;
@class UPMarketLevel2PoolRsp;
@class UPMarketDDERsp;
@class UPMarketIndexRsp;
@class UPMarketIndexStockListRsp;
@class UPMarketFlowRankRsp;
@class UPMarketStatusRsp;
@class UPMarketBrokerQueueRsp;
@class UPMarketAHStockRsp;
@class UPMarketRelateAHStockRsp;
@class UPMarketOptStockHqRsp;
@class UPMarketRequest;
@class UPMarketHKWarrantRsp;
@class UPMarketMoneyRankTopDataRsp;
@class UPMarketWarrantRelatedStockRsp;
@class UPMarketStockFactorDataRsp;
@class UPMarketStockBaseInfoRsp;
@class UPMarketStaticModel;
@class UPMarketStockPoolStatRsp;
@class UPMarketSubjectChangeListRsp;
@class UPMarketLeadSubjectRsp;
@class UPMarketDDERankDataRsp;
@class UPMarketStockIndexRsp;
@class UPMarketIndexStocksRsp;
@class UPMarketRegStockListRsp;
@class UPMarketPriceVolRsp;
@class UPMarketChipRsp;
@class UPMarketOptionListRsp;
@class UPMarketOptionBaseInfoRsp;
@class UPMarketOptionUnderlyingListRsp;
@class UPMarketXSBYYStockListRsp;
@class UPMarketXSBYYStockListRsp;
@class UPMarketXSBFXStockListRsp;
@class UPMarketLatestStockChangeRsp;
@class UPMarketDXJLRsp;
@class UPMarketStockAuctionRsp;
@class UPMarketStockAuctionV2Rsp;
@class UPMarketRelateAHStockBatchRsp;
@class UPMarketSouthMoneyFlowDataRsp;
@class UPMarketCloseInfoRsp;
@class UPMarketAnomalyBroadcastRsp;
@class UPMarketSNFundsDataRsp;
@class UPMarketSNNetDataRsp;
@class UPMarketHisSNNetDataRsp;
@class UPMarketCurrencyInfoRsp;

@class MStockItem;
@class UPMarketKZZDataRsp;
@class UPMarketJPJLStockRsp;
@class UPMarketZDFBInfoRsp;
@class UPMarketL2SzfyComRsp;
@class UPMarketRangeStatsByDateRsp;
/**
 * 基础行情接口回调
 *
 * @param rsp UPMarketStockHqRsp 基础行情回包Rsp
 * @param error 错误信息
 */
typedef void (^upMarketStockHqCompletionHandler)(UPMarketStockHqRsp *rsp, NSError *error);

/**
 * 行情列表请求回调
 *
 * @param rsp UPMarketType2StockRsp 行情列表请求回包Rsp
 * @param error 错误信息
 */
typedef void (^upMarketStockTypeCompletionHandler)(UPMarketType2StockRsp *rsp, NSError *error);

/**
 * 根据板块请求股票回调
 *
 * @param rsp UPMarketBlock2StockRsp 根据板块请求股票回包Rsp
 * @param error 错误信息
 */
typedef void (^upMarketStockBlockCompletionHandler)(UPMarketBlock2StockRsp *rsp, NSError *error);

/**
 * 根据股票查询板块回调
 *
 * @param rsp UPMarketStock2BlockRsp 根据股票查询板块回包Rsp
 * @param error 错误信息
 */
typedef void (^upMarketBlockStockCompletionHandler)(UPMarketStock2BlockRsp *rsp, NSError *error);

/**
 * 请求股票分笔明细数据回调
 *
 * @param rsp UPMarketTickDataRsp 股票分笔明细数据
 * @param error 错误信息
 */
typedef void (^upMarketStockTickCompletionHandler)(UPMarketTickDataRsp *rsp, NSError *error);

/**
 * 请求股票L2逐笔数据回调
 *
 * @param rsp UPMarketTransRsp 股票L2逐笔数据
 * @param error 错误信息
 */
typedef void (^upMarketStockTransCompletionHandler)(UPMarketTransRsp *rsp, NSError *error);

/**
 * 请求股票股票分时数据回调
 *
 * @param rsp UPMarketMinDataRsp 股票分时数据
 * @param error 错误信息
 */
typedef void (^upMarketStockMinuteCompletionHandler)(UPMarketMinDataRsp *rsp, NSError *error);

/**
 * 请求股票股票K线数据回调
 *
 * @param rsp UPMarketKLineDataRsp 股票K线数据
 * @param error 错误信息
 */
typedef void (^upMarketStockKlineCompletionHandler)(UPMarketKLineDataRsp *rsp, NSError *error);

/**
 * 请求股票股票除权除息数据回调
 *
 * @param rsp UPMarketQXDataRsp 股票除权除息数据
 * @param error 错误信息
 */
typedef void (^upMarketStockQXCompletionHandler)(UPMarketQXDataRsp *rsp, NSError *error);

/**
 * 请求股票实时资金流数据回调
 *
 * @param rsp UPMarketCapitalFlowDataRsp 股票实时资金流数据
 * @param error 错误信息
 */
typedef void (^upMarketCapitalFlowCompletionHandler)(UPMarketCapitalFlowDataRsp *rsp, NSError *error);

/**
 * 请求南向资金流
 *
 * @param rsp UPMarketSouthMoneyFlowDataRsp 南向资金流数据
 * @param error 错误信息
 */
typedef void (^upMarketSouthMoneyFlowCompletionHandler)(UPMarketSouthMoneyFlowDataRsp *rsp, NSError *error);

// 南北向资金
typedef void (^upMarketSNFundsDataCompletionHandler)(UPMarketSNFundsDataRsp *rsp, NSError *error);

// 南北向资金净流入
typedef void (^upMarketSNNetDataCompletionHandler)(UPMarketSNNetDataRsp *rsp, NSError *error);

// 南北向资金历史净流入
typedef void (^upMarketHisSNNetDataCompletionHandler)(UPMarketHisSNNetDataRsp *rsp, NSError *error);

/**
 * 请求休市信息
 *
 * @param rsp UPMarketCloseInfoRsp 休市信息
 * @param error 错误信息
 */
typedef void (^upMarketCloseInfoCompletionHandler)(UPMarketCloseInfoRsp *rsp, NSError *error);

/**
 * 请求股票委托队列数据回调
 *
 * @param rsp UPMarketOrderQueueRsp 股票委托队列数据
 * @param error 错误信息
 */
typedef void (^upMarketOrderQueueCompletionHandler)(UPMarketOrderQueueRsp *rsp, NSError *error);

/**
 * 请求股票价格委托数据(L2千档盘口)回调
 *
 * @param rsp UPMarketPriceOrderRsp 股票价格委托数据(L2千档盘口)
 * @param error 错误信息
 */
typedef void (^upMarketPriceOrderCompletionHandler)(UPMarketPriceOrderRsp *rsp, NSError *error);

/**
 * 请求Level2选股池数据回调
 *
 * @param rsp UPMarketLevel2PoolRspLevel2 选股池数据
 * @param error 错误信息
 */
typedef void (^upMarketLevel2CompletionHandler)(UPMarketLevel2PoolRsp *rsp, NSError *error);

/**
 * 请求股票DDE系列数据回调
 *
 * @param rsp UPMarketDDERsp DDE系列数据
 * @param error 错误信息
 */
typedef void (^upMarketDDECompletionHandler)(UPMarketDDERsp *rsp, NSError *error);

/**
 * 请求股票指标数据回调
 *
 * @param rsp UPMarketIndexRsp 股票指标数据
 * @param error 错误信息
 */
typedef void (^upMarketIndexCompletionHandler)(UPMarketIndexRsp *rsp, NSError *error);

/**
 * 请求指标对应的股票回调
 *
 * @param rsp UPMarketIndexStockListRsp 指标对应的股票数据
 * @param error 错误信息
 */
typedef void (^upMarketIndexStockListCompletionHandler)(UPMarketIndexStockListRsp *rsp, NSError *error);

/**
 * 请求资金排名数据回调
 *
 * @param rsp UPMarketFlowRankRsp 资金排名数据
 * @param error 错误信息
 */
typedef void (^upMarketFlowRankCompletionHandler)(UPMarketFlowRankRsp *rsp, NSError *error);

/**
 * 请求市场状态数据回调
 *
 * @param rsp UPMarketStatusRsp 市场状态数据
 * @param error 错误信息
 */
typedef void (^upMarketStatusCompletionHandler)(UPMarketStatusRsp *rsp, NSError *error);

/**
 * 请求港股经纪数据 (港股L2)回调
 *
 * @param rsp UPMarketBrokerQueueRsp 港股经纪数据
 * @param error 错误信息
 */
typedef void (^upMarketBrokerQueueCompletionHandler)(UPMarketBrokerQueueRsp *rsp, NSError *error);

/**
 * 请求AH股列表回调
 *
 * @param rsp UPMarketAHStockRsp AH股列表数据
 * @param error 错误信息
 */
typedef void (^upMarketAHStockCompletionHandler)(UPMarketAHStockRsp *rsp, NSError *error);

/**
 * 请求对应的AH股票回调
 *
 * @param rsp UPMarketRelateAHStockRsp 对应的AH股票
 * @param error 错误信息
 */
typedef void (^upMarketRelateAHStockCompletionHandler)(UPMarketRelateAHStockRsp *rsp, NSError *error);

/**
 * 请求对应的AH股票回调(新)
 *
 * @param rsp UPMarketRelateAHStockRsp 对应的AH股票
 * @param error 错误信息
 */
typedef void (^upMarketRelateAHStockBatchCompletionHandler)(UPMarketRelateAHStockBatchRsp *rsp, NSError *error);

/**
 * 请求自选股票行情数据回调
 *
 * @param rsp UPMarketOptStockHqRsp 自选股票行情数据
 * @param error 错误信息
 */
typedef void (^upMarketOptStockHqCompletionHandler)(UPMarketOptStockHqRsp *rsp, NSError *error);

/**
 * 请求港股权证数据回调
 *
 * @param rsp UPMarketHKWarrantRsp 港股权证数据
 * @param error 错误信息
 */
typedef void (^upMarketHKWarrantCompletionHandler)(UPMarketHKWarrantRsp *rsp, NSError *error);

/**
 * 请求股票或板块资金排名净流入和净流出的Top数据列表回调
 *
 * @param rsp UPMarketMoneyRankTopDataRsp 资金排名净流入和净流出的Top数据列表
 * @param error 错误信息
 */
typedef void (^upMarketMoneyRankTopDataCompletionHandler)(UPMarketMoneyRankTopDataRsp *rsp, NSError *error);

/**
 * 请求港股权证所属的港股数据回调
 *
 * @param rsp UPMarketWarrantRelatedStockRsp 港股权证所属的港股数据
 * @param error 错误信息
 */
typedef void (^upMarketWarrantRelatedStockCompletionHandler)(UPMarketWarrantRelatedStockRsp *rsp, NSError *error);

/**
 * 请求股票因子库指标数据回调
 *
 * @param rsp UPMarketStockFactorDataRsp `股票因子库指标数据
 * @param error 错误信息
 */
typedef void (^upMarketStockFactorDataCompletionHandler)(UPMarketStockFactorDataRsp *rsp, NSError *error);

/**
 * 请求股票静态数据回调
 *
 * @param rsp UPMarketStockBaseInfoRsp 股票静态数据
 * @param error 错误信息
 */
typedef void (^upMarketBaseInfoCompletionHandler) (UPMarketStockBaseInfoRsp *rsp, NSError *error);

/**
 * 请求股池统计数据回调
 *
 * @param rsp UPMarketStockPoolStatRsp 股池统计数据
 * @param error 错误信息
 */
typedef void (^upMarketStockPoolStatCompletionHandler) (UPMarketStockPoolStatRsp *rsp, NSError *error);

/**
 * 请求股牛牛题材异动数据回调
 *
 * @param rsp UPMarketSubjectChangeListRsp 股牛牛题材异动数据
 * @param error 错误信息
 */
typedef void (^upMarketSubjectChangeListCompletionHandler)(UPMarketSubjectChangeListRsp *rsp, NSError *error);

/**
 * 请求股牛牛领涨题材数据回调
 *
 * @param rsp UPMarketLeadSubjectRsp 股牛牛领涨题材数据
 * @param error 错误信息
 */
typedef void (^upMarketLeadSubjectBlockCompletionHandler)(UPMarketLeadSubjectRsp *rsp, NSError *error);

/**
 * 请求DDE排名数据回调
 *
 * @param rsp UPMarketDDERankDataRsp DDE排名数据
 * @param error 错误信息
 */
typedef void (^upMarketDDERankListCompletionHandler)(UPMarketDDERankDataRsp *rsp, NSError *error);

/**
 * 请求股票指标数据回调
 *
 * @param rsp UPMarketStockIndexRsp 股票指标数据
 * @param error 错误信息
 */
typedef void (^upMarketStockIndexPushCompletionHandler)(UPMarketStockIndexRsp *rsp, NSError *error);

/**
 * 请求指标股池回调
 *
 * @param rsp UPMarketIndexStocksRsp 指标股池
 * @param error 错误信息
 */
typedef void (^upMarketIndexStocksPushCompletionHandler)(UPMarketIndexStocksRsp *rsp, NSError *error);

/**
 * 请求趋势龙头
 *
 * @param rsp UPMarketRegStockListRsp
 * @param error 错误信息
 */
typedef void (^upMarketRegStockListCompletionHandler)(UPMarketRegStockListRsp *rsp, NSError *error);

/**
 * 请求股票L2价量分布数据回调
 *
 * @param rsp UPMarketPriceVolRsp 股票L2价量分布数据
 * @param error 错误信息
 */
typedef void (^upMarketPriceVolCompletionHandler)(UPMarketPriceVolRsp *rsp, NSError *error);

/**
 * 请求股票筹码分布数据回调
 *
 * @param rsp UPMarketChipRsp 股票筹码分布数据
 * @param error 错误信息
 */
typedef void (^upMarketChipCompletionHandler)(UPMarketChipRsp *rsp, NSError *error);

/**
 * 请求期权列表和T型报价列表回调
 *
 * @param rsp UPMarketOptionListRsp 期权列表和T型报价列表
 * @param error 错误信息
 */
typedef void (^upMarketOptionListCompletionHandler)(UPMarketOptionListRsp *rsp, NSError *error);

/**
 * 期权标的列表回调
 *
 * @param rsp UPMarketOptionUnderlyingListRsp 期权标的列表,只包含市场和代码字段, 需要再次请求行情数据
 * @param error 错误信息
 */
typedef void (^upMarketOptionUnderlyingListCompletionHandler)(UPMarketOptionUnderlyingListRsp *rsp, NSError *error);

/**
 * 期权基础数据请求回调 - 内部使用
 *
 * @param rsp UPMarketOptionBaseInfoRsp 期权基础数据
 * @param error 错误信息
 */
typedef void (^upMarketOptionBaseInfoCompletionHandler)(UPMarketOptionBaseInfoRsp *rsp, NSError *error);

/**
 * 要约股票列表(新三板)回调
 *
 * @param rsp UPMarketXSBYYStockListRsp 要约股票列表(新三板)数据
 * @param error 错误信息
 */
typedef void (^upMarketYYStockListCompletionHandler)(UPMarketXSBYYStockListRsp *rsp, NSError *error);

/**
 * 发行股票列表(新三板)回调
 *
 * @param rsp UPMarketXSBFXStockListRsp 发行股票列表(新三板)
 * @param error 错误信息
 */
typedef void (^upMarketFXStockListCompletionHandler)(UPMarketXSBFXStockListRsp *rsp, NSError *error);

/**
 * 个股异动回调
 *
 * @param rsp UPMarketLatestStockChangeRsp 最新个股异动列表
 * @param error 错误信息
 */
typedef void (^upMarketStockChangeCompletionHandler)(UPMarketLatestStockChangeRsp *rsp, NSError *error);


/**
 * 股票异动(百度新接口)
 *
 * @param rsp UPMarketLatestStockChangeRsp 最新个股异动列表
 * @param error 错误信息
 */
typedef void (^upMarketAnormalyBroadcastCompletionHandler)(UPMarketAnomalyBroadcastRsp *rsp, NSError *error);

/**
 * 短线精灵回调
 *
 * @param rsp UPMarketDXJLRsp 短线精灵股票列表
 * @param error 错误信息
 */
typedef void (^upMarketDXJLCompletionHandler)(UPMarketDXJLRsp *rsp, NSError *error);

/**
 * 集合竞价回调
 *
 * @param rsp UPMarketStockAuctionRsp 集合竞价数据
 * @param error 错误信息
 */
typedef void (^upMarketStockAuctionCompletionHandler)(UPMarketStockAuctionRsp *rsp, NSError *error);

/**
 * 集合竞价回调
 *
 * @param rsp UPMarketStockAuctionV2Rsp 集合竞价数据
 * @param error 错误信息
 */
typedef void (^upMarketStockAuctionV2CompletionHandler)(UPMarketStockAuctionV2Rsp *rsp, NSError *error);

/**
 * 可转债数据回调
 *
 * @param rsp UPMarketKZZDataRsp 可转债数据
 * @param error 错误信息
 */
typedef void (^upMarketStockKZZDataCompletionHandler)(UPMarketKZZDataRsp *rsp, NSError *error);

/**
 * 可转债数据回调
 *
 * @param rsp UPMarketKZZDataRsp 可转债数据
 * @param error 错误信息
 */
typedef void (^upMarketCurrencyInfoCompletionHandler)(UPMarketCurrencyInfoRsp *rsp, NSError *error);

/**
 * 键盘精灵股票搜索回调
 *
 * @param rsp UPMarketJPJLStockRsp 键盘精灵搜索结果
 * @param error 错误信息
 */
typedef void (^upMarketJPJLStockCompletionHandler)(UPMarketJPJLStockRsp *rsp, NSError *error);

/**
 * 涨跌分布回调(主站接口)
 *
 * @param rsp UPMarketZDFBInfoRsp 集合竞价数据
 * @param error 错误信息
 */
typedef void (^upMarketZDFBCompletionHandler)(UPMarketZDFBInfoRsp *rsp, NSError *error);


typedef void (^upMarketL2SzfyComCompletionHandler)(UPMarketL2SzfyComRsp *rsp, NSError *error);


typedef void (^upMarketRangeStatsByDateCompletionHandler)(UPMarketRangeStatsByDateRsp *rsp, NSError *error);


// Push Monitor中转Handler -- 内部使用
typedef void (^upMarketMonitorHandler)(UPMarketRequest *req, UPHqRsp *rsp, NSError *error);

// ---------------------基础行情-----------------------
// MARK: - Basic
@interface UPHqReq : NSObject <NSCopying>

/**
 *拷贝基础信息-内部使用
 *
 * @param newCopy 新的数据结构
 */
- (void)copyBasicInfo:(__kindof UPHqReq *)newCopy;

/**
 * 根据市场代码和股票码构造请求体
 *
 * @param setCode  市场码 如深圳市场 UPMarketSetCodeSZ
 * @param code 股票代码 如 万科A:'000002'
 */
- (instancetype)initWithSetCode:(UPMarketSetCode)setCode code:(NSString *)code;

/**
 * 根据股票列表来构造请求,适用于同时请求多只股票行情的场景
 *
 * @param stockArray 股票列表
 */
- (instancetype)initWithStockArray:(NSArray <UPHqStockUnique *> *)stockArray;

/**
 * 根据Index来获取请求体中的股票市场码, 如获取第一只股的市场:[req getSetCode:0]
 * 
 * @param index 索引
 */
- (UPMarketSetCode)getSetCode:(int)index;

/**
 * 向请求体中追加股票
 *
 * @param setCode  市场码 如深圳市场 UPMarketSetCodeSZ
 * @param code 股票代码 如 万科A:'000002'
 */
- (void)add:(UPMarketSetCode)setCode code:(NSString *)code;

/**
 * 判断请求体中是否包含某只股票- 内部使用
 *
 * @param unique 股票数据
 */
- (BOOL)contains:(UPHqStockUnique *)unique;

/**
 * 内部使用
 */
- (void)setRemainStocks:(NSArray<UPHqStockUnique *> *)stocks;
/**
 * 内部使用
 */
- (void)removeRemainStock:(UPHqStockUnique *)unique;
/**
 * 内部使用
 */
- (BOOL)isRemainStock:(UPHqStockUnique *)unique;
/**
 * 内部使用
 */
- (BOOL)hasRemainStocks;
/**
 * 内部使用
 */
- (void)clearRemainStocks;

/**
 * 清空请求体中所有的股票
 */
- (void)clear;

/**
 * 获取请求体中的股票列表
 */
- (NSArray <UPHqStockUnique *> *)getStocksArray;

/**
 * 获取请求体中的第一只股票
 */
- (UPHqStockUnique *)getFirstStockUnique;

@property(nonatomic, assign) BOOL isForceHTTP;                 // 是否强制使用HTTP, 默认走长连接
@property(nonatomic, strong) NSString *forceHttpServantName;   // 强制使用HTTP所访问的servantName
@property(nonatomic, assign) UPMarketDataLevel dataLevel;      // 数据级别
@property(nonatomic, assign) BOOL simpleData;                  // 是否返回简版数据， 默认返回全量行情

@property(nonatomic, assign) UPMarketPushFlag pushFlag;        // Push标志
@property(nonatomic, assign) NSInteger wantNum;                // 请求个数
@property(nonatomic, assign) UPMarketSortColumn sortColumn;    // 排序列
@property(nonatomic, assign) UPMarketSortOrder sortOrder;      // 排序顺序
@property(nonatomic, assign) BOOL preReqBaseInfo;              // 提前请求静态数据-内部使用

/**上证云特有请求参数 - Start */
@property(nonatomic, strong) MStockItem *stockItem;            // 上证云的股票行情快照模型, 用于K线等的请求中可以加快请求速度
@property(nonatomic, copy) NSString *oldIndex;                 // 向前查询索引（更老）
@property(nonatomic, copy) NSString *startIndex;               // 向后查询索引（更新）
@property(nonatomic, assign) UPMarketQueryDir queryDirection;  // 查询方向 -1表示获取最新的
@property(nonatomic, assign) BOOL includeAfterHours;           // 是否包含科创板盘后数据
@property(nonatomic, assign) BOOL isForceUPStation;            // 是否强走优品主站
@property(nonatomic, assign) BOOL isForceSHY;                  // 是否强走上证云SDK
@property(nonatomic, assign) BOOL onlyTenQuote;                // 之请求十档数据

/**上证云特有请求参数 - End */

@property (nonatomic, assign) UPMarketSetCode relatedSetCode;  // 关联市场代码

/// 记录该请求触发位置及堆栈信息（内部调试使用）
@property (nonatomic, strong) NSArray *debugCallStack;
@end

/**
 * 回包结构基类
 */
@interface UPHqRsp : NSObject

@property(nonatomic, assign) int ret;     // 返回值

@property(nonatomic, assign)BOOL partError; //多链路请求是否有部分请求错误(内部使用)

@property(nonatomic, assign)BOOL isFromHttp; // 是否回包至HTTP,内部使用

/**
 * 通过错误码构建回包Rsp - 内部使用
 */
- (instancetype)initWithCode:(UPMarketErrorType)errorType;

@end

//MARK: ---------------------股票行情数据-----------------------

@interface UPMarketStockHqReq : UPHqReq

@end

@interface UPMarketStockHqRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqStockHq *> *dataArray;                    // 行情数据

@end



//MARK: ---------------------根据类型请求股票数据-----------------------

@interface UPMarketType2StockReq : UPHqReq

/// 板块类型
@property(nonatomic, assign) UPMarketBlockType type;
/// 请求位置
@property(nonatomic, assign) int startNo;
/// 过滤类型，不支持外盘。具体查看UPMarketRankFilterType枚举
@property(nonatomic, strong) NSArray<NSNumber* > *filterType;

@end

@interface UPMarketType2StockRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqStockHq *> *stockArray;                           // 返回列表
@property(nonatomic, assign) int totalNum;                                                 // 总个数

@end

//MARK: ---------------------根据板块类型请求股票数据-----------------------

@interface UPMarketBlock2StockReq : UPHqReq

/// 请求位置
@property(nonatomic, assign) int startNo;
/// 过滤类型，不支持外盘。具体查看UPMarketRankFilterType枚举
@property(nonatomic, strong) NSArray<NSNumber* > *filterType;

@end

@interface UPMarketBlock2StockRsp : UPMarketType2StockRsp

@end

//MARK: ---------------------根据板块类型请求股票数据-----------------------

//MARK: ---------------------根据股票查询板块-----------------------

@interface UPMarketStock2BlockReq : UPHqReq

@end

@interface UPMarketStock2BlockRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqStockHq *> *stockArray;

@end

@interface UPMarketTickDataReq : UPHqReq

@property(nonatomic) short startNo;                                                // 起始位置

@end

@interface UPMarketTickDataRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqTickData *> *tickDataArray;               // 明细数据
@property(nonatomic, copy) NSString *startIndex;
@property(nonatomic, copy) NSString *endIndex;

@end

//MARK: ---------------------股票逐笔数据-----------------------

@interface UPMarketTransReq : UPHqReq

@end

@interface UPMarketTransRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqTransData *> *transDataArray;
@property(nonatomic, copy) NSString *startIndex;
@property(nonatomic, copy) NSString *endIndex;

@end

//MARK: ---------------------股票当日分时数据-----------------------

@interface UPMarketMinDataReq : UPHqReq

@property(nonatomic, assign) int startNo;                                          // 请求起始位置
@property(nonatomic, assign) int date;                                             // 请求日期（格式：YYYYMMDD）
@property(nonatomic, assign) BOOL includeAuction;                                  // 是否包含集合竞价数据

@end

@interface UPMarketMinDataRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqRTMinData *> *minuteDataArray;            // UPHqRTMinData  分时数据

@end



//MARK: ---------------------股票K线数据-----------------------

@interface UPMarketKLineDataReq : UPHqReq

@property(nonatomic) UPMarketKlineType type;                                       // 请求K线数据类型
@property(nonatomic) short startNo;                                                // 起始位置
@property(nonatomic, assign) BOOL isXRXD;                                          // 是否复权（除权除息）默认YES DEPRECATED, 使用fqMode替换
@property(nonatomic, assign) UPMarketFQMode fqMode;                                // 复权模式，默认前复权
@property(nonatomic, assign) BOOL isKlineRspExt;                                   // 为true时，K线返回振幅和换手率

@end

@interface UPMarketKLineDataRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqAnalyData *> *kLineDataArray;             // K线数据

@end


//MARK: - 股票除权除息数据

@interface UPMarketQXDataReq : UPHqReq

@end

@interface UPMarketQXDataRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqQXData *> *qxDataArray;             // K线数据

@end


// MARK: - 实时资金流数据

@interface UPMarketCapitalFlowDataReq : UPHqReq

@property(nonatomic, assign) UPMarketMoneyType type;

@end

@interface UPMarketCapitalFlowDataRsp : UPHqRsp

@property(nonatomic, strong) UPHqCapitalFlowData *capitalFlowData;                 // 资金流数据

@end

// MARK: - 南向资金流数据

@interface UPMarketSouthMoneyFlowDataReq : UPHqReq


@end

@interface UPMarketSouthMoneyFlowDataRsp : UPHqRsp

@property (strong, nonatomic) UPHqSouthMoneyFlow *shFlow; //上海

@property (strong, nonatomic) UPHqSouthMoneyFlow *szFlow; //深圳

@end

// MARK: - 南北向资金

@interface UPMarketSNFundsDataReq : UPHqReq

@property (assign, nonatomic) BOOL isSouth; //yes:南向 no:北向

@end

@interface UPMarketSNFundsDataRsp : UPHqRsp

@property (strong, nonatomic) NSArray<UPMarketSNFundData *> *dataList;

@property (assign, nonatomic) int date;

@end

// MARK: - 南北向资金净流入

@interface UPMarketSNNetDataReq : UPHqReq

@property (assign, nonatomic) BOOL isSouth; //yes:南向 no:北向

@property (assign, nonatomic) BOOL isNetBuy; //yes:净买入 no:净流入

@property (assign, nonatomic) int startPos; //起始位置


@end

@interface UPMarketSNNetDataRsp : UPHqRsp

@property (strong, nonatomic) NSArray<UPMarketSNNetData *> *dataList;

@end

// MARK: - 南北向资金历史净流入

@interface UPMarketHisSNNetDataReq : UPHqReq

@property (assign, nonatomic) BOOL isSouth; //yes:南向 no:北向

@property (assign, nonatomic) BOOL isNetBuy; //yes:净买入 no:净流入

@property(nonatomic) UPMarketKlineType type; // 请求K线数据类型


@end

@interface UPMarketHisSNNetDataRsp : UPHqRsp

@property (strong, nonatomic) NSArray<UPMarketSNNetData *> *dataList;

@end

// MARK: - 请求休市数据

@interface UPMarketCloseInfoReq : UPHqReq


@end

@interface UPMarketCloseInfoRsp : UPHqRsp


@property (nonatomic, assign) int date;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSString*>* marketCloseTips;
@end

//MARK: --------------------- 获取委托队列数据-----------------------

@interface UPMarketOrderQueueReq : UPHqReq


@end

@interface UPMarketOrderQueueRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqOrderQueueData *> *orderDataArray;        // 委托队列数据

@end

//MARK: --------------------- 获取股票价格委托数据-----------------------

@interface UPMarketPriceOrderReq : UPHqReq

@end

@interface UPMarketPriceOrderRsp : UPHqRsp

@property(nonatomic, strong) UPHqPriceOrderData *priceOrderData;                  // 价格委托数据

@end


// MARK: --------------------- 获取Level2拖拉机单，顶级挂单，主力撤单数据-----------------------

@interface UPMarketLevel2PoolReq : UPHqReq

@property(nonatomic, assign) UPMarketLevel2PoolType type;

@end

@interface UPMarketLevel2PoolRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketLevel2PoolInfo *> *level2Array;      // level2 数据

@end

// MARK: --------------------- 获取股票DDE系列数据-----------------------

@interface UPMarketDDEReq : UPHqReq

@property(nonatomic) short startNo;                                                // 起始位置
@property(nonatomic, assign) long date;                                         // 起始日期
@property(nonatomic, assign) UPMarketDDEType type;                                 // DDE请求类型
@property(nonatomic, assign) BOOL bincludeDate;                         //返回数据是否包含IDate

@end

@interface UPMarketDDERsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketDDEInfo *> *ddeArray;                 // dde 数据

@end

// MARK: --------------------- 获取深沪市场的资金流排名数据-----------------------

@interface UPMarketFlowRankReq : UPHqReq

@property(nonatomic, assign) UPMarketBlockType blockType;                          // 板块类型
@property(nonatomic) short startNo;                                                // 起始位置

@end

@interface UPMarketFlowRankRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketFlowRankData *> *rankArray;
@property(nonatomic, assign) NSInteger totalSize;

@end

// MARK: - 获取股票指标数据

@interface UPMarketIndexReq : UPHqReq

@property(nonatomic) short startNo;                                                // 起始位置
@property(nonatomic, assign) BOOL isXRXD;                                          // 是否复权（除权除息）默认YES DEPRECATED, 使用fqMode替换
@property(nonatomic, assign) UPMarketFQMode fqMode;                                // 复权模式，默认前复权
@property(nonatomic, assign) UPMarketIndexType type;                               // 指标类型
@property(nonatomic, assign) int orignIndexType;                                   // 后台指标类型(直接透传后台,无需进行转换)
@property(nonatomic, assign) int periodType;                                       // 周期类型 {@link UPMarketKlineType | UPMarketMinuteType}

@end

@interface UPMarketIndexRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketIndexInfo *> *indexArray;            // 指标数据

@end

// MARK: - 指标对应股票列表

@interface UPMarketIndexStockListReq : UPHqReq

@property(nonatomic, assign) UPMarketIndexType indexType;                           // 指标类型
@property(nonatomic, strong) NSArray *indexTypes;                                   // 多个指标类型
@property(nonatomic, assign) UPMarketIndexSubType indexSubType;                     // 指标子类型
@property(nonatomic, strong) NSDictionary *queryCondition;                          // 查询条件
@property(nonatomic, assign) int startDate;                                         // 起始日期
@property(nonatomic, assign) int endDate;                                           // 结束日期
@property(nonatomic, assign) int tradeDays;                                         // 请求交易日数量
@property(nonatomic, assign) int period;

@end

@interface UPMarketIndexStockListRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketIndexStockData *> *indexArray;        // 指标股票数据
@property(nonatomic, strong) NSDictionary <NSNumber *, NSArray <UPMarketIndexStockData *> *> *indexArrayDic;        // 多个指标股票数据

@end

// MARK: - 获取市场状态数据

@interface UPMarketStatusReq : UPHqReq

@end

@interface UPMarketStatusRsp : UPHqRsp

@property(nonatomic, strong) UPMarketStatusData *marketStatusData;            // 市场状态数据

@end

// MARK: - 请求港股经纪商数据

@interface UPMarketBrokerQueueReq : UPHqReq

@end

@interface UPMarketBrokerQueueRsp : UPHqRsp

@property(nonatomic, strong) UPMarketBrokerQueueData *brokerQueueData;         // 港股经纪数据（L2）

@end

//MARK: --------------------- 获取AH股票数据-----------------------

@interface UPMarketAHStockReq : UPHqReq

@property(nonatomic) short startNo;                                                // 起始位置
@property (assign, nonatomic) UPMarketAMType AMType; // 请求类型
@property (assign, nonatomic) UPMarketPremiumType premiumType; // 溢价率类型

@end

@interface UPMarketAHStockRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketAHStockData *> *ahDataArray;

@end

//MARK: --------------------- 请求对应的AH股票-----------------------

@interface UPMarketRelateAHStockReq : UPHqReq

@property (assign, nonatomic) UPMarketAMType AMType; // 请求类型
@property (assign, nonatomic) UPMarketPremiumType premiumType; // 溢价率类型

@end

@interface UPMarketRelateAHStockRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketAHStockData *> *ahDataArray;

@end

//MARK: -------------------- 请求对应的AH股票(新) --------------------

@interface UPMarketRelateAHStockBatchReq : UPHqReq

@property (assign, nonatomic) UPMarketAMType AMType; // 请求类型
@property (assign, nonatomic) UPMarketPremiumType premiumType; // 溢价率类型


@end

@interface UPMarketRelateAHStockBatchRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketRelatedAHStockBatchData *> *dataArray;

@end


// MARK: --------------------- 自选行情请求-----------------------
@interface UPMarketOptStockHqReq : UPHqReq

@property(nonatomic, assign) BOOL isHKDelay;        // 请求港股延时行情
@property(nonatomic, assign) BOOL rtMinInOpt;       // 是否需要返回分时当日分时数据
@property(nonatomic, assign) BOOL includeAuction;   // 分时是否需要集合竞价时段
@property(nonatomic, assign) int minStartNo;        // 分时起始位置
@property(nonatomic, assign) int rtMinDataType;     // 自选分时字段控制 0:都返回,1:只返回现价,2:只返回均价,3:都不返回

@end


@interface UPMarketOptStockHqRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqStockHq *> *dataArray;                    // 行情数据

@end

// MARK: --------------------- 港股权证数据请求-----------------------
@interface UPMarketHKWarrantReq : UPHqReq

@property(nonatomic, assign) UPMarketHKWarrantType warrantType;                     // 港股权证类型

@property(nonatomic, assign) int startNo;                                          // 请求起始位置


@end


@interface UPMarketHKWarrantRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqStockHq *> *dataArray;                    // 行情数据
@property (assign, nonatomic) NSInteger totalNum;
@end

// MARK: - --------------------- 股票或板块资金排名净流入和净流出的TOP数据请求-----------------------
@interface UPMarketMoneyRankTopDataReq : UPHqReq

@property(nonatomic, assign) UPMarketBlockType type;                               // 请求种类

@end

@interface UPMarketMoneyRankTopDataRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketMoneyRankTopData *> *dataArray;

@end

// MARK: --------------------- 港股权证对应的港股数据请求-----------------------
@interface UPMarketWarrantRelatedStockReq : UPHqReq

@end


@interface UPMarketWarrantRelatedStockRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqStockHq *> *dataArray;                    // 行情数据

@end


// MARK: --------------------- 股票因子库数据请求-----------------------

@interface UPMarketStockFactorDataReq : UPHqReq

@property(nonatomic, assign) UPMarketStockFactorType type;          // 因子类型
@property(nonatomic, assign) int startDate;                         // 起始日期
@property(nonatomic, assign) int endDate;                           // 结束日期

@end

/// 批量股票因子库数据请求
@interface UPMarketStockFactorBatchDataReq : UPHqReq

@property(nonatomic, strong) NSArray<UPHqStock *> *stk;       //股票
@property(nonatomic, assign) UPMarketStockFactorType type;          // 因子类型
@property(nonatomic, assign) int startDate;                         // 起始日期
@property(nonatomic, assign) int endDate;                           // 结束日期

@end

@interface UPMarketStockFactorDataRsp : UPHqRsp

@property(nonatomic, strong) UPMarketStockFactorData *factorData;       // 股票因子数据

@end

// MARK: --------------------- 股票静态数据请求 -----------------------

// MARK: - UPHqStockBaseInfo
@interface UPMarketStockBaseInfoReq : UPHqReq

@end

@interface UPMarketStockBaseInfoRsp : UPHqRsp

@property (nonatomic, strong) NSArray<UPMarketStaticModel *> *baseInfoArray;          // UPMarketStaticCacheModel

@end

// MARK: --------------------- 股池统计数据请求 -----------------------
@interface UPMarketStockPoolStatReq : UPHqReq

@property(nonatomic, assign) UPMarketIndexType type;                         // 股池类型
@property(nonatomic, assign) int startDate;                                   // 起始日期
@property(nonatomic, assign) int endDate;                                     // 结束日期

@end

@interface UPMarketStockPoolStatRsp : UPHqRsp

@property (nonatomic, strong) UPMarketStockPoolStatData *statData;          // 股池统计数据

@end

//MARK: --------------------- 获取题材异动数据-----------------------

@interface UPMarketSubjectChangeListReq : UPHqReq

@property(nonatomic, assign) NSInteger date;                                     // 日期：YYYYMMDD
@property(nonatomic, assign) UPMarketSubjectChangeType changeType;               // 异动类型
@property(nonatomic, assign) NSInteger leadStockNum;                             // 板块领涨股个数
@property(nonatomic, assign) NSInteger startNum;                                 // 起始位置

@end

@interface UPMarketSubjectChangeListRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketSubjectChangeListData *> *changeListArray;   // 异动列表数据

@end

@interface UPMarketLeadSubjectReq : UPHqReq

@property(nonatomic, assign) NSInteger date;                                      // //日期：YYYYMMDD

@end

@interface UPMarketLeadSubjectRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketLeadSubjectData *> *changeListArray;

@end

//MARK: --------------------- 请求DDE排名数据 -----------------------
@interface UPMarketDDERankDataReq : UPHqReq

@property(nonatomic, assign) UPMarketBlockType type;                               // 请求种类
@property(nonatomic, assign) int startNo;                                          // 请求位置

@end

@interface UPMarketDDERankDataRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPMarketDDERankData *> * ddeRankList;      // DDE 排名列表

@end

//MARK: --------------------- 请求个股指标数据(Push) -----------------------
@interface UPMarketStockIndexReq : UPHqReq

/// 请求种类
@property(nonatomic, assign) UPMarketIndexType type;
/// 后台指标类型(直接透传后台,无需进行转换)
@property(nonatomic, assign) int orignIndexType;
/// 开始日期
@property(nonatomic, assign) int startDate;
/// 结束日期
@property(nonatomic, assign) int endDate;
/// 周期类型
@property(nonatomic, assign) UPMarketKlineType periodType;
/// 指标公式支持参数可配置，这种类型的公式历史数据没有落RockDB，都是实时计算的
@property (nonatomic, strong) NSDictionary<NSString*, NSNumber*>* mapParams;
/// 是否全部实时计算  IndexDataServer -> CpuCalcServer
@property (nonatomic, assign) BOOL realTimeCalcAll;
@end

@interface UPMarketStockIndexRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketIndexInfo *> *indexArray;            // 指标数据

@property(nonatomic, strong) NSArray <UPMarketIndexInfo *> *minArray;               // 分时数据

@end

//MARK: --------------------- 请求指标股池数据(Push) -----------------------
@interface UPMarketIndexStocksReq : UPHqReq

@property(nonatomic, assign) UPMarketIndexType type;                               // 请求种类
@property(nonatomic, assign) int orignIndexType;                                   // 后台指标类型(直接透传后台,无需进行转换)
@property(nonatomic, assign) int date;                                             // 指定日期

@end

@interface UPMarketIndexStocksRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketIndexStockData *> *indexStockArray;        // 指标股票数据

@end

@interface UPMarketRegStockListRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqStockUnique *> *stockArray;
@property(nonatomic, assign) int date;
@property(nonatomic, assign) int type;

@end

//MARK: --------------------- 请求股票价量分布数据 -----------------------
@interface UPMarketPriceVolReq : UPHqReq

@end

@interface UPMarketPriceVolRsp : UPHqRsp

@property(nonatomic, strong) UPMarketPriceVolData *priceVolData;        // 价量分布数据

@end

//MARK: --------------------- 请求股票筹码分布数据 -----------------------
@interface UPMarketChipReq : UPHqReq

@property(nonatomic, assign) int date;                                             // 指定日期

@property(nonatomic, assign) BOOL simpleChip;                                      // 简易筹码(不包含盈利套牢筹码、百分比集中度)

@end

@interface UPMarketChipRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketChipData *>* chipDataArray;          // 筹码分布数据

@end

//MARK: --------------------- 请求期权基础信息 -----------------------
@interface UPMarketOptionBaseInfoReq : UPHqReq

@end

@interface UPMarketOptionBaseInfoRsp : UPHqRsp

@end

//MARK: --------------------- 请求期权列表 -----------------------
@interface UPMarketOptionListReq : UPHqReq

@property(nonatomic, copy) NSString *optionContractType;                      // 合约类型

@end

@interface UPMarketOptionListRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPHqStockHq *>* optionArray;               // 期权列表

@property(nonatomic, strong) NSArray <UPMarketOptionTData *>* optionTArray;      // T型报价列表

@end


//MARK: --------------------- 请求期权标的列表数据 -----------------------
@interface UPMarketOptionUnderlyingListReq : UPHqReq

@end

@interface UPMarketOptionUnderlyingListRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPHqStockHq *>* dataList;          // 期权标的列表

@end

//MARK: --------------------- 请求新三板要约股票列表数据 -----------------------
@interface UPMarketYYStockListReq : UPHqReq

@end

@interface UPMarketXSBYYStockListRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketXSBYYStockData *>* dataList;          // 新三板要约股票列表

@end

//MARK: --------------------- 请求新三板发行股票列表数据 -----------------------
@interface UPMarketFXStockListReq : UPHqReq

@property(nonatomic, assign) UPMarketFXType type;                               // 请求种类

@end

@interface UPMarketXSBFXStockListRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketXSBFXStockData *>* dataList;          // 新三板发行股票列表

@end


//MARK: --------------------- 获取最新的个股异动数据 -----------------------
@interface UPMarketLatestStockChangeReq : UPHqReq

@property(nonatomic, assign) UPMarketStockChangeType type;                          // 请求异动类型

@end

@interface UPMarketLatestStockChangeRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketStockChangeData *>* dataList;         // 异动股票列表

@end

//MARK:----------------- 异动(百度新接口) -------------------
@interface UPMarketAnomalyBroadcastReq : UPHqReq

/// 新高和均线策略（个股异动类型）
@property(nonatomic, assign)UPMarketStockChangeType anomalyType;
/// 新高类型集合（例：60日新高 填 60，200日新高填200）
@property(nonatomic, strong)NSArray<NSNumber*>* typeList;

@end

@interface UPMarketAnomalyBroadcastRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketStockChangeData *>* dataList;         // 异动股票列表

@end

//MARK: --------------------- 短线精灵 -----------------------
@interface UPMarketDXJLReq : UPHqReq

@property(nonatomic, strong) NSArray <NSNumber *> *dxjlTypes;                       // 短线精灵类型

@end

@interface UPMarketDXJLRsp : UPHqRsp

@property(nonatomic, strong) NSArray <UPMarketDXJLStockData *>* dataList;         // 异动股票列表

@end

//MARK: --------------------- 集合竞价行情快照请求 -----------------------
@interface UPMarketStockAuctionReq : UPHqReq

@end

@interface UPMarketStockAuctionRsp : UPHqRsp

@property(nonatomic, strong) UPMarketAuctionData *auctionData;

@end

@interface UPMarketStockAuctionV2Rsp : UPHqRsp

@property(nonatomic, strong) UPMarketAuctionData *auctionData;

@end

//MARK: --------------------- 上证云可转债请求 -----------------------
@interface UPMarketKZZDataReq : UPHqReq

@end

@interface UPMarketKZZDataRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPHqStockHq *> *dataArray;                  // 可转债数据

@end

// MARK: - ------------- 外汇信息 -------------

@interface UPMarketCurrencyInfoReq : UPHqReq

@end

@interface UPMarketCurrencyInfoRsp : UPHqRsp

@property (strong, nonatomic) NSArray<UPCurrencyInfo *> *currencyInfos;

@end

//MARK: --------------------- 码表搜索参数请求 -----------------------
@interface UPMarketQueryParam : NSObject

/// 输入内容
@property(nonatomic, copy) NSString *input;
/// 是否只搜索代码
@property(nonatomic, assign) BOOL onlyCode;
/// 自定义条件
@property(nonatomic, copy) NSString *customWhere;
/// 当搜索到曾用名和现用名都存在时,是否过滤掉曾用名,默认true
@property(nonatomic, assign) BOOL filterOldName;
/// 最大搜索数量, 默认50条
@property(nonatomic, assign) int maxMatchCount;
/// 搜索时是否优先展示长名称,默认为false
@property(nonatomic, assign) BOOL showLongName;
/// 通过SetCode过滤特定种类的长名称展示, {@link #showLongName}为true时生效
@property(nonatomic, strong) NSSet* longNameFilterBySetCode;
/// 通过origCategory过滤特定种类的长名称展示, {@link #showLongName}为true时生效,过滤优先级小于longNameFilterBySetCode
@property(nonatomic, strong) NSSet* longNameFilterByOrigCategory;
/// 通过category展示特定种类的长名称展示, {@link #showLongName}为false时生效
@property(nonatomic, strong) NSSet* longNameShownByCategory;
/// 通过origCategory展示特定种类的长名称展示, {@link #showLongName}为false时生效
@property(nonatomic, strong) NSSet* longNameShownByOrigCategory;


-(instancetype)initWithInput:(NSString *)input;

-(instancetype)initWithInput:(NSString *)input
                    onlyCode:(BOOL)onlyCode
                 customWhere:(NSString *)customWhere
               filterOldName:(BOOL)filterOldName
               maxMatchCount:(int)maxMatchCount;

-(instancetype)initWithInput:(NSString *)input
                    onlyCode:(BOOL)onlyCode
                 customWhere:(NSString *)customWhere
               filterOldName:(BOOL)filterOldName
               maxMatchCount:(int)maxMatchCount
                showLongName:(BOOL)showLongName
     longNameFilterBySetCode:(NSSet *)longNameFilterBySetCode
longNameFilterByOrigCategory:(NSSet *)longNameFilterByOrigCategory
     longNameShownByCategory:(NSSet *)longNameShownByCategory
 longNameShownByOrigCategory:(NSSet *)longNameShownByOrigCategory;

@end


//MARK: --------------------- 键盘精灵请求 -----------------------

@interface UPMarketFilter : NSObject
@property (nonatomic, assign) UPMarketSetCode setCode;                      // 市场码
@property (nonatomic, copy) NSArray<NSNumber *> *origCategories;            // 市场中支持的类型,为空则获取该市场下所有类型
@end

@interface UPMarketCategoryFilter : NSObject
@property (nonatomic, assign) int origCategory;                             // 原始类型码,对应E_STOCK_CATEGORY
@property (nonatomic, copy) NSArray<NSNumber *> *origSubCategories;         // 原始子类型, 为空表示获取所有
@end

@interface UPMarketJPJLStockReq : UPHqReq
@property(nonatomic, copy) NSString *input;                                         //用户输入-用户云端股票查询
@property(nonatomic, copy) NSArray<UPMarketFilter *> *marketFilters;                //搜索过滤器-By市场码和origCategory
@property(nonatomic, copy) NSArray<UPMarketCategoryFilter *> *categoryFilters;      //搜索过滤器-By origCategory和origSubCategory
@end

@interface UPMarketJPJLStockRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPMarketCodeMatchInfo *> *dataArray;       // 键盘精灵码表搜索结果

/// 是否使用本地搜索
@property(nonatomic, assign) BOOL isUseLocalDict;

@end


// MARK: - ------------- 涨跌分布请求(主站) -------------

@interface UPMarketZDFBInfoReq : UPHqReq

@property(nonatomic, copy) NSArray* zdfbTypes;              // 涨跌分布类型,传入类型:UPMarketZDFBType
@property(nonatomic, assign) int zdfbWidth;                 // 涨跌分布-区间宽度,如2%~4%宽度为2

@end

@interface UPMarketZDFBInfoRsp : UPHqRsp

@property(nonatomic, copy) NSDictionary <NSNumber *, UPMarketZDFBData*>* zdfbDic;

@end


// MARK: - ------------- L2拖拉机单/顶级挂单/主力撤单请求 -------------

@interface UPMarketL2SzfyComReq : UPHqReq

@property(nonatomic) int startNo;                      // 起始位置
@property(nonatomic, assign) int type;                 // 类型具体看HQSys_E_COM_ORDER_TYPE

@end

@interface UPMarketL2SzfyComRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPMarketHSZFYCom*>* monit;
@property(nonatomic, assign) int totalSize;

@end

//MARK: --------------------- 请求区间统计(懂牛需求) -----------------------
@interface UPMarketRangeStatsByDateReq : UPHqReq

@property(nonatomic, assign) UPMarketRangeType rangeType;
@property(nonatomic, assign) UPMarketKlineType lineType;
@property(nonatomic, assign) int startxh;
@property(nonatomic, assign) int startDate;
@property(nonatomic, assign) int endDate;
@property(nonatomic, assign) UPMarketQXModelType qxMode;

@end

@interface UPMarketRangeStatsByDateRsp : UPHqRsp

@property(nonatomic, strong) NSArray<UPMarketRangeStats*>* rangeStats;

@end

