//
// Created by Nic on 2017/10/10.
// Copyright (c) 2017 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, UPMarketArchiveType) {
    UPMarketArchiveTypeGZIP,
    UPMarketArchiveTypeLZMA,
    UPMarketArchiveTypeLZ4
};

/*
 * 压缩Util
 * */
@interface UPMarketArchiveUtil : NSObject

+ (NSData *)decodeData:(NSData *)data type:(UPMarketArchiveType)type;

+ (NSData *)encodeData:(NSData *)data type:(UPMarketArchiveType)type;

+ (BOOL)decodeFile:(NSString *)srcPath toPath:(NSString *)destPath type:(UPMarketArchiveType)type;

+ (BOOL)encodeFile:(NSString *)srcPath toPath:(NSString *)destPath type:(UPMarketArchiveType)type;

@end
