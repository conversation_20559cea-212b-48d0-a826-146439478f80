//
// Created by j<PERSON><PERSON> on 2019/11/5.
// Copyright (c) 2019 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

@class UPMarketAddressModel;

typedef NSInteger UPMarketAddressTestStatus;

NS_ENUM(UPMarketAddressTestStatus) {
    UPMarketAddressTesting = -2,
    UPMarketAddressNoAddress = -1,
    UPMarketAddressStartSucc = 0
};


@protocol UPMarketAddressTestDelegate <NSObject>

@optional
- (void)onTestDone:(NSArray<UPMarketAddressModel *> *) addressList;

- (void)onAddressTest:(UPMarketAddressModel *) address;

@end

@interface UPMarketAddressTester : NSObject

@property(nonatomic, weak) id<UPMarketAddressTestDelegate> delegate;

@property(nonatomic, assign) BOOL needReconnect;

- (UPMarketAddressTestStatus)start;

@end
