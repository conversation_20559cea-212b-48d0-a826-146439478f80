//
//  UPMarketLogUtil.h
//  UPMarketSDK
//
//  Created by <PERSON><PERSON> on 2017/5/26.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketDefine.h"

@class UPMarketRequest;

/*
 日志工具
 */
@interface UPMarketLogUtil : NSObject

@property (nonatomic, assign) UPMarketLogLevel logLevel;

+ (instancetype)sharedInstance;

+ (void)logMessage:(NSString *)message request:(NSObject *)req;

/*
 根据UPMarketLogLevel 输出log，保存到文件

 @param message log信息
 */
+ (void)logMessage:(NSString *)message;


/**
 展示行情打印信息在屏幕上
 */
+ (void)logMessageWithReqOnScreen:(UPMarketRequest *)req isSend:(BOOL)send;

+ (void)switchLogInfoWindow;

@end
