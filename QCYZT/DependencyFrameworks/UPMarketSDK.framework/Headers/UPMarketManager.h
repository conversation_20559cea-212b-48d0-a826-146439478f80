//
//  UPMarketManager.h
//  UPMarketSDK
//
//  Created by <PERSON><PERSON> on 2017/5/22.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketModel.h"
#import "UPMarketReqAndRsp.h"
#import "UPMarketDefine.h"
#import "UPMarketAddressModel.h"

@class UPMarketStockBaseInfoReq;

@interface UPMarketManager : NSObject

/**
 设置日志输出类型

 @param logLevel UPMarketLogLevel
 */
+ (void)setLogLevel:(UPMarketLogLevel)logLevel;

/**
 是否L2行情
 */
+ (BOOL)isL2Hq;

/**
 用户登录
 
 @param uid 用户id
 @param rd 跟token里面的rd对应，主要用于加密uid
 @param token 用户token
 @param isL2 是否L2用户
 */
+ (void)userLoginWithUid:(NSString *)uid rd:(NSString *)rd token:(NSString *)token isL2:(BOOL)isL2 DEPRECATED_MSG_ATTRIBUTE("use [UPMarketManager userLoginWithUid:rd:token:l2Type:] instead");

/**
 用户登录

 @param uid 用户id
 @param rd 跟token里面的rd对应，主要用于加密uid
 @param token 用户token
 @param l2Type L2 类型
 */
+ (void)userLoginWithUid:(NSString *)uid rd:(NSString *)rd token:(NSString *)token l2Type:(UPMarketL2Type)l2Type;

/**
 用户登录

 @param uid 用户id
 @param rd 跟token里面的rd对应，主要用于加密uid
 @param token 用户token
 @param oemToken 三方token
 @param l2Type L2 类型
 */
+ (void)userLoginWithUid:(NSString *)uid rd:(NSString *)rd token:(NSString *)token oemToken:(NSString *)oemToken l2Type:(UPMarketL2Type)l2Type;

/**
 用户登出
 */
+ (void)userLogout DEPRECATED_MSG_ATTRIBUTE("use [UPMarketManager userLogout:reason:] instead");;

/**
用户登出
 @param l2Type - L2类型
*/
+ (void)userLogout:(UPMarketL2Type)l2Type;

/**
用户登出
 @param l2Type - L2类型
 @param reason - 切换原因
*/
+ (void)userLogout:(UPMarketL2Type)l2Type reason:(UPMarketL2SwitchReason)reason;
/**
 * 请求股票行情数据

 * @param req     - UPMarketStockHqReq
 * @param handler - (UPMarketStockHqRsp、NSError)
 */
+ (void)requestStockHq:(UPMarketStockHqReq *)req completionHandler:(upMarketStockHqCompletionHandler)handler;

/**
 根据类型请求股票
 
 @param req  - UPMarketType2StockReq
 @param handler - (UPMarketType2StockRsp，NSError)
 */
+ (void)requestStockByType:(UPMarketType2StockReq *)req completionHandler:(upMarketStockTypeCompletionHandler)handler;

/**
 * 根据股票查询板块
 * @param req - UPMarketStock2BlockReq
 * @param handler - (UPMarketStock2BlockRsp, NSError)
 * */
+ (void)requestBlockByStock:(UPMarketStock2BlockReq *)req completionHandler:(upMarketBlockStockCompletionHandler)handler;

/**
 根据板块请求股票
 
 @param req UPMarketBlock2StockReq
 @param handler (UPMarketBlock2StockRsp，NSError)
 */
+ (void)requestStockByBlock:(UPMarketBlock2StockReq *)req completionHandler:(upMarketStockBlockCompletionHandler)handler;

/**
 请求股票明细数据
 
 @param req  - UPMarketTickDataReq
 @param handler (UPMarketTickDataRsp,NSError)
 */
+ (void)requestStockTickData:(UPMarketTickDataReq *)req completionHandler:(upMarketStockTickCompletionHandler)handler;

/**
 请求股票逐笔数据
 
 @param req UPMarketTransReq
 @param handler (UPMarketTransRsp, NSError)
 */
+ (void)requestStockTransData:(UPMarketTransReq *)req completionHanlder:(upMarketStockTransCompletionHandler)handler;

/**
 * 请求股票当日分时数据
 *
 * @param req - UPMarketMinDataReq
 * @param handler - (UPMarketMinDataRsp、NSError)
 */
+ (void)requestStockMinuteData:(UPMarketMinDataReq *)req completionHandler:(upMarketStockMinuteCompletionHandler)handler;

/**
 * 请求股票K线数据
 *
 * @param req - UPMarketKLineDataReq
 * @param handler - (UPMarketKLineDataRsp、NSError)
 */
+ (void)requestStockKLineData:(UPMarketKLineDataReq *)req completionHandler:(upMarketStockKlineCompletionHandler)handler;

/**
 * 请求股票除权除息数据
 *
 * @param req - UPMarketQXDataReq
 * @param handler - (UPMarketQXDataRsp、NSError)
 */
+ (void)requestStockQXData:(UPMarketQXDataReq *)req completionHandler:(upMarketStockQXCompletionHandler)handler;

/**
 请求股票实时资金流数据
 
 @param req UPMarketCapitalFlowDataReq
 @param handler (UPMarketCapitalFlowDataRsp,NSError)
 */
+ (void)requestStockCapitalFlowData:(UPMarketCapitalFlowDataReq *)req completionHandler:(upMarketCapitalFlowCompletionHandler)handler;

/**
 请求南向资金流向数据
 
 @param req UPMarketSouthMoneyFlowDataReq
 @param handler (UPMarketSouthMoneyFlowDataRsp,NSError)
 */
+ (void)requestSouthMoneyFlowData:(UPMarketSouthMoneyFlowDataReq *)req completionHandler:(upMarketSouthMoneyFlowCompletionHandler)handler;

// 南北向资金
+ (void)requestSNFundsData:(UPMarketSNFundsDataReq *)req completionHandler:(upMarketSNFundsDataCompletionHandler)handler;

// 南北向资金净流入
+ (void)requestSNNetData:(UPMarketSNNetDataReq *)req completionHandler:(upMarketSNNetDataCompletionHandler)handler;

// 南北向资金净流入
+ (void)requestHisSNNetData:(UPMarketHisSNNetDataReq *)req completionHandler:(upMarketHisSNNetDataCompletionHandler)handler;

/**
 请求休市信息
 
 @param req UPMarketCloseInfoReq
 @param handler (UPMarketCloseInfoRsp,NSError)
 */
+ (void)requestMarketCloseInfo:(UPMarketCloseInfoReq *)req completionHandler:(upMarketCloseInfoCompletionHandler)handler;

/**
 请求股票委托队列数据
 
 @param req UPMarketOrderQueueReq
 @param handler (UPMarketOrderQueueRsp, NSError)
 */
+ (void)requestStockOrderQueueData:(UPMarketOrderQueueReq *)req completionHandler:(upMarketOrderQueueCompletionHandler)handler;

/**
 请求股票价格委托数据
 
 @param req UPMarketPriceOrderReq
 @param handler (UPMarketPriceOrderRsp, NSError)
 */
+ (void)requestStockPriceOrderData:(UPMarketPriceOrderReq *)req completionHandler:(upMarketPriceOrderCompletionHandler)handler;

/**
 请求股票L2价量分布数据

 @param req UPMarketPriceVolReq
 @param handler (UPMarketPriceVolRsp, NSError)
 */
+ (void)requestStockPriceVolData:(UPMarketPriceVolReq *)req completionHandler:(upMarketPriceVolCompletionHandler)handler;

/**
 请求股票筹码分布数据

 @param req UPMarketChipReq
 @param handler (UPMarketChipRsp, NSError)
 */
+ (void)requestStockChipDistributionData:(UPMarketChipReq *)req completionHandler:(upMarketChipCompletionHandler)handler;

/**
 * 请求股票DDE系列数据
 * @param req UPMarketDDEReq
 * @param handler (UPMarketDDERsp,NSError)
 * */
+ (void)requestStockDDE:(UPMarketDDEReq *)req completionHandler:(upMarketDDECompletionHandler)handler;

/**
 * 请求股票指标数据
 * @param req UPMarketIndexReq
 * @param handler (UPMarketIndexRsp,NSError)
 */
+ (void)requestStockIndex:(UPMarketIndexReq *)req completionHandler:(upMarketIndexCompletionHandler)handler;

/**
 * 请求指标对应的股票
 * @param req UPMarketIndexStockListReq
 * @param handler (UPMarketIndexStockListRsp,NSError)
 */
+ (void)requestIndexStockList:(UPMarketIndexStockListReq *)req completionHandler:(upMarketIndexStockListCompletionHandler)handler;

/**
 * 请求Level2选股池数据 (拖拉机单，顶级挂单，主力撤单)
 * @param req UPMarketLevel2PoolReq
 * @param handler (UPMarketLevel2PoolRsp, NSError)
 * */
+ (void)requestLevel2Pool:(UPMarketLevel2PoolReq *)req completionHandler:(upMarketLevel2CompletionHandler)handler;

/**
 * 请求资金排名数据
 * @param req UPMarketFlowRankReq
 * @param handler (UPMarketFlowRankRsp,NSError)
 */
+ (void)requestFlowRank:(UPMarketFlowRankReq *)req completionHandler:(upMarketFlowRankCompletionHandler)handler;

/**
 * 请求市场状态数据
 * @param req UPMarketStatusReq
 * @param handler (UPMarketStatusRsp,NSError)
 */
+ (void)requestMarketStatus:(UPMarketStatusReq *)req completionHandler:(upMarketStatusCompletionHandler)handler;

/**
 * 请求港股经纪数据 (L2)
 * @param req UPMarketBrokerQueueReq
 * @param handler (UPMarketBrokerQueueRsp,NSError)
 */
+ (void)requestStockBrokerQueueData:(UPMarketBrokerQueueReq *)req completionHandler:(upMarketBrokerQueueCompletionHandler)handler;

/**
 * 请求AH股列表
 * @param req UPMarketAHStockReq
 * @param handler (UPMarketAHStockRsp, NSError)
 * */
+ (void)requestAHStockList:(UPMarketAHStockReq *)req completionHandler:(upMarketAHStockCompletionHandler)handler;

/**
 * 请求关联股票列表(通用接口)
 * 该接口和requestAHStockList是同一个接口,新增该接口的目的是为了兼容旧接口
 * @param req UPMarketAHStockReq
 * @param handler (UPMarketAHStockRsp, NSError)
 * */
+ (void)requestRelatedStockList:(UPMarketAHStockReq *)req completionHandler:(upMarketAHStockCompletionHandler)handler;

/**
 * 请求对应的AH股票
 * @param req UPMarketRelateAHStockReq 必填参数：
 *              setCode -- 市场码
 *              code -- 股票码
 */
+ (void)requestRelatedAHStock:(UPMarketRelateAHStockReq *)req completionHandler:(upMarketRelateAHStockCompletionHandler)handler;

/**
 * 请求对应的AH股票对应的关联股票
 * 新增该接口的目的是为了兼容旧接口,该接口支持传ALL
 * @param req UPMarketRelateAHStockReq 必填参数：
 *              setCode -- 市场码
 *              code -- 股票码
 */
+ (void)requestRelatedAHStockBatch:(UPMarketRelateAHStockBatchReq *)req completionHandler:(upMarketRelateAHStockBatchCompletionHandler)handler;

/**
 * 请求自选股票行情数据

 * @param req     - UPMarketOptStockHqReq
 * @param handler - (UPMarketOptStockHqRsp、NSError)
 */
+ (void)requestOptStockHq:(UPMarketOptStockHqReq *)req completionHandler:(upMarketOptStockHqCompletionHandler)handler;

/**
 * 请求港股权证数据

 * @param req     - UPMarketHKWarrantReq
 * @param handler - (UPMarketHKWarrantRsp、NSError)
 */
+ (void)requestHKWarrant:(UPMarketHKWarrantReq *)req completionHandler:(upMarketHKWarrantCompletionHandler)handler;


/**
 * 请求港股权证所属的港股数据
 
 * @param req     - UPMarketWarrantRelatedStockReq
 * @param handler - (UPMarketWarrantRelatedStockRsp、NSError)
 */
+ (void)requestWarrantRelatedHKStock:(UPMarketWarrantRelatedStockReq *)req completionHandler:(upMarketWarrantRelatedStockCompletionHandler)handler;

/**
 * 请求股票或板块资金排名净流入和净流出的Top数据列表
 *
 * @param req     - UPMarketMoneyRankTopDataReq
 *                  type:目前支持所有板块和行业、概念、地区和沪深A股
 *                       UPMarketBlockTypeAll
 *                       UPMarketBlockTypeIndustry  行业
 *                       UPMarketBlockTypeRegion    地区
 *                       UPMarketBlockTypeConcept   概念
 *                       不传默认沪深A
 *                  wantNum:需要排名前几的数据  如：请求前三，返回净流入和净流出各3条共6条数据
 * @param handler - (UPMarketMoneyRankTopDataRsp、NSError)
 */
+ (void)requestStockMoneyRankTopData:(UPMarketMoneyRankTopDataReq *)req completionHandler:(upMarketMoneyRankTopDataCompletionHandler)handler;

/**
 * 请求股票因子库指标数据
 * @param req   UPMarketStockFactorDataReq
 *              setCode -- 市场码
 *              code -- 股票码
 *              type -- 因子类型
 *              startDate -- 起始日期
 *              endDate -- 结束日期
 * @param handler - (UPMarketStockFactorDataRsp、NSError)
*/
+ (void)requestStockFactorData:(UPMarketStockFactorDataReq *)req completionHandler:(upMarketStockFactorDataCompletionHandler)handler;

/// 批量请求股票因子库指标数据
+ (void)requestStockFactorBatchData:(UPMarketStockFactorBatchDataReq *)req completionHandler:(upMarketStockFactorDataCompletionHandler)handler;

/**
 * 请求股票静态数据
 * @param req   UPMarketStockBaseInfoReq
 *              setCode -- 市场码
 *              code -- 股票码
 * @param handler - (UPMarketStockBaseInfoRsp、NSError)
*/
+ (void)requestStockStaticInfoData:(UPMarketStockBaseInfoReq *)req completionHandler:(upMarketBaseInfoCompletionHandler)handler;

/**
 * 请求股池统计数据
 * @param req   UPMarketStockPoolStatReq
 *              type -- 股池类型 - UPMarketIndexTypeGNNTop
 * @param handler - (UPMarketStockPoolStatRsp、NSError)
*/
+ (void)requestStockPoolStatData:(UPMarketStockPoolStatReq *)req completionHandler:(upMarketStockPoolStatCompletionHandler)handler;

/**
 * 请求股牛牛领涨题材数据- 走index服务
 * @param req   UPMarketLeadSubjectReq

 * @param handler - (UPMarketLeadSubjectRsp、NSError)
*/
+ (void)requestGNNLeadSubjectData:(UPMarketLeadSubjectReq *)req completionHandler:(upMarketLeadSubjectBlockCompletionHandler)handler;

/**
 * 请求股牛牛题材异动数据- 走index服务
 * @param req   UPMarketSubjectChangeListReq

 * @param handler - (UPMarketSubjectChangeListRsp、NSError)
*/
+ (void)requestGNNSubjectChangeListData:(UPMarketSubjectChangeListReq *)req completionHandler:(upMarketSubjectChangeListCompletionHandler)handler;

/**
 * 请求DDE排名数据
 * @param req   UPMarketDDERankDataReq
 
 * @param handler - (UPMarketDDERankDataRsp、NSError)
 */
+ (void)requestStockDDERankData:(UPMarketDDERankDataReq *)req completionHandler:(upMarketDDERankListCompletionHandler)handler;

/**
 * 请求指标股池 - 按指定日期 通过指标主站获取
 * @param req   UPMarketIndexStocksReq - UPMarketIndexTypeLTJJ
 *                                       UPMarketIndexTypeHHBZTYZ
 *
 * @param handler - (UPMarketIndexStocksRsp、NSError)
 */
+ (void)requestIndexStockListByDate:(UPMarketIndexStocksReq *)req completionHandler:(upMarketIndexStocksPushCompletionHandler)handler;

/// 趋势龙头
+ (void)requestRegStockListByDate:(UPMarketIndexStocksReq *)req completionHandler:(upMarketRegStockListCompletionHandler)handler;

/**
 * 请求股票指标数据 - 按指定日期 通过指标主站获取
 * @param req   UPMarketStockIndexReq - UPMarketIndexTypeLTJJGG
 *                                      UPMarketIndexTypeHHBZTYZ
 *                                      UPMarketIndexTypeZTKline
 *
 * @param handler - (UPMarketStockIndexRsp、NSError)
 */
+ (void)requestStockIndexByDate:(UPMarketStockIndexReq *)req completionHandler:(upMarketStockIndexPushCompletionHandler)handler;


/**
 * 期权列表和T型报价列表
 * @param req UPMarketOptionListReq
 *              setCode -- 市场码 UPMarketSetCodeSH
 *              code -- 股票码 '510050'
 * @param handler - (UPMarketOptionListRsp、NSError)
 */
+ (void)requestOptionList:(UPMarketOptionListReq *)req completionHandler:(upMarketOptionListCompletionHandler)handler;

/**
 * 期权标的列表 - 只包含市场和代码字段, 需要再次请求行情数据
 *
 * @param req    必填参数：
 *                 setCode -- 市场码 UPMarketSetCodeSH  UPMarketSetCodeSZ
 * @param handler - (UPMarketOptionListRsp、NSError)
 */
+ (void)requestOptionUnderlyingList:(UPMarketOptionUnderlyingListReq *)req completionHandler:(upMarketOptionUnderlyingListCompletionHandler)handler;

/**
 * 发行股票列表(新三板)
 *
 * @param req    必填参数：
 *                 setCode -- 市场码 UPMarketSetCodeXSB
 *                 选填: Code -- 股票代码  填入股票代码则只会返回指定股票代码的数据
 * @param handler - (UPMarketFXStockListRsp、NSError)
 */
+ (void)requestFXStockList:(UPMarketFXStockListReq *)req completionHandler:(upMarketFXStockListCompletionHandler)handler;

/**
 * 要约股票列表(新三板)
 *
 * @param req    必填参数：
 *                 setCode -- 市场码 UPMarketSetCodeXSB
 *                 选填: Code -- 股票代码  填入股票代码则只会返回指定股票代码的数据
 * @param handler - (UPMarketYYStockListRsp、NSError)
 */
+ (void)requestYYStockList:(UPMarketYYStockListReq *)req completionHandler:(upMarketYYStockListCompletionHandler)handler;

/**
 * 获取最新的个股异动数据
 *
 * @param req    必填参数：
 *                 type: -- 异动类型 UPMarketStockChangeType
 *                 wantNum:请求条数，缺省返回默认3条
 * @param handler - (UPMarketLatestStockChangeRsp、NSError)
 */
+ (void)requestLatestStockChanges:(UPMarketLatestStockChangeReq *)req completionHandler:(upMarketStockChangeCompletionHandler)handler;

/**
 * 股票异动
 *  @param req      必填参数：
 *                  wantNum:请求条数，缺省返回默认3条
 *                  anomalyType: -- 异动类型 UPMarketStockChangeType
 *                  typeList: -- NSArray<NSNumber*>类型
 */
+ (void)requestAnormalyBroadcast:(UPMarketAnomalyBroadcastReq *)req completionHandler:(upMarketAnormalyBroadcastCompletionHandler)handler;

/**
 * 获取短线精灵数据
 *
 * @param req    setCode:
 *                  1. 不传获取全市场
 *                  2. 传入 UPMarketSetCodeSH 上海市场
 *                  3. 传入 UPMarketSetCodeSZ 深圳市场
 *                 dxjlTypes:
 *                  1.不传默认取所有类型，具体类型见UPMarketDXJLType
 *                  2.传入具体类型  UPMarketDXJLType
 *                 wantNum:请求条数，缺省返回默认3条
 * @param handler - (UPMarketDXJLRsp、NSError)
 */
+ (void)requestDXJLStockList:(UPMarketDXJLReq *)req completionHandler:(upMarketDXJLCompletionHandler)handler;


/**
 * 获取集合竞价行情快照数据
 *
 * @param req    setCode: 市场代码
 *               code:股票代码
 * @param handler - (UPMarketStockAuctionRsp、NSError)
 */
+ (void)requestStockAuctionData:(UPMarketStockAuctionReq *)req completionHandler:(upMarketStockAuctionCompletionHandler)handler;

/**
 * 获取主站集合竞价数据
 *
 * @param req    setCode: 市场代码
 *               code:股票代码
 * @param handler - (UPMarketStockAuctionV2Rsp、NSError)
 */
+ (void)requestStockAuctionDataV2:(UPMarketStockAuctionReq *)req completionHandler:(upMarketStockAuctionV2CompletionHandler)handler;

/**
 * 股票搜索请求
 *
 * @param  req  必填参数：
 *              input  用户输入
 *              选填参数:
 *              wantNum 查询最大数量
 *              marketFilters 根据市场和origCategory过滤
 *              categoryFilters 根据origCategory和origSubCategory过滤
 * @param handler - (UPMarketJPJLStockRsp、NSError)
 */
+ (void)requestJPJLStock:(UPMarketJPJLStockReq *)req completionHandler:(upMarketJPJLStockCompletionHandler)handler;

/**
 * 请求可转债数据(为上证云SDK添加,补充上证云SDK stockHq接口没有可转债数据的问题)
 *
 * @param req    setCode: 市场代码
 *               code:股票代码
 * @param handler - (UPMarketKZZDataRsp、NSError)
 */
+ (void)requestKZZStockData:(UPMarketKZZDataReq *)req completionHandler:(upMarketStockKZZDataCompletionHandler)handler;

/**
 * 请求当前外汇列表
 *
 * @param handler - (upMarketCurrencyInfoCompletionHandler、NSError)
 */
+ (void)requestCurrencyInfo:(UPMarketCurrencyInfoReq *)req completionHandler:(upMarketCurrencyInfoCompletionHandler)handler;

/**
 * 请求涨跌分布-主站接口
 *
 * @param req    zdfbType: 涨跌分布类型
 *               zdfbWidth:涨跌分布-区间宽度,如2%~4%宽度为2
 * @param handler - (UPMarketCurrencyInfoRsp、NSError)
 */
+ (void)requestZDFenBuData:(UPMarketZDFBInfoReq *)req completionHandler:(upMarketZDFBCompletionHandler)handler;


/// 获取L2中拖拉机单/顶级挂单/主力撤单
/// @param req type 0-拖拉机单 1-顶级挂单 2-主力撤单
/// @param handler upMarketL2SzfyComCompletionHandler
+ (void)requestL2SzfyCom:(UPMarketL2SzfyComReq *)req completionHandler:(upMarketL2SzfyComCompletionHandler)handler;

/// 区间统计(懂牛需求)
+ (void)requestRangeStatsByDate:(UPMarketRangeStatsByDateReq *)req completionHandler:(upMarketRangeStatsByDateCompletionHandler)handler;


/**
 @Deprecated
 获取交易时期数据,返回该市场默认交易时期数据
 
 @param setCode 市场代码
 @return 交易时间二维数组 示例：@[ @[@(570),@(690)], @[@(780),@(900)] ]
 
 @see -getMarketTradePeriodWithSetCode:code:
 */
+ (NSArray *)getMarketTradePeriodWithSetCode:(NSInteger)setCode DEPRECATED_ATTRIBUTE;

/**
 获取交易时期数据 在同一个市场下不同的股票可能出现不同的交易时期
 
 @param setCode 市场代码
 @param code    股票代码
 @return 交易时间二维数组 示例：@[ @[@(570),@(690)], @[@(780),@(900)] ]
 */
+ (NSArray *)getMarketTradePeriodWithSetCode:(NSInteger)setCode code:(NSString *)code;

/**
 获取交易时期数据 在同一个市场下不同的股票可能出现不同的交易时期
 
 @param setCode 市场代码
 @param code    股票代码
 @param origCategory 股票原始类别
 @return 交易时间二维数组 示例：@[ @[@(570),@(690)], @[@(780),@(900)] ]
 */
+ (NSArray *)getMarketTradePeriodWithSetCode:(NSInteger)setCode code:(NSString *)code origCategory:(NSUInteger)origCategory;

/**
 获取交易时期数据 在同一个市场下不同的股票可能出现不同的交易时期
 
 @param setCode 市场代码
 @param code    股票代码
 @param origCategory 股票原始类别
 @param origSubCategory 股票原始子类别,用于区分同一个类别下不同子类别时间轴不一致的情况,可转债必传,否则时间轴会不正确
 @return 交易时间二维数组 示例：@[ @[@(570),@(690)], @[@(780),@(900)] ]
 */

+ (NSArray *)getMarketTradePeriodWithSetCode:(NSInteger)setCode code:(NSString *)code origCategory:(NSUInteger)origCategory origSubCategory:(NSUInteger)origSubCategory;
/**
 根据关键词搜索码表
 @param input 搜索字符串
 @param onlyCode 是否只匹配股票代码
 @param customWhere 自定义查询语句 示例：@"setcode IN (1,0,8,9,48) OR setcode >= 16 AND setcode <= 40"
 @return UPMarketCodeMatchInfo 匹配数组
 */
+ (NSArray <UPMarketCodeMatchInfo *> *)queryStockWithInput:(NSString *)input onlyCode:(BOOL)onlyCode customWhere:(NSString *)customWhere;

/**
 根据关键词搜索码表
 @param input 搜索字符串
 @param onlyCode 是否只匹配股票代码
 @param customWhere 自定义查询语句 示例：@"setcode IN (1,0,8,9,48) OR setcode >= 16 AND setcode <= 40"
 @param filterOld 当同时搜索到新旧名称时,是否过滤掉旧名称结果,默认YES
 @param maxMatch 最大匹配数量,默认50条
 @return UPMarketCodeMatchInfo 匹配数组
 */
+ (NSArray <UPMarketCodeMatchInfo *> *)queryStockWithInput:(NSString *)input onlyCode:(BOOL)onlyCode customWhere:(NSString *)customWhere filterOldName:(BOOL)filterOld maxMatchCount:(int)maxMatch;


/**
 根据关键词搜索码表
 @param queryParam 查询参数
 @return UPMarketCodeMatchInfo 匹配数组
 */
+ (NSArray <UPMarketCodeMatchInfo *> *)queryStockWithParam:(UPMarketQueryParam *)queryParam;

/**
 查询码表数据
 @param setCode 市场代码
 @param code 股票代码
 */
+ (UPMarketCodeMatchInfo *)queryStockWithSetCode:(NSInteger)setCode code:(NSString *)code;

/**
 查询码表数据
 @param code 股票代码
 @param name 股票名称(拼音or简称)
*/
+ (UPMarketCodeMatchInfo *)queryStockWithCodeAndName:(NSString *)code name:(NSString *)name;

/**
 查询沪深港通类型
 @param setCode 市场代码
 @param code 股票代码
 */
+ (UPMarketHsgtType)queryHsgtTypeWithSetCode:(NSInteger)setCode code:(NSString *)code;

/**
 * 初始化
 * */
+ (void)start;

/**
 * 初始化
 * @param userId 用户ID, 用于行情config依据用户ID下发测试主站
 * */
+ (void)startWithUserId:(NSString *)userId;

/**
 * 是否交易日
 *
 * @param date 日期:YYYYMMDD  传0查询当日
 * @param setCode 市场代码 目前支持 沪深 港股 港股通市场
 *
 * @return 是否交易日
 */
 + (BOOL)isTradeDay:(int)date setCode:(UPMarketSetCode)setCode;
 
 /**
 * 获取交易日期
 *
 * @param date 日期
 * @param setCode 市场代码
 * @return 如果date为交易日,返回date, 如果为非交易日,返回上一个交易日
 * */
+ (int)getTradeDate:(int)date setCode:(UPMarketSetCode)setCode;

/// 获取所有缓存交易日期
+ (NSDictionary *)getTradeCalendarCache;

/**
 * 获取下一次拉取行情的时间间隔
 * @param setCode 市场代码
 * */
+ (long)getMarketNextInterval:(UPMarketSetCode)setCode;

/**
 * 静态数据填充
 * @param array 待填充数据
 * @param param 原始请求体
 * @return 需要进行静态数据请求的请求体
 * */
+ (UPMarketStockBaseInfoReq *)checkAndFillStaticData:(NSArray<UPHqStockBaseHq *> *)array sourceParam:(UPHqReq *)param;


+ (NSString *)currentSocketAddress:(UPMarketAddressType)addressType DEPRECATED_MSG_ATTRIBUTE("use [UPMarketDebugTool getHostAddress:] instead");

+ (BOOL)setDebugAddress:(NSString *)address addressType:(UPMarketAddressType)addressType DEPRECATED_MSG_ATTRIBUTE("use [UPMarketDebugTool setDebugAddress:addressType:] instead");

/**
 * 测速接口:获取当前主站列表
 */
+ (NSArray <UPMarketAddressModel *> *)getAddressList:(UPMarketL2Type)l2Type;

/**
 * 测速接口:指定连接主站(强制连接, 不再自动切换)
 * 传nil清空配置, 重新选择最优主站
 * 返回YES/NO 设置成功/失败
 */
+ (BOOL)setAddress:(UPMarketAddressModel *)address;

/**
 * 测速接口:获取当前指定的主站地址, 如果没有指定,则返回当前连接站点地址
 */
+ (UPMarketAddressModel *)getCurrentSettingAddress:(UPMarketL2Type)l2Type;

/**
 * 测速接口:获取当前主站地址
 */
 + (UPMarketAddressModel *)getCurrentAddress;

/**
 * 测速接口:获取当前主站地址,适用于多种L2权限共存的场景
 * @param l2Type L2类型
 */
 + (UPMarketAddressModel *)getCurrentAddressWithL2Type:(UPMarketL2Type)l2Type;


/**
* 获取当前主站地址列表 - 多链路主站
*/
+ (NSArray <UPMarketAddressModel *> *)getCurrentAddressArray;


/**
 * 测速接口:判断是否有指定唯一连接主站
 */
+ (BOOL)didSetAddress:(UPMarketL2Type)l2Type;

/**
 * 清空所有指定的行情主站 - 自动选择
 */
+ (void)clearAllSettingAddress;

/**
 * 清空指定类型的行情主站 - 自动选择
 */
+ (void)clearSettingAddress:(UPMarketL2Type)l2Type;

 /**
  * 是否使用上证云SDK的接口
  */
 + (BOOL)isUsingSHYSDK;

/**
 *获取上证云SDK的唯一Hid
 */
+ (NSString *)getSHYHid;


/**
 * 判断股票是否为原精选层
 */
+ (BOOL)isOldJXCStock:(UPMarketSetCode)setcode code:(NSString *)code;

/**
 * 判断北交所开市
 */
+ (BOOL)isBJSOpen;

@end

