// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `Factor.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>
#import "Common.h"

enum {
    HQSys_E_FACTOR_TYPE_E_MARGIN_TRADE = 1,
    HQSys_E_FACTOR_TYPE_E_EXCRA_INFO_MAIN = 2,
    HQSys_E_FACTOR_TYPE_E_SEC_TRADE_INFO = 3,
    HQSys_E_FACTOR_TYPE_E_SHARE_LIST_DTL = 4,
    HQSys_E_FACTOR_TYPE_E_MNG_HOLD_DESC = 5,
    HQSys_E_FACTOR_TYPE_E_MNG_HOLD_ASC = 6,
    HQSys_E_FACTOR_TYPE_E_COM_PLED_INFO = 7,
    HQSys_E_FACTOR_TYPE_E_COM_NPUB_ADDI = 8,
    HQSys_E_FACTOR_TYPE_E_HOT_MONEY = 9,
    HQSys_E_FACTOR_TYPE_E_HOT_INDEX = 10,
    HQSys_E_FACTOR_TYPE_E_TREND_STRENGTH = 11,
    HQSys_E_FACTOR_TYPE_E_STOCK_STARTTIME = 12,
    HQSys_E_FACTOR_TYPE_E_GGT_MONEY = 13,
    HQSys_E_FACTOR_TYPE_E_GGT_HOLD_STOCK = 14,
    HQSys_E_FACTOR_TYPE_E_FACTOR_CJDD = 15,
    HQSys_E_FACTOR_TYPE_E_FACTOR_VALUE_LEAD = 16,
    HQSys_E_FACTOR_TYPE_E_FACTOR_LHZY = 17,
    HQSys_E_FACTOR_TYPE_E_FACTOR_GGT_MONEY_HOLD = 18,
    HQSys_E_FACTOR_TYPE_E_FACTOR_SH_NH_FLOW = 19,
    HQSys_E_FACTOR_TYPE_E_FACTOR_SH_NH_FLOWNET_DAILY = 20,
    HQSys_E_FACTOR_TYPE_E_FACTOR_SH_NH_FLOWQUOTA = 21,
    HQSys_E_FACTOR_TYPE_E_FACTOR_LH_TREND = 22,
    HQSys_E_FACTOR_TYPE_E_FACTOR_SH_NH_HOLD_VALUE = 23,
    HQSys_E_FACTOR_TYPE_E_FACTOR_LGT_VALUE = 24,
    HQSys_E_FACTOR_TYPE_E_FACTOR_LGT_HOLD_JG_LIST = 25,
    HQSys_E_FACTOR_TYPE_E_FACTOR_TOP_TEN_LTGD_HOLD = 26,
    HQSys_E_FACTOR_TYPE_E_FACTOR_NH_INDUSTRY = 27,
    HQSys_E_FACTOR_TYPE_E_FACTOR_LGT_TOP_TENCJ = 28,
    HQSys_E_FACTOR_TYPE_E_FACTOR_BLK_LGT_VALUE = 29,
    HQSys_E_FACTOR_TYPE_E_FACTOR_STK_FACTOR_TYPE = 30,
    HQSys_E_FACTOR_TYPE_E_FACTOR_STK_YJ_TYPE = 31,
    HQSys_E_FACTOR_TYPE_E_FACTOR_LGT_JG_STAT = 32,
    HQSys_E_FACTOR_TYPE_E_FACTOR_LGT_JG_TO_STK = 33,
    HQSys_E_FACTOR_TYPE_E_FACTOR_NH_INDUSTRY_STK_FLOW = 34
};
#define HQSys_E_FACTOR_TYPE NSInteger


enum {
    HQSys_E_NTRADE_DATE_ZT_TYPE_E_NTRADE_DATE_ZT_NONE = 0,
    HQSys_E_NTRADE_DATE_ZT_TYPE_E_NTRADE_DATE_ZT_ONCE,
    HQSys_E_NTRADE_DATE_ZT_TYPE_E_NTRADE_DATE_ZT_TWICE_CONTINUE,
    HQSys_E_NTRADE_DATE_ZT_TYPE_E_NTRADE_DATE_ZT_TWICE_INCONTINUITY,
    HQSys_E_NTRADE_DATE_ZT_TYPE_E_NTRADE_DATE_ZT_TWICE_ALL
};
#define HQSys_E_NTRADE_DATE_ZT_TYPE NSInteger


enum {
    HQSys_E_SH_NH_MARKET_TYPE_E_SH_NH_MARKET_HGT = 1,
    HQSys_E_SH_NH_MARKET_TYPE_E_SH_NH_MARKET_GGTSH,
    HQSys_E_SH_NH_MARKET_TYPE_E_SH_NH_MARKET_SGT,
    HQSys_E_SH_NH_MARKET_TYPE_E_SH_NH_MARKET_GGTSZ,
    HQSys_E_SH_NH_MARKET_TYPE_E_SH_NH_MARKET_NH,
    HQSys_E_SH_NH_MARKET_TYPE_E_SH_NH_MARKET_SH,
    HQSys_E_SH_NH_MARKET_TYPE_E_SH_NH_MARKET_END
};
#define HQSys_E_SH_NH_MARKET_TYPE NSInteger


enum {
    HQSys_E_LH_TREND_PERIOD_TYPE_E_LH_TREND_PERIOD_THREE = 0,
    HQSys_E_LH_TREND_PERIOD_TYPE_E_LH_TREND_PERIOD_FIVE,
    HQSys_E_LH_TREND_PERIOD_TYPE_E_LH_TREND_PERIOD_TEN,
    HQSys_E_LH_TREND_PERIOD_TYPE_E_LH_TREND_PERIOD_TWNTY,
    HQSys_E_LH_TREND_PERIOD_TYPE_E_LH_TREND_PERIOD_CURR,
    HQSys_E_LH_TREND_PERIOD_TYPE_E_LH_TREND_PERIOD_ONE,
    HQSys_E_LH_TREND_PERIOD_TYPE_E_LH_TREND_PERIOD_END
};
#define HQSys_E_LH_TREND_PERIOD_TYPE NSInteger


enum {
    HQSys_E_NH_NET_BUY_TYPE_E_NH_NET_BUY_ONE = 0,
    HQSys_E_NH_NET_BUY_TYPE_E_NH_NET_BUY_FIVE,
    HQSys_E_NH_NET_BUY_TYPE_E_NH_NET_BUY_THIRTY,
    HQSys_E_NH_NET_BUY_TYPE_E_NH_NET_BUY_END
};
#define HQSys_E_NH_NET_BUY_TYPE NSInteger


enum {
    HQSys_E_JG_Type_E_JG_TYPE_GENERAL = 0,
    HQSys_E_JG_Type_E_JG_TYPE_STAR,
    HQSys_E_JG_Type_E_JG_TYPE_ALL
};
#define HQSys_E_JG_Type NSInteger


enum {
    HQSys_E_HOLD_TYPE_E_HOLD_NEW = 1,
    HQSys_E_HOLD_TYPE_E_HOLD_ADD,
    HQSys_E_HOLD_TYPE_E_HOLD_REDUCE,
    HQSys_E_HOLD_TYPE_E_HOLD_NO_CHANGE,
    HQSys_E_HOLD_TYPE_E_HOLD_ALL = 100
};
#define HQSys_E_HOLD_TYPE NSInteger


enum {
    HQSys_E_BLK_TYPE_E_BLK_ALL = 0,
    HQSys_E_BLK_TYPE_E_BLK_HY,
    HQSys_E_BLK_TYPE_E_BLK_DY,
    HQSys_E_BLK_TYPE_E_BLK_GN
};
#define HQSys_E_BLK_TYPE NSInteger


enum {
    HQSys_E_YJ_GG_TYPE_E_YJ_TYPE_ZX = 0,
    HQSys_E_YJ_GG_TYPE_E_YJ_TYPE_LH,
    HQSys_E_YJ_GG_TYPE_E_YJ_TYPE_LK
};
#define HQSys_E_YJ_GG_TYPE NSInteger


enum {
    HQSys_E_HIS_FACTOR_TYPE_E_HIS_FACTOR_MAKE_MONEY_EFFECT = 0,
    HQSys_E_HIS_FACTOR_TYPE_E_HIS_FACTOR_PREDICT_TOP_BOTTOM,
    HQSys_E_HIS_FACTOR_TYPE_E_HIS_FACTOR_END
};
#define HQSys_E_HIS_FACTOR_TYPE NSInteger

@interface HQSysStockInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@end

@interface HQSysFHeaderInfo : UPTAFJceObject
@property (nonatomic, strong) NSData* jce_vGuid;
@property (nonatomic, strong) NSString* jce_sXua;
@end

@interface HQSysFactorInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_strJceBuf;
@property (nonatomic, strong) NSString* jce_strUrl;
@property (nonatomic, assign) JceInt32 jce_intWidth;
@property (nonatomic, assign) JceInt32 jce_intHeight;
@property (nonatomic, strong) NSData* jce_vJceBuf;
@end

@interface HQSysMarginTrade : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceDouble jce_dBuyValue;
@property (nonatomic, assign) JceDouble jce_dBuyReturn;
@property (nonatomic, assign) JceDouble jce_dBuyBalance;
@property (nonatomic, assign) JceInt64 jce_lSellValue;
@property (nonatomic, assign) JceInt64 jce_lSellReturn;
@property (nonatomic, assign) JceInt64 jce_lSellBalanceVol;
@property (nonatomic, assign) JceDouble jce_dSellBalance;
@property (nonatomic, assign) JceDouble jce_dMarginBalance;
@end

@interface HQSysExcraInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt32 jce_iTypeCode;
@property (nonatomic, assign) JceInt64 jce_lTradeNum;
@property (nonatomic, assign) JceDouble jce_lTradeAmount;
@end

@interface HQSysSecTradeInfo : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceInt64 jce_lTradeVol;
@property (nonatomic, assign) JceDouble jce_dTradeAmt;
@end

@interface HQSysSecTradeDay : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, strong) NSArray<HQSysSecTradeInfo*>* jce_vSec;
@end

@interface HQSysShareListGen : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt64 jce_lNewNum;
@property (nonatomic, assign) JceInt64 jce_lMonNum;
@property (nonatomic, assign) JceInt64 jce_lTolNum;
@property (nonatomic, assign) JceFloat jce_fRate;
@property (nonatomic, assign) JceInt64 jce_lShareNum;
@end

@interface HQSysHMngHoldChgInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_strMngName;
@property (nonatomic, assign) JceInt32 jce_intChgVol;
@property (nonatomic, assign) JceDouble jce_dChgEp;
@property (nonatomic, assign) JceInt64 jce_lBeginVol;
@property (nonatomic, assign) JceInt64 jce_lAfterVol;
@property (nonatomic, assign) JceInt64 jce_lTotShare;
@end

@interface HQSysHMngHoldChgDay : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, strong) NSArray<HQSysHMngHoldChgInfo*>* jce_vList;
@end

@interface HQSysComPledHold : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_strHolderName;
@property (nonatomic, assign) JceInt64 jce_lPledVol;
@property (nonatomic, assign) JceFloat jce_fRate;
@property (nonatomic, assign) JceFloat jce_fRateTol;
@property (nonatomic, assign) JceInt64 jce_lPledVolSum;
@property (nonatomic, assign) JceFloat jce_fRateSum;
@property (nonatomic, assign) JceFloat jce_fRateTolSum;
@end

@interface HQSysComPledDay : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt64 jce_lPledVolRest;
@property (nonatomic, assign) JceInt64 jce_lPledVolShare;
@property (nonatomic, assign) JceFloat jce_fRateCom;
@property (nonatomic, assign) JceInt64 jce_lPledVolCom;
@property (nonatomic, strong) NSArray<HQSysComPledHold*>* jce_vPledHold;
@end

@interface HQSysSHotMoneyInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iUpStopType;
@property (nonatomic, assign) JceInt32 jce_iShutStopVol;
@property (nonatomic, assign) JceFloat jce_fShutStopRate;
@property (nonatomic, assign) JceDouble jce_dCirculateValue;
@property (nonatomic, assign) JceDouble jce_dTotalValue;
@property (nonatomic, strong) NSString* jce_strTradePlate;
@property (nonatomic, strong) NSString* jce_strTheme;
@property (nonatomic, strong) NSString* jce_strStopReason;
@property (nonatomic, assign) JceInt32 jce_iFirstUpStopTime;
@property (nonatomic, assign) JceInt32 jce_iLastUpStopTime;
@property (nonatomic, assign) JceInt32 jce_iFirstDownStopTime;
@property (nonatomic, assign) JceInt32 jce_iLastDownStopTime;
@property (nonatomic, assign) JceInt32 jce_iShutUpStopNum;
@property (nonatomic, assign) JceInt32 jce_iShutDownStopNum;
@property (nonatomic, assign) JceInt32 jce_iKeepStopNum;
@property (nonatomic, assign) JceInt32 jce_iDayToStop;
@property (nonatomic, assign) JceInt32 jce_iStrongDay;
@end

@interface HQSysFSHNHReq : UPTAFJceObject
@property (nonatomic, strong) NSArray<NSNumber*>* jce_eVecMarketType;
@end

@interface HQSysHStockFactorReq : UPTAFJceObject
@property (nonatomic, strong) HQSysFHeaderInfo* jce_stHeader;
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_strCode;
@property (nonatomic, assign) JceInt32 jce_iStartDate;
@property (nonatomic, assign) JceInt32 jce_iEndDate;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vFactorType;
@property (nonatomic, strong) HQSysFSHNHReq* jce_stSHNHReq;
@property (nonatomic, assign) JceInt32 jce_iHoldType;
@end

@interface HQSysFRemoteFactorReq : UPTAFJceObject
@property (nonatomic, strong) HQSysFHeaderInfo* jce_stHeader;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceInt64 jce_lRefreshTime;
@property (nonatomic, assign) JceInt64 jce_lLoadTime;
@end

@interface HQSysFRemoteFactorRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vecDate;
@property (nonatomic, assign) JceInt64 jce_lRefreshTime;
@property (nonatomic, assign) JceBool jce_bReLoad;
@property (nonatomic, assign) JceInt64 jce_lLoadTime;
@end

@interface HQSysFMakeMoneyIndexInfo : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dIncrease;
@end

@interface HQSysFMakeMoneyEffect : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_nDate;
@property (nonatomic, strong) HQSysFMakeMoneyIndexInfo* jce_SHInfo;
@property (nonatomic, strong) HQSysFMakeMoneyIndexInfo* jce_SZInfo;
@property (nonatomic, strong) HQSysFMakeMoneyIndexInfo* jce_SHSZInfo;
@property (nonatomic, strong) HQSysFMakeMoneyIndexInfo* jce_KCBZInfo;
@end

@interface HQSysFIndexMoneyEffect : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceDouble jce_dIncrease;
@end

@interface HQSysFTrendStrengthIndexInfo : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dShortTerm;
@property (nonatomic, assign) JceDouble jce_dWaveBand;
@property (nonatomic, assign) JceDouble jce_dMidTerm;
@end

@interface HQSysFTrendStrength : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_nDate;
@property (nonatomic, strong) HQSysFTrendStrengthIndexInfo* jce_SHInfo;
@property (nonatomic, strong) HQSysFTrendStrengthIndexInfo* jce_SZInfo;
@property (nonatomic, strong) HQSysFTrendStrengthIndexInfo* jce_SHSZInfo;
@end

@interface HQSysFTrendStrengthDay : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceDouble jce_dShortTerm;
@property (nonatomic, assign) JceDouble jce_dWaveBand;
@property (nonatomic, assign) JceDouble jce_dMidTerm;
@end

@interface HQSysFTwiceZTStkIf : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) HQSys_E_NTRADE_DATE_ZT_TYPE jce_ztType;
@property (nonatomic, assign) JceInt32 jce_iLastZDTime;
@property (nonatomic, assign) JceInt32 jce_iFirstZDTime;
@property (nonatomic, assign) JceInt64 jce_lLastZDTVol;
@property (nonatomic, assign) JceBool jce_bIsZt;
@property (nonatomic, assign) JceBool jce_bIsST;
@property (nonatomic, assign) JceBool jce_bUnOpenNewStock;
@end

@interface HQSysFTwiceZTStk : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_nDate;
@property (nonatomic, strong) NSArray<HQSysFTwiceZTStkIf*>* jce_vecStk;
@end

@interface HQSysISuperShortStrikeInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stock;
@property (nonatomic, assign) JceInt32 jce_iBuySignal;
@end

@interface HQSysISuperShortStrike : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_nDate;
@property (nonatomic, strong) NSArray<HQSysISuperShortStrikeInfo*>* jce_vecStock;
@end

@interface HQSysFSuperShortStrike : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_nDate;
@property (nonatomic, assign) JceInt32 jce_iBuySignal;
@end

@interface HQSysFStartTimeInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iZtType;
@property (nonatomic, assign) JceInt32 jce_iLastZDTime;
@property (nonatomic, assign) JceInt32 jce_iFirstZDTime;
@property (nonatomic, assign) JceInt64 jce_lLastZDTVol;
@property (nonatomic, assign) JceBool jce_bIsZt;
@property (nonatomic, assign) JceBool jce_bIsST;
@property (nonatomic, assign) JceBool jce_bUnOpenNewStock;
@end

@interface HQSysFGgtMoneyInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceDouble jce_dMoney;
@property (nonatomic, assign) JceFloat jce_fRatio;
@property (nonatomic, assign) JceDouble jce_dClosePrice;
@property (nonatomic, assign) JceDouble jce_dMoneyAcc;
@property (nonatomic, assign) JceDouble jce_dHoldValue;
@end

@interface HQSysFGgtMoneyFlow : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceDouble jce_dMoney;
@property (nonatomic, assign) JceDouble jce_dMoneyAcc;
@property (nonatomic, assign) JceDouble jce_dClosePrice;
@end

@interface HQSysFGgtHoldStockVolInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt64 jce_lVolume;
@property (nonatomic, assign) JceFloat jce_fRatio;
@property (nonatomic, assign) JceDouble jce_dHoldValue;
@property (nonatomic, assign) JceDouble jce_dClosePrice;
@end

@interface HQSysFValueLeading : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iEndDate;
@property (nonatomic, assign) JceInt64 jce_lStkNumOfFundHold;
@property (nonatomic, assign) JceFloat jce_fChainMoreOrLess;
@property (nonatomic, strong) NSString* jce_strUpdateTime;
@property (nonatomic, strong) NSString* jce_strCode;
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, assign) JceDouble jce_dHoldValue;
@property (nonatomic, assign) JceFloat jce_fFloatRatio;
@end

@interface HQSysFValueLeadingInfoRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lStkNumOfFundHold;
@property (nonatomic, assign) JceFloat jce_fChainMoreOrLess;
@property (nonatomic, assign) JceDouble jce_dHoldValue;
@property (nonatomic, assign) JceFloat jce_fFloatRatio;
@end

@interface HQSysFLHZYBuySellSeat : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, strong) HQSysStockInfo* jce_stock;
@property (nonatomic, strong) NSString* jce_sOrgUniCode;
@property (nonatomic, strong) NSString* jce_sOrgChiName;
@property (nonatomic, assign) JceDouble jce_dBuyAmout;
@property (nonatomic, assign) JceDouble jce_dSellAmount;
@property (nonatomic, assign) JceInt32 jce_iRankType;
@end

@interface HQSysLhzyBranchInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sOrgUniCode;
@property (nonatomic, strong) NSString* jce_sOrgChiName;
@property (nonatomic, assign) JceDouble jce_dBuyAmount;
@property (nonatomic, assign) JceDouble jce_dSellAmount;
@property (nonatomic, assign) JceDouble jce_dNetAmount;
@property (nonatomic, assign) JceInt32 jce_iInfoTypeCode;
@property (nonatomic, assign) JceInt32 jce_iOrgGroupCode;
@property (nonatomic, strong) NSString* jce_sOrgGroupName;
@end

@interface HQSysFLHZY : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceDouble jce_dSeatBuyAmout;
@property (nonatomic, assign) JceDouble jce_dSeatSellAmout;
@property (nonatomic, assign) JceDouble jce_dOrganSeatBuyAmout;
@property (nonatomic, assign) JceDouble jce_dOrganSeatSellAmout;
@property (nonatomic, assign) JceInt32 jce_iNumOfOrganSeatBuy;
@property (nonatomic, assign) JceInt32 jce_iNumOfOrganSeatSell;
@property (nonatomic, assign) JceDouble jce_dTopHotMoneySeatBuyAmout;
@property (nonatomic, assign) JceDouble jce_dTopHotMoneySeatSellAmout;
@property (nonatomic, strong) NSString* jce_strCode;
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, strong) NSString* jce_strUpdateTime;
@property (nonatomic, assign) JceInt32 jce_iRankType;
@property (nonatomic, strong) NSArray<HQSysFLHZYBuySellSeat*>* jce_vBuyList;
@property (nonatomic, strong) NSArray<HQSysFLHZYBuySellSeat*>* jce_vSellList;
@property (nonatomic, assign) JceInt32 jce_iNumOfYZBuy;
@property (nonatomic, assign) JceInt32 jce_iNumOfYZSell;
@property (nonatomic, assign) JceInt32 jce_iIsZMYZ;
@end

@interface HQSysFSHNHInFlowDataInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceDouble jce_dInFlow;
@property (nonatomic, assign) JceDouble jce_dInFlowAcc;
@end

@interface HQSysFSHNHInFlowQuotaRemainingDataInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceDouble jce_dQuotaRemainingOfHGT;
@property (nonatomic, assign) JceDouble jce_dQuotaRemainingOfSGT;
@property (nonatomic, assign) JceDouble jce_dQuotaRemainingOfGGTSH;
@property (nonatomic, assign) JceDouble jce_dQuotaRemainingOfGGTSZ;
@end

@interface HQSysFSHNHInFlowRspInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, assign) JceDouble jce_dQuoteRemaining;
@property (nonatomic, assign) JceDouble jce_dInFlowNetDaily;
@property (nonatomic, assign) JceDouble jce_dInFlowAcc;
@end

@interface HQSysFSHNHInFlowRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFSHNHInFlowRspInfo*>* jce_vecSHNHInFlow;
@end

@interface HQSysFNHNetBuyData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceDouble jce_dNetBuy1D;
@property (nonatomic, assign) JceDouble jce_dNetBuy5D;
@property (nonatomic, assign) JceDouble jce_dNetBuy30D;
@property (nonatomic, assign) JceDouble jce_dChgRatio;
@end

@interface HQSysFNHNetBuyRspInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceDouble jce_dChgRatio;
@property (nonatomic, assign) JceDouble jce_dNetBuy;
@end

@interface HQSysFZTStkInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceBool jce_bUnOpenNewStock;
@end

@interface HQSysFLHTrend : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iRankType;
@property (nonatomic, strong) NSString* jce_strUpdateTime;
@end

@interface HQSysFHotBlkInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iStkNum;
@end

@interface HQSysFHotBlkRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFHotBlkInfo*>* jce_vecBlk;
@end

@interface HQSysFActiveBranch : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, strong) NSString* jce_sBranchCode;
@property (nonatomic, strong) NSString* jce_sBranchName;
@property (nonatomic, strong) NSString* jce_sHotMoneyName;
@property (nonatomic, assign) JceDouble jce_dBuyAmount;
@property (nonatomic, assign) JceDouble jce_dSellAmount;
@property (nonatomic, assign) JceInt32 jce_iHotMoneyCode;
@property (nonatomic, strong) NSString* jce_strUpdateTime;
@property (nonatomic, strong) NSString* jce_sHotMoneyNameDN;
@end

@interface HQSysFActiveBranchRspInfo : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysStockInfo*>* jce_vecStk;
@property (nonatomic, strong) NSString* jce_sBranchCode;
@property (nonatomic, strong) NSString* jce_sBranchName;
@property (nonatomic, strong) NSString* jce_sHotMoneyName;
@property (nonatomic, assign) JceInt32 jce_iZTNum;
@property (nonatomic, assign) JceDouble jce_dNetBuyAmount;
@property (nonatomic, assign) JceInt32 jce_iRankNum;
@property (nonatomic, strong) NSString* jce_sHotMoneyNameDN;
@end

@interface HQSysFActiveBranchRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFActiveBranchRspInfo*>* jce_vecActiveBranch;
@end

@interface HQSysFHotStkOfJG : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceDouble jce_dChgRatioOfPeriod;
@property (nonatomic, assign) JceInt32 jce_iRankingNumOfB;
@property (nonatomic, assign) JceInt32 jce_iRankingNumOfS;
@end

@interface HQSysFHotMoneyInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sGroupName;
@property (nonatomic, assign) JceInt32 jce_iGroupCode;
@property (nonatomic, assign) JceInt32 jce_iRankNum;
@property (nonatomic, strong) NSString* jce_sGroupNameDN;
@end

@interface HQSysFHotStkOfYZ : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceDouble jce_dChgRatioOfPeriod;
@property (nonatomic, strong) NSArray<HQSysFHotMoneyInfo*>* jce_vecHotMoney;
@end

@interface HQSysFLHTrendRspInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysFHotBlkRsp* jce_stHotblkOfYZ;
@property (nonatomic, strong) HQSysFHotBlkRsp* jce_stHotblkOfJG;
@property (nonatomic, strong) NSArray<HQSysFHotStkOfYZ*>* jce_vecHotStkOfYZ;
@property (nonatomic, strong) NSArray<HQSysFHotStkOfJG*>* jce_vecHotStkOfJG;
@end

@interface HQSysFactorDayList : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysFactorInfo*>* jce_mapFactorDay;
@end

@interface HQSysHStockFactorRsp : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysFactorDayList*>* jce_mapFactor;
@end

@interface HQSysFLHTrendReq : UPTAFJceObject
@property (nonatomic, strong) HQSysFHeaderInfo* jce_stHeader;
@property (nonatomic, assign) JceInt32 jce_iPeriode;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt32 jce_iPage;
@property (nonatomic, assign) JceInt32 jce_iWantNum;
@end

@interface HQSysFHotStkReq : UPTAFJceObject
@property (nonatomic, strong) HQSysFHeaderInfo* jce_stHeader;
@property (nonatomic, strong) NSArray<HQSysStockInfo*>* jce_vecStk;
@property (nonatomic, assign) JceInt32 jce_iPeriode;
@property (nonatomic, assign) JceInt32 jce_iDate;
@end

@interface HQSysFHotStkRsp : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSString*, NSNumber*>* jce_mapStkYZNum;
@end

@interface HQSysFLHTrendSaveInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysFLHTrendRspInfo*>* jce_mapLHTrend;
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysFActiveBranchRsp*>* jce_mapActiveBranch;
@property (nonatomic, strong) NSDictionary<NSNumber*, HQSysFHotStkRsp*>* jce_mapYZNumOfStk;
@end

@interface HQSysFNHNetBuyReq : UPTAFJceObject
@property (nonatomic, strong) HQSysFHeaderInfo* jce_stHeader;
@property (nonatomic, assign) JceInt32 jce_iBuyType;
@property (nonatomic, assign) JceInt32 jce_iDate;
@end

@interface HQSysFNHNetBuyRsq : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, strong) NSArray<HQSysFNHNetBuyRspInfo*>* jce_vecStk;
@end

@interface HQSysFTopTenLTGDHold : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceFloat jce_fHoldVal;
@end

@interface HQSysFLGTValue : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceFloat jce_fHoldVal;
@property (nonatomic, assign) JceFloat jce_fHoldPCT;
@property (nonatomic, assign) JceFloat jce_fPreAddHoldAmout;
@property (nonatomic, assign) JceFloat jce_fPreAddHoldRatio;
@property (nonatomic, assign) JceFloat jce_fPreChgRatio;
@property (nonatomic, assign) JceFloat jce_f5AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_f5AddHoldRatio;
@property (nonatomic, assign) JceFloat jce_f5ChgRatio;
@property (nonatomic, assign) JceFloat jce_f20AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_f20AddHoldRatio;
@property (nonatomic, assign) JceFloat jce_f20ChgRatio;
@property (nonatomic, assign) JceFloat jce_f60AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_f60AddHoldRatio;
@property (nonatomic, assign) JceFloat jce_f60ChgRatio;
@property (nonatomic, assign) JceInt32 jce_iContAddHoldDays;
@property (nonatomic, assign) JceFloat jce_fPreDayNetAddVol;
@property (nonatomic, assign) JceFloat jce_fHoldPCTTot;
@property (nonatomic, assign) JceFloat jce_fHoldVolume;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vecMktType;
@property (nonatomic, assign) JceFloat jce_fT3NetBuyAmout;
@property (nonatomic, assign) JceFloat jce_fT3NetBuyVol;
@property (nonatomic, assign) JceFloat jce_fT3AddHoldRatio;
@end

@interface HQSysFLGTValueList : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFLGTValue*>* jce_vLGTValue;
@end

@interface HQSysFLGTHoldJGInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sJgCode;
@property (nonatomic, strong) NSString* jce_sJgName;
@property (nonatomic, assign) HQSys_E_JG_Type jce_eJgType;
@property (nonatomic, assign) JceFloat jce_fHoldVal;
@property (nonatomic, assign) JceFloat jce_fBuyCost;
@property (nonatomic, assign) JceFloat jce_fHoldReturnRate;
@property (nonatomic, assign) JceFloat jce_fHoldPCT;
@property (nonatomic, assign) JceInt32 jce_iContAddHoldDays;
@property (nonatomic, assign) JceFloat jce_f1DayAddAmount;
@property (nonatomic, assign) JceFloat jce_fHoldStkSY;
@property (nonatomic, assign) HQSys_E_HOLD_TYPE jce_eHoldType;
@property (nonatomic, assign) JceFloat jce_fUp1AddHold;
@property (nonatomic, assign) JceFloat jce_fUp5AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_fUp20AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_fUp60AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_fUpHoldStkSY;
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@end

@interface HQSysFLGTHoldJGList : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFLGTHoldJGInfo*>* jce_vLGTHoldJg;
@end

@interface HQSysFLGTJGStatsInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sJgCode;
@property (nonatomic, strong) NSString* jce_sJgName;
@property (nonatomic, assign) HQSys_E_JG_Type jce_eJgType;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceFloat jce_fHoldVal;
@property (nonatomic, assign) JceFloat jce_fTotBuyAmout;
@property (nonatomic, assign) JceFloat jce_fTotReturnRate;
@property (nonatomic, assign) JceFloat jce_fSuccessRate;
@property (nonatomic, assign) JceInt32 jce_iContAddHoldDays;
@property (nonatomic, assign) JceInt32 jce_iHoldNum;
@property (nonatomic, assign) JceFloat jce_f1DayAddAmount;
@property (nonatomic, assign) JceFloat jce_f5DayAddAmount;
@property (nonatomic, assign) JceFloat jce_f20DayAddAmount;
@property (nonatomic, assign) JceFloat jce_f60DayAddAmount;
@property (nonatomic, assign) JceInt32 jce_i1DayNewNum;
@property (nonatomic, assign) JceInt32 jce_i5DayNewNum;
@property (nonatomic, assign) JceInt32 jce_i20DayNewNum;
@property (nonatomic, assign) JceInt32 jce_i60DayNewNum;
@property (nonatomic, assign) JceInt32 jce_i1DayAddNum;
@property (nonatomic, assign) JceInt32 jce_i5DayAddNum;
@property (nonatomic, assign) JceInt32 jce_i20DayAddNum;
@property (nonatomic, assign) JceInt32 jce_i60DayAddNum;
@property (nonatomic, assign) JceInt32 jce_i1DayReduceNum;
@property (nonatomic, assign) JceInt32 jce_i5DayReduceNum;
@property (nonatomic, assign) JceInt32 jce_i20DayReduceNum;
@property (nonatomic, assign) JceInt32 jce_i60DayReduceNum;
@property (nonatomic, assign) JceFloat jce_fUpHoldBuyAmout;
@property (nonatomic, assign) JceFloat jce_fUpSuccessRate;
@property (nonatomic, assign) JceFloat jce_fUpHoldReturnRate;
@property (nonatomic, assign) JceFloat jce_fUpTotBuyAmount;
@end

@interface HQSysFLGTJGStatsList : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFLGTJGStatsInfo*>* jce_vLGTJg;
@end

@interface HQSysFNHIndustryInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_blk;
@property (nonatomic, assign) JceInt32 jce_iHoldStkNum;
@property (nonatomic, assign) JceFloat jce_fPreAddHoldAmout;
@property (nonatomic, assign) JceFloat jce_f5AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_f20AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_f60AddHoldAmout;
@property (nonatomic, assign) JceFloat jce_fTotHoldAmount;
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceFloat jce_fHoldVolume;
@property (nonatomic, assign) JceFloat jce_fHoldValue;
@property (nonatomic, assign) JceFloat jce_fHoldPCTTot;
@property (nonatomic, assign) JceFloat jce_fHoldPCTFloat;
@property (nonatomic, assign) JceFloat jce_f1DayAddVolume;
@property (nonatomic, assign) JceFloat jce_f5DayAddVolume;
@property (nonatomic, assign) JceFloat jce_f20DayAddVolume;
@property (nonatomic, assign) JceFloat jce_f60DayAddVolume;
@property (nonatomic, assign) JceInt32 jce_iBlkType;
@end

@interface HQSysFNHIndustryList : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFNHIndustryInfo*>* jce_vecNHIndustry;
@end

@interface HQSysFNHIndustryStkFlowInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceFloat jce_fPreAddHoldAmout;
@end

@interface HQSysFNHIndustryStkFlowList : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFNHIndustryStkFlowInfo*>* jce_vecFlowInStk;
@property (nonatomic, strong) NSArray<HQSysFNHIndustryStkFlowInfo*>* jce_vecFlowOutStk;
@end

@interface HQSysFTopTenCJInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysStockInfo* jce_stk;
@property (nonatomic, assign) JceFloat jce_fGZAmount;
@property (nonatomic, assign) JceFloat jce_fNetBuyRatio;
@property (nonatomic, assign) JceFloat jce_fHoldValue;
@property (nonatomic, assign) JceFloat jce_fHoldPCT;
@property (nonatomic, assign) JceFloat jce_fNetBuyAmout;
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@property (nonatomic, assign) JceInt32 jce_iMktType;
@property (nonatomic, assign) JceFloat jce_fBuyAmout;
@property (nonatomic, assign) JceFloat jce_fSellAmout;
@property (nonatomic, assign) JceFloat jce_fHoldVol;
@end

@interface HQSysFTopTenCJStkList : UPTAFJceObject
@property (nonatomic, strong) NSArray<HQSysFTopTenCJInfo*>* jce_vecStk;
@end

@interface HQSysFStkYJTypeInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iType;
@property (nonatomic, strong) NSString* jce_sContext;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, strong) NSString* jce_sNewId;
@end

@interface HQSysFStkTag : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, NSString*>* jce_mapTag;
@property (nonatomic, assign) JceInt32 jce_iYJType;
@end

@interface HQSysHStockFactorBatchReq : UPTAFJceObject
@property (nonatomic, strong) HQSysFHeaderInfo* jce_stHeader;
@property (nonatomic, strong) NSArray<HQSysStockInfo*>* jce_vStk;
@property (nonatomic, assign) JceInt32 jce_iStartDate;
@property (nonatomic, assign) JceInt32 jce_iEndDate;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vFactorType;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vMarketType;
@property (nonatomic, assign) JceInt32 jce_iJGType;
@property (nonatomic, assign) JceInt32 jce_iBlkType;
@end

@interface HQSysHMonitorCheckBaseInfoReq : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@end

@interface HQSysHMonitorCheckBaseInfoRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iTradeDate;
@end



