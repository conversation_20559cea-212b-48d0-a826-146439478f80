// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `CommonData.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>
#import "Common.h"

enum {
    HQSys_E_STOCK_TYPE_E_STOCK_TYPE_INVALID = 0,
    HQSys_E_STOCK_TYPE_E_STOCK_TYPE_A = 1,
    HQSys_E_STOCK_TYPE_E_STOCK_TYPE_B = 2,
    HQSys_E_STOCK_TYPE_E_STOCK_TYPE_CONVERTTIBLE_BOND = 206,
    HQSys_E_STOCK_TYPE_E_STOCK_TYPE_REITS_FUND = 1001
};
#define HQSys_E_STOCK_TYPE NSInteger


enum {
    HQSys_E_DATACENTER_BLOCK_TYPE_E_DATACENTER_BLOCK_TYPE_ZB = 1,
    HQSys_E_DATACENTER_BLOCK_TYPE_E_DATACENTER_BLOCK_TYPE_ZXB = 2,
    HQSys_E_DATACENTER_BLOCK_TYPE_E_DATACENTER_BLOCK_TYPE_CYB = 3,
    HQSys_E_DATACENTER_BLOCK_TYPE_E_DATACENTER_BLOCK_TYPE_XSB = 4,
    HQSys_E_DATACENTER_BLOCK_TYPE_E_DATACENTER_BLOCK_TYPE_LSB = 7,
    HQSys_E_DATACENTER_BLOCK_TYPE_E_DATACENTER_BLOCK_TYPE_KCB = 8,
    HQSys_E_DATACENTER_BLOCK_TYPE_E_DATACENTER_BLOCK_TYPE_UNKNOWN = 999
};
#define HQSys_E_DATACENTER_BLOCK_TYPE NSInteger


enum {
    HQSys_E_SHT_NAME_CHG_TYPE_E_OTHER_CHG = 0,
    HQSys_E_SHT_NAME_CHG_TYPE_E_XGFX_CHG = 1,
    HQSys_E_SHT_NAME_CHG_TYPE_E_XGSR_CHG = 2,
    HQSys_E_SHT_NAME_CHG_TYPE_E_ST_CHG = 3,
    HQSys_E_SHT_NAME_CHG_TYPE_E_CXST_CHG = 4,
    HQSys_E_SHT_NAME_CHG_TYPE_E_PT_CHG = 5,
    HQSys_E_SHT_NAME_CHG_TYPE_E_CXPT_CHG = 6,
    HQSys_E_SHT_NAME_CHG_TYPE_E_XST_CHG = 7,
    HQSys_E_SHT_NAME_CHG_TYPE_E_CXXST_CHG = 8,
    HQSys_E_SHT_NAME_CHG_TYPE_E_CXXSTBST_CHG = 9,
    HQSys_E_SHT_NAME_CHG_TYPE_E_WCGGJCJG_CHG = 10,
    HQSys_E_SHT_NAME_CHG_TYPE_E_QDGGWCBZG_CHG = 11,
    HQSys_E_SHT_NAME_CHG_TYPE_E_JSGGWWCBZS_CHG = 12,
    HQSys_E_SHT_NAME_CHG_TYPE_E_WCGGJCQDS_CHG = 13,
    HQSys_E_SHT_NAME_CHG_TYPE_E_WCGGQDSTCXXSTBST_CHG = 15,
    HQSys_E_SHT_NAME_CHG_TYPE_E_GSMCHJYFWBG_CHG = 16,
    HQSys_E_SHT_NAME_CHG_TYPE_E_HFSSSR_CHG = 17,
    HQSys_E_SHT_NAME_CHG_TYPE_E_HFSSCR_CHG = 18
};
#define HQSys_E_SHT_NAME_CHG_TYPE NSInteger


enum {
    HQSys_E_CONNE_TYPE_E_UNKONWN = 0,
    HQSys_E_CONNE_TYPE_E_CONNECT = 1,
    HQSys_E_CONNE_TYPE_E_DISCONNE = 2,
    HQSys_E_CONNE_TYPE_E_DELETE = 3
};
#define HQSys_E_CONNE_TYPE NSInteger


enum {
    HQSys_E_CONNEINFO_TYPE_E_LOCATE = 1,
    HQSys_E_CONNEINFO_TYPE_E_TIME = 2
};
#define HQSys_E_CONNEINFO_TYPE NSInteger


enum {
    HQSys_E_LOG_LEVEL_E_LOG_DEBUG = 0,
    HQSys_E_LOG_LEVEL_E_LOG_INFO = 1,
    HQSys_E_LOG_LEVEL_E_LOG_WARN = 2,
    HQSys_E_LOG_LEVEL_E_LOG_ERROR = 3
};
#define HQSys_E_LOG_LEVEL NSInteger


enum {
    HQSys_EQXChgType_E_PX = 1,
    HQSys_EQXChgType_E_SG = 2,
    HQSys_EQXChgType_E_ZZ = 3,
    HQSys_EQXChgType_E_PG = 4,
    HQSys_EQXChgType_E_GKZF = 5,
    HQSys_EQXChgType_E_FGKZF = 6
};
#define HQSys_EQXChgType NSInteger

@interface HQSysHStockUnique : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@end

@interface HQSysHCWVerData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetCode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt64 jce_lCWVersion;
@property (nonatomic, assign) JceInt64 jce_lQXVersion;
@property (nonatomic, assign) JceInt64 jce_lLTGChgVersion;
@end

@interface HQSysHNameChgData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt64 jce_lChgDate;
@property (nonatomic, strong) NSString* jce_sOldName;
@property (nonatomic, strong) NSString* jce_sNewName;
@property (nonatomic, assign) HQSys_E_SHT_NAME_CHG_TYPE jce_eType;
@end

@interface HQSysHNameUsedBefore : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lChgDate;
@property (nonatomic, strong) NSString* jce_sOldName;
@property (nonatomic, assign) HQSys_E_SHT_NAME_CHG_TYPE jce_eType;
@end

@interface HQSysHStockUpInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt16 jce_shtUnit;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_iVolBase;
@property (nonatomic, assign) JceInt8 jce_cPrecise;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, strong) NSArray<HQSysHNameUsedBefore*>* jce_vChgData;
@property (nonatomic, assign) JceInt32 jce_iBaseFreshCount;
@property (nonatomic, assign) JceInt32 jce_iGbFreshCount;
@property (nonatomic, assign) JceBool jce_bDiffRight;
@property (nonatomic, assign) JceBool jce_bCDR;
@property (nonatomic, assign) JceBool jce_bGDR;
@property (nonatomic, assign) JceInt32 jce_iUnit;
@property (nonatomic, strong) NSString* jce_sNameEx;
@property (nonatomic, assign) JceBool jce_bDeficit;
@property (nonatomic, assign) JceBool jce_bProControl;
@property (nonatomic, assign) JceInt16 jce_shtSubType;
@end

@interface HQSysHStockDelistInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysHStockUpInfo* jce_stInfo;
@property (nonatomic, assign) JceInt64 jce_lTsDate;
@end

@interface HQSysHStockSuspendInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysHStockUpInfo* jce_stInfo;
@property (nonatomic, assign) JceInt64 jce_lSuspendDate;
@end

@interface HQSysHStockUpcomingInfo : UPTAFJceObject
@property (nonatomic, strong) HQSysHStockUpInfo* jce_stInfo;
@property (nonatomic, assign) JceInt64 jce_lUpcomingDate;
@property (nonatomic, assign) JceDouble jce_dPrice;
@end

@interface HQSysHMarketCodes : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetCode;
@property (nonatomic, assign) JceInt16 jce_shtFlag;
@property (nonatomic, strong) NSString* jce_sCheckSum;
@property (nonatomic, strong) NSArray<HQSysHStockUpInfo*>* jce_vInfo;
@property (nonatomic, strong) NSArray<HQSysHStockDelistInfo*>* jce_vDelist;
@property (nonatomic, strong) NSArray<HQSysHStockUpcomingInfo*>* jce_vUpcoming;
@property (nonatomic, strong) NSArray<HQSysHStockSuspendInfo*>* jce_vSuspend;
@end

@interface HQSysSCWData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt32 jce_iUpdateDate;
@property (nonatomic, assign) JceInt32 jce_iIPODate;
@property (nonatomic, assign) JceDouble jce_dZGB;
@property (nonatomic, assign) JceDouble jce_dLTGB;
@property (nonatomic, assign) JceDouble jce_dBShare;
@property (nonatomic, assign) JceDouble jce_dHShare;
@property (nonatomic, assign) JceDouble jce_dPerCapitaHold;
@property (nonatomic, assign) JceDouble jce_dMGSY;
@property (nonatomic, assign) JceDouble jce_dMGJZC;
@property (nonatomic, assign) JceDouble jce_dTZMGJZC;
@property (nonatomic, assign) JceDouble jce_dMGGJJ;
@property (nonatomic, assign) JceDouble jce_dMGWFPLR;
@property (nonatomic, assign) JceDouble jce_dYYSRTB;
@property (nonatomic, assign) JceDouble jce_dJLRTB;
@property (nonatomic, assign) JceDouble jce_dJZCSYL;
@property (nonatomic, assign) JceDouble jce_dJLL;
@property (nonatomic, assign) JceDouble jce_dYYLRL;
@property (nonatomic, assign) JceDouble jce_dXSMLL;
@property (nonatomic, assign) JceDouble jce_dLDBL;
@property (nonatomic, assign) JceDouble jce_dZCFZBL;
@property (nonatomic, assign) JceDouble jce_dGDQYB;
@property (nonatomic, assign) JceDouble jce_dYYSR;
@property (nonatomic, assign) JceDouble jce_dYYCB;
@property (nonatomic, assign) JceDouble jce_dTZSY;
@property (nonatomic, assign) JceDouble jce_dYYLR;
@property (nonatomic, assign) JceDouble jce_dYYWSZ;
@property (nonatomic, assign) JceDouble jce_dLRZE;
@property (nonatomic, assign) JceDouble jce_dJLR;
@property (nonatomic, assign) JceDouble jce_dZZC;
@property (nonatomic, assign) JceDouble jce_dLDZC;
@property (nonatomic, assign) JceDouble jce_dGDZC;
@property (nonatomic, assign) JceDouble jce_dWXZC;
@property (nonatomic, assign) JceDouble jce_dCQGQTZ;
@property (nonatomic, assign) JceDouble jce_dZFZ;
@property (nonatomic, assign) JceDouble jce_dLDFZ;
@property (nonatomic, assign) JceDouble jce_dCQFZ;
@property (nonatomic, assign) JceDouble jce_dJZC;
@property (nonatomic, assign) JceDouble jce_dZBGJJ;
@property (nonatomic, assign) JceDouble jce_dWFPLR;
@property (nonatomic, assign) JceInt64 jce_lBGQ;
@property (nonatomic, strong) NSString* jce_sSSHY;
@property (nonatomic, assign) JceDouble jce_dFXJ;
@property (nonatomic, assign) JceInt64 jce_lVer;
@property (nonatomic, assign) JceDouble jce_dMGSYDT;
@property (nonatomic, assign) JceDouble jce_dMGJZCDT;
@property (nonatomic, assign) JceDouble jce_dYYSRZZL;
@property (nonatomic, assign) JceDouble jce_dJLRZZL;
@property (nonatomic, strong) NSString* jce_sSSHYCode;
@property (nonatomic, assign) JceDouble jce_dZCZB;
@property (nonatomic, assign) JceDouble jce_dCASH_DVD_TTM;
@property (nonatomic, assign) JceDouble jce_dCASH_DVD_LFY;
@property (nonatomic, assign) JceDouble jce_dJTMGSY;
@property (nonatomic, assign) JceDouble jce_dTTMMGSY;
@property (nonatomic, assign) JceDouble jce_dFXGB;
@property (nonatomic, assign) JceDouble jce_dSecurityZGB;
@property (nonatomic, assign) JceDouble jce_dExchangeRMBRate;
@end

@interface HQSysSCWData_Extra : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt32 jce_iUpdateDate;
@property (nonatomic, assign) JceDouble jce_dPE_Static;
@property (nonatomic, assign) JceDouble jce_dPE_TTM;
@property (nonatomic, assign) JceDouble jce_dDivide_TTM;
@property (nonatomic, assign) JceDouble jce_dDivide_LFY;
@property (nonatomic, assign) JceDouble jce_dDivideRate_TTM;
@property (nonatomic, assign) JceDouble jce_dDivideRate_LFY;
@property (nonatomic, assign) JceDouble jce_dFXGB;
@property (nonatomic, assign) JceDouble jce_dJTMGSY;
@property (nonatomic, assign) JceDouble jce_dDTMGSY;
@property (nonatomic, assign) JceDouble jce_dTTMMGSY;
@property (nonatomic, assign) JceFloat jce_fExchangeRateJT;
@property (nonatomic, assign) JceFloat jce_fExchangeRateDT;
@property (nonatomic, assign) JceFloat jce_fExchangeRateTTM;
@end

@interface HQSysSQXChgData : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lChanDate;
@property (nonatomic, assign) JceDouble jce_dCashBt;
@property (nonatomic, assign) JceDouble jce_dBonusShr;
@property (nonatomic, assign) JceDouble jce_dCapShr;
@property (nonatomic, assign) JceDouble jce_dAllotPct;
@property (nonatomic, assign) JceDouble jce_dAllotPrice;
@property (nonatomic, assign) JceDouble jce_dIssPri;
@property (nonatomic, assign) JceDouble jce_dIssShareNum;
@property (nonatomic, assign) HQSys_EQXChgType jce_eType;
@end

@interface HQSysSAFundQX : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iExDate;
@property (nonatomic, assign) JceInt32 jce_iOutExDate;
@property (nonatomic, assign) JceDouble jce_dCashBt;
@property (nonatomic, assign) JceDouble jce_dSplitRatio;
@end

@interface HQSysSHKStkQx : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iExDate;
@property (nonatomic, assign) JceDouble jce_dBonus;
@property (nonatomic, assign) JceDouble jce_dTurn;
@property (nonatomic, assign) JceDouble jce_dAllotPct;
@property (nonatomic, assign) JceDouble jce_dAllotPrice;
@property (nonatomic, assign) JceDouble jce_dDiv;
@property (nonatomic, assign) JceDouble jce_dSpeDiv;
@property (nonatomic, assign) JceDouble jce_dComb;
@property (nonatomic, assign) JceDouble jce_dSplit;
@property (nonatomic, strong) NSString* jce_sDesc;
@end

@interface HQSysSQXData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSArray<HQSysSQXChgData*>* jce_mChgData;
@property (nonatomic, assign) JceInt64 jce_lVer;
@property (nonatomic, assign) JceInt32 jce_iStkType;
@property (nonatomic, strong) NSArray<HQSysSAFundQX*>* jce_vecAFundQx;
@property (nonatomic, strong) NSArray<HQSysSHKStkQx*>* jce_vecHKStkQx;
@end

@interface HQSysSXrdFactor : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceDouble jce_dA;
@property (nonatomic, assign) JceDouble jce_dB;
@end

@interface HQSysSMultiKlineXrdData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt32 jce_iOpenDate;
@property (nonatomic, assign) JceInt32 jce_iHighDate;
@property (nonatomic, assign) JceInt32 jce_iLowDate;
@property (nonatomic, assign) JceDouble jce_dHigh;
@property (nonatomic, assign) JceDouble jce_dLow;
@end

@interface HQSysHStockXrdFactor : UPTAFJceObject
@property (nonatomic, strong) HQSysHStockUnique* jce_stock;
@property (nonatomic, strong) NSArray<HQSysSXrdFactor*>* jce_vecQXrdFactor;
@property (nonatomic, strong) NSArray<HQSysSXrdFactor*>* jce_vecHXrdFactor;
@end

@interface HQSysHTSStock : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt64 jce_lTsDate;
@property (nonatomic, assign) HQSys_E_STOCK_TYPE jce_eType;
@property (nonatomic, assign) JceInt32 jce_iBlockType;
@end

@interface HQSysHJjssStockData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceInt64 jce_lDate;
@property (nonatomic, assign) HQSys_E_STOCK_TYPE jce_eType;
@property (nonatomic, assign) HQSys_E_DATACENTER_BLOCK_TYPE jce_eBlockType;
@property (nonatomic, assign) JceInt16 jce_nType;
@end

@interface HQSysHLTGChgData : UPTAFJceObject
@property (nonatomic, assign) JceInt64 jce_lChgTime;
@property (nonatomic, assign) JceInt64 jce_lAFloatShare;
@property (nonatomic, assign) JceInt64 jce_lBFloatShare;
@property (nonatomic, assign) JceInt64 jce_lHFloatShare;
@property (nonatomic, assign) JceDouble jce_dZGB;
@end

@interface HQSysHLTGChg : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt64 jce_lVer;
@property (nonatomic, strong) NSArray<HQSysHLTGChgData*>* jce_vData;
@end

@interface HQSysHNewStockInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceDouble jce_dFXJ;
@property (nonatomic, strong) NSString* jce_sListDate;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_iAPLDate;
@end

@interface HQSysHBrokerData : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sId;
@property (nonatomic, strong) NSString* jce_sEnName;
@property (nonatomic, strong) NSString* jce_sEnShtName;
@property (nonatomic, strong) NSString* jce_sChName;
@property (nonatomic, strong) NSString* jce_sChShtName;
@end

@interface HQSysHBrokerQueue : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSArray<HQSysHBrokerData*>*>* jce_buySide;
@property (nonatomic, strong) NSDictionary<NSNumber*, NSArray<HQSysHBrokerData*>*>* jce_sellSide;
@end

@interface HQSysTradeCale : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt16 jce_shtStatus;
@end

@interface HQSysHOptionUnderlyingDictInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtSetcode;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@end

@interface HQSysHYYInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSString* jce_sBdCode;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceInt16 jce_shtStatus;
@property (nonatomic, assign) JceInt32 jce_iStartDate;
@property (nonatomic, assign) JceInt32 jce_iEndDate;
@property (nonatomic, assign) JceInt32 jce_dVol;
@property (nonatomic, assign) JceDouble jce_dBdPrice;
@property (nonatomic, assign) JceDouble jce_dYJL;
@end

@interface HQSysHFXInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSString* jce_sBdCode;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, assign) JceInt32 jce_iStartDate;
@property (nonatomic, assign) JceInt32 jce_iEndDate;
@property (nonatomic, assign) JceDouble jce_dLowPrice;
@property (nonatomic, assign) JceDouble jce_dHighPrice;
@property (nonatomic, assign) JceDouble jce_dMinVol;
@property (nonatomic, assign) JceDouble jce_dMaxVol;
@property (nonatomic, assign) JceDouble jce_dPrice;
@property (nonatomic, assign) JceDouble jce_dVol;
@property (nonatomic, assign) JceInt16 jce_shtFXMethod;
@end

@interface HQSysHKZZInfo : UPTAFJceObject
@property (nonatomic, assign) JceFloat jce_fPrice;
@property (nonatomic, assign) JceUInt32 jce_iStartDate;
@property (nonatomic, assign) JceUInt32 jce_iEndDate;
@end

@interface HQSysHStaticDataSHSZ : UPTAFJceObject
@property (nonatomic, assign) JceBool jce_bMarginMark;
@property (nonatomic, assign) JceBool jce_bSecuritiesMark;
@property (nonatomic, assign) JceBool jce_bIpoFlag;
@property (nonatomic, assign) JceBool jce_bIpoCFlag;
@property (nonatomic, assign) JceBool jce_bSFlag;
@property (nonatomic, assign) JceBool jce_bPFlag;
@property (nonatomic, assign) JceBool jce_bTFlag;
@property (nonatomic, assign) JceBool jce_bDeficit;
@property (nonatomic, assign) JceBool jce_bDiffRight;
@property (nonatomic, assign) JceBool jce_bCDR;
@property (nonatomic, assign) JceBool jce_bGDR;
@property (nonatomic, assign) JceBool jce_bProControl;
@end

@interface HQSysHStaticDataCW : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_dLtg;
@property (nonatomic, assign) JceDouble jce_dZgb;
@property (nonatomic, assign) JceDouble jce_dMGDTSY;
@property (nonatomic, assign) JceDouble jce_dMGJTSY;
@property (nonatomic, assign) JceDouble jce_dMGTTMSY;
@property (nonatomic, assign) JceDouble jce_dNetValue;
@property (nonatomic, assign) JceDouble jce_dJzc;
@end

@interface HQSysHStaticDataBase : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt16 jce_shtType;
@property (nonatomic, assign) JceInt16 jce_shtSubType;
@property (nonatomic, assign) JceDouble jce_dZTPrice;
@property (nonatomic, assign) JceDouble jce_dDTPrice;
@property (nonatomic, assign) JceInt8 jce_cCoinType;
@property (nonatomic, assign) JceDouble jce_d5SumVol;
@property (nonatomic, assign) JceDouble jce_dPreClose;
@end

@interface HQSysHStaticDataHK : UPTAFJceObject
@property (nonatomic, assign) JceBool jce_bVCM;
@property (nonatomic, assign) JceBool jce_bCAS;
@property (nonatomic, assign) JceBool jce_bPOS;
@property (nonatomic, assign) JceDouble jce_dOpenLowLimitPrice;
@property (nonatomic, assign) JceDouble jce_dOpenHighLimitPrice;
@property (nonatomic, assign) JceInt16 jce_shtSpreadTableCode;
@property (nonatomic, assign) JceInt32 jce_iLotSize;
@property (nonatomic, assign) JceBool jce_bShortSell;
@property (nonatomic, assign) JceBool jce_bCCASS;
@property (nonatomic, assign) JceBool jce_bDummy;
@property (nonatomic, assign) JceBool jce_bStampDuty;
@property (nonatomic, assign) JceBool jce_bEFN;
@property (nonatomic, assign) JceDouble jce_dAccruedInterest;
@property (nonatomic, assign) JceDouble jce_dCouponRate;
@property (nonatomic, assign) JceDouble jce_dConversionRatio;
@property (nonatomic, assign) JceDouble jce_dStrikePrice1;
@property (nonatomic, assign) JceDouble jce_dStrikePrice2;
@property (nonatomic, assign) JceInt32 jce_iMaturityDate;
@property (nonatomic, assign) JceInt8 jce_callPutFlag;
@property (nonatomic, assign) JceInt8 jce_style;
@property (nonatomic, assign) JceInt8 jce_warrantType;
@property (nonatomic, assign) JceInt32 jce_iCallPrice;
@property (nonatomic, assign) JceInt16 jce_shtDecimalInCallPrice;
@property (nonatomic, assign) JceInt32 jce_iEntitlement;
@property (nonatomic, assign) JceInt16 jce_shtDecimalInEntitlement;
@property (nonatomic, assign) JceInt32 jce_iWarrantPerEntitlementNum;
@end

@interface HQSysHForeignRateMidPrice : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_date;
@property (nonatomic, assign) JceDouble jce_price;
@property (nonatomic, assign) JceDouble jce_preClose;
@end

@interface HQSysHPeekOptionExt : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sUnderlyingMarket;
@property (nonatomic, strong) NSString* jce_sUnderlyingCode;
@property (nonatomic, strong) NSString* jce_sUnerlyingName;
@property (nonatomic, assign) JceInt8 jce_cOptionType;
@property (nonatomic, assign) JceInt8 jce_cCallOrPut;
@property (nonatomic, assign) JceInt32 jce_iContractMutiplierUnit;
@property (nonatomic, assign) JceDouble jce_dExercisePrice;
@property (nonatomic, assign) JceInt32 jce_iOpenDate;
@property (nonatomic, assign) JceInt32 jce_iEndDate;
@property (nonatomic, assign) JceInt32 jce_iExerciseDate;
@property (nonatomic, assign) JceFloat jce_fTickSize;
@property (nonatomic, assign) JceInt32 jce_iDaysLeft;
@property (nonatomic, assign) JceDouble jce_dPreDelta;
@property (nonatomic, assign) JceDouble jce_dCurrDelta;
@property (nonatomic, assign) JceInt8 jce_cTradeFlag;
@property (nonatomic, strong) NSString* jce_sContractType;
@end

@interface HQSysHPeekDeriveData : UPTAFJceObject
@property (nonatomic, strong) HQSysHPeekOptionExt* jce_optionExt;
@end

@interface HQSysHConnectionInfo : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sGuid;
@property (nonatomic, strong) NSString* jce_sIp;
@property (nonatomic, strong) NSString* jce_sPlatName;
@property (nonatomic, strong) NSString* jce_sCVerion;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt64 jce_lDuration;
@property (nonatomic, assign) JceInt8 jce_cConnType;
@property (nonatomic, strong) NSString* jce_sServerIp;
@property (nonatomic, strong) NSString* jce_sServerName;
@end

@interface HQSysHConnectionReq : UPTAFJceObject
@property (nonatomic, assign) JceInt8 jce_cInfoType;
@property (nonatomic, strong) NSArray<HQSysHConnectionInfo*>* jce_vecConneInfo;
@end

@interface HQSysHMarketCheckSum : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCheckSum;
@end



