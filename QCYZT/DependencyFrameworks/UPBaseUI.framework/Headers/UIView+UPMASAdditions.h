//
//  UIView+UPMASAdditions.h
//  UPBaseUI
//
//  Created by sammy<PERSON> on 2020/5/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <Masonry/Masonry.h>

NS_ASSUME_NONNULL_BEGIN

@class MASViewAttribute;

@interface UIView (UPMASAdditions)

@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuide;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideLeading;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideTrailing;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideLeft;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideRight;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideTop;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideBottom;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideWidth;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideHeight;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideCenterX;
@property (nonatomic, strong, readonly) MASViewAttribute *up_mas_safeAreaLayoutGuideCenterY;

@end

NS_ASSUME_NONNULL_END
