//
//  UIImageView+ImageLoader.h
//  UPBaseUI
//
//  Created by sammy<PERSON> on 2020/3/12.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^UPImageLoaderCompletionBlock)(UIImage * _Nullable image, NSError * _Nullable error);

@interface UIImageView (UPImageLoader)

- (void)up_setImageWithURLString:(nullable NSString *)urlString;

- (void)up_setImageWithURL:(nullable NSURL *)url;

- (void)up_setImageWithURLString:(nullable NSString *)urlString
                placeholderImage:(nullable UIImage *)placeholder;

- (void)up_setImageWithURL:(nullable NSURL *)url
          placeholderImage:(nullable UIImage *)placeholder;

- (void)up_setImageWithURLString:(nullable NSString *)urlString
                placeholderImage:(nullable UIImage *)placeholder
                       completed:(nullable UPImageLoaderCompletionBlock)completedBlock;

- (void)up_setImageWithURL:(nullable NSURL *)url
          placeholderImage:(nullable UIImage *)placeholder
                 completed:(nullable UPImageLoaderCompletionBlock)completedBlock;

@end

NS_ASSUME_NONNULL_END
