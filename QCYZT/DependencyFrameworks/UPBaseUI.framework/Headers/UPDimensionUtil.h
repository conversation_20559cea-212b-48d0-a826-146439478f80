//
//  UPDimensionUtil.h
//  UPBaseUI
//
//  Created by sa<PERSON><PERSON> on 2020/5/12.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

#define UPWidth(width) ([UPDimensionUtil scaleWidth:width])
#define UPHeight(height) ([UPDimensionUtil scaleHeight:height])

@interface UPDimensionUtil : NSObject

+(CGFloat)scaleWidth:(CGFloat)width;

+(CGFloat)scaleWidthWithoutLimit:(CGFloat)width;

+(CGFloat)scaleHeight:(CGFloat)height;

@end

NS_ASSUME_NONNULL_END
