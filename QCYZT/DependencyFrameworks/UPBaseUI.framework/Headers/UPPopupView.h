//
//  UPPopupView.h
//  UPBaseUI
//
//  Created by sammy<PERSON> on 2020/2/10.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 当前弹窗有几种场景：
 * 1.启动app签署协议的弹窗，使用launchVC弹窗；
 * 2.首页公告、新股申购，新股中签弹窗使用tabBarVC；
 *   不能直接使用[UPRouter rootViewController]，会和场景1冲突；
 * 3.普通popupView，使用VC.view进行弹窗，有时会用到[UPRouter
 *   visibleViewController]；
 * 4.alert，也使用tabBarVC进行弹窗，用[UPRouter tabViewController]获取;
 * 5.toast，直接使用window；
 *
 * 优先级：普通页面<普通popupView<alert弹窗<首页弹窗<升级弹框(强制)<toast
 *
 * 可以使用具体的NSInteger值来匹配特殊场景；
 */
typedef NS_ENUM(NSInteger, UPPopupViewPriority) {
    UPPopupViewPriorityLow = 0,
    UPPopupViewPriorityAlert = 500,
    UPPopupViewPriorityMainPage = 1000,
    UPPopupViewPriorityHigh = 1500
};

typedef NS_ENUM(NSInteger, UPPopupViewQuitBtnStyle) {
    UPPopupViewQuitBtnStyleNone = 0,
    UPPopupViewQuitBtnStyleBottomCenter,
    UPPopupViewQuitBtnStyleTopRightInline,
    UPPopupViewQuitBtnStyleTopRightOutside
};

@class UPPopupView;

@protocol UPPopupViewDelegate <NSObject>
@optional

-(void)upPopupView:(UPPopupView *)popupView didHide:(BOOL)touchOutside;

@end

typedef void(^UPPopupViewContraintsHandler)(UIView *popView);

@interface UPPopupView : UIView

@property(nonatomic, weak) id<UPPopupViewDelegate> delegate;

@property(nonatomic, strong, nullable) UIView * contentView;

@property(nonatomic, assign) CGSize contentSize;

@property(nonatomic, assign) BOOL outsideTouchable;

@property(nonatomic, assign) BOOL hidesWhenTouchOutside;

@property(nonatomic, assign, readonly) BOOL isShowing;

@property(nonatomic, strong, nullable) id context;

@property (assign, nonatomic) UPPopupViewQuitBtnStyle quitBtnStyle;

@property (strong, nonatomic) UIImage *quitBtnImage;

@property (assign, nonatomic) CGSize quitBtnSize;

// 优先级高的在view的上层，只针对同一个parent才有效；
@property (assign, nonatomic) UPPopupViewPriority priority;

@property (nonatomic, assign) BOOL ignorePriority; // 不使用优先级, 默认NO

- (void)showAsDropdownForView:(UIView *)anchorView byOffset:(CGPoint)offset;

- (void)showInView:(UIView *)parentView atPosition:(CGPoint)position;

- (void)hide;

- (void)showInView:(UIView *)parentView constranitsHandler:(UPPopupViewContraintsHandler)handler;

@end

NS_ASSUME_NONNULL_END
