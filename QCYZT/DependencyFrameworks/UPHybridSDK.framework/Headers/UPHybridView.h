//
//  UPHybridView.h
//  UPHybridFramework
//
//  Created by sammy<PERSON> on 2017/6/2.
//  Copyright © 2017年 UPChina. All rights reserved.
//

#ifndef UPHybridView_h
#define UPHybridView_h

#import <UIKit/UIKit.h>
#import <WebKit/WebKit.h>

@class UPHybridView;
@class UPHybridUser;
@class UPHybridPlugin;

typedef NS_ENUM(NSUInteger,UPHybridViewFontMode) {
    UPHybridViewFontModeNormal = 0,
    UPHybridViewFontModeOlder = 1
};

// MARK: UPHybridViewDelegate

@protocol UPHybridViewDelegate <NSObject>

@optional

-(BOOL)upHybridViewShouldOverrideUrl:(NSURL *)url;

-(BOOL)upHybridViewShouldOverrideRequest:(NSURLRequest *)request;

-(void)upHybridViewOnRequestLoadFinished:(NSURLRequest *)request response:(NSURLResponse *)response;

-(void)upHybridViewOnPageLoadStarted;

-(void)upHybridViewOnPageLoadFinished;

-(void)upHybridViewOnPageLoadFinished:(UPHybridView *)hybridView;

-(void)upHybridViewOnPageLoadError;

-(void)upHybridViewOnPresentViewController:(UIViewController *)controller;

-(void)upHybridViewOnOpenUrl:(NSURL *)url;

-(void)upHybridViewOnCloseView;

-(void)upHybridViewOnCanGoBackChanged:(BOOL)canGoBack;

-(void)upHybridViewGoAfterUserLoginCompletion:(void(^)(void))completion;

-(void)upHybridViewSetStatusBarStyle:(UIStatusBarStyle)statusBarStyle;

-(void)upHybridViewChangeFontMode:(UPHybridViewFontMode)mode;

-(UPHybridViewFontMode)upHybridViewGetFontMode;

- (BOOL)upHybridViewOnBackBtnClick;

@end

// MARK: UPHybridViewMenuItem

@interface UPHybridViewMenuItem : NSObject

@property (nonatomic, copy) NSString * itemId;
@property (nonatomic, copy) NSString * title;
@property (nonatomic, copy) NSString * icon;

@end

// MARK: UPHybridViewProgressType

typedef NSUInteger UPHybridViewProgressType;

NS_ENUM(UPHybridViewProgressType) {
    UPHybridViewProgressTypeDefault,
    UPHybridViewProgressTypeCircle
};

// MARK: UPHybridViewThemeType

typedef NSUInteger UPHybridViewThemeType;

NS_ENUM(UPHybridViewThemeType) {
    UPHybridViewThemeTypeDark = 1,
    UPHybridViewThemeTypeLight
};

// MARK: UPHybridViewLoadState

typedef NSUInteger UPHybridViewLoadState;

NS_ENUM(UPHybridViewLoadState) {
    UPHybridViewLoadStateInit,
    UPHybridViewLoadStateLoading,
    UPHybridViewLoadStateFinished,
    UPHybridViewLoadStateError
};

// MARK: UPHybridView

@interface UPHybridView : UIView

@property (nonatomic, weak) id<UPHybridViewDelegate> delegate;

// 是否添加请求头，存在兼容问题，默认为NO
// 内部的WebView默认只支持第一个请求带自定义头，页面中点击等二次跳转是没有的
@property (nonatomic, assign) BOOL allowOverrideRequestHeader;
// 忽略HTTPS连接问题
@property (nonatomic, assign) BOOL ignoreSSLError;

@property (nonatomic, copy) NSArray * trustedHost;

@property (nonatomic, strong) UPHybridUser * user;

@property (nonatomic, strong, readonly) UIScrollView * scrollView;

@property (nonatomic, strong, readonly) WKWebView * wkWebView;

@property (nonatomic, assign, readonly) UPHybridViewLoadState loadState;

@property (nonatomic, assign) UPHybridViewThemeType themeType;
@property (nonatomic, assign) UPHybridViewFontMode fontMode;
@property (nonatomic, assign) UPHybridViewProgressType progressType;

//固定标题，设置后忽略H5设置的标题
@property (nonatomic, copy) NSString * fixedTitle;

@property (nonatomic, assign) BOOL hideHeader;
@property (nonatomic, strong) UIColor * headerColor;
@property (nonatomic, strong) UIColor * titleTextColor;
@property (nonatomic, strong) UIImage * backIcon;
@property (nonatomic, strong) UIImage * closeIcon;
@property (nonatomic, strong) UIImage * moreMenuIcon;
@property (nonatomic, strong) UIColor * progressColor;
@property (nonatomic, strong) UIColor * statusBarColor;

+(void)enableDebug:(BOOL)debug;

+(BOOL)isDebug;

+(void)javaScriptEnabled:(BOOL)enable;

+(BOOL)isJavaScriptEnabled;

-(instancetype)initWithFrame:(CGRect)frame uaVersion:(NSString *)version uaChannel:(NSString *)channel;

-(instancetype)initWithFrame:(CGRect)frame uaVersion:(NSString *)version uaChannel:(NSString *)channel uaExtra:(NSString *)extra;

-(void)addPlugin:(UPHybridPlugin *)plugin;

-(void)loadUrlString:(NSString *)url;

-(void)loadUrl:(NSURL *)url;

-(void)loadHTMLString:(NSString *)string;

-(void)loadHTMLString:(NSString *)string baseURL:(NSURL *)baseURL;

-(void)loadFileURL:(NSURL *)url allowingReadAccessToURL:(NSURL *)readAccessURL;

-(NSString *)getURL;

-(void)reload;

-(void)stopLoading;

-(BOOL)canGoBack;

-(BOOL)goBack;

-(BOOL)canGoForward;

-(BOOL)goForward;

-(NSString *)getTitle;

-(void)viewWillAppear;

-(void)viewAppear;

-(void)viewWillDisappear;

-(void)viewDisappear;

- (void)setBackHidden:(BOOL)hidden;

- (void)setCloseHidden:(BOOL)hidden;

-(void)addHttpHeader:(NSString *)name value:(NSString *)value;

-(void)executeJavascript:(NSString *)js;

-(void)executeJavascript:(NSString *)js completionHandler:(void (^)(NSString * result, NSError * error))completionHandler;

// 在Block中注意使用weak self, 否则会循环引用
-(void)addJavascriptHandler:(NSString *)name handler:(void(^)(id data))handler;

-(UIView *)errorViewForError:(NSError *)error;

- (void)addNavGradientLayerWith:(CGPoint)startPoint endPoint:(CGPoint)endPoint colors:(NSArray *)colors locations:(NSArray<NSNumber *> *)locations;
@end

#endif /* UPHybridView_h */
