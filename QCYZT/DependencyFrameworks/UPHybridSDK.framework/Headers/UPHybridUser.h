//
//  UPHybridUser.h
//  UPHybridSDK
//
//  Created by sa<PERSON><PERSON> on 2017/7/28.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#ifndef UPHybridUser_h
#define UPHybridUser_h

@interface UPHybridUser : NSObject

@property (nonatomic, copy) NSString * uid;
@property (nonatomic, copy) NSString * cid;
@property (nonatomic, copy) NSString * nickName;
@property (nonatomic, copy) NSString * avatar;
@property (nonatomic, copy) NSString * sign;
// uid+timeStamp再des之后构造的token
@property (nonatomic, copy) NSString * token;
@property (nonatomic, copy) NSString * cidSign;
@property (nonatomic, copy) NSString * cidToken;
// 真实的token(和refreshToken匹配的token)
@property (nonatomic, copy) NSString * accessToken;
@property (nonatomic, assign) BOOL isTourist;
@property (nonatomic, copy) NSString * phone;
@property (nonatomic, copy) NSString * fundAccount;
@property (nonatomic, copy) NSString * channel;
@property (nonatomic, copy) NSDictionary<NSString *, NSString *> * privileges;

@property (nonatomic, copy) NSString * extra;

@end

#endif /* UPHybridUser_h */
