//
//  TAFHTTP.h
//  UPTAF
//
//  Created by sa<PERSON><PERSON> on 2018/5/15.
//  Copyright © 2018年 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <UPTAF/TAFHTTPRequest.h>
#import <UPTAF/TAFHTTPResponse.h>

// MARK: UPTAFHTTPClientURLRewriter

@protocol UPTAFHTTPClientURLRewriter <NSObject>

@optional

-(NSString *)httpClientHandleURL:(NSString *)URLString;

@end

// MARK: UPTAFHTTPClient

typedef NS_ENUM(NSInteger, UPTAFHTTPClientSSLChallengeMode) {
    UPTAFHTTPClientSSLChallengeModeNone, // 忽略
    UPTAFHTTPClientSSLChallengeModeSimple, // 不校验host
    UPTAFHTTPClientSSLChallengeModeFull // 正常校验
};

typedef void (^UPTAFHTTPHandler)(UPTAFHTTPResponse * response);

@interface UPTAFHTTPClient : NSObject

@property (nonatomic, assign) BOOL enableRedirect; // 默认YES
@property (nonatomic, assign) UPTAFHTTPClientSSLChallengeMode sslChallengeMode; // 默认UPTAFHTTPClientSSLChallengeModeFull

/*!
 * @abstract
 * 返回默认的HTTPClient, handler queue为main queue, 禁止缓存, 允许重定向, UPTAFHTTPClientSSLChallengeModeNone
 */
+ (instancetype)defaultHTTPClient;

/*!
 * @abstract
 * 设置全局的UPTAFHTTPClientURLRewriter
 */
+ (void)setURLRewriter:(id<UPTAFHTTPClientURLRewriter>)URLRewriter;

/*!
 * @abstract
 * 使用传递进来的handler queue创建一个HTTPClient
 *
 * @param queue
 * handler queue
 *
 * @param enableCache
 * 是否允许缓存
 */
- (instancetype)initWithHandlerQueue:(dispatch_queue_t)queue enableCache:(BOOL)enableCache;

/*!
 * @abstract
 * 发送数据请求, 这里有一个要特别注意的限制, 同一个request, 不要sendRequest多次
 * 这是因为HTTPClient内部目前没有对request做深度copy, 对象就会复用, 部分类型的请求可能会傻逼
 */
- (void)sendRequest:(UPTAFHTTPRequest *)request handler:(UPTAFHTTPHandler)handler;

/*!
 * @abstract
 * HTTPClient不再使用的时候, 需要调用一下invalidate, 不然可能会泄漏, NSURLSession的bug
 * 一般在dealloc里面撸一下就行了, UI组件里面切记要调用
 *
 * @param cancelPendingTasks
 * 是否取消还没执行的请求
 */
- (void)invalidate:(BOOL)cancelPendingTasks;

@end

// MARK: UPTAFHTTPDownloader

@class UPTAFHTTPDownloader;

@protocol UPTAFHTTPDownloaderDelegate <NSObject>

@optional

- (void)httpDownloader:(UPTAFHTTPDownloader *)downloader didUpdateProgress:(int)progress;

- (void)httpDownloader:(UPTAFHTTPDownloader *)downloader didFinishWithError:(NSError *)error;

@end

@interface UPTAFHTTPDownloader : NSObject

@property(nonatomic, weak) id<UPTAFHTTPDownloaderDelegate> delegate; // 主线程调用
@property(nonatomic, readonly, copy) NSString * downloadURL; // 下载地址
@property(nonatomic, readonly, copy) NSString * savePath; // 保存路径
@property(nonatomic, readonly, assign) int progress; // 0 ~ 100

@property(nonatomic, copy) NSString * md5; // 下载后校验MD5
@property(nonatomic, copy) NSString * unzipPath; // 下载后解压路径

/*!
 * @abstract
 * 创建一个UPTAFHTTPDownloader
 *
 * @param downloadURL
 * 下载地址
 *
 * @param savePath
 * 保存路径
 */
- (instancetype)initWithURL:(NSString *)downloadURL toPath:(NSString *)savePath;

- (void)start;

- (void)cancel;

@end
