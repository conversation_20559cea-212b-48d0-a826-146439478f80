//
//  UPTAFNSString.h
//  UPTAF
//
//  Created by sa<PERSON><PERSON> on 2016/10/29.
//  Copyright © 2016年 TAF. All rights reserved.
//

#ifndef UPTAFNSString_h
#define UPTAFNSString_h

@interface UPTAFNSString : NSObject

+ (BOOL)hasPrefixIgnoreCase:(NSString *)str prefix:(NSString *)prefix;

+ (BOOL)hasSuffixIgnoreCase:(NSString *)str suffix:(NSString *)suffix;

+ (BOOL)isEqual:(NSString *)string other:(NSString *)other;

+ (BOOL)isEqualIgnoreCase:(NSString *)string other:(NSString *)other;

+ (BOOL)containsIgnoreCase:(NSString *)string other:(NSString *)other;

@end

#endif /* UPTAFNSString_h */
