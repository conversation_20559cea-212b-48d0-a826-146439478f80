
#import <Foundation/Foundation.h>

@class UPTAFJceObject;

#pragma mark -

typedef unsigned char           u8;
typedef unsigned short          u16;
typedef unsigned int            u32;
typedef unsigned long long      u64;

#define __LITTLE_ENDIAN	        (0)
#define STREAM_CAPABILITY_EX    (64)
#define STREAM_BUFFER_SIZE      (4096)

#define ASSERT_TRHOW_WS_EXCEPTION(x) ASSERT_FAIL_TRHOW_WUP_SERIALIZABLE_EXCEPTION(x)
#define ASSERT_FAIL_TRHOW_WUP_SERIALIZABLE_EXCEPTION(x)                         \
do {                                                                            \
    if (!(x)) {                                                                 \
        NSException *exception = [NSException exceptionWithName:@"WupSerializableException" reason:[NSString stringWithFormat:@"%s, %d: assert(%@) fail!", __func__, (__LINE__), @#x] userInfo:nil];  \
        @throw exception;                                                       \
    }                                                                           \
} while(0)

#pragma mark -

#define JCE_TYPE_INT1			(0)		// int1 紧跟1个字节整型数据
#define JCE_TYPE_INT2			(1)		// int2 紧跟2个字节整型数据
#define JCE_TYPE_INT4			(2)		// int4 紧跟4个字节整型数据
#define JCE_TYPE_INT8			(3)		// int8 紧跟8个字节整型数据
#define JCE_TYPE_FLOAT			(4)		// float 紧跟4个字节浮点型数据
#define JCE_TYPE_DOUBLE			(5)		// double 紧跟8个字节浮点型数据
#define JCE_TYPE_STRING1		(6)		// String1 紧跟1个字节长度，再跟内容
#define JCE_TYPE_STRING4		(7)		// String4 紧跟4个字节长度，再跟内容
#define JCE_TYPE_MAP			(8)		// Map 紧跟一个整型数据表示Map的大小，再跟[key, value]对列表
#define JCE_TYPE_LIST			(9)		// List 紧跟一个整型数据表示List的大小，再跟元素列表
#define JCE_TYPE_STRUCT_S		(10)	// 自定义结构开始 自定义结构开始标志
#define JCE_TYPE_STRUCT_E		(11)	// 自定义结构结束 自定义结构结束标志，Tag为0
#define JCE_TYPE_ZERO			(12)	// 数字0 表示数字0，后面不跟数据
#define JCE_TYPE_SIMPLE_LIST	(13)	// SimpleList 简单列表（目前用在byte数组），紧跟一个类型字段（目前只支持byte），紧跟4字节表示长度，再跟byte数据
#define JCE_TYPE_DOUBLE_COMPRESS (14) // 浮点数启用压缩
#define JCE_TYPE_UNUSED			(15)

#pragma mark -

@interface UPTAFJceStream : NSObject
{
    unsigned char *_streamBuffer;
    int _streamSize;
    int	_cursor;
    
    unsigned char _double_precision;
    NSMutableArray *_old_double_precision;
}

@property (nonatomic, assign) unsigned char *streamBuffer;
@property (nonatomic, assign) int			 streamSize;
@property (nonatomic, assign) int			 cursor;
@property (nonatomic, assign) unsigned char  double_precision;

- (NSData *)data;
- (NSString *)description;

- (BOOL)hasSetPrecision;
- (char)getPrecision;
- (void)setPrecision:(unsigned char)precision;

- (void)preventBufferOverflow:(int)size;

- (void)writeByte1:(u8)val;
- (void)writeByte2:(u16)val;
- (void)writeByte4:(u32)val;
- (void)writeByte8:(u64)val;
- (void)writeFloat:(float)val;
- (void)writeDouble:(double)val;
- (void)writeBytes:(const void*)data size:(int)size;

- (u8)readByte1;
- (u16)readByte2;
- (u32)readByte4;
- (u64)readByte8;
- (float)readFloat;
- (double)readDouble;
- (void*)readBytes:(int)size;

@end

#pragma mark -

@interface UPTAFJcePair : NSObject

@property (nonatomic, retain) id key;
@property (nonatomic, retain) id value;

+ (UPTAFJcePair *)pairWithValue:(id)value forKey:(id)key;

+ (id)analyzeExtStr:(NSString *)str;
+ (UPTAFJcePair *)pairFromExtStr:(NSString *)str;

@end