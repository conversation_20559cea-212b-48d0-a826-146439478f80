#import "JceInputStream.h"
#import "JceOutputStream.h"

#pragma mark -

typedef BOOL                    JceBool;
typedef char                    JceInt8;
typedef unsigned char           JceUInt8;
typedef short                   JceInt16;
typedef unsigned short          JceUInt16;
typedef int                     JceInt32;
typedef unsigned int            JceUInt32;
typedef long long               JceInt64;
typedef unsigned long long      JceUInt64;
typedef float                   JceFloat;
typedef double                  JceDouble;

//#define AUTO_COLLECT            NSAutoreleasePool *autoReleasePool = [[NSAutoreleasePool alloc] init];
//#define AUTO_RELEASE            [autoReleasePool release];

#define DefaultJceString        @""
#define DefaultJceData          [NSData data]
#define DefaultJceArray         [NSArray array]
#define DefaultJceDictionary    [NSDictionary dictionary]

#pragma mark - 

////////////////////////////////////////////////////////////////////////

//#if __BIG_ENDIAN
//#   define jce_ntohll(x)    (x)
//#   define jce_htonll(x)    (x)
//#   define jce_ntohf(x)     (x)
//#   define jce_htonf(x)     (x)
//#   define jce_ntohd(x)     (x)
//#   define jce_htond(x)     (x)
//#else
//#   if __BYTE_ORDER == __LITTLE_ENDIAN
//#       define jce_ntohll(x)    jce_htonll(x)

union bswap_helper
{
    u64   i64;
    u32   i32[2];
};

//static u64 jce_htonll(u64 x)
//{
//    union bswap_helper h;
//    h.i64 = x;
//    u32 tmp = htonl(h.i32[1]);
//    h.i32[1] = htonl(h.i32[0]);
//    h.i32[0] = tmp;
//    return h.i64;
//}

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-function"
static float jce_ntohf(float x)
{
    union {
        float f;
        u32 i32;
    } helper;
    
    helper.f = x;
    helper.i32 = ntohl( helper.i32 );
    
    return helper.f;
}
#pragma clang diagnostic pop

#       define jce_htonf(x)     jce_ntohf(x)

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-function"
static double jce_ntohd(double x)
{
    union {
        double d;
        u64 i64;
    } helper;
    
    helper.d = x;
    helper.i64 = ntohll( helper.i64 );
    
    return helper.d;
}
#pragma clang diagnostic pop

#       define jce_htond(x)     jce_ntohd(x)
//#   endif
//#endif

////////////////////////////////////////////////////////////////////////
@interface UPTAFJceObject : NSObject <NSCoding>
{
    JceInt8 _jce_double_precision;
}

+ (id)object;
+ (NSDictionary *)jceIvarListWithEncodedTypes;

- (NSDictionary *)jcePropsDescription;

+ (id)fromData:(NSData *)data;
- (id)fromData:(NSData *)data;
- (NSData *)toData;

+ (NSString *)jceType;
- (NSString *)jceType;

- (JceInt8)getPrecision;
- (JceBool)setPrecision:(JceInt8)precision;
- (void)resetPrecision;
- (JceBool)hasSetPrecision;

- (void)__pack:(UPTAFJceOutputStream *)stream;	// !!! INTERNAL USE ONLY
- (void)__unpack:(UPTAFJceInputStream *)stream;	// !!! INTERNAL USE ONLY

@end

/**
 * 分利用Object-C的动态特定，提供更智能(自动化)的JCE打包解包方案
 *
 * 如果一个类要支持Jce编解码，只需要继承JceObjectV2类，并且使用J_PROP_**宏来定义其属性，该
 * 属性的get方法和set方法分别为：jce_name, setJce_name:
 *
 * 对于NSArray和NSDictionary(即JCE中的Vector和Map)，需要用到附加信息ext编码。附加信息编码
 * 字符串的第一个字母为'V'或者‘M’或者'O'，分别代表Vector、Map和支持的对象类型。如果是Vector，
 * 后面字符串是该Vector容器中对象类型的附加信息编码；如果是MAP，后面字符串的前两个字符的数值等于
 * Key的类型信息编码字符串长度(00, 99]，然后再是Key的类型信息编码和Value的类型信息编码。
 *
 * Vector<string>                    ->> VONSString
 * MAP<string, string>               ->> M09ONSStringONSString
 * MAP<string, Vector<string>>       ->> M09ONSStringVONSString
 * MAP<MttJceObject1, MttJceObject2> ->> M14OMttJceObject1OMttJceObject2
 * Vector<byte>                      ->> 映射到NSData类型，不需要附加信息编码
 * Vector<int>                       ->> 因NSArray中必须是对象，按NSArray<NSNumber>处理
 * Map<int, Vector<byte>>            ->> M09ONSNumberONSData
 *
 * 框架所支持的非容器对象类型包括：NSData，NSNumber，NSString，JceObject及其派生对象
 * 不支持包含下划线的属性名，不支持类名中包含下划线的JceObject派生类，不支持ext长度超过99
 *
 */
#define JCE_SEPARATOR $
#define JCE_SEPARATOR_STR @"$"

#define JCE_PROPERTY_NAME_PREFIX          jce_
#define JCE_PROPERTY_NAME_PREFIX_U        Jce_
#define JCE_PROPERTY_NAME_PREFIX_STR      @"jce_"
#define JCE_PROPERTY_LVNAME_PREFIX        jce_
#define JCE_PROPERTY_LVNAME_PREFIX_STR    @"jce_"

//#define JCE_PROPERTY_ATTR_GETTER_AND_SETTER__(prefixL, prefixU, name) getter = prefixL##name, setter = set##prefixU##name:
//#define JCE_PROPERTY_ATTR_GETTER_AND_SETTER_(prefixL, prefixU, name) JCE_PROPERTY_ATTR_GETTER_AND_SETTER__(prefixL, prefixU, name)
//#define JCE_PROPERTY_ATTR_GETTER_AND_SETTER(name) JCE_PROPERTY_ATTR_GETTER_AND_SETTER_(JCE_PROPERTY_NAME_PREFIX, JCE_PROPERTY_NAME_PREFIX_U, name)
//#define JCE_PROPERTY_ATTR_GETTER_AND_SETTER_V2(gname, sname) getter = gname, setter = sname

#define JCE_PROPERTY_NAME_NM__(separator, prefix, flag, tag, name)       prefix##name##separator##tag##separator##flag
#define JCE_PROPERTY_NAME_EX__(separator, prefix, flag, tag, name, ext)  prefix##name##separator##tag##separator##flag##separator##ext
#define JCE_PROPERTY_NAME_NM_(separator, prefix, flag, tag, name)        JCE_PROPERTY_NAME_NM__(separator, prefix, flag, tag, name)
#define JCE_PROPERTY_NAME_EX_(separator, prefix, flag, tag, name, ext)   JCE_PROPERTY_NAME_EX__(separator, prefix, flag, tag, name, ext)
#define JCE_PROPERTY_NAME_NM(flag, tag, name)                 JCE_PROPERTY_NAME_NM_(JCE_SEPARATOR, JCE_PROPERTY_LVNAME_PREFIX, flag, tag, name)
#define JCE_PROPERTY_NAME_EX(flag, tag, name, ext)            JCE_PROPERTY_NAME_EX_(JCE_SEPARATOR, JCE_PROPERTY_LVNAME_PREFIX, flag, tag, name, ext)

//#define J_PROP_GS(name)                   JCE_PROPERTY_ATTR_GETTER_AND_SETTER(name)

//#if JCE_DEFAULT_GETTER_AND_SETTER
//#define J_PROP_GS_V2(gname, sname)    JCE_PROPERTY_ATTR_GETTER_AND_SETTER_V2(gname, sname)
//#define J_PROP_(name)                 self.name
//#define J_PROP(name)                  J_PROP_(name)
//#else /*JCE_DEFAULT_GETTER_AND_SETTER*/
//#define J_PROP_GS_V2(gname, sname)    J_PROP_GS(gname)
#define J_PROP(name)                  self.jce_##name
//#endif /*JCE_DEFAULT_GETTER_AND_SETTER*/

#define J_PROP_NM(flag, tag, name)        JCE_PROPERTY_NAME_NM(flag, tag, name)
#define J_PROP_EX(flag, tag, name, ext)   JCE_PROPERTY_NAME_EX(flag, tag, name, ext)
#define J_PROP_NFX_STR                    JCE_PROPERTY_NAME_PREFIX_STR
#define J_PROP_LFX_STR                    JCE_PROPERTY_LVNAME_PREFIX_STR

static inline NSArray * __jce_simpleCallStackSymbols(NSArray<NSString *> * stack) {
    if(stack.count > 5) {
        stack = [stack subarrayWithRange:NSMakeRange(0, 5)];
    }

    return stack;
}

#define JCEAssert(condition, desc, ...) \
    do { \
        __PRAGMA_PUSH_NO_EXTRA_ARG_WARNINGS \
        if (__builtin_expect(!(condition), 0)) { \
            NSString * func = [NSString stringWithUTF8String:__FUNCTION__]; \
            NSArray * stack = __jce_simpleCallStackSymbols(NSThread.callStackSymbols); \
            NSString * format = [NSString stringWithFormat:(desc), ##__VA_ARGS__]; \
            NSString * reason = [NSString stringWithFormat:@"%@(L:%d) %@ %@", func, __LINE__, stack, format]; \
            @throw [NSException exceptionWithName:@"JCEException" reason:reason userInfo:nil]; \
        } \
        __PRAGMA_POP_NO_EXTRA_ARG_WARNINGS \
    } while(0)
