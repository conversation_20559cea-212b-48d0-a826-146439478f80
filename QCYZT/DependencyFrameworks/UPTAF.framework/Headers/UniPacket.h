
#import "UniAttribute.h"

@class UPTAFBasePacket;

@interface UPTAFUniPacket : UPTAFJceObject

@property (nonatomic, readonly) UPTAFUniAttribute* attributes;
@property (nonatomic, readonly) UPTAFBasePacket* basePacket;

+ (UPTAFUniPacket *)packet;

/////////////////////////////////////////////////////////////
//support: NSNumber NSString JceObject NSData
- (id)get:(NSString *)attrName forClass:(Class)theClass;

- (void)put:(NSString *)attrName value:(id)attrValue;

/////////////////////////////////////////////////////////////
-(int)getResultCode;
-(void)setResultCode:(int)iRet;
-(NSString*)getResultDesc;
-(NSDictionary*)getStatus;
-(char)getPacketType;
-(NSData*)getPacketBuffer;
-(void)setPacketBuffer:(NSData*)sBuffer;
-(short)getVersion;
-(void)setVersion:(short)iVer;
-(int)getRequestId;
-(void)setRequestId:(int)iRequestId;
-(NSString*)getServantName;
-(void)setServantName:(NSString*)sServantName;
-(NSString*)getFuncName;
-(void)setFuncName:(NSString*)sFuncName;
-(NSString *)getContextValue:(NSString *)key;
-(void)setContextValue:(NSString *)key value:(NSString *)value;

@end
