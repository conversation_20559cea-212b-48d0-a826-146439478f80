//
//  TAFLogger.h
//  UPTAF
//
//  Created by sa<PERSON><PERSON> on 2019/1/14.
//  Copyright © 2019 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT void UPTAFLog(NSString * identifier, NSString * format, ...) NS_FORMAT_FUNCTION(2,3) NS_NO_TAIL_CALL;
FOUNDATION_EXPORT void UPTAFLogFile(NSString * identifier, NSString * format, ...) NS_FORMAT_FUNCTION(2,3) NS_NO_TAIL_CALL;

@interface UPTAFLogger : NSObject

+(void)logForIdentifier:(NSString *)identifier message:(NSString *)msg toFile:(BOOL)toFile;

+(void)uploadForIdentifier:(NSString *)identifier;

+(NSString *)logFilePathForIdentifier:(NSString *)identifier;

+(void)setDebug:(BOOL)debuggable;

@end

NS_ASSUME_NONNULL_END
