//
//  TAFStatistics.h
//  UPTAFStatistics
//
//  Created by sa<PERSON><PERSON> on 2016/11/8.
//  Copyright © 2016年 TAF. All rights reserved.
//

#ifndef UPTAFStatistics_h
#define UPTAFStatistics_h

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@interface UPTAFStatistics : NSObject

-(instancetype)initWithBID:(NSString *)bid;

+(void)setUID:(NSString *)uid;

+(void)enableReqStat;

+(void)disableUploadStat;

+(void)upload;

-(void)uiEnter:(NSString *)name;

-(void)uiEnter:(NSString *)name extras:(NSArray *)extras;

-(void)uiExit:(NSString *)name;

-(void)uiExit:(NSString *)name extras:(NSArray *)extras;

-(void)uiClick:(NSString *)name;

-(void)uiClick:(NSString *)name extras:(NSArray *)extras;

-(void)timeStart:(NSString *)name;

-(void)timeStart:(NSString *)name extras:(NSArray *)extras;

-(void)timeStop:(NSString *)name;

-(void)timeStop:(NSString *)name extras:(NSArray *)extras;

-(void)reqStart:(NSString *)busType address:(NSString *)address reqId:(int)reqId;

-(void)reqStart:(NSString *)busType address:(NSString *)address servantName:(NSString *)servant funcName:(NSString *)func reqId:(int)reqId;

-(void)reqStop:(NSString *)busType address:(NSString *)address reqId:(int)reqId ret:(int)ret;

-(void)reqStop:(NSString *)busType address:(NSString *)address servantName:(NSString *)servant funcName:(NSString *)func reqId:(int)reqId ret:(int)ret;

-(void)avg:(NSString *)key value:(int)value;

-(void)avg:(NSString *)key name:(NSString *)name value:(int)value;

-(void)count:(NSString *)key;

-(void)count:(NSString *)key name:(NSString *)name;

-(void)sum:(NSString *)key value:(int)value;

-(void)sum:(NSString *)key name:(NSString *)name value:(int)value;

-(void)avg:(NSString *)key name:(NSString *)name extras:(NSArray *)extras value:(int)value;

-(void)count:(NSString *)key name:(NSString *)name extras:(NSArray *)extras;

-(void)sum:(NSString *)key name:(NSString *)name extras:(NSArray *)extras value:(int)value;

@end

#endif /* UPTAFStatistics_h */
