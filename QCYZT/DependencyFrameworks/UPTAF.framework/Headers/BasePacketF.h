// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 4.1.0.0 by WSRD Tencent.
// Generated from `../../../wup-linux-c++/BasePacketF.jce'
// **********************************************************************

#import "JceObject.h"

@interface UPTAFBasePacket : UPTAFJceObject

@property (nonatomic, assign) JceInt16 jce_iVersion;
@property (nonatomic, assign) JceInt8 jce_cPacketType;
@property (nonatomic, assign) JceInt32 jce_iMessageType;
@property (nonatomic, assign) JceInt32 jce_iRequestId;
@property (nonatomic, assign) JceInt32 jce_iRet;
@property (nonatomic, strong) NSString* jce_sServantName;
@property (nonatomic, strong) NSString* jce_sFuncName;
@property (nonatomic, strong) NSData* jce_sBuffer;
@property (nonatomic, assign) JceInt32 jce_iTimeout;
@property (nonatomic, strong) NSString* jce_sResultDesc;
@property (nonatomic, strong) NSDictionary* jce_context;
@property (nonatomic, strong) NSDictionary* jce_status;

@end
