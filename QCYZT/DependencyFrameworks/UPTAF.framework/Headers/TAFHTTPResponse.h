//
//  TAFHTTPResponse.h
//  UPTAF
//
//  Created by sammy<PERSON> on 2020/12/14.
//  Copyright © 2020 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <UPTAF/TAFJSONObject.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPTAFHTTPResponse : NSObject

@property (nonatomic, readonly, assign) int code;
@property (nonatomic, readonly, copy) NSString * contentType;
@property (nonatomic, readonly, assign) long contentLength;

- (instancetype)initWith:(int)code
             contentType:(NSString * _Nullable)contentType
           contentLength:(long)contentLength
                    data:(NSData * _Nullable)data
                   error:(NSError * _Nullable)error;

- (BOOL)isSuccessful;

- (NSError *)error;

- (NSString *)getHeader:(NSString *)name;

- (NSArray *)getHeaders:(NSString *)name;

- (NSDictionary *)allHeaders;

- (NSData *)data;

- (NSString *)string;

- (UPTAFJSONObject *)jsonObject;

- (UPTAFJSONArray *)jsonArray;

@end

NS_ASSUME_NONNULL_END
