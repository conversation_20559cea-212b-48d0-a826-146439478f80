//
//  TAFNetwork.h
//  UPTAFNetwork
//
//  Created by sa<PERSON><PERSON> on 16/9/24.
//  Copyright © 2016年 TAF. All rights reserved.
//

#ifndef UPTAFNetwork_h
#define UPTAFNetwork_h

#import "TAFRequest.h"
#import "TAFResponse.h"

@interface UPTAFNetwork : NSObject

+ (instancetype)sharedInstance;

- (instancetype)initWithPoolSize:(NSUInteger)size;

- (UPTAFResponse *)execute:(UPTAFRequest *)request;

- (void)enqueue:(UPTAFRequest *)request
       callback:(void (^)(UPTAFResponse *))callback;

@end

#endif /* UPTAFNetwork_h */
