//
//  TAFManager.h
//  UPTAFLogin
//
//  Created by sa<PERSON><PERSON> on 16/10/2.
//  Copyright © 2016年 TAF. All rights reserved.
//

#ifndef UPTAFManager_h
#define UPTAFManager_h

#import <Foundation/Foundation.h>
#import <UPTAF/TAFAddress.h>

extern const int kUPTAFAddressTypeWUP;
extern const int kUPTAFAddressTypePush;
extern const int kUPTAFAddressTypeTrade;

extern const int kUPTAFEnvProduction;
extern const int kUPTAFEnvPre;
extern const int kUPTAFEnvTest;

extern NSString * const kUPTAFNotificationGUIDChanged;
extern NSString * const kUPTAFNotificationTokenChanged;
extern NSString * const kUPTAFNotificationAddressUpdated;

@interface UPTAFManager : NSObject

+ (void)start;

+ (void)setExtraID:(NSString *)key value:(NSString *)value;

+ (NSData *)GUID;

+ (NSString *)GUIDString;

+ (NSString *)XUA;

+ (void)setRetSimpleXua:(BOOL)simple;

+ (NSString *)SimpleXUA;

+ (NSString *)ModuleName;

+ (BOOL)isEncrypt;

+ (NSData *)Token;

+ (NSString *)TokenString;

+ (NSString *)Channel;

+ (NSString *)OriginChannel;

+ (NSString *)publicIP;

+ (NSString *)getMappedServant:(NSString *)originServant;

+ (NSString *)getAddressByType:(int)type;

+ (NSArray<UPTAFAddress *>*)getAllAddressByType:(int)type;

+ (NSString *)getAddressByServant:(int)type servant:(NSString *)servant;

+ (void)addressFailed:(int)type address:(NSString *)address;

+ (void)setTestEnv:(BOOL)isTest;

+ (BOOL)isTestEnv;

+ (void)setEnv:(int)env;

+ (int)getEnv;

+ (void)setSpecifiedWUPAddress:(NSString *)address;

+ (NSString *)getSpecifiedWUPAddress;

@end

#endif /* UPTAFManager_h */
