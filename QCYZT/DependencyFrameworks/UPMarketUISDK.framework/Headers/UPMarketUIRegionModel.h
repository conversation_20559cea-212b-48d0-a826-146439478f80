//
//  UPMarketUIRegionModel.h
//  UPMarketUISDK
//
//  Created by fang on 2021/8/25.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, UPMarketUIRegionLongPressType) {
    UPMarketUIRegionLongPressTypeNone,
    UPMarketUIRegionLongPressTypePan,
    UPMarketUIRegionLongPressTypeChangeLeft,
    UPMarketUIRegionLongPressTypeChangeRight,
};

FOUNDATION_EXPORT NSInteger const kMinRegionNumber;

@interface UPMarketUIRegionModel : NSObject

@property (nonatomic, assign, readonly) NSInteger regionStartIndex,regionEndIndex;

@property (nonatomic, assign) NSInteger regionCount;

@property (nonatomic, assign) UPMarketUIRegionLongPressType longPressType;

@property (nonatomic, assign) CGFloat leftIconY,rightIconY;

@property (nonatomic, assign) CGFloat leftOffsetY,rightOffsetY;

@property (nonatomic, strong, readonly) UIImage *exitImage, *moveImage;

- (instancetype)initWithHeight:(CGFloat)height;

- (void)updateWithStartIndex:(NSInteger)displayStart endIndex:(NSInteger)displayEnd;

- (void)updateWithRegionStartIndex:(NSInteger)regionStart endIndex:(NSInteger)regionEnd;

@end

NS_ASSUME_NONNULL_END
