//
//  UPMarketUIMinuteBaseLayer.h
//  UPMarketUISDK
//
//  Created by fang on 2020/2/19.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPMarketUISDK/UPMarketUISDK.h>

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT NSInteger const kAuctionStartMinute;
FOUNDATION_EXPORT NSInteger const kAuctionEndMinute;

@interface UPMarketUIMinuteBaseLayer : UPMarketUIBaseStockLayer

@property (nonatomic, strong) UPMarketAuctionData *auctionData;

@property (assign, nonatomic, readonly) BOOL shouldShowAuction;

@property (nonatomic, assign) UPMarketUIMinuteJHJJState jhjjState;

@property (nonatomic, assign) long maxAuctionVolumn;

// 从当日00:00起经过的分钟数，如570->9:30，690->11:30
@property (nonatomic, strong) NSArray<NSArray<NSNumber *>*> *tradeTimeArray;
@property (nonatomic, assign) NSInteger totalTradeMinutes;

//当日或五日
@property (nonatomic, assign) UPMarketUIMinuteDataType dataType;

@property (nonatomic, strong) NSArray<NSArray *> *extraDashLineArray;

@property (nonatomic, strong, readonly) NSDictionary<NSNumber *, NSString *> *type2KeyDic;

//显示简版的时间区域(仅显示开始时间与结束时间)
@property (nonatomic, assign) BOOL showSimpleTimeRect;

//显示昨收
@property (nonatomic, assign) BOOL showYClosePrice;

//绘制买卖力道
@property (nonatomic, assign) BOOL showIndexMMLD;

- (CGFloat)auctionWidth;

- (void)drawAuctionView:(CGContextRef)ctx auctionWidth:(CGFloat)auctionWidth unitHeight:(CGFloat)unitHeight;
@end

NS_ASSUME_NONNULL_END
