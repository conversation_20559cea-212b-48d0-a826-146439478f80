//
//  UPMarketUIKlineBaseLayer.h
//  UPMarketUISDK
//
//  Created by fang on 2020/3/23.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPMarketUISDK/UPMarketUISDK.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, UPMarketUIKlineDisplayIndexType) {
    UPMarketUIKlineDisplayIndexTypeLarge = 3002,
    UPMarketUIKlineDisplayIndexTypeSmall,
    UPMarketUIKlineDisplayIndexTypeMoveLeft,
    UPMarketUIKlineDisplayIndexTypeMoveRight
};

@interface UPMarketUIKlineBaseLayer : UPMarketUIBaseStockLayer

@property (nonatomic, assign) UPMarketUIKlineDataType klineType;

@property (nonatomic, strong) NSArray<NSArray *> *extraDashLineArray;

/*
* 子类使用这三个指标进行蜡烛图的绘制
* itemMargin：蜡烛图左右间距之和，使用时常除2
* 配合itemWidth使用，蜡烛图实际部分默认宽度为：
* defaultWidth(4.25)=self.itemWidth(6.25=375/60)-self.itemMargin(2)
*/
@property (nonatomic, assign) NSInteger displayStartIndex;
@property (nonatomic, assign) NSInteger displayEndIndex;
@property (nonatomic, assign, readonly) CGFloat itemMargin;
@property (nonatomic, assign, readonly) BOOL canLoadingMore;
@property (nonatomic, assign, readonly) BOOL isScrolling;
@property (nonatomic, assign) BOOL isInnerRegion;

@property (nonatomic, strong, readonly) NSDictionary<NSNumber *, NSString *> *type2KeyDic;

- (NSInteger)getCurrentPointIndex:(NSArray *)dataList point:(CGPoint)point;

- (BOOL)setDisplayIndexWithType:(UPMarketUIKlineDisplayIndexType)type;

- (void)stopScroll;

- (void)layerBeforePan;

- (void)layerAfterPan;

- (void)layerBeforeScale;

- (void)layerAfterScale;

@end

NS_ASSUME_NONNULL_END
