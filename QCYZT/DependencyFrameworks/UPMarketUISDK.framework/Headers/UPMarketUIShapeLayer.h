//
//  UPShapeLayer.h
//  UPMarketUISDK
//
//  Created by fang on 2020/2/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

#define UPShapeLayerDefaultDuration 0.35

/**
 基于CALayer的绘图基类
 */
@interface UPMarketUIShapeLayer : CAShapeLayer

@property (nonatomic, assign) BOOL animate;

+ (instancetype)shapeLayer;

/**
 画折线的方法

 @param pointArray 折线位置数组
 @param strokeColor 折线颜色
 @param lineWidth 线宽
 */
- (void)drawLineWithPointArray:(NSArray *)pointArray strokeColor:(UIColor *)strokeColor lineWidth:(CGFloat)lineWidth;

/**
 画间断折线的方法
 
 @param pointlistArray 装着折线数组的数组
 @param strokeColor 折线颜色
 @param lineWidth 线宽
 */
- (void)drawBreakLineWithPointArray:(NSArray<NSArray *> *)pointlistArray strokeColor:(UIColor *)strokeColor lineWidth:(CGFloat)lineWidth;

/**
 画直方图的方法

 @param pointsArray 直方图坐标数组(每一条直方图包含起始点和结束点) NSArray<NSArray<startPoint,endPoint> *> *)
 @param strokeColor 直方图颜色
 @param lineWidth 线宽
 */
- (void)drawHistogramWithPointsArray:(NSArray<NSArray *> *)pointsArray strokeColor:(UIColor *)strokeColor lineWidth:(CGFloat)lineWidth;

/**
 画空心直方图的方法
 
 @param pointsArray 直方图坐标数组(每一条直方图包含起始点和结束点) NSArray<NSArray<startPoint,endPoint> *> *)
 @param strokeColor 直方图边线颜色
 @param rectWidth 矩形宽
 */
- (void)drawHollowHistogramWithPointsArray:(NSArray<NSArray *> *)pointsArray strokeColor:(UIColor *)strokeColor rectWidth:(CGFloat)rectWidth;

/**
 画圆的方法
 
 @param center 圆中心点
 @param radius 圆半径
 @param startAngle 起始角度
 @param endAngle 结束角度
 @param clockwise 绘图方向
 @param strokeColor 笔划颜色
 @param lineWidth 线宽
 */
- (void)drawArcWithArcCenter:(CGPoint)center radius:(CGFloat)radius startAngle:(CGFloat)startAngle endAngle:(CGFloat)endAngle clockwise:(BOOL)clockwise strokeColor:(UIColor *)strokeColor lineWidth:(CGFloat)lineWidth;


/**
 画圆点组的方法
 
 @param pointArray 圆点位置数组
 @param fillColor 点颜色
 @param radius 圆半径
 */
- (void)drawArcArrayWithPointArray:(NSArray *)pointArray fillColor:(UIColor *)fillColor radius:(CGFloat)radius;

/**
 画颜色填充多边形的方法

 @param pointsArray 多边形坐标数组(包含起始点和结束点(n个)) NSArray<NSArray<Point1,Point2...> *> *)
 @param fillColor 填充颜色
 @param lineWidth 线宽
 */
- (void)drawPolygonWithPointsArray:(NSArray<NSArray *> *)pointsArray fillColor:(UIColor *)fillColor lineWidth:(CGFloat)lineWidth;

@end
