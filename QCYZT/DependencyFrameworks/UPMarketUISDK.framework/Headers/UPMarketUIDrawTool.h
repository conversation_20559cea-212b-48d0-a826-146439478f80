//
//  UPDrawTool.h
//  UPBaseUI
//
//  Created by <PERSON> on 17/3/30.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 基于CGContext绘图
 */
@interface UPMarketUIDrawTool : NSObject

typedef enum  {
    UPMarketUIDrawGradientTypeTopToBottom = 0,//从上到小
    UPMarketUIDrawGradientTypeLeftToRight = 1,//从左到右
    UPMarketUIDrawGradientTypeUpleftTolowRight = 2,//左上到右下
    UPMarketUIDrawGradientTypeUprightTolowLeft = 3,//右上到左下
}UPMarketUIDrawGradientType;


/**
 传入数组计算能放下的最大字号
 
 @param stringArray 字符串数组
 @param OriginalFontSize 原始字体大小
 @param startX 起始X
 @param maxX 最大的X
 */
+ (void)fontSizeStrings:(NSArray *)stringArray WithTiledStyleOriginalFontSize:(CGFloat)OriginalFontSize startX:(CGFloat)startX maxX:(CGFloat)maxX finishBlock:(void (^)(CGFloat fontSize,CGFloat margin))finish;


/**
 计算文字rect

 @param string 字符串
 @param attribute 样式
 @return 计算好的rect
 */
+ (CGRect)rectOfNSString:(NSString *)string attribute:(NSDictionary *)attribute;
/**
 计算文字rect
 
 @param string 字符串
 @param fontSize 字体大小
 @return 计算好的rect
 */
+ (CGRect)rectOfNSString:(NSString *)string fontSize:(CGFloat)fontSize;

/**
 计算等宽字体rect

 @param string 字符串
 @param fontSize 字体大小
 @return 计算好的rect
 */
+ (CGRect)rectOfDigitalNSString:(NSString *)string fontSize:(CGFloat)fontSize;

/**
 绘画等宽字体

 @param text 字符串
 @param fontSize 字体大小
 @param color 颜色
 @param x x坐标
 @param y y坐标
 @return 绘制的坐标
 */
+ (CGRect)drawDigitalWithTextString:(NSString *)text fontSize:(CGFloat)fontSize color:(UIColor *)color x:(CGFloat)x y:(CGFloat)y;

/**
 绘画文字

 @param text 字符串
 @param fontSize 字体大小
 @param color 颜色
 @param x x坐标
 @param y y坐标
 @return 绘制的坐标
 */
+ (CGRect)drawTextWithTextString:(NSString *)text fontSize:(CGFloat)fontSize color:(UIColor*)color x:(CGFloat)x y:(CGFloat)y;

/**
绘画等宽字体

@param text 字符串
@param fontSize 字体大小
@param color 颜色
@param centerPoint 中心点坐标
@return 绘制的坐标
*/
+ (CGRect)drawDigitalWithTextString:(NSString *)text fontSize:(CGFloat)fontSize color:(UIColor *)color centerPoint:(CGPoint)centerPoint;
/**
 绘画文字
 
 @param text 字符串
 @param fontSize 字体大小
 @param color 颜色
 @param centerPoint 中心点坐标
 @return 绘制的坐标
 */
+ (CGRect)drawTextWithTextString:(NSString *)text fontSize:(CGFloat)fontSize color:(UIColor*)color centerPoint:(CGPoint)centerPoint;

/**
 画直方图的方法
 
 @param pointsArray 直方图坐标数组(每一条直方图包含起始点和结束点) NSArray<NSArray<startPoint,endPoint> *> *)
 @param strokeColor 直方图颜色
 @param lineWidth 线宽
 */
+ (void)drawHistogramWithPointsArray:(NSArray<NSArray *> *)pointsArray strokeColor:(UIColor *)strokeColor lineWidth:(CGFloat)lineWidth;
/**
 绘线

 @param startPoint 起始点
 @param endPoint 终点
 @param lineWidth 线宽
 @param color 颜色
 */
+ (void)drawLineWithStartPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint lineWidth:(CGFloat)lineWidth lineColor:(UIColor *)color;
/**
 绘折线
 
 @param pointArray 折线数组(CGPoint)
 @param lineWidth 线宽
 @param color 颜色
 */
+ (void)drawLineWithArray:(NSArray *)pointArray lineWidth:(CGFloat)lineWidth lineColor:(UIColor *)color;
//带阴影
+ (void)drawLineWithShadowArray:(NSArray *)pointArray lineWidth:(CGFloat)lineWidth lineColor:(UIColor *)color;
/**
 绘虚线
 
 @param startPoint 起始点
 @param endPoint 终点
 @param lineWidth 线宽
 @param color 颜色
 @param length 每段虚线长度
 @param margin 虚线间间隔
 */
+ (void)drawDashLineWithStartPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint lineWidth:(CGFloat)lineWidth unitLength:(CGFloat)length margin:(CGFloat)margin lineColor:(UIColor *)color;

/**
 绘虚线返回Layer
 
 @param startPoint 起始点
 @param endPoint 终点
 @param lineWidth 线宽
 @param color 颜色
 @param length 每段虚线长度
 @param margin 虚线间间隔
 */
+ (CAShapeLayer *)drawDashLineLayerWithStartPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint lineWidth:(CGFloat)lineWidth unitLength:(CGFloat)length margin:(CGFloat)margin lineColor:(UIColor *)color;

/**
 绘制圆点

 @param centerPoint 中心点
 @param cornerWidth 半径
 @param fillColor 填充色
 */
+ (void)drawPointAtCenterPoint:(CGPoint)centerPoint cornerWidth:(CGFloat)cornerWidth fillColor:(UIColor *)fillColor;

/**
 绘制圆点返回layer

 @param centerPoint 中心点
 @param cornerWidth 半径
 @param fillColor 填充色
 */
+ (CAShapeLayer *)drawPointLayerAtCenterPoint:(CGPoint)centerPoint cornerWidth:(CGFloat)cornerWidth fillColor:(UIColor *)fillColor;

/**
 绘制矩形框

 @param rect 位置大小
 @param strokeColor 边框颜色
 @param fillColor 填空颜色
 @param lineWidth 边框宽度
 */
+ (void)drawRect:(CGRect)rect strokeColor:(UIColor *)strokeColor fillColor:(UIColor *)fillColor lineWidth:(CGFloat)lineWidth;

/**
 绘制圆角矩形框

 @param rect 位置大小
 @param strokeColor 边框颜色
 @param fillColor 填空颜色
 @param lineWidth 边框宽度
 @param radius 圆角半径
 */

+ (void)drawRectWithCornerRadius:(CGRect)rect strokeColor:(UIColor *)strokeColor fillColor:(UIColor *)fillColor lineWidth:(CGFloat)lineWidth cornerRadius:(CGFloat)radius;

/**
 绘制圆角矩形框返回layer

 @param rect 位置大小
 @param strokeColor 边框颜色
 @param fillColor 填空颜色
 @param lineWidth 边框宽度
 @param radius 圆角半径
 */

+ (CAShapeLayer *)drawRectLayerWithCornerRadius:(CGRect)rect strokeColor:(UIColor *)strokeColor fillColor:(UIColor *)fillColor lineWidth:(CGFloat)lineWidth cornerRadius:(CGFloat)radius;

/**
 绘制矩圆形

 @param center 圆点
 @param radius 半径
 @param strokeColor 边框颜色
 @param fillColor 填空颜色
 @param lineWidth 边框宽度
 */
+ (void)drawCircular:(CGPoint)center radius:(CGFloat)radius strokeColor:(UIColor *)strokeColor fillColor:(UIColor *)fillColor lineWidth:(CGFloat)lineWidth;

/**
 绘制渐变图形

 @param colors 渐变颜色数组
 @param gradientType 渐变方向
 @param frame 生成图形大小
 @return 渐变图形
 */
+ (UIImage *)imageFromColors:(NSArray*)colors ByGradientType:(UPMarketUIDrawGradientType)gradientType withFrame:(CGRect)frame;


/**
 绘制渐变

 @param path 渐变path路径
 @param startColor 渐变开始颜色
 @param endColor 渐变结束颜色
 */
+ (void)drawLinearGradientPath:(CGPathRef)path startColor:(UIColor *)startColor endColor:(UIColor *)endColor;

/**
 画颜色填充多边形的方法

 @param pointsArray 多边形坐标数组(包含起始点和结束点(n个)) NSArray<NSArray<Point1,Point2...> *> *)
 @param fillColor 填充颜色
 @param lineWidth 线宽
 */
+ (void)drawPolygonWithPointsArray:(NSArray<NSArray *> *)pointsArray fillColor:(UIColor *)fillColor lineWidth:(CGFloat)lineWidth;


/// 画特殊圆（中空带点）
/// @param centerPoint 中心点
/// @param cornerWidth 圆半径
/// @param pointWidth 中间点半径
/// @param color 边线颜色
+ (void)drawSpecialPointAtCenterPoint:(CGPoint)centerPoint cornerWidth:(CGFloat)cornerWidth pointWidth:(CGFloat)pointWidth color:(UIColor *)color;

@end
