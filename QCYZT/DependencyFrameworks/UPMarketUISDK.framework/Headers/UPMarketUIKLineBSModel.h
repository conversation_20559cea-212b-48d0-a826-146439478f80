//
//  UPMarketUIKLineBSModel.h
//  UPMarketUISDK
//
//  Created by fang on 2020/2/5.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUIKLineBSModel : NSObject

/// @brief 是否融资融券买卖点
@property (nonatomic, assign) BOOL isRZRQ;

/// @brief 成交日期
@property (nonatomic, assign) int dealDate;

/// @brief 买卖类别
/// @discussion B,S,T,
@property (nonatomic, copy) NSString *dealType;

/// @brief 买入成交均价
@property (nonatomic, assign) double buyAvgPrice;

/// @brief 卖出成交均价
@property (nonatomic, assign) double sellAvgPrice;

/// @brief 买入成交总数
@property (nonatomic, assign) double buyNum;

/// @brief 卖出成交总数
@property (nonatomic, assign) double sellNum;

/// @brief 绘制位置-内部使用
@property (nonatomic, assign) CGRect drawRect;



@end

NS_ASSUME_NONNULL_END
