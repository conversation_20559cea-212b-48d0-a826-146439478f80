//
//  UPYunShenStockModel.h
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/5/29.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, UPYunShenSignalType) {
    UPYunShenSignalNONE = 0,
    UPYunShenSignalD,
    UPYunShenSignalK
};

@class UPYunShenStockModel,UPGoldenChannelModel;

@interface UPYunShenModel : NSObject

@property (nonatomic, assign) int code;

@property (nonatomic, assign) int error;

@property (nonatomic, assign) BOOL success;

@property (nonatomic, copy) NSString *message;

@property (nonatomic, strong) NSArray<UPYunShenStockModel *> *result;

@property (nonatomic, copy) NSString *tradeld;

+(UPYunShenStockModel *)modelFromDictionary:(NSDictionary *)dict;

@end

@interface UPYunShenStockModel : NSObject

//更新日期
@property(nonatomic, assign)NSInteger date;

//云参信号(枚举 D,K,NONE)
@property(nonatomic, copy) NSString *signal;

@property (nonatomic, assign) double highPrice;

@property (nonatomic, assign) double lowPrice;

@end

@interface UPGoldenModel : NSObject

@property (nonatomic, assign) int code;

@property (nonatomic, assign) int error;

@property (nonatomic, assign) BOOL success;

@property (nonatomic, copy) NSString *message;

@property (nonatomic, strong) NSArray<UPGoldenChannelModel *> *result;

@property (nonatomic, copy) NSString *tradeld;

+(UPGoldenChannelModel *)modelFromDictionary:(NSDictionary *)dict;

@end

@interface UPGoldenChannelModel : NSObject

//关闭日收盘涨幅
@property(nonatomic, assign) double closeChange;

//关闭日期
@property(nonatomic, copy) NSString *closeDate;

//关闭时收盘价
@property(nonatomic, assign) double closePrice;

//股票代码
@property(nonatomic, copy) NSString *code;

//期间最高收益
@property(nonatomic, assign) double maxProfit;

//股票名称
@property(nonatomic, copy) NSString *name;

//开启日收盘涨幅
@property(nonatomic, assign) double openChange;

//开启日期
@property(nonatomic, copy) NSString *openDate;

//通道已开启交易日数
@property(nonatomic, assign) NSInteger openDays;

//开启时收盘价
@property(nonatomic, assign) double openPrice;

//涨幅
@property(nonatomic, assign) double priceChange;

@end

NS_ASSUME_NONNULL_END
