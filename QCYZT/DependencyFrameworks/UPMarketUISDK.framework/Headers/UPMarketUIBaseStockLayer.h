//
//  UPMarketUIBaseStockLayer.h
//  UPMarketUISDK
//
//  Created by sammy<PERSON> on 2020/2/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UPMarketUISDK/UPMarketUISelectedModel.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 个股行情图基类
 * 主要实现的功能：
 * 1. 通过View调用手势方法，让layer响应事件
 * 2. 十字星的实现
 * 3. 个股的基本属性
 * 4. 布局的基本属性
 * 5. ...
 */

typedef NS_ENUM(NSUInteger, UPMarketCrossLineState){
    UPMarketCrossLineStateEnd = 0,      // 退出
    UPMarketCrossLineStateBegin,        // 开始
    UPMarketCrossLineStateUp,           // 手指抬起
};

typedef NS_ENUM(NSUInteger, UPMarketUIMoveCrossLineType) {
    UPMarketUIMoveCrossLineTypeLeft,
    UPMarketUIMoveCrossLineTypeRight,
};

@class UPMarketUIBaseStockLayer;
@class UPMarketUIRegionModel;

// MARK:UPMarketUIBaseStockLayerDelegate

@protocol UPMarketUIBaseStockLayerDelegate <NSObject>
@optional

-(void)stockLayer:(UPMarketUIBaseStockLayer *)stockLayer didClickHistoryMinute:(BOOL)iskline;

-(void)stockLayerDidClickLockLayer:(UPMarketUIBaseStockLayer *)stockLayer;

-(void)stockLayerDidClickLockLayerUserInteractionRect:(UPMarketUIBaseStockLayer *)stockLayer;

-(void)stockLayer:(UPMarketUIBaseStockLayer *)stockLayer didSelectData:(NSArray<UPMarketUISelectedModel *> *)data;

-(void)stockLayer:(UPMarketUIBaseStockLayer *)stockLayer crossState:(UPMarketCrossLineState)state;

-(void)stockLayer:(UPMarketUIBaseStockLayer *)stockLayer displayIndexChange:(NSInteger)startIndex end:(NSInteger)endIndex scale:(CGFloat)scale;

- (void)stockLayerPanToEnd:(UPMarketUIBaseStockLayer *)stockLayer;

- (BOOL)stockLayerIsLandscape:(UPMarketUIBaseStockLayer *)stockLayer;

- (UIView *)associatedGestureViewWithStockLayer:(UPMarketUIBaseStockLayer *)stockLayer;

- (void)stockLayerExitRegion:(UPMarketUIBaseStockLayer *)stockLayer error:(NSError *)error;

- (void)stockLayer:(UPMarketUIBaseStockLayer *)stockLayer didSelectRegionData:(NSArray *)data regionModel:(UPMarketUIRegionModel *)model;

- (void)stockLayer:(UPMarketUIBaseStockLayer *)stockLayer isInnerRegion:(BOOL)isInnerRegion;

- (void)stockLayer:(UPMarketUIBaseStockLayer *)stockLayer didClickQXModel:(id)model;

@end

@interface UPMarketUIBaseStockLayer<__covariant T> : CALayer

// 股票信息
@property (nonatomic, strong) UPMarketUIBaseModel *stockModel;

// 行情数据源
@property (nonatomic, strong) UPHqStockHq *stockHq;

// 转换后的数据源
@property (nonatomic, strong) NSArray<T> *dataList;

// 子类使用较多
@property(nonatomic, strong) NSDictionary * stockDataDict;

// 十字星是否正在显示中
@property (nonatomic, assign, readonly) BOOL isShowCross;

@property (nonatomic, assign, readonly) CGPoint crossLinePoint;

@property (assign, nonatomic) BOOL shouldHideQX;

// 集合竞价时十字星不延伸到集合竞价区域
@property (assign, nonatomic) CGFloat crossLeftMargin;

@property (nonatomic, assign) BOOL historyMinuteNotificationEnable;

@property(nonatomic, weak) id<UPMarketUIBaseStockLayerDelegate> stockLayerDelegate;


@property(nonatomic, weak) UIView * layoutView;

@property (nonatomic, strong, readonly) UIView *associatedView;

//添加
@property (nonatomic, assign) NSInteger indexID;

//最大值
@property(nonatomic, assign) double maxValue;

//最小值
@property(nonatomic, assign) double minValue;

//线与线之间的间距，K线中为蜡烛图所占宽度，包括margin，默认为375/60=6.25
@property (nonatomic, assign) CGFloat itemWidth;

//单位差值代表的像素
@property (nonatomic, assign, readonly) CGFloat unitHeight;

//绘图的区域, 只读
@property (nonatomic, assign) CGRect drawingRect;

//绘制底部时间的区域, 只读
@property (nonatomic, assign) CGRect timeRect;

// 时间区域高度
@property (assign, nonatomic) CGFloat bottomMargin;

@property (nonatomic, assign) CGFloat offsetX;

@property (nonatomic, assign) CGFloat startX;

// layer 是否支持历史分时，当前仅日k在竖屏逻辑下为YES，其它layer 为NO
@property (nonatomic, assign) BOOL supportHistoryMinute;
// layer 是否进入历史分时，layer 从长按进入历史分时后会设置该变量为YES,会影响点击的交互逻辑
@property (nonatomic, assign) BOOL isHistoryMinute;
// layer 是否是历史分时的layer
@property (nonatomic, assign) BOOL isHistoryLayer;

@property (nonatomic, assign) UPMarketCrossLineState crossState;

@property (nonatomic, assign) NSInteger displayKlineNum;

@property (nonatomic, assign, readonly) BOOL isLandscape;

//十字丝跟随主图走势
@property (nonatomic, assign) BOOL isCrossLineTraceMain;

/// stockLayer加锁
@property (nonatomic, assign) BOOL lock;

/// lockLayer宽度
@property (nonatomic, assign) CGFloat lockLayerWidth;

//隐藏底部时间区域
@property (nonatomic, assign) BOOL hideTimeRect;

//隐藏最值的显示
@property (nonatomic, assign) BOOL hideMaxMinDisplay;

@property (assign, nonatomic) CGFloat displayScale;

/** 持仓成本价 */
@property (nonatomic, assign) CGFloat positionPrice;

- (void)stockLayerInit NS_REQUIRES_SUPER;

- (NSSet<NSString *> *)stockLayerDependentDataKeyList;

- (void)stockLayerDrawInContext:(CGContextRef)ctx NS_REQUIRES_SUPER;

- (void)stockLayerDrawMaxMinInContext:(CGContextRef)ctx;

// MARK:手势相关方法，利用View来进行事件响应
- (BOOL)stockLayerOnGestureTap:(UIGestureRecognizerState)state point:(CGPoint)point stop:(BOOL *)stop;
- (BOOL)stockLayerOnGestureLongPress:(UIGestureRecognizerState)state point:(CGPoint)point;
- (BOOL)stockLayerOnGesturePan:(UIGestureRecognizerState)state point:(CGPoint)point translation:(CGPoint)translation velocity:(CGPoint)velocity;
- (BOOL)stockLayerOnGestureSwipe:(UIGestureRecognizerState)state direction:(UISwipeGestureRecognizerDirection)direction;
- (BOOL)stockLayerOnGesturePinch:(UIPinchGestureRecognizer *)recognizer;


- (void)stockLayerDidSelectData:(id)data;

- (NSInteger)getCurrentIndex:(NSArray *)dataList;

- (NSInteger)getTouchIndex;

- (void)setDisplayIndex:(NSInteger)start end:(NSInteger)end;

- (void)setDisplayIndex:(NSInteger)start end:(NSInteger)end scale:(CGFloat)scale;

//返回索引对应的数据;长按返回当前索引对应数据,非长按返回最新索引对应的数据
- (id)getCurrentData:(NSArray<T> *)dataList;

//根据当前长按的 y 坐标返回绘制的文本;子类自己实现
- (NSString *)getCrossYText:(CGFloat)y;
- (NSString *)getCrossXText;

- (NSArray<NSNumber *>*)getShowYAndYWithTouchPoint:(CGPoint)touchPoint;

- (void)updateCrossLineWithPoint:(CGPoint)point;

//返回绘制的文本;子类自己实现
- (NSArray <UPMarketUISelectedModel *> *)selectedData;

- (void)notifyDisplayIndexChanged;

- (void)calculateMaxMin;

- (void)exitCrossState;

- (void)clearData;

- (void)moveCrossLine:(UPMarketUIMoveCrossLineType)type;

- (void)setDisplayIndexWithCrossLineType:(UPMarketUIMoveCrossLineType)type;

- (NSArray *)updateDDEDataList:(NSArray<UPMarketDDEInfo *> *)ddeDataList targetCount:(NSInteger)targetCount;

- (void)lockDataIfNecessary:(NSInteger)lockSize;

// 处理展示之前的一些初始化逻辑
- (void)transactionBeforeViewWiilAppear;
@end

@interface UPMarketUIBaseStockLayer (UPMarketUIDebugging)

@property(nonatomic, copy, nullable) NSString *upmarketui_debugKey;

@end

@interface UPMarketUIBaseStockLayer (UPMarketUIRegion)

FOUNDATION_EXPORT NSString * const UPMarketUIRegionNotification;

@property (nonatomic, assign) BOOL isRegion;

- (void)showRegion;

- (void)showRegionWithStartIndex:(NSInteger)startIndex endIndex:(NSInteger)endIndex;

- (void)endRegion;

@end

NS_ASSUME_NONNULL_END
