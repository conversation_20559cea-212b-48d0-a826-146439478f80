//
//  NSString+UPTimeFormat.h
//  UPMarket
//
//  Created by <PERSON> on 2017/5/17.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface NSString (UPMarketUITimeFormat)

/**
 格式化HHmmss为NSString
 
 @return 格式化字符串
 */
+ (NSString *)up_formatHHmmssTime:(NSInteger)time;

/**
 格式化YYYYMMDD为NSString
 @param time 格式为YYYYMMDD
 
 @return MM-DD格式化字符串
 */
+ (NSString *)up_formatMMDDTime:(NSInteger)time;

/**
 格式化YYYYMMDD为NSString
 @param time 格式为YYYYMMDD
 
 @return YYYY-MM-DD格式化字符串
 */
+ (NSString *)up_formatYYYYMMDDTime:(NSInteger)time;

/**
格式化MMDDHHMM为NSString
@param time 格式为MMDDHHMM

@return MM-DD HH:mm格式化字符串
*/
+ (NSString *)up_formatMMDDHHmmTime:(NSInteger)time minute:(NSInteger)minutes;

@end
