//
//  UPMarketUIFactory.h
//  UPMarketUISDK
//
//  Created by fang on 2020/3/5.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPMarketUISDK/UPMarketUISDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUIFactory : NSObject

+ (UPMarketUIBaseStockLayer *)createLayerWithIndexID:(NSInteger)indexID isKline:(BOOL)isKline settingDic:(NSDictionary *)settingDic;

+ (UPMarketUIBaseStockLayer *)createLayerWithIndexID:(NSInteger)indexID isKline:(BOOL)isKline settingDic:(NSDictionary *)settingDic classLayer:(Class)classLayer;

+ (void)configLayer:(UPMarketUIBaseStockLayer *)layer fetcherKey:(NSString *)fetcherKey;

+ (NSSet<UPMarketUIBaseStockDataFetcher *> *)createFetcherWithKeys:(NSArray<NSString *> *)keys fetcherKey:(NSString *)fetcherKey indexID:(NSInteger)indexID settingDic:(NSDictionary *)settingDic;

+ (UPMarketUIBaseStockDataFetcher *)createMaskFetcherWithKey:(NSString *)key settingDic:(NSDictionary *)settingDic;

+ (BOOL)isUseDDEDataWithIndexIDs:(NSArray *)indexIDs;

+ (BOOL)isUseDDEDataWithIndexID:(NSInteger)indexID;

+ (BOOL)isUseQXDataWithIndexID:(NSInteger)indexID;

+ (BOOL)isUseStockIndexDataWithIndexID:(NSInteger)indexID;

@end

NS_ASSUME_NONNULL_END
