//
//  UPMarketUIDataTool.h
//  UPMarketUISDK
//
//  Created by fang on 2020/2/26.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUIDataTool : NSObject

+ (NSArray<UPHqRTMinData *> *)mergeMinDataWithSourceArray:(NSArray<UPHqRTMinData *> *)sourceArray newArray:(NSArray<UPHqRTMinData *> *)newArray dayNum:(int)dayNum;

+ (NSArray<UPHqAnalyData *> *)mergeDataWithSourceArray:(NSArray<UPHqAnalyData *> *)sourceArray newArray:(NSArray<UPHqAnalyData *> *)newArray;

+ (NSString *)getStateWithTradeStatus:(UPMarketTradeStatus)tradeStatus;

+ (NSArray <UPHqRTMinData *>*)updateMinuteDataByStockHq:(NSArray<UPHqRTMinData *> *)minuteList newData:(UPHqStockHq *)data dayNum:(int)dayNum;

@end

NS_ASSUME_NONNULL_END
