//
//  UPMarketUIDefine.h
//  UPMarketUISDK
//
//  Created by fang on 2020/2/17.
//  Copyright © 2020 UpChina. All rights reserved.
//

#ifndef UPMarketUIDefine_h
#define UPMarketUIDefine_h

// MARK: - layer

// 十字丝字体大小
static CGFloat kUPMarketUIMaskViewTextFont = 11;

// 默认请求的k线数据的数目
static NSInteger kUPMarketUIKLineDefaultReqNum = 500;

// 默认线宽
static const CGFloat kDefaultLineWidth = 1.5f;

// 时分线的宽度
static const CGFloat kMarketUITimeLineWidth = 1;

// 集合竞价线的宽度
static const CGFloat kMarketUIAuctionLineWidth = 1;

// 均线的宽度
static const CGFloat kMarketUITimeAvgLineWidth = 1.5f;

// MA线宽度
static const CGFloat kMarketUIMALineWidth = 1.5f;

// K线间距
static const CGFloat kKlineItemMargin = 2;

// 圆点的背景半径
static const CGFloat kUPMarketUIPointBgRadius = 3.0;

// 主图距离左部的距离
static const CGFloat kUPMarketUIMainViewLeftGap = 5;

// K线指标上锁根数
static const CGFloat kUPMarketUIKlineLockNumber = 5;

// 分时指标上锁根数
static const CGFloat kUPMarketUIMinuteLockNumber = 30;

// MARK: - fetcher

static const NSInteger kUPMarketUIMinuteTag = 1000;

static const NSInteger kUPMarketUIKlineTag = 1001;

static const NSInteger kUPMarketUIDDEDataTag = 1002;

static const NSInteger kUPMarketUIQXDataTag = 1003;

static const NSInteger kUPMarketUIAuctionDataTag = 1004;

static const NSInteger kUPMarketUIStockIndexDataTag = 1005;

// MARK: - Enum

typedef NS_ENUM(NSInteger, UPMarketUIKlineDataType) {
    UPMarketUIKlineDataTypeNone = -1,
    UPMarketUIKlineDataTypeDaily,
    UPMarketUIKlineDataTypeWeek,
    UPMarketUIKlineDataTypeMonth,
    UPMarketUIKlineDataTypeMinute1,
    UPMarketUIKlineDataTypeMinute5,
    UPMarketUIKlineDataTypeMinute15,
    UPMarketUIKlineDataTypeMinute30,
    UPMarketUIKlineDataTypeMinute60,
    UPMarketUIKlineDataTypeMinute120,
    UPMarketUIKlineDataTypeSeason,
    UPMarketUIKlineDataTypeYear,
};

typedef NS_ENUM(NSUInteger, UPMarketUIMinuteDataType) {
    UPMarketUIMinuteDataTypeNone        = 0,
    UPMarketUIMinuteDataTypeSingle      = 1,
    UPMarketUIMinuteDataTypeFiveDay     = 5
};

typedef NS_ENUM(NSUInteger, UPMarketUIMajorIndex) {
    // 主图
    UPMarketUIMajorIndexTraditional = 3000,           //传统模式
    UPMarketUIMajorIndexMALine   = 4000,              //MA均线
    UPMarketUIMajorIndexBOLL = 4005,                  // 主图布林线
    UPMarketUIMajorIndexCustom,                       // 自定义
};

typedef NS_ENUM(NSUInteger, UPMarketUIMinorIndex) {
    UPMarketUIMinorIndexVOL = 5000,      //成交量
    UPMarketUIMinorIndexMACD,
    UPMarketUIMinorIndexKDJ,
    UPMarketUIMinorIndexRSI,
    UPMarketUIMinorIndexBOLL,            //布林线
    UPMarketUIMinorIndexZJLX,            //资金流向
    UPMarketUIMinorIndexZLJC,            //主力净差
    UPMarketUIMinorIndexYLZC,            //支撑压力
    UPMarketUIMinorIndexFJX,             //风警线
    UPMarketUIMinorIndexZJKP,            //资金控盘
    UPMarketUIMinorIndexBIAS,
    UPMarketUIMinorIndexVR,
    UPMarketUIMinorIndexCR,
    UPMarketUIMinorIndexWR,
    UPMarketUIMinorIndexDMA,
    UPMarketUIMinorIndexOBV,
    UPMarketUIMinorIndexCCI,
    UPMarketUIMinorIndexDDX,
    UPMarketUIMinorIndexDDY,
    UPMarketUIMinorIndexDDZ,
    UPMarketUIMinorIndexZLGZ,          //主力跟踪
    UPMarketUIMinorIndexLB,             // 量比
    UPMarketUIMinorIndexZLZJ,             //主力资金
    UPMarketUIMinorIndexZJBY,              //资金博弈
    UPMarketUIMinorIndexZJDL,              //资金动力
    UPMarketUIMinorIndexZLT0,             // 主力T+0
    UPMarketUIMinorIndexCPXHD,            //操盘信号灯
    UPMarketUIMinorIndexMMLD,             // 买卖力道
    UPMarketUIMinorIndexDDLDJ,             // 顶底雷达机
    UPMarketUIMinorIndexDDCM,             // 短打筹码
    UPMarketUIMinorIndexDDDN,             // 短打动能
    UPMarketUIMinorIndexZLZD,             // 主力阵地
    UPMarketUIMinorIndexXBLD,             // 谐波雷达
    UPMarketUIMinorIndexSAR,             // SAR指标
    UPMarketUIMinorIndexCustom,            // 自定义
};

/// 分时-集合竞价状态
typedef NS_ENUM(NSUInteger, UPMarketUIMinuteJHJJState) {
    UPMarketUIMinuteSmartOn,    // 智能开启
    UPMarketUIMinuteKeepOn,        // 保持开启
    UPMarketUIMinuteKeepOff,    // 保持关闭
};

/// k线-除复权
typedef NS_ENUM(NSUInteger, UPMarketUIKLineCFQState) {
    UPMarketUIKLineCFQStateBefore,        // 前复权
    UPMarketUIKLineCFQStateAfter,        // 后复权
    UPMarketUIKLineCFQStateNone,         // 不复权
};

/// k线-样式
typedef NS_ENUM(NSUInteger, UPMarketUIKLinePattern) {
    UPMarketUIKLinePatternHollow,        // 空心
    UPMarketUIKLinePatternSolid,         // 实心
};


/// 手势
typedef NS_OPTIONS(NSUInteger, UPMarketUIBaseStockViewGesture) {
    UPMarketUIBaseStockViewGestureTap       = 1 << 0,
    UPMarketUIBaseStockViewGestureLongPress = 1 << 1,
    UPMarketUIBaseStockViewGesturePan       = 1 << 2,
    UPMarketUIBaseStockViewGestureSwipe     = 1 << 3,
    UPMarketUIBaseStockViewGesturePinch     = 1 << 4,
};

/// 云参选股指标类型
typedef NS_ENUM(NSUInteger, UPMarketUIKLineYSXGIndexType) {
    UPMarketUIKLineYSXGIndexTypeTwo = 0,   //双指标
    UPMarketUIKLineYSXGIndexTypeKDJ,     //KDJ单指标
    UPMarketUIKLineYSXGIndexTypeMACD,   //MACD单指标
};

#endif /* UPMarketUIDefine_h */
