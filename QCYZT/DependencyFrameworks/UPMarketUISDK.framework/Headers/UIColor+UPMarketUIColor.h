
#import <UIKit/UIKit.h>

@interface UIColor (UPMarketUIColor)

// 股票涨颜色(#D22325)
@property(class, nonatomic, readonly) UIColor * upmarketui_riseColor;

// 股票平颜色(#909090)
@property(class, nonatomic, readonly) UIColor * upmarketui_equalColor;

// 股票跌颜色(#21AB6F)
@property(class, nonatomic, readonly) UIColor * upmarketui_fallColor;

// 图表背景颜色(#FFFFFF->#222531)
@property(class, nonatomic, readonly) UIColor * upmarketui_bgColor;

// layer 文本基本色(#99111111)
@property(class, nonatomic, readonly) UIColor * upmarketui_textBaseColor;

// layer 文本基本色2(#909090)
@property(class, nonatomic, readonly) UIColor * upmarketui_secondaryColor;

// 图表边线颜色(#D8D8D8)
@property(class, nonatomic, readonly) UIColor * upmarketui_borderColor;

// 十字丝矩形框文本颜色(#FEFEFF)
@property(class, nonatomic, readonly) UIColor * upmarketui_crossTextColor;

// 十字丝矩形框边线颜色(#CC2277CC)
@property(class, nonatomic, readonly) UIColor * upmarketui_crossColor;

// 十字丝矩形框填充颜色(#CC2277CC)
@property(class, nonatomic, readonly) UIColor * upmarketui_crossRectColor;

// 十字丝颜色(#333333)
@property(class, nonatomic, readonly) UIColor * upmarketui_axisLineColor;

// 均价线颜色(#FF9D03)
@property(class, nonatomic, readonly) UIColor * upmarketui_averagePriceLineColor;

// 现价线颜色(#2277CC)
@property(class, nonatomic, readonly) UIColor * upmarketui_nowPriceLineColor;

// 昨收颜色(#EA59CA)
@property(class, nonatomic, readonly) UIColor * upmarketui_yClosePriceColor;

// lockView 背景颜色(#F4F4F4)
@property(class, nonatomic, readonly) UIColor * upmarketui_lockViewBGColor;

// 集合竞价背景颜色(#F6F6F6)
@property(class, nonatomic, readonly) UIColor * upmarketui_auctionMaskColor;

// 现价锚点背景色(#4D6395EE)
@property(class, nonatomic, readonly) UIColor * upmarketui_nowPricePointBgColor;

// 现价锚点色(#6395EE)
@property(class, nonatomic, readonly) UIColor * upmarketui_nowPricePointColor;

// 分时图渐变开始颜色(#332277CC)
@property(class, nonatomic, readonly) UIColor * upmarketui_nowPriceShaderStartColor;

// 分时图渐变结束颜色(#00FFFFFF)
@property(class, nonatomic, readonly) UIColor * upmarketui_nowPriceShaderEndColor;

// MA1 颜色(#6395EE)
@property(class, nonatomic, readonly) UIColor * upmarketui_ma1Color;

// MA2 颜色(#FF9850)
@property(class, nonatomic, readonly) UIColor * upmarketui_ma2Color;

// MA3 颜色(#E051B6)
@property(class, nonatomic, readonly) UIColor * upmarketui_ma3Color;

// MA4 颜色(#19B2B0)
@property(class, nonatomic, readonly) UIColor * upmarketui_ma4Color;

// MA5 颜色(#19B2B0)
@property(class, nonatomic, readonly) UIColor * upmarketui_ma5Color;

// MA6 颜色(#E051B6)
@property(class, nonatomic, readonly) UIColor * upmarketui_ma6Color;

// MA7 颜色(#2277CC)
@property(class, nonatomic, readonly) UIColor * upmarketui_ma7Color;

// MA8 颜色(#ff0000)
@property(class, nonatomic, readonly) UIColor * upmarketui_ma8Color;

// 乾坤线 颜色(#FF9850)
@property(class, nonatomic, readonly) UIColor * upmarketui_qkColor;

// 多线颜色(#E051B6)
@property(class, nonatomic, readonly) UIColor * upmarketui_duoColor;

// 空线颜色(#5D52FC)
@property(class, nonatomic, readonly) UIColor * upmarketui_kongColor;

// macd 颜色(#E94D2E)
@property(class, nonatomic, readonly) UIColor * upmarketui_macdColor;

// dif 颜色(#6395EE)
@property(class, nonatomic, readonly) UIColor * upmarketui_difColor;

// dea 颜色(#FF9850)
@property(class, nonatomic, readonly) UIColor * upmarketui_deaColor;

// dd1 颜色(#E94D2E)
@property(class, nonatomic, readonly) UIColor * upmarketui_dd1Color;

// dd2 颜色(#6395EE)
@property(class, nonatomic, readonly) UIColor * upmarketui_dd2Color;

// dd3 颜色(#FF9850)
@property(class, nonatomic, readonly) UIColor * upmarketui_dd3Color;

// dd4 颜色(#E051B6)
@property(class, nonatomic, readonly) UIColor * upmarketui_dd4Color;

// dd5 颜色(#1F6AFF)
@property(class, nonatomic, readonly) UIColor * upmarketui_dd5Color;

// 分时量比颜色(#6395EE)
@property(class, nonatomic, readonly) UIColor * upmarketui_lbColor;

// bias1 颜色(#3691E1)
@property(class, nonatomic, readonly) UIColor * upmarketui_bias1Color;

// bias2 颜色(#EF7F21)
@property(class, nonatomic, readonly) UIColor * upmarketui_bias2Color;

// bias3 颜色(#E051B6)
@property(class, nonatomic, readonly) UIColor * upmarketui_bias3Color;

// cr 颜色(#FE1B12)
@property(class, nonatomic, readonly) UIColor * upmarketui_crColor;

// 叠加线颜色(#66111111)
@property(class, nonatomic, readonly) UIColor * upmarketui_maskLineColor;

// 叠加蜡烛图颜色(#B8B8B8)
@property(class, nonatomic, readonly) UIColor * upmarketui_maskCandleColor;

// K线缺口颜色(#D8D8D8)
@property(class, nonatomic, readonly) UIColor * upmarketui_kLineGapColor;

// 持仓成本线颜色(#FECD86)
@property(class, nonatomic, readonly) UIColor * upmarketui_cccbxColor;

// 买卖点B颜色(#E63131)
@property(class, nonatomic, readonly) UIColor * upmarketui_mmdBColor;
// 买卖点S颜色(#2277CC)
@property(class, nonatomic, readonly) UIColor * upmarketui_mmdSColor;
// 买卖点T颜色(#FF9D03)
@property(class, nonatomic, readonly) UIColor * upmarketui_mmdTColor;

// 两融买卖点B颜色(#FF437B)
@property(class, nonatomic, readonly) UIColor * upmarketui_mmdRZRQBColor;
// 两融买卖点B颜色(#229DCC)
@property(class, nonatomic, readonly) UIColor * upmarketui_mmdRZRQSColor;
// 两融买卖点T颜色(#F2B960)
@property(class, nonatomic, readonly) UIColor * upmarketui_mmdRZRQTColor;
// 买卖点选中浮层背景颜色(#********)
@property(class, nonatomic, readonly) UIColor * upmarketui_mmdSelectViewBgColor;

// 区间统计背景色(#332277CC)
@property(class, nonatomic, readonly) UIColor * upmarketui_regionBgColor;
// 区间统计边线色(#662277CC)
@property(class, nonatomic, readonly) UIColor * upmarketui_regionBorderColor;

// 除权除息文案颜色(#FF9D03)
@property(class, nonatomic, readonly) UIColor * upmarketui_cqcxTextColor;

// 风警线最低颜色（#20D51A）
@property(class, nonatomic, readonly) UIColor * upmarketui_fjxlColor;
// 风警线最高颜色（#FF421D）
@property(class, nonatomic, readonly) UIColor * upmarketui_fjxhColor;
// 风警线中间颜色（#00D9FF）
@property(class, nonatomic, readonly) UIColor * upmarketui_fjxbdColor;
// 风警线颜色（#A1A1A1）
@property(class, nonatomic, readonly) UIColor * upmarketui_fjxzdxColor;
// 风警线颜色（#FFC000）
@property(class, nonatomic, readonly) UIColor * upmarketui_fjxhlyjColor;

// 主力T0颜色（#F18308）
@property(class, nonatomic, readonly) UIColor * upmarketui_zlt0bColor;
// 主力T0颜色（#426DD0）
@property(class, nonatomic, readonly) UIColor * upmarketui_zlt0sColor;

/// 高度控盘颜色（#F54646）
@property(class, nonatomic, readonly) UIColor * upmarketui_gdkpColor;
/// 平衡控盘颜色（#FFC000）
@property(class, nonatomic, readonly) UIColor * upmarketui_phkpColor;
/// 无序控盘颜色（#27B666）
@property(class, nonatomic, readonly) UIColor * upmarketui_wxkpColor;

/// 资金波段颜色（#F825A2）
@property(class, nonatomic, readonly) UIColor * upmarketui_zjbdColor;

/// 九转序列颜色
//（#CC111111）
@property(class, nonatomic, readonly) UIColor * upmarketui_jzxlColor;
//（#FFD40000）
@property(class, nonatomic, readonly) UIColor * upmarketui_jzxl9textbelowColor;
//（#007D3E）
@property(class, nonatomic, readonly) UIColor * upmarketui_jzxl9textupColor;
//（#44F54646）
@property(class, nonatomic, readonly) UIColor * upmarketui_jzxl9strokebelowColor;
//（#4427B666）
@property(class, nonatomic, readonly) UIColor * upmarketui_jzxl9strokeupColor;
//（#33F54646）
@property(class, nonatomic, readonly) UIColor * upmarketui_jzxl9fillbelowColor;
//（#3327B666）
@property(class, nonatomic, readonly) UIColor * upmarketui_jzxl9fillupColor;

@end
