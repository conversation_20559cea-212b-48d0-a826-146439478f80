//
//  UPCategories.h
//  UPBase
//
//  Created by sa<PERSON><PERSON> on 2018/12/11.
//  Copyright © 2018 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <UPTAF/TAFJSONObject.h>

NS_ASSUME_NONNULL_BEGIN

// MARK: - NSData

@interface NSData (UPCategory)

/*
 * to hex字符串
 */
- (NSString *)up_toHexString;

- (NSString *)up_toString;

/*
 * 转换成UPTAFJSONObject
 */
- (UPTAFJSONObject *)up_toJSONObject;

/*
 * 转换成UPTAFJSONArray
 */
- (UPTAFJSONArray *)up_toJSONArray;

@end

// MARK: - NSArray

@interface NSArray (UPCategory)

-(NSString *)up_toJSONString;

@end

// MARK: - NSDictionary

@interface NSDictionary (UPCategory)

-(NSString *)up_toJSONString;

@end

// MARK: - NSString

@interface NSString (UPCategory)

/*
 * 字符串对比, 两个字符串都为nil的话, 也算相等
 */
+ (BOOL)up_isEqual:(NSString *)a to:(NSString *)b;

/*
 * 字符串对比, 忽略大小写, 两个字符串都为nil的话, 也算相等
 */
+ (BOOL)up_isEqualIgnoreCase:(NSString *)a to:(NSString *)b;

/*
 * 去除所有换行和空格
 */
- (NSString *)up_trimAllWhitespaceAndNewline;

/*
 * 去除所有换行
 */
- (NSString *)up_trimAllNewline;

/*
 * 去除头尾空格
 */
- (NSString *)up_trimWhitespace;

/*
 * 去除头尾空格和换行符
 */
- (NSString *)up_trimWhitespaceAndNewline;

/*
 * 去除HTML标签，包含在<>内的都去掉，目前比较粗暴
 */
- (NSString *)up_trimHTMLTag;

/*
 * 转换成UPTAFJSONObject
 */
- (UPTAFJSONObject *)up_toJSONObject;

/*
 * 转换成UPTAFJSONArray
 */
- (UPTAFJSONArray *)up_toJSONArray;

@end

// MARK: UIColor

@interface UIColor (UPCategory)

+ (instancetype)up_colorFromString:(NSString *)colorString;

+ (instancetype)up_colorFromHexString:(NSString *)hexString;

@end

NS_ASSUME_NONNULL_END
