//
//  DESEncryptAndDecrypt.h
//  HQProject
//
//  Created by UP-LiuL on 14/11/27.
//  Copyright (c) 2014年 UP-LiuL. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_OPTIONS(NSUInteger, PaddingMode) {
    PaddingModeNone = 1,
    PaddingModeSpace = 2,
};

@interface DESUtils : NSObject

// MARK: - DES
+ (NSString *)decryptUseDES:(NSString *)cipherText key:(NSString *)key;
+ (NSString *)decryptUseDES:(NSString *)cipherText key:(NSString *)key iv:(NSString *)iv;
+ (NSString *)encryptUseDES:(NSString *)plainText key:(NSString *)key;
+ (NSString *)DESEncrypt:(NSData *)data WithKey:(NSString *)key;
+ (NSData *)DESDecrypt:(NSData *)data WithKey:(NSString *)key;

// MARK: - AES
+ (NSString *)decryptUseAES:(NSString *)cipherText key:(NSString *)key;
+ (NSData *)decryptUseAES:(NSData *)cipher key:(NSString *)key iv:(NSString *)iv;
+ (NSString *)encryptUseAES:(NSString *)plainText key:(NSString *)key paddingMode:(PaddingMode)paddingMode;
+ (NSString *)encryptDataUseAES:(NSData *)plainData key:(NSString *)key paddingMode:(PaddingMode)paddingMode;

// MARK: - 3DES
+ (NSString *)encryptUse3DES:(NSString *)plainText key:(NSString *)key;
+ (NSString *)decryptUse3DES:(NSString *)cipherText key:(NSString *)key;

@end
