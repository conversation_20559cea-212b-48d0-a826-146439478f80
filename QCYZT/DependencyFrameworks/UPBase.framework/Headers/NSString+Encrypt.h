//
// Created by <PERSON><PERSON> on 2017/6/25.
// Copyright (c) 2017 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

extern const NSString *kUPEncryptKeyUPChina1;
extern const NSString *kUPEncryptKeyUPChina6;
extern const NSString *kUPEncryptKeyUPChina8;

@interface NSString (UPEncrypt)

/*
 * DES加密
 */
- (NSString *)up_desEncryptStringWithKey:(const NSString *)key;

/*
 * DES解密
 */
- (NSString *)up_desDecryptStringWithKey:(const NSString *)key;

/*
 * Blowfish加密
 */
- (NSString *)up_blowfishEncryptWithKey:(const NSString *)key;

/*
 * Blowfish解密
 */
- (NSString *)up_blowfishDecryptWithKey:(const NSString *)key;

/*
 * fclBlowFish加密
 */
- (NSString *)up_fclBlowfishEncryptWithKey:(const NSString *)key;

/*
 * MD5加密
 */
- (NSString *)up_md5EncryptWithKey:(const NSString *)key;

/*
 * base64 加密
 */
- (NSString *)up_toBase64String;

/*
 * base64 转NSData
 */
- (NSData *)up_base64StringToData;

/*
 * base64 转NSString
 */
- (NSString *)up_base64StringToString;

/*
 * 生成MD5字符串
 */
-(NSString *)up_toMD5String;

/*
 * SHA256字符串
 */
-(NSString *)up_toSHA256String;

/*
 * 用户转换一些特殊字符串, 普通的逻辑不要使用这个方法
 */
- (NSString *)up_transformToOrigin;

@end
