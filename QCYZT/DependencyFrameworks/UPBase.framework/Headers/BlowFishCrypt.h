//
//  BlowFishCrypt.h
//  HQProject
//
//  Created by UP-LiuL on 15/4/7.
//  Copyright (c) 2015年 UP-LiuL. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface BlowFishCrypt : NSObject

// BlowFish解密
+ (NSString *)decryptUseBlowFish:(NSString *)cipherText key:(NSString *)key;
+ (NSString *)decryptUseBlowFish2:(NSString *)cipherText key:(NSString *)key;
+ (NSString *)decryptUseBlowFish3:(NSString *)cipherText key:(NSString *)key;
+ (NSString *)decryptUseBlowFish:(NSString *)cipherText key:(NSString *)key iv:(NSString *)iv;
// BlowFish加密
+ (NSString *)encryptUseBlowFish:(NSString *)plainText key:(NSString *)key;
+ (NSString *)encryptUseBlowFish2:(NSString *)plainText key:(NSString *)key;
+ (NSString *)encryptUseBlowFish3:(NSString *)plainText key:(NSString *)key iv:(NSString *)iv;

@end
