//
//  UPHTMLParser.h
//  UPBase
//
//  Created by sammy<PERSON> on 2018/4/3.
//  Copyright © 2018年 sammywu. All rights reserved.
//

#import <Foundation/Foundation.h>

@class UPHTMLParser;

@protocol UPHTMLParserDelegate <NSObject>

@optional

/// 每次图片下载完成之后会调用该方法
/// @param parser parser
/// @param newAttString 完整的富文本对象
/// @param range attachment所处的range
/// @使用：
/// 1. 直接将富文本对象赋值后刷新整个textView/lable；
/// 2.  因为富文本在赋值给textView的过程中，即使发生了copy，但是内部的attachment对象并未发生改变。所以可以直接使用range来调用[textView.layoutManager invalidateLayoutForCharacterRange:range actualCharacterRange:nil];，这样就不需要刷新整个textview
- (void)parser:(UPHTMLParser *)parser attrStringDidChange:(NSAttributedString *)newAttString range:(NSRange)range;

@end

@interface UPHTMLParser : NSObject

// 默认文本字体
@property (nonatomic, assign) CGFloat fontSize;

// 图片最大宽度，默认为屏幕宽度，超过这个宽度，图片会进行等比例缩放
@property (assign, nonatomic) CGFloat maxImgWidth;

// 默认文本颜色
@property (nonatomic, strong) UIColor * textColor;

@property (weak, nonatomic) id<UPHTMLParserDelegate> delegate;

@property (assign, nonatomic) NSInteger tag;

@property (nonatomic, readonly, copy) NSString * string;

@property (nonatomic, readonly, copy) NSAttributedString * attrString;

// 是否开启style解析，默认YES
@property (assign, nonatomic) BOOL styleParseEnable;

/// 通过data来解析，默认编码方式为NSUTF8StringEncoding
+ (instancetype)parserWithData:(NSData *)data;

/// 通过字符串来解析HTML，默认编码方式为NSUTF8StringEncoding
+ (instancetype)parserWithHTML:(NSString *)html;

@end
