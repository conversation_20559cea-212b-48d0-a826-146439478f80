//
// Created by <PERSON><PERSON> on 2017/6/27.
// Copyright (c) 2017 UpChina. All rights reserved.
//

#ifndef UPUserManagerDefine_h
#define UPUserManagerDefine_h

#import "UPUserResponse.h"

#define UPUserCallback(obj) void(^)(UPUserResponse<obj> *response)

typedef void (^UPUserCallback)(UPUserResponse *response);

typedef void (^userRequestHandler)(NSInteger retCode, NSString *message);

typedef void (^upAdvisorOrderHandler)(NSArray *orderArray, NSInteger page, NSError *error);

typedef void (^upOrderCountHandler)(NSInteger count);

typedef void (^iconInfoRequestHandler) (NSInteger retCode, NSString *message, id result);

typedef void (^sendCodeRequestHandler) (NSInteger retCode, NSString *codeId);

/*
 * 验证码请求类型
 * */
typedef NS_ENUM(NSUInteger, UPUserCodeRequestType) {
    UPUserCodeRequestTypeRegister,                      // 注册
    UPUserCodeRequestTypeBind,                          // 绑定手机号
    UPUserCodeRequestTypeModifyBind,                    // 修改手机号
    UPUserCodeRequestTypeFindPassword,                  // 找回密码
};

/*
 * 手机号绑定方式
 * */
typedef NS_ENUM(NSUInteger, UPUserPhoneBindType) {
    UPUserPhoneBindTypeSetup,                           // 首次绑定手机号
    UPUserPhoneBindTypeModify,                          // 更改绑定手机号
};

typedef NS_ENUM(NSUInteger, UPUserInvestPreferenceType) {
    UPUserInvestPreferenceTypeShort,
    UPUserInvestPreferenceTypeTrend,
    UPUserInvestPreferenceTypeValue,
    UPUserInvestPreferenceTypeBalance,
};


// MARK: - User state
#define kUPUserStateLogin           (1 << 0)
#define kUPUserStateLogout          (1 << 1)
#define kUPUserStateTokenExpire     (1 << 2)
#define kUPUserStateTokenChange     (1 << 3)
#define kUPUserInfoUpdate           (1 << 4)
#define kUPUserRightUpdate          (1 << 5)

#define kUPUserRetcodeSuccess  10000                                             // 请求成功

#define kUPUserRetcodeNetError  -90001                                              // 网络请求失败,请检查网络设置
#define kUPUserRetcodeUnknownError  -90002                                          // 未知错误
#define kUPUserRetcodeUnLogin  -90003                                               // 未登录
#define kUPUserRetcodeParamError  -90004                                            // 入参不合法
#define kUPUserNetErrorDescription  @"网络连接失败"                                   // 网络连接失败描述

#define kUPStock_StatusCode_Success 0                                               // 添加备注成功

// MARK: - Error Code

// MARK: Register
#define kUPUserRegisterParamError -1
#define kUPUserNumberRegistered -2
#define kUPUserRegisterServiceError -100


// MARK: - Login

#define kUPUserTokenExpire -1                                                    // 新登录接口：无效token，token过期，无效的客户端类型
#define kUPUserTokenExpireNew -2                                                 // 新登录接口：无效token，token过期，无效的客户端类型
#define kUPUserLoginAccountError -3                                              // 新登录接口：用户不存在或密码不正确
#define kUPUserLoginServiceError -100                                            // 新登录接口：服务异常

// MARK: - NickName

#define kUPUserRetcodeNickNameExists  -1                                         // 该昵称已经存在，请重新输入

// MARK: - Password

#define kUPUserRetcodeUserNotExists  10030                                      // 不存在此用户
#define kUPUserRetCodeNoUserForResetPassword 10012                               // 该用户未绑定优品账号
#define kUPUserRetCodePasswordError 10050                                        // 密码错误

// MARK: - Thirdparty bind

#define kUPUserBindParamError  -1                   //-1: 参数不合法,
#define kUPUserBindUserRegisterError  -2            //-2: 用户名已经被注册
#define kUPUserBindUserExistError  -3               //-3: 用户不存在
#define kUPUserBindUserPWDError  -4                 //-4: 用户密码不匹配
#define kUPUserBindMobileError  -5                  //-5: 用户手机号不匹配
#define kUPUserBindMobileRegisteredError  -6        //-6: 手机号、邮箱等已经被其它用户注册
#define kUPUserBindOpenRegisteredError  -7          //-7: 第三方账号已经被注册
#define kUPUserBindMobileBindError  -8              //-8: 用户已经绑定其它手机、邮箱等
#define kUPUserBindUserBindError  -9                //-9: 该三方用户已经绑定了其它用户名
#define kUPUserBindUnRegisteredError  -10           //-10: 三方账号尚未注册
#define kUPUserBindOpenBindError  -11               //-11: 三方账号已经被其它用户绑定
#define kUPUserBindHadBindedError  -12              //-12：该用户已经绑定过该三方账号
#define kUPUserBindUnBindOpenError  -13             //-13：该用户未绑定过该第三方账号
#define kUPUserBindOnlyBindError  -14               //-14：唯一绑定账号不能解绑
#define kUPUserBindUnbindError  -15                 //-15: 三方账号尚未绑定
#define kUPUserBindRepeatError  -16                 //-16: 三方平台重复绑定
#define kUPUserBindServiceError  -100               //-100: 系统异常

#endif
