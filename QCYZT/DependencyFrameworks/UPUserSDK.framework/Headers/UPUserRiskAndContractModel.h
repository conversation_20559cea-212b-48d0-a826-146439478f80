//
//  UPUserRiskAndContractModel.h
//  UPUserSDK
//
//  Created by yxg on 2022/7/19.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPUserContractInfo : NSObject
/**
 * 权限ID
 */
@property (nonatomic, copy) NSString *rightID;

/**
 * 单个合同链接
 */
@property (nonatomic, copy) NSString *contractUrl;

@end

@interface UPUserRiskAndContractModel : NSObject

/**
 * 0：已风测  -101：未知错误，-102：数据错误，-103：服务异常， -104：暂无测评， -105：参数错误， -106：用户真实信息为空， -107：为用户未签名，-108：重复提交， -109：为测评过期，-110：风险测评更新，需要重测
 * 风测代码
 */
@property (nonatomic, assign) NSInteger riskCode;

/**
 * 风测链接
 */
@property (nonatomic, copy) NSString *riskUrl;

/**
 * 公共合同链接
 */
@property (nonatomic, copy) NSString *contractCommonUrl;

/**
 * 未签合同列表
 */
@property (nonatomic, strong) NSArray<UPUserContractInfo *> *rightIDArray;

@end

NS_ASSUME_NONNULL_END
