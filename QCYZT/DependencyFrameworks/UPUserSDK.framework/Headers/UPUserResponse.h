//
// Created by j<PERSON><PERSON> on 2018/4/23.
// Copyright (c) 2018 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface UPUserResponse<__covariant ObjectType> : NSObject

@property (nonatomic, strong) ObjectType result;
@property (nonatomic, assign) int retCode;
@property (nonatomic, copy)NSString *retMsg;

- (BOOL)isSuccess;

-(NSError *)error;

+ (instancetype)initWithMsg:(int)retCode retMsg:(NSString *)msg;

+ (instancetype)initWithResult:(id)result retCode:(int)retCode retMsg:(NSString *)msg;

@end
