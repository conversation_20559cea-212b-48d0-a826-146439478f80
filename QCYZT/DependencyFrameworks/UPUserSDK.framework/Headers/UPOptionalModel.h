//
//  UPOptionalModel.h
//  UPMarket
//
//  Created by <PERSON> on 17/4/1.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface UPOptionalModel : NSObject <NSCopying>

/*  用户ID */
@property (nonatomic, copy) NSString *uid;
/* 平台 */
@property (nonatomic, copy) NSString *platform;
/* 分组id */
@property (nonatomic, assign) NSInteger groupId;
/* 位置 */
@property (nonatomic, copy) NSString *position;
/* 1:未删除;0:删除 */
@property (nonatomic, assign) NSInteger status;
/* 删除时间 */
@property (nonatomic, assign) long long delete_time;
/* 修改时间(排序修改，自选股重新分组) */
@property (nonatomic, assign) long long update_time;
/* 增加时间 */
@property (nonatomic, assign) long long create_time;
/* 股票市场 */
@property (nonatomic, assign) short setCode;
/* 股票代码 */
@property (nonatomic, copy) NSString *code;
/* 股票名称 */
@property (nonatomic, copy) NSString *name;
/* 股票类别 */
@property (nonatomic, assign) NSInteger category;
/* 精度 */
@property (nonatomic, assign) short precise;
/* 现价 */
@property (nonatomic, assign) double nowPrice;
/* 涨跌额 = 现价 - 昨收价 */
@property (nonatomic, assign) double changeValue;
/* 涨跌幅 = 涨跌额 / 昨收价 * 100% */
@property (nonatomic, assign) double changeRatio;
/* 总市值 */
@property (nonatomic, assign) double totalMarketValue;
/* 是否有融资标识 */
@property (nonatomic, assign) BOOL hasMarginMark;
/* 是否有融券标识 */
@property (nonatomic, assign) BOOL hasSecuritiesMark;
/* 交易状态标志 */
@property (nonatomic, assign) NSInteger tradeStatus;
//编辑自选专用
@property (nonatomic, assign) BOOL selected;
// 个股标签
@property (nonatomic, assign) int tagType;

- (NSString *)uniqueQuery;

@end
