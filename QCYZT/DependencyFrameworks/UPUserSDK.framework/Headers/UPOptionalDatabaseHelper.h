//
// Created by <PERSON><PERSON> on 2017/6/10.
// Copyright (c) 2017 UpChina. All rights reserved.
//

#import <UPTAF/TAFFMDBHelper.h>
#import "UPOptionalModel.h"
#import "UPOptionalGroupModel.h"

/*
 * version 3 table_config 添加唯一索引 optional_config_index_key
 * version 4 补充 table_alarm 升级逻辑
 * version 5 增加optional_group表
 * */
#define UPDatabaseOptionalVersion 5


@protocol UPOptionalDataObserver <NSObject>

- (void)onOptionalDataChanged:(BOOL)sendNotify;

@end

/*
 * 自选数据库
 * */
@interface UPOptionalDatabaseHelper : UPTAFFMDBHelper

@property(nonatomic, weak) id<UPOptionalDataObserver> delegate;

/*
 * 添加自选数据
 * @param update 更新标记
 * */
- (BOOL)saveOptional:(NSArray <UPOptionalModel *>*)array needUpdate:(BOOL)update uid:(NSString *)uid;

/*
 * 删除自选数据
 * */
- (BOOL)removeOptionalWithArray:(NSArray <UPOptionalModel *>*)array uid:(NSString *)uid;

/*
 * 删除自选数据 删除所有分组的 同一只股票
 * */
- (BOOL)removeOptionalsFromAllGroupWithArray:(NSArray <UPOptionalModel *>*)array uid:(NSString *)uid;

/*
 * 根据用户查询自选数据
 * */
- (NSArray <UPOptionalModel *>*)getOptionalArrayWithUID:(NSString *)uid deleteFlag:(BOOL)isDeleted;

/*
 * 根据用户删除自选数据，同时删除自选分组
 * */
- (BOOL)deleteOptionalWithUid:(NSString *)uid needNotify:(BOOL)notify;

/*
 * 根据更新标记查询自选数据
 * */
- (NSArray <UPOptionalModel *>*)getOptionalArrayWithUpdateFlag:(BOOL)flag uid:(NSString *)uid;

/*
 * 根据股票信息查询自选数据
 * */
- (UPOptionalModel *)getOptionalWithSetCode:(NSInteger)setCode code:(NSString *)code;

- (void)updateOptionalInfo:(NSArray <UPOptionalModel *>*)array;

- (BOOL)saveAlarmWithSetCode:(NSInteger)setCode code:(NSString *)code uid:(NSString *)uid;

- (BOOL)deleteAlarmWithSetCode:(NSInteger)setCode code:(NSString *)code uid:(NSString *)uid;

- (NSDictionary <NSString *, UPOptionalModel *> *)getAlarmWithUid:(NSString *)uid;

/*
 * 设置更新标记
 * */
- (BOOL)setUpdateFlag:(BOOL)flag;

/*
 * 设置同步版本号
 * */
- (BOOL)setOptionalVersion:(NSString *)version uid:(NSString *)uid;

/*
 * 获取同步版本号
 * */
- (NSString *)getOptionalVersion:(NSString *)uid;

/**
 清除自选数据
 */
- (BOOL)clearOptionalData;

/**
 清除自选版本数据
 */
- (BOOL)clearOptionalVersion;

/*
 * 添加自选分组数据
 * @param update 更新标记
 * */
- (BOOL)saveOptionalGroup:(NSArray <UPOptionalGroupModel *>*)array uid:(NSString *)uid needUpdate:(BOOL)update;
/*
 * 删除自选分组数据
 * deleteOption 是否删除分组内的自选
 * */
- (BOOL)removeOptionalGroupWithArray:(NSArray <UPOptionalGroupModel *>*)array uid:(NSString *)uid deleteOptional:(BOOL)deleteOptional;
/*
 * 根据用户查询自选分组数据
 * */
- (NSArray <UPOptionalGroupModel *>*)getOptionalGroupArrayWithUID:(NSString *)uid deleteFlag:(BOOL)isDeleted;

/*
 * 根据更新标记查询自选分组数据
 * */
- (NSArray <UPOptionalGroupModel *>*)getOptionalGroupArrayWithUpdateFlag:(BOOL)flag uid:(NSString *)uid;

@end
