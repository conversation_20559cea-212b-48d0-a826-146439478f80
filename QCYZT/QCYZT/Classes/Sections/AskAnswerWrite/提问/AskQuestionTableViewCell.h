//
//  AskQuestionTableViewCell.h
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2017/1/10.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

@class BigCastModel;
@class FMBigCastCommonModel;

typedef void (^AskQuestionBtnBlock)();


@interface AskQuestionTableViewCell : UITableViewCell

@property (nonatomic,copy) AskQuestionBtnBlock clickBlock;


/**
 *  我的关注  模型类
 */
@property (nonatomic, strong) BigCastModel *model;

/**
 *  热门推荐  模型类
 */
@property (nonatomic, strong) FMBigCastCommonModel *recommendModel;

@property (nonatomic, copy) NSString *userId;


@end
