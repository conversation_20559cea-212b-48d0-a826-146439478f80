//
//  AskQuestionViewController.m
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2017/1/10.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "AskQuestionViewController.h"
#import "AskQuestionListViewController.h"
#import "YTGOtherWebVC.h"

@interface AskQuestionViewController ()<SGPageTitleViewDelegate,SGPageContentCollectionViewDelegate>

@property (nonatomic, strong) NSArray *titleArray;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;
@property (nonatomic, strong) UIBarButtonItem *rightItem;

@end



@implementation AskQuestionViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.title = @"提问";
    self.view.backgroundColor = UIColor.up_contentBgColor;
    self.navigationItem.rightBarButtonItem = self.rightItem;
    
    self.titleArray = @[@"热门推荐", @"我的关注"];
    [self setupPageView];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavRedColor];
}

-(void)setupPageView {
    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = FontWithSize(16.0);
    configure.titleSelectedColor = UIColor.up_textPrimaryColor;
    configure.titleSelectedFont = BoldFontWithSize(18.0);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = YES;
    configure.showBottomSeparator = YES;
    configure.bottomSeparatorColor = UIColor.fm_sepline_color;
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SegmentControl_Height) delegate:self titleNames:self.titleArray configure:configure];
    self.pageTitleView.backgroundColor = UIColor.up_contentBgColor;
    [self.view addSubview:self.pageTitleView];
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, UI_SegmentControl_Height, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + UI_SegmentControl_Height + UI_SAFEAREA_BOTTOM_HEIGHT)) parentVC:self childVCs:[self addChildVC]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
    // 处理侧滑返回失效
    [self.pageContentCollectionView.collectionView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
}

- (NSArray *)addChildVC {
    AskQuestionListViewController *askCodeVC = [[AskQuestionListViewController alloc] init];
    askCodeVC.list_type = @"2";    // 热门推荐
    askCodeVC.relationStock = self.relationStock;
    [self addChildViewController:askCodeVC];
    
    AskQuestionListViewController *fouceVC = [[AskQuestionListViewController alloc] init];
    fouceVC.list_type = @"1";     // 我的关注
    fouceVC.relationStock = self.relationStock;
    [self addChildViewController:fouceVC];

    return self.childViewControllers;
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}


#pragma mark - Private
- (void)back {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)jumpToAskRule {
    WEAKSELF
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
        webVC.startPage = [NSString stringWithFormat:@"%@%@",prefix, kAPI_Question_TWGZ];
        webVC.titleStr = @"提问规则";
        [__weakSelf.navigationController pushViewController:webVC animated:YES];
    }];
}

#pragma mark - Getter/Setter
- (UIBarButtonItem *)rightItem {
    if (!_rightItem) {
        _rightItem = [[UIBarButtonItem alloc] initWithTitle:@"提问规则"
                                                      style:UIBarButtonItemStylePlain
                                                     target:self
                                                     action:@selector(jumpToAskRule)];
    }
    return _rightItem;
}
@end
