//
//  AskQuestionTableViewCell.m
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2017/1/10.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "AskQuestionTableViewCell.h"
#import "BigCastModel.h"
#import "FMBigCastCommonModel.h"

@interface AskQuestionTableViewCell()

@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) ZLTagLabel *tagLabel;
@property (nonatomic, strong) ZLTagView *tagView;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) UILabel *fansNumLabel;
@property (nonatomic, strong) UILabel *noteNumLabel;
@property (nonatomic, strong) UILabel *answerNumLabel;
@property (nonatomic, strong) UIButton *questionBtn;

@end

@implementation AskQuestionTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    UIImageView *iconImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:iconImgV];
    [iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@60);
        make.left.equalTo(@15);
        make.top.equalTo(@18);
    }];
    UI_View_Radius(iconImgV, 30);
    self.iconImgV = iconImgV;
    
    UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:nameLabel];
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(iconImgV.mas_right).offset(10);
        make.top.equalTo(iconImgV).offset(0);
    }];
    self.nameLabel = nameLabel;
    
    ZLTagView *tagView = [[ZLTagView alloc] init];
    [self addSubview:tagView];
    [tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel.mas_right).offset(6);
        make.top.equalTo(iconImgV).offset(0);
        make.right.equalTo(@-15);
    }];
    tagView.tagLabelFont = [UIFont systemFontOfSize:12];
    tagView.tagLabelWidthPadding = 10.0f;
    tagView.tagLabelHeightPadding = 6.0f;
    tagView.middlePadding = 6.0f;
    tagView.tagLabelTextColor = ColorWithHex(0xFF7200);
    tagView.tagLabelCornerRadius = 2.0f;
    tagView.numberofLines = 1;
    tagView.tagLabelBgColor = ColorWithHex(0xffedde);
    tagView.hidden = YES;
    self.tagView = tagView;
    
    UILabel *descLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:2 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:descLabel];
    [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel);
        make.top.equalTo(nameLabel.mas_bottom).offset(5);
        make.right.equalTo(@-15);
    }];
    self.descLabel = descLabel;
    
    UILabel *fansNumLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:fansNumLabel];
    [fansNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(iconImgV);
        make.top.equalTo(iconImgV.mas_bottom).offset(12);
    }];
    self.fansNumLabel = fansNumLabel;
    
    UILabel *noteNumLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:noteNumLabel];
    [noteNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(iconImgV).offset((UI_SCREEN_WIDTH - 30) * 0.37);
        make.top.equalTo(fansNumLabel);
    }];
    self.noteNumLabel = noteNumLabel;
    
    UILabel *answerNumLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:answerNumLabel];
    [answerNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-20);
        make.top.equalTo(fansNumLabel);
    }];
    self.answerNumLabel = answerNumLabel;
    
    UIButton *questionBtn = [[UIButton alloc] init];
    [self.contentView addSubview:questionBtn];
    [questionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(iconImgV.mas_bottom).offset(45);
        make.height.equalTo(@40);
        make.bottom.equalTo(@-15);
    }];
    UI_View_Radius(questionBtn, 5.0f);
    CAGradientLayer *gl = [CAGradientLayer layer];
    gl.frame = CGRectMake(0,0,UI_SCREEN_WIDTH - 30,40);
    gl.startPoint = CGPointMake(0.5, 0);
    gl.endPoint = CGPointMake(0.5, 1);
    gl.colors = @[(__bridge id)[UIColor colorWithRed:255/255.0 green:236/255.0 blue:235/255.0 alpha:1.0].CGColor,(__bridge id)[UIColor colorWithRed:255/255.0 green:221/255.0 blue:219/255.0 alpha:1.0].CGColor];
    gl.locations = @[@(0),@(1.0f)];
    [questionBtn.layer addSublayer:gl];
    [questionBtn addTarget:self action:@selector(askQuestionBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    questionBtn.titleLabel.font = BoldFontWithSize(15);
    [questionBtn setTitleColor:FMNavColor forState:UIControlStateNormal];
    self.questionBtn = questionBtn;
    
    [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.bottom.equalTo(@0);
        make.height.equalTo(@0.7);
    }].backgroundColor = UIColor.fm_sepline_color;
}

// 我的关注
- (void)setModel:(BigCastModel *)model {
    _model = model;

    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:model.user_ico] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.nameLabel.text = model.user_name;
    self.descLabel.text = model.user_profiles.length > 0 ? model.user_profiles : @"暂无简介";
    
    if (model.good_at.length) {
        NSArray *goodAtArr = [model.good_at componentsSeparatedByString:@"、"];
        self.tagView.titleArray = goodAtArr;
        self.tagView.hidden = NO;
    } else {
        self.tagView.hidden = YES;
    }
    
    NSString *fansNumStr = nil;
    if ([model.user_notice_num integerValue] < 10000) {
        fansNumStr = [NSString stringWithFormat:@"粉丝数：%@", model.user_notice_num];
    } else {
        double wan = [model.user_notice_num intValue] / 10000.0;
        fansNumStr = [NSString stringWithFormat:@"粉丝数：%.1f万", wan];
    }
    self.fansNumLabel.attributedText = [fansNumStr attrStrWithMatchColor:UIColor.up_textPrimaryColor pattern:@"[1-9][0-9]*(\\.[0-9]万)?" textFont:FontWithSize(13)];
    
    NSString *noteNumStr = nil;
    if ([model.noteNum integerValue] < 10000) {
        noteNumStr = [NSString stringWithFormat:@"发布笔记：%@", model.noteNum];
    } else {
        double wan = [model.noteNum intValue] / 10000.0;
        noteNumStr = [NSString stringWithFormat:@"发布笔记：%.1f万", wan];
    }
    self.noteNumLabel.attributedText = [noteNumStr attrStrWithMatchColor:UIColor.up_textPrimaryColor pattern:@"[1-9][0-9]*(\\.[0-9]万)?" textFont:FontWithSize(13)];
    
    NSString *answerNumStr = nil;
    if ([model.answerNum integerValue] < 10000) {
        answerNumStr = [NSString stringWithFormat:@"回答问股：%@", model.answerNum];
    } else {
        double wan = [model.answerNum intValue] / 10000.0;
        answerNumStr = [NSString stringWithFormat:@"回答问股：%.1f万", wan];
    }
    self.answerNumLabel.attributedText = [answerNumStr attrStrWithMatchColor:UIColor.up_textPrimaryColor pattern:@"[1-9][0-9]*(\\.[0-9]万)?" textFont:FontWithSize(13)];
    
    [self.questionBtn setTitle:[NSString stringWithFormat:@"%@金币向TA提问", model.answer_price] forState:UIControlStateNormal];

    self.userId = model.userid;
}

// 热门推荐
- (void)setRecommendModel:(FMBigCastCommonModel *)recommendModel {
    _recommendModel = recommendModel;
    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:recommendModel.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.nameLabel.text = recommendModel.userName;
    self.descLabel.text = recommendModel.userProfiles;
    
    if (recommendModel.userGoodAt.length) {
        NSArray *goodAtArr = [recommendModel.userGoodAt componentsSeparatedByString:@"、"];
        self.tagView.titleArray = goodAtArr;
        self.tagView.hidden = NO;
    } else {
        self.tagView.hidden = YES;
    }
    
    NSString *fansNumStr = nil;
    if (recommendModel.userNoticerNums < 10000) {
        fansNumStr = [NSString stringWithFormat:@"粉丝数：%zd", recommendModel.userNoticerNums];
    } else {
        double wan = recommendModel.userNoticerNums / 10000.0;
        fansNumStr = [NSString stringWithFormat:@"粉丝数：%.1f万", wan];
    }
    self.fansNumLabel.attributedText = [fansNumStr attrStrWithMatchColor:UIColor.up_textPrimaryColor pattern:@"[1-9][0-9]*(\\.[0-9]万)?" textFont:FontWithSize(13)];
    
    NSString *noteNumStr = nil;
    if (recommendModel.userNoteNums.integerValue < 10000) {
        noteNumStr = [NSString stringWithFormat:@"发布笔记：%@", recommendModel.userNoteNums];
    } else {
        double wan = recommendModel.userNoteNums.integerValue / 10000.0;
        noteNumStr = [NSString stringWithFormat:@"发布笔记：%.1f万", wan];
    }
    self.noteNumLabel.attributedText = [noteNumStr attrStrWithMatchColor:UIColor.up_textPrimaryColor pattern:@"[1-9][0-9]*(\\.[0-9]万)?" textFont:FontWithSize(13)];
    
    NSString *answerNumStr = nil;
    if (recommendModel.userAnswerNums.integerValue < 10000) {
        answerNumStr = [NSString stringWithFormat:@"回答问股：%@", recommendModel.userAnswerNums];
    } else {
        double wan = recommendModel.userAnswerNums.integerValue  / 10000.0;
        answerNumStr = [NSString stringWithFormat:@"回答问股：%.1f万", wan];
    }
    self.answerNumLabel.attributedText = [answerNumStr attrStrWithMatchColor:UIColor.up_textPrimaryColor pattern:@"[1-9][0-9]*(\\.[0-9]万)?" textFont:FontWithSize(13)];
    
    [self.questionBtn setTitle:[NSString stringWithFormat:@"%@金币向TA提问", recommendModel.answerPrice] forState:UIControlStateNormal];
    
    self.userId = recommendModel.userId;
}

#pragma mark - Event
- (void)askQuestionBtnClicked:(UIButton *)sender {
    if ([FMHelper checkLoginStatus]) {
        if (self.clickBlock) {
            self.clickBlock();
        }
    }
}

@end
