//
//  CustomSwitch.m
//  QCYZT
//
//  Created by zeng on 2022/3/23.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "CustomSwitch.h"

static CGFloat kSliderPadding = 2.0f;

@interface CustomSwitch()

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UIView *sliderView;
@property (nonatomic, strong) UILabel *onLabel;
@property (nonatomic, strong) UILabel *offLabel;

//@property (nonatomic, assign) CGFloat sliderX;
@property (nonatomic, assign) CGFloat sliderRatio;


@end

@implementation CustomSwitch

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    self.bgView.frame = self.bounds;
    UI_View_Radius(self.bgView, self.size.height * 0.5);

    self.sliderView.size = CGSizeMake(self.size.height - 2 * kSliderPadding, self.size.height - 2 * kSliderPadding);
    UI_View_Radius(self.sliderView, self.sliderView.height * 0.5);
    self.sliderView.centerY = self.bgView.centerY;
    
    CGSize onTextSize = [self.onAttrString boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin context:nil].size;
    self.onLabel.centerY = self.bgView.centerY;
    self.onLabel.x = self.size.height * 0.5 - kSliderPadding;
    self.onLabel.size = onTextSize;
    
    CGSize offTextSize = [self.offAttrString boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin context:nil].size;
    self.offLabel.centerY = self.bgView.centerY;
    self.offLabel.x = self.size.width - self.size.height * 0.5 + kSliderPadding - offTextSize.width;
    self.offLabel.size = offTextSize;
    
    self.sliderView.x = self.sliderRatio * (self.size.width - self.size.height) + kSliderPadding;
    self.bgView.backgroundColor = [UIColor mixColor1:self.onBgColor color2:self.offBgColor ratio:self.sliderRatio];
    self.onLabel.alpha = self.sliderRatio;
    self.offLabel.alpha = 1 - self.sliderRatio;
}

- (void)setUp {
    self.sliderRatio = 0;
    
    UIView *bgView = [[UIView alloc] init];
    [self addSubview:bgView];
    bgView.backgroundColor = ColorWithHex(0xed3fce); // 随便给的
    self.bgView = bgView;
    bgView.userInteractionEnabled = YES;
    [bgView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapClick:)]];
    [bgView addGestureRecognizer:[[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(panSlider:)]];

    UIView *sliderView = [[UIView alloc] init];
    [self addSubview:sliderView];
    sliderView.backgroundColor = [UIColor whiteColor];
    self.sliderView = sliderView;
    sliderView.userInteractionEnabled = YES;
    [sliderView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapClick:)]];
    [sliderView addGestureRecognizer:[[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(panSlider:)]];
    
    UILabel *onLabel = [[UILabel alloc] init];
    [self addSubview:onLabel];
    onLabel.textColor = [UIColor whiteColor];
    self.onLabel = onLabel;
    
    UILabel *offLabel = [[UILabel alloc] init];
    [self addSubview:offLabel];
    offLabel.textColor = [UIColor whiteColor];
    self.offLabel = offLabel;
}

- (void)tapClick:(UITapGestureRecognizer *)recognizer {
    self.on = !self.isOn;
    
    [UIView animateWithDuration:0.25 animations:^{
        [self layoutIfNeeded];
    }];
}

- (void)panSlider:(UIPanGestureRecognizer *)recognizer {
    CGPoint translation = [recognizer translationInView:recognizer.view];
    [recognizer setTranslation:CGPointZero inView:recognizer.view];
    
    UIGestureRecognizerState recState =  recognizer.state;
    switch (recState) {
        case UIGestureRecognizerStateBegan:
            break;
        case UIGestureRecognizerStateChanged: {
            [self dontSlideOutOfEdgeWithTranslationX:translation.x];
        }
            break;
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled:{
            [self autoSlideToEdge];
        }
            break;
            
        default:
            break;
    }
}

// 控制不要滑出边缘
- (void)dontSlideOutOfEdgeWithTranslationX:(CGFloat)translationX {
    CGFloat sliderX = self.sliderView.x += translationX;
    if (sliderX < kSliderPadding) {
        sliderX = kSliderPadding;
    } else if (sliderX + self.sliderView.width + kSliderPadding > self.size.width) {
        sliderX = self.size.width - self.sliderView.width - kSliderPadding;
    }
    
    self.sliderRatio = (sliderX - kSliderPadding) / (self.size.width - self.size.height);
    self.sliderView.x = sliderX;
    self.bgView.backgroundColor = [UIColor mixColor1:self.onBgColor color2:self.offBgColor ratio:self.sliderRatio];
    self.onLabel.alpha = self.sliderRatio;
    self.offLabel.alpha = 1 - self.sliderRatio;
}
    
// 自动滑到边缘
- (void)autoSlideToEdge {
    CGFloat sliderX = self.sliderView.x;
    if (sliderX <= (self.size.width - self.size.height) * 0.5 + kSliderPadding) {
        self.on = NO;
    } else {
        self.on = YES;
    }

    [UIView animateWithDuration:0.125 animations:^{
        [self layoutIfNeeded];
    }];
}

- (void)setOn:(BOOL)on {
    _on = on;
    [self sendActionsForControlEvents:UIControlEventValueChanged];

    self.sliderRatio = on ? 1 : 0;
    [self setNeedsLayout];
}

- (void)setOnBgColor:(UIColor *)onBgColor {
    _onBgColor = onBgColor;
    
    [self setNeedsLayout];
}

- (void)setOffBgColor:(UIColor *)offBgColor {
    _offBgColor = offBgColor;
    
    [self setNeedsLayout];
}

- (void)setSliderColor:(UIColor *)sliderColor {
    _sliderColor = sliderColor;
    
    self.sliderView.backgroundColor = sliderColor;
}

- (void)setOnAttrString:(NSAttributedString *)onAttrString {
    _onAttrString = onAttrString;
    
    self.onLabel.attributedText = onAttrString;
}

- (void)setOffAttrString:(NSAttributedString *)offAttrString {
    _offAttrString = offAttrString;
    
    self.offLabel.attributedText = offAttrString;
}

@end
