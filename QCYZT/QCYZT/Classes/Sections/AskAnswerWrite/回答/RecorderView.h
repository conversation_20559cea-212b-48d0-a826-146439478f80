//
//  RecorderView.h
//  录音转码
//
//  Created by MacPro on 16/8/15.
//  Copyright © 2016年 MacPro. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface RecorderView : UIView

@property (nonatomic, copy) NSString *questionId; // 回答的是哪条问股

// Declare properties for UI elements previously connected via IBOutlet
@property (nonatomic, strong) UIView *outView;
@property (nonatomic, strong) UIButton *quitBtn;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) UILabel *introLabel;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIButton *playBtn;
@property (nonatomic, strong) UIButton *RecordBtn;
@property (nonatomic, strong) UIButton *re_recordBtn;
@property (nonatomic, strong) UIButton *publishBtn;
@property (nonatomic, strong) NSLayoutConstraint *outViewHeightConstraint; // To replace outVIewHeightCons

@end

NS_ASSUME_NONNULL_END



