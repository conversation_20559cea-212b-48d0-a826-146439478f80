//
//  RecorderView.m
//  录音转码
//
//  Created by MacPro on 16/8/15.
//  Copyright © 2016年 MacPro. All rights reserved.
//

#import "RecorderView.h"
#import <AVFoundation/AVFoundation.h>
#import "lame.h"
#import "FMPlayerManager.h"


#define kCafAudioFile @"myRecord.caf"
#define kMp3AudioFile @"myRecord.mp3"

@interface RecorderView() <AVAudioRecorderDelegate, AVAudioPlayerDelegate>

@property (nonatomic,strong) AVAudioRecorder *audioRecorder;
@property (nonatomic,strong) AVAudioPlayer *audioPlayer;
@property (strong, nonatomic) NSTimer *recordTimer;
@property (strong, nonatomic) NSTimer *playTimer;
@property (nonatomic,assign) long long recordTime;

@end

@implementation RecorderView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setupConstraints];
        [self initialConfiguration];

        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(removeFromSuperview) name:kSubmitAnswerSuccess object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(removeFromSuperview) name:kALiMessageAffirm object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(uptheme_themeDidUpdate) name:kUPThemeDidChangeNotification object:nil];

    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor colorWithWhite:0.0 alpha:0.5];

    _outView = [[UIView alloc] init];
    _outView.backgroundColor = UIColor.up_contentBgColor;
    _outView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:_outView];

    _quitBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [_quitBtn setBackgroundImage:[UIImage imageNamed:@"record_close"] forState:UIControlStateNormal];
    [_quitBtn addTarget:self action:@selector(quit:) forControlEvents:UIControlEventTouchUpInside];
    _quitBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [_outView addSubview:_quitBtn];

    _timeLabel = [[UILabel alloc] init];
    _timeLabel.text = @"00:00";
    _timeLabel.font = [UIFont systemFontOfSize:16];
    _timeLabel.textColor = UIColor.up_textSecondary1Color;
    _timeLabel.textAlignment = NSTextAlignmentCenter;
    _timeLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [_outView addSubview:_timeLabel];

    _RecordBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [_RecordBtn addTarget:self action:@selector(recordOrFinish:) forControlEvents:UIControlEventTouchUpInside];
    _RecordBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [_outView addSubview:_RecordBtn];

    _playBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [_playBtn setBackgroundImage:[UIImage imageNamed:@"play"] forState:UIControlStateNormal];
    [_playBtn addTarget:self action:@selector(play:) forControlEvents:UIControlEventTouchUpInside];
    _playBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [_outView addSubview:_playBtn];

    _introLabel = [[UILabel alloc] init];
    _introLabel.font = [UIFont systemFontOfSize:14];
    _introLabel.textColor = UIColor.up_textSecondary1Color;
    _introLabel.textAlignment = NSTextAlignmentCenter;
    _introLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [_outView addSubview:_introLabel];

    _contentView = [[UIView alloc] init];
    _contentView.backgroundColor = UIColor.up_contentBgColor;
    _contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [_outView addSubview:_contentView];

    _re_recordBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [_re_recordBtn setTitle:@"重录" forState:UIControlStateNormal];
    [_re_recordBtn setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateNormal];
    _re_recordBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    [_re_recordBtn addTarget:self action:@selector(reSet:) forControlEvents:UIControlEventTouchUpInside];
    _re_recordBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [_contentView addSubview:_re_recordBtn];

    _publishBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [_publishBtn setTitle:@"确认发送" forState:UIControlStateNormal];
    [_publishBtn setTitleColor:ColorWithHex(0xEA4F4D) forState:UIControlStateNormal];
    _publishBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    [_publishBtn addTarget:self action:@selector(publish:) forControlEvents:UIControlEventTouchUpInside];
    _publishBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [_contentView addSubview:_publishBtn];
}

- (void)setupConstraints {
    [self.outView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self);
        make.height.equalTo(@220);
    }];

    [self.quitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.outView).offset(5);
        make.trailing.equalTo(self.outView).offset(-5);
        make.width.height.equalTo(@22);
    }];

    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.outView).offset(20);
        make.centerX.equalTo(self.outView);
    }];

    [self.RecordBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.timeLabel.mas_bottom).offset(15);
        make.centerX.equalTo(self.outView);
        make.width.height.equalTo(@60);
    }];

    [self.playBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.RecordBtn);
        make.size.equalTo(self.RecordBtn);
    }];

    [self.introLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.RecordBtn.mas_bottom).offset(18);
        make.centerX.equalTo(self.outView);
    }];

    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.outView);
        make.height.equalTo(@50);
    }];

    [self.re_recordBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(20);
        make.top.equalTo(self.contentView);
        make.width.equalTo(@100);
        make.height.equalTo(@36);
    }];

    [self.publishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).offset(-20);
        make.centerY.equalTo(self.re_recordBtn);
        make.width.equalTo(@100);
        make.height.equalTo(@36);
    }];
}

- (void)initialConfiguration {
    [self.RecordBtn setBackgroundImage:[UIImage imageNamed:@"begin"] forState:UIControlStateNormal];
    self.timeLabel.hidden = YES;

    // Round only the top corners
    self.outView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    self.outView.layer.cornerRadius = 10.0f;
    self.outView.clipsToBounds = YES; // Use clipsToBounds instead of masksToBounds for performance with cornerRadius

    UIColor *resolvedColor = [UIColor.up_textSecondary1Color performSelector:@selector(resolvedColor)];
    self.re_recordBtn.layer.cornerRadius = 18.0f;
    self.re_recordBtn.layer.masksToBounds = YES;
    self.re_recordBtn.layer.borderColor = resolvedColor.CGColor;
    self.re_recordBtn.layer.borderWidth = 1.0f;

    self.publishBtn.layer.cornerRadius = 18.0f;
    self.publishBtn.layer.masksToBounds = YES;
    self.publishBtn.layer.borderColor = resolvedColor.CGColor;
    self.publishBtn.layer.borderWidth = 1.0f;

    self.introLabel.attributedText = [self attrStrWithNormalStr:@"点击开始录音" range:NSMakeRange(2, 2) textColor:FMPickColor textFont:14];

    self.playBtn.hidden = YES;
    self.contentView.hidden = YES;
}

- (void)dealloc {
    [FMPlayerManager play];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - AVAudioRecorderDelegate
-(void)audioRecorderDidFinishRecording:(AVAudioRecorder *)recorder successfully:(BOOL)flag{
    //(@"录音完成：fileSize:%zd---%zd", [self fileSizeAtPath:[self cafFilePath]], [self fileSizeAtPath:[self mp3FilePath]]);
}

#pragma mark - AVAudioPlayerDelegate
-(void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag{
    //(@"录音播放结束...");
    //根据实际情况播放完成可以将会话关闭，其他音频应用继续播放
    [[AVAudioSession sharedInstance] setActive:NO error:nil];
    
    [self stopPlayTimer];
    
    [_playBtn setBackgroundImage:[UIImage imageNamed:@"play"] forState:UIControlStateNormal];
    _introLabel.attributedText = [self attrStrWithNormalStr:@"点击预览录音" range:NSMakeRange(2, 2) textColor:FMPickColor textFont:14];
}

#pragma mark - Method Response
/**
 *  退出页面
 */
- (void)quit:(id)sender {
    // Animate the view sliding down
    [UIView animateWithDuration:0.3 animations:^{
        self.outView.transform = CGAffineTransformMakeTranslation(0, self.bounds.size.height);
        self.backgroundColor = [UIColor clearColor]; // Fade out the background dimming
    } completion:^(BOOL finished) {
        if (self.audioPlayer) {
             self.audioPlayer = nil;
        }
        if (self.audioRecorder) {
            self.audioRecorder = nil;
        }
        [self stopPlayTimer];
        [self stopRecordTimer];
        
        [self removeFromSuperview];
    }];
}

/**
 *  重置
 */
- (void)reSet:(id)sender {
    _timeLabel.hidden = YES;
    _contentView.hidden = YES;
    [self.outView mas_updateConstraints:^(MASConstraintMaker *make) {
         make.height.equalTo(@220);
    }];
    [self layoutIfNeeded];
    _playBtn.hidden = YES;
    _RecordBtn.hidden = NO;
    _timeLabel.text = @"00:00";
    [_playBtn setBackgroundImage:[UIImage imageNamed:@"play"] forState:UIControlStateNormal];
    [_RecordBtn setBackgroundImage:[UIImage imageNamed:@"begin"] forState:UIControlStateNormal];
    _introLabel.attributedText = [self attrStrWithNormalStr:@"点击开始录音" range:NSMakeRange(2, 2) textColor:FMPickColor textFont:14];
    if (self.audioPlayer) {
        self.audioPlayer = nil;
    }
    if (self.audioRecorder) {
        self.audioRecorder = nil;
    }
    [self stopRecordTimer];
    
    NSFileManager *mgr = [NSFileManager defaultManager];
    if([mgr removeItemAtPath:[self mp3FilePath] error:nil])
    {
        //(@"重置，删除mp3文件");
    }
    
    [self setAudioSession];
}

/**
 *  录音或者停止
 */
- (void)recordOrFinish:(UIButton *)sender {
    if (![self.audioRecorder isRecording]) {
        [[FMGlobalPlayTool shareTool] globalPlayViewPlayOrPause];
        [FMPlayerManager pause];
        [self setAudioSession];
        [self startRecordTimer];
        [self.audioRecorder record];
        _timeLabel.hidden = NO;
        _introLabel.attributedText = [self attrStrWithNormalStr:@"点击结束录音" range:NSMakeRange(2, 2) textColor:FMPickColor textFont:14];
        [_RecordBtn setBackgroundImage:[UIImage imageNamed:@"finish"] forState:UIControlStateNormal];
    } else {
        [FMPlayerManager play];

        [self resumeAudioSession];
        [self stopRecordTimer];
        [self.audioRecorder stop];
        [self cafToMp3];
        
        _introLabel.attributedText = [self attrStrWithNormalStr:@"点击预览录音" range:NSMakeRange(2, 2) textColor:FMPickColor textFont:14];
        [_RecordBtn setBackgroundImage:[UIImage imageNamed:@"begin"] forState:UIControlStateNormal];
        _RecordBtn.hidden = YES;
        _contentView.hidden = NO;
        [self.outView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@220);
        }];
        [self layoutIfNeeded];
        _playBtn.hidden = NO;
    }
}

/**
 *  播放或者停止播放
 */
- (void)play:(UIButton *)sender {    
    if (![self.audioPlayer isPlaying]) {
        [FMPlayerManager pause];
        [self startPlayTimer];
        [self.audioPlayer play];
        _introLabel.attributedText = [self attrStrWithNormalStr:@"点击停止播放" range:NSMakeRange(2, 2) textColor:FMPickColor textFont:14];
        [_playBtn setBackgroundImage:[UIImage imageNamed:@"finish"] forState:UIControlStateNormal];
    } else {
        [FMPlayerManager play];
        [self stopPlayTimer];
        [self.audioPlayer stop];
        _introLabel.attributedText = [self attrStrWithNormalStr:@"点击预览录音" range:NSMakeRange(2, 2) textColor:FMPickColor textFont:14];
        [_playBtn setBackgroundImage:[UIImage imageNamed:@"play"] forState:UIControlStateNormal];
        
    }
}

- (void)publish:(UIButton *)sender {
    if ([self.audioPlayer isPlaying]) {
        [self stopPlayTimer];
        [self.audioPlayer stop];
    }
        
    NSDate *now = [NSDate date];
    
    NSDateFormatter* fmt1 = [[NSDateFormatter alloc] init];
    fmt1.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
    fmt1.dateFormat = @"yyyyMMdd";
    NSDateFormatter* fmt2 = [[NSDateFormatter alloc] init];
    fmt2.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
    fmt2.dateFormat = @"HH:mm:ss";
    
    NSString *userId = [FMUserDefault getUserId];
    
    NSString *updateName = [NSString stringWithFormat:@"%@_%@_%@_%@", userId, self.questionId,[fmt2 stringFromDate:now], kMp3AudioFile];
    
    [SVProgressHUD show];
    [NetworkManager uploadAudioWithOperations:nil withAudioURL:[NSURL fileURLWithPath:[self mp3FilePath]] updateName:updateName withUrlString:kAPI_System_Uplaod withSuccessBlock:^(NSDictionary *object) {
        if ([object[@"status"] isEqualToString:@"1"]) {
            NSDictionary *dict = [object[@"data"] firstObject];
                NSString *mp3UrlStr = dict[@"url"];
                dispatch_async(dispatch_get_main_queue(), ^{
                    [[NSNotificationCenter defaultCenter] postNotificationName:kAliUpdateMp3Success object:nil userInfo:@{@"url":mp3UrlStr, @"length":[NSString stringWithFormat:@"%lld", self.recordTime], @"questionId":self.questionId,}];
                });
                
                NSFileManager *mgr = [NSFileManager defaultManager];
                if([mgr removeItemAtPath:[self mp3FilePath] error:nil]) {
                    NSLog(@"mp3删除成功");
                }
        }
    } withFailurBlock:^(NSError *error) {
        [SVProgressHUD showErrorWithStatus:@"语音文件上传失败"];
    } withUpLoadProgress:^(float progress) {
        
    }];
}

#pragma mark - Private
/**
 *  设置音频会话
 */
-(void)setAudioSession {
    AVAudioSession *audioSession=[AVAudioSession sharedInstance];
    //设置为播放和录音状态，以便可以在录制完之后播放录音
    [audioSession setCategory:AVAudioSessionCategoryPlayAndRecord error:nil];
    [audioSession setActive:YES error:nil];
}

- (void)resumeAudioSession {
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [audioSession setCategory:AVAudioSessionCategoryPlayback error:nil];
    [audioSession setActive:YES error:nil];
}

/**
 *  取得录音文件保存路径
 *
 *  @return 录音文件路径
 */
-(NSString *)cafFilePath {
    NSString *urlStr=[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    urlStr=[urlStr stringByAppendingPathComponent:kCafAudioFile];
    return urlStr;
}

-(NSString *)mp3FilePath {
    NSString *urlStr=[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    urlStr=[urlStr stringByAppendingPathComponent:kMp3AudioFile];
    return urlStr;
}

- (NSInteger)fileSizeAtPath:(NSString *)filePath {
    NSFileManager *mgr = [NSFileManager defaultManager];
    
    BOOL exists = [mgr fileExistsAtPath:filePath];
    // 文件\文件夹不存在
    if (exists == NO) return 0;
    
    return [[mgr attributesOfItemAtPath:filePath error:nil][NSFileSize] integerValue];
}

- (NSString *)recordTimeStr {
    NSUInteger time = (NSUInteger)self.audioRecorder.currentTime;
    _recordTime = time;
//    if (time >= 120) {
//        [self recordOrFinish:_RecordBtn];
//    }
    
    NSInteger minutes = time/60;
    NSInteger seconds = time%60;
    
    return [NSString stringWithFormat:@"%02zd:%02zd", minutes, seconds];
}

- (NSString *)playTimeStr {
    NSUInteger time = (NSUInteger)self.audioPlayer.currentTime;
    
    NSInteger minutes = time/60;
    NSInteger seconds = time%60;
    
    return [NSString stringWithFormat:@"%02zd:%02zd", minutes, seconds];
    
}

- (void)startRecordTimer {
    if (!self.recordTimer) {
        self.recordTimer = [NSTimer timerWithTimeInterval:0.5
                                                   target:self
                                                 selector:@selector(updateTimeDisplay)
                                                 userInfo:nil
                                                  repeats:YES];
        [[NSRunLoop mainRunLoop] addTimer:self.recordTimer forMode:NSRunLoopCommonModes];
    }
}

- (void)stopRecordTimer {
    if (self.recordTimer) {
        [self.recordTimer invalidate];
        self.recordTimer = nil;
    }
}

- (void)startPlayTimer {
    if (!self.playTimer) {
        self.playTimer = [NSTimer timerWithTimeInterval:0.5
                                                 target:self
                                               selector:@selector(updateTimeDisplay)
                                               userInfo:nil
                                                repeats:YES];
        [[NSRunLoop mainRunLoop] addTimer:self.playTimer forMode:NSRunLoopCommonModes];
    }
}

- (void)stopPlayTimer {
    if (self.playTimer) {
        [self.playTimer invalidate];
        self.playTimer = nil;
    }
}

- (void)updateTimeDisplay {
    if ([self.audioRecorder isRecording]) {
        self.timeLabel.text = [self recordTimeStr];
    } else if ([self.audioPlayer isPlaying]) {
        self.timeLabel.text = [self playTimeStr];
    }
}

- (NSMutableAttributedString *)attrStrWithNormalStr:(NSString *)str
                                              range:(NSRange)textRange
                                          textColor:(UIColor *)textColor
                                           textFont:(CGFloat)textFont {
    NSMutableAttributedString *attrstr =  [[NSMutableAttributedString alloc] initWithString:str];
    UIColor *resolvedTextColor = textColor ?: FMPickColor;
    [attrstr addAttributes:@{
                             NSForegroundColorAttributeName: resolvedTextColor,
                             NSFontAttributeName:[UIFont systemFontOfSize:textFont]
                             } range:textRange];
    return attrstr;
}


#pragma mark - 转码
- (void)cafToMp3 {
    NSString *cafFilePath = [self cafFilePath];
    NSString *mp3FilePath = [self mp3FilePath];
    
    NSFileManager* fileManager=[NSFileManager defaultManager];
    if ([fileManager fileExistsAtPath:mp3FilePath]) {
        if([fileManager removeItemAtPath:mp3FilePath error:nil])
        {
            //(@"转换之前，删除mp3文件");
        }
    } else {
        //(@"转换之前，没有mp3文件");
    }
    
    
    @try {
        int read, write;
        
        FILE *pcm = fopen([cafFilePath cStringUsingEncoding:1], "rb");  //source 被转换的音频文件位置
        fseek(pcm, 4*1024, SEEK_CUR);                                   //skip file header
        FILE *mp3 = fopen([mp3FilePath cStringUsingEncoding:1], "wb");  //output 输出生成的Mp3文件位置
        
        const int PCM_SIZE = 8192;
        const int MP3_SIZE = 8192;
        short int pcm_buffer[PCM_SIZE*2];
        unsigned char mp3_buffer[MP3_SIZE];
        
        lame_t lame = lame_init();
        lame_set_in_samplerate(lame, 11025.0);
        lame_set_VBR(lame, vbr_default);
        lame_init_params(lame);
        
        do {
            read = fread(pcm_buffer, 2*sizeof(short int), PCM_SIZE, pcm);
            if (read == 0)
                write = lame_encode_flush(lame, mp3_buffer, MP3_SIZE);
            else
                write = lame_encode_buffer_interleaved(lame, pcm_buffer, read, mp3_buffer, MP3_SIZE);
            
            fwrite(mp3_buffer, write, 1, mp3);
            
        } while (read != 0);
        
        lame_close(lame);
        fclose(mp3);
        fclose(pcm);
    }
    @catch (NSException *exception) {
        //(@"%@",[exception description]);
    }
    @finally {
        //(@"转换成功----%zd----%zd", [self fileSizeAtPath:[self cafFilePath]], [self fileSizeAtPath:[self mp3FilePath]]);
        NSLog(@"转换成功----%zd----%zd", [self fileSizeAtPath:[self cafFilePath]], [self fileSizeAtPath:[self mp3FilePath]]);
        
        NSFileManager *mgr = [NSFileManager defaultManager];
        if ([mgr removeItemAtPath:cafFilePath error:nil]) {
            NSLog(@"删除caf文件成功");
        }
    }
}

#pragma mark - Getter/Setter
/**
 *  创建播放器
 *
 *  @return 播放器
 */
-(AVAudioPlayer *)audioPlayer{
    if (!_audioPlayer) {
        NSFileManager *mgr = [NSFileManager defaultManager];
        if (![mgr fileExistsAtPath:[self mp3FilePath]]) {
            //(@"没有mp3文件");
            return nil;
        }
        NSURL *url = [NSURL fileURLWithPath:[self mp3FilePath]];
        NSError *error=nil;
        _audioPlayer=[[AVAudioPlayer alloc] initWithContentsOfURL:url error:&error];
        _audioPlayer.numberOfLoops=0;
        _audioPlayer.delegate = self;
        [_audioPlayer prepareToPlay];
        if (error) {
            NSLog(@"创建播放器过程中发生错误，错误信息：%@",error.localizedDescription);
            return nil;
        }
    }
    return _audioPlayer;
}

/**
 *  获得录音机对象
 *
 *  @return 录音机对象
 */
-(AVAudioRecorder *)audioRecorder{
    if (!_audioRecorder) {
        //创建录音文件保存路径
        NSURL *url= [NSURL fileURLWithPath:[self cafFilePath]];
        //创建录音格式设置
        NSDictionary *setting=[self getAudioSetting];
        //创建录音机
        NSError *error=nil;
        _audioRecorder=[[AVAudioRecorder alloc]initWithURL:url settings:setting error:&error];
        _audioRecorder.delegate=self;
        _audioRecorder.meteringEnabled = YES;
        if (error) {
            NSLog(@"创建录音机对象时发生错误，错误信息：%@",error.localizedDescription);
            return nil;
        }
    }
    return _audioRecorder;
}

/**
 *  取得录音文件设置
 *
 *  @return 录音设置
 */
-(NSDictionary *)getAudioSetting{
    NSMutableDictionary *settings = [[NSMutableDictionary alloc] init];
    //录音格式
    [settings setValue:[NSNumber numberWithInt:kAudioFormatLinearPCM] forKey:AVFormatIDKey];
    //采样率 44.1kHz
    [settings setValue:[NSNumber numberWithFloat:44100.0] forKey:AVSampleRateKey];
    //通道数 mono
    [settings setValue:[NSNumber numberWithInt:1] forKey:AVNumberOfChannelsKey];
    //线性采样位数 16-bit
    [settings setValue:[NSNumber numberWithInt:16] forKey:AVLinearPCMBitDepthKey];
    //音频质量
    [settings setValue:[NSNumber numberWithInt:AVAudioQualityHigh] forKey:AVEncoderAudioQualityKey];
    //是否使用浮点数采样
    [settings setValue:@(YES) forKey:AVLinearPCMIsFloatKey];
    //是否使用大端模式
    [settings setValue:@(NO) forKey:AVLinearPCMIsBigEndianKey];
    
    return settings;
}

@end
