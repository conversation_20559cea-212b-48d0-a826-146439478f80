//
//  FMNoteDetailHeaderCell.m
//  QCYZT
//  文字笔记 投顾信息 内容  引导支付UI  权限UI 音频播放UI 支付按钮
//  Created by MacPro on 16/12/21.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "FMNoteDetailHeaderCell.h"
#import "FMNoteModel.h"
#import "FMUpDownButton.h"
#import "FMPayTool.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "DakaInfoNewView.h"
#import "FMGlobalPlayTool.h"
#import "NSObject+FBKVOController.h"
#import "FMMemberCenterProductViewController.h"
#import "FMNoteDetailVIPInfoView.h"
#import "HttpRequestTool+Pay.h"
#import "FMNoteDetailViewController.h"
#import "FMCouponUseGuideVC.h"
#import "FMCouponTableModel.h"
#import "FMPaySuccessPopView.h"

@interface FMNoteDetailHeaderAudioPlayView : UIView

@property (nonatomic, strong) UIImageView *statusImgV;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *timeLabel;

@property (nonatomic, strong) FMNoteModel *detailModel;
@property (nonatomic, assign) MP3PlayToolPlayStatus playStatus;
@end

@implementation FMNoteDetailHeaderAudioPlayView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    
    return self;
}

- (void)setUp {
    self.userInteractionEnabled = YES;
    
    WEAKSELF
    [self addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
        FMGlobalPlayControlModel *playModel = [[FMGlobalPlayControlModel alloc] init];
        if (__weakSelf.detailModel.noteTitle.length) {
            playModel.title = __weakSelf.detailModel.noteTitle;
        } else {
            playModel.title = __weakSelf.detailModel.noteSummary;
        }
        playModel.contentAudio = __weakSelf.detailModel.contentAudio;
        playModel.audioLength = __weakSelf.detailModel.audioLength;
        playModel.noteId = __weakSelf.detailModel.noteId;
                
        if ([[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:__weakSelf.detailModel.contentAudio]) {
            if ([MP3PlayTool shareMusicPlay].playStatus == MP3PlayToolPlayStatusStopped) {
                FMGlobalPlayTool *tool = [FMGlobalPlayTool shareTool];
                tool.playModel = playModel;
                [tool showGlobalPlayView];
            } 
        } else {
            FMGlobalPlayTool *tool = [FMGlobalPlayTool shareTool];
            tool.playModel = playModel;
            [tool showGlobalPlayView];
        }
        [[FMGlobalPlayTool shareTool] globalPlayViewPlayOrPause];
    }]];
    
    UIView *borderView = [[UIView alloc] init];
    [self addSubview:borderView];
    [borderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 15, 0, 15));
    }];
    borderView.backgroundColor = UIColor.fm_FFFFFF_2E2F33;
    UI_View_BorderRadius(borderView, 5, 1, UIColor.fm_note_audio_borderColor);
    
    UIView *contentView = [[UIView alloc] init];
    [borderView addSubview:contentView];
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.greaterThanOrEqualTo(@15);
        make.centerX.equalTo(@0);
        make.top.bottom.equalTo(@0);
    }];
    
    UIImageView *statusImgV = [[UIImageView alloc] init];
    [contentView addSubview:statusImgV];
    [statusImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.left.equalTo(@0);
        make.width.height.equalTo(@0);
    }];
    self.statusImgV = statusImgV;
    NSMutableArray *imagesArr = [NSMutableArray array];
    for (NSInteger i = 0; i < 15; i ++) {
        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"1 – %ld",i + 1]];
        [imagesArr addObject:image];
    }
    
    self.statusImgV.animationImages = imagesArr;
    self.statusImgV.animationDuration=1.0f;

    
    UILabel *statusLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleBoldFont:16] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [contentView addSubview:statusLabel];
    [statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.left.equalTo(statusImgV.mas_right).offset(10);
    }];
    self.statusLabel = statusLabel;
    
    UILabel *timeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:14.0] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [contentView addSubview:timeLabel];
    [timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.left.equalTo(statusLabel.mas_right).offset(8);
        make.right.equalTo(@0);
    }];
    self.timeLabel = timeLabel;
    
    self.playStatus = MP3PlayToolPlayStatusStopped;
}

- (void)setPlayStatus:(MP3PlayToolPlayStatus)playStatus {
    if (_playStatus == MP3PlayToolPlayStatusStopped && playStatus == MP3PlayToolPlayStatusPaused) { // 播放完毕后回到待播发状态
        [self.statusImgV stopAnimating];
        self.statusImgV.image = ImageWithName(@"note_audio_idle");
        [self.statusImgV mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.height.equalTo(@17);
        }];
        self.statusLabel.text = @"一键收听";
        self.statusLabel.textColor = ColorWithHex(0xAA611D);
        self.timeLabel.textColor = ColorWithHex(0xc37F40);
    } else {
        if (playStatus == MP3PlayToolPlayStatusStopped) {
            [self.statusImgV stopAnimating];
            self.statusImgV.image = ImageWithName(@"note_audio_idle");
            [self.statusImgV mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.height.equalTo(@17);
            }];
            self.statusLabel.text = @"一键收听";
            self.statusLabel.textColor = ColorWithHex(0xAA611D);
            self.timeLabel.textColor = ColorWithHex(0xc37F40);
        } else if (playStatus == MP3PlayToolPlayStatusPaused) {
            [self.statusImgV stopAnimating];
            self.statusImgV.image = ImageWithName(@"note_audio_paused2");
            [self.statusImgV mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.height.equalTo(@21);
            }];
            self.statusLabel.text = @"继续播放";
            self.statusLabel.textColor = UIColor.up_textPrimaryColor;
            self.timeLabel.textColor = UIColor.up_textSecondaryColor;
        } else if (playStatus == MP3PlayToolPlayStatusPlaying) {
            if ([self.statusImgV isAnimating]) {
                return;
            }
            [self.statusImgV startAnimating];
            [self.statusImgV mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.height.equalTo(@21);
            }];

            self.statusLabel.text = @"播放中";
            self.statusLabel.textColor = UIColor.up_textPrimaryColor;
            self.timeLabel.textColor = UIColor.up_textSecondaryColor;
        }
    }
    
    _playStatus = playStatus;
}

- (void)setDetailModel:(FMNoteModel *)detailModel {
    _detailModel = detailModel;
    
    self.timeLabel.text = [NSString stringWithFormat:@"预计播放%@", [self formatStrWithTimeInterval:detailModel.audioLength]];
    
    if ([[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:detailModel.contentAudio]){ // 如果URL相同，同步UI，并添加self为观察者
        self.playStatus = [MP3PlayTool shareMusicPlay].playStatus;
    } else { // 如果URL不同，恢复UI为初始状态
        self.playStatus = MP3PlayToolPlayStatusStopped;
    }
    
    [self setSelfAsObserver];
}

- (void)setSelfAsObserver {
    [self addPlayProgressObserver];
    [self addPlayStatusObserver];
}


- (void)addPlayProgressObserver {
    WEAKSELF;
    // 监听播放进度
    [self.KVOController observe:[MP3PlayTool shareMusicPlay] keyPath:@"playProgress" options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (![[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:__weakSelf.detailModel.contentAudio]) { // 如果不是同一个播放源，将UI回复到初始状态
            __weakSelf.playStatus = MP3PlayToolPlayStatusStopped;
            return;
        }
        
        CGFloat remainingTime = __weakSelf.detailModel.audioLength - [MP3PlayTool shareMusicPlay].playTime;
        __weakSelf.timeLabel.text = [NSString stringWithFormat:@"预计剩余%@", [__weakSelf formatStrWithTimeInterval:remainingTime]];
    }];
}

- (void)addPlayStatusObserver {
    WEAKSELF
    [self.KVOController observe:[MP3PlayTool shareMusicPlay] keyPath:@"playStatus" options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (![[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:__weakSelf.detailModel.contentAudio]) { // 如果不是同一个播放源，直接返回
            return;
        }

        MP3PlayToolPlayStatus playStatus = [[change valueForKey:NSKeyValueChangeNewKey] integerValue];
        __weakSelf.playStatus = playStatus;
    }];
}

// 将float秒转换为 00:00 格式的字符串
- (NSString *)formatStrWithTimeInterval:(NSTimeInterval)interval {
    interval = ceil(interval);
    int m = interval / 60;
    int s = (int)interval % 60;
    return [NSString stringWithFormat:@"%d分%d秒", m , s];
}

@end



@interface FMNoteDetailHeaderCell() <UIAlertViewDelegate>
/// 头像、姓名、发布时间
@property (nonatomic, strong) DakaInfoNewView *infoView;
/// 关注按钮
@property (nonatomic, strong) FocusButton *focusBtn;
/// 问股按钮
@property (nonatomic, strong) UIButton *askCodeBtn;
/// 文章标题
@property (nonatomic, strong) UILabel *titleLB;
/// 文章摘要
@property (nonatomic, strong) UILabel *summaryLB;
/// 灰色背景view
@property (nonatomic, strong) UIView *summaryBg;
/// 付费权限标记ui
@property (nonatomic, strong) UIButton *vipPerButton;
/// 付费引导，不再显示了，布局使用
@property (nonatomic, strong) UILabel *payGuideLB;
/// 付费按钮
@property (nonatomic, strong) UIButton *payBtn;
///  无专属付费提示使用
@property (nonatomic, strong) UILabel *payGuideLabel;

/// 音频播放试图
@property (nonatomic, strong) FMNoteDetailHeaderAudioPlayView *audioPlayView;
/// vip权限试图
@property (nonatomic, strong) FMNoteDetailVIPInfoView *vipInfoView;

@end

@implementation FMNoteDetailHeaderCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.backgroundColor = self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    DakaInfoNewView *infoView = [[DakaInfoNewView alloc] init];
    [self.contentView addSubview:infoView];
    [infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@20);
    }];
    infoView.nameLabel.textColor = UIColor.up_textPrimaryColor;
    self.infoView = infoView;
    [infoView setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    
    WEAKSELF
    FocusButton *focusBtn = [FocusButton buttonWithType:UIButtonTypeCustom];
    CGSize focusSize = [FMHelper isBigFont] ? CGSizeMake(75, 30) : CGSizeMake(65, 30);
    focusBtn.size = focusSize;
    focusBtn.text = @"关注";
    focusBtn.focusText = @"已关注";
    focusBtn.textColor = FMNavColor;
    focusBtn.focusTextColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"add_focus")];
    focusBtn.focusImage = ImageWithName(@"");
    focusBtn.boardColor =  FMNavColor;
    focusBtn.focusBoardColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.backColor = UIColor.up_contentBgColor;
    focusBtn.focusBackColor = UIColor.up_contentBgColor;
    focusBtn.titleLabel.font = [FMHelper scaleFont:14.0];
    focusBtn.compeletionClock = ^(NSString * _Nonnull dakaId, BOOL isfocus) {
    };
    [self.contentView addSubview:focusBtn];
    [focusBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(-15);
        make.centerY.equalTo(infoView);
        make.size.equalTo(@(focusSize));
    }];
    self.focusBtn = focusBtn;
    
    UIButton *askCodeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [askCodeBtn setTitle:@"问股" forState:UIControlStateNormal];
    [askCodeBtn setTitleColor:FMNavColor forState:UIControlStateNormal];
    [askCodeBtn setBackgroundColor:UIColor.up_contentBgColor];
    UI_View_BorderRadius(askCodeBtn, focusSize.height / 2.0, 1, FMNavColor);
    askCodeBtn.titleLabel.font = FontWithSize(14);
    [askCodeBtn addTarget:self action:@selector(askCodeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:askCodeBtn];
    [askCodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(focusBtn);
        make.right.equalTo(focusBtn.mas_left).offset(-10);
        make.size.equalTo(focusSize);
        make.left.greaterThanOrEqualTo(infoView.mas_right).offset(10);
    }];
    self.askCodeBtn = askCodeBtn;

    // 文章标题
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleBoldFont:23] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:titleLB];
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(infoView.mas_bottom).offset(0);
        make.height.equalTo(@(CGFLOAT_MIN));
    }];
    self.titleLB = titleLB;
    
    // 文章摘要
    UILabel *summaryLB = [[UILabel alloc] init];
    [self.contentView addSubview:summaryLB];
    [summaryLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@30);
        make.right.equalTo(@-30);
        make.top.equalTo(titleLB.mas_bottom).offset(0);
    }];
    summaryLB.font = [FMHelper scaleFont:16.0];
//    summaryLB.textColor = UIColor.up_textPrimaryColor;
    summaryLB.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 60;
    summaryLB.numberOfLines = 0;
    [summaryLB setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisVertical];
    self.summaryLB = summaryLB;
    
    UIView *summaryBg = [[UIView alloc] init];
    [self.contentView insertSubview:summaryBg belowSubview:summaryLB];
    [summaryBg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(summaryLB).insets(UIEdgeInsetsMake(-15, -15, -15, -15));
    }];
    summaryBg.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    self.summaryBg = summaryBg;
    
    UIImageView *yinhaoImgV = [[UIImageView alloc] init];
    [summaryBg addSubview:yinhaoImgV];
    [yinhaoImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@0);
        make.top.equalTo(@-10);
        make.width.height.equalTo(@20);
    }];
    yinhaoImgV.image = ImageWithName(@"note_detail_yinhao");
    
    // 支付引导
    UILabel *payGuideLB = [[UILabel alloc] init];
    [self.contentView addSubview:payGuideLB];
    [payGuideLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.width.equalTo(self.contentView).offset(-60);
        make.top.equalTo(summaryLB.mas_bottom).offset(0);
    }];
    payGuideLB.textColor = FMNavColor;
    payGuideLB.textAlignment = NSTextAlignmentCenter;
    payGuideLB.numberOfLines = 0;
    payGuideLB.font = [FMHelper scaleFont:16.0];
    self.payGuideLB = payGuideLB;
    payGuideLB.hidden = YES;
    
    // 付费权限标记UI
    UIButton *vipPerButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [vipPerButton setTitleColor:ColorWithHex(0x7B3000) forState:UIControlStateNormal];
    vipPerButton.titleLabel.font = [FMHelper scaleFont:14.0];
    vipPerButton.hidden = YES;
    [self.contentView addSubview:vipPerButton];
    [vipPerButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@(0));
        make.top.equalTo(payGuideLB.mas_bottom);
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH - 30, 37)));
    }];
    self.vipPerButton = vipPerButton;
    
    
    // 音频播放
    FMNoteDetailHeaderAudioPlayView *audioPlayView = [[FMNoteDetailHeaderAudioPlayView alloc] init];
    [self.contentView addSubview:audioPlayView];
    [audioPlayView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(payGuideLB.mas_bottom);
        make.height.equalTo(@45);
    }];
    self.audioPlayView = audioPlayView;
    
//    // 支付按钮
    UIButton *payBtn = [[UIButton alloc] init];
    [self.contentView addSubview:payBtn];
    [payBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(audioPlayView.mas_bottom);
        make.height.equalTo(@45);
    }];
    [payBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
    payBtn.titleLabel.font = [FMHelper scaleFont:16.0];
    [payBtn setBackgroundColor:FMNavColor];
    payBtn.layer.cornerRadius = 5.0f;
    payBtn.layer.masksToBounds = YES;
    [payBtn addTarget:self action:@selector(payBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    self.payBtn = payBtn;
    
    UILabel *payGuideLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [self.contentView addSubview:payGuideLabel];
    [payGuideLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(payBtn);
        make.top.equalTo(payBtn.mas_bottom).offset(10);
        make.bottom.equalTo(@0);
    }];
    self.payGuideLabel = payGuideLabel;
    
    FMNoteDetailVIPInfoView *vipInfoView = [[FMNoteDetailVIPInfoView alloc] init];
    vipInfoView.payBtnClickBlock = ^{
        [__weakSelf gotoPay];
    };
    [self.contentView addSubview:vipInfoView];
    [vipInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(payBtn.mas_top);
        make.height.equalTo(@(CGFLOAT_MIN));
    }];
    self.vipInfoView = vipInfoView;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(wxPayNoteSuccess:) name:kNoteWxPaySuccess object:nil];
}

-  (void)askCodeBtnClick:(UIButton *)sender {
    if (self.askCodeClick) {
        self.askCodeClick(sender);
    }
}

- (void)setModel:(FMNoteModel *)model {
    _model = model;
    
    // 时间
    NSString *timeStr = nil;
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.noteTime/1000];
    if ([date isToday]) {
        timeStr = [NSString stringFromDate:date format:@"HH:mm"];
    } else {
        if ([date isThisYear]) {
            timeStr = [NSString stringFromDate:date format:@"MM-dd HH:mm"];
        } else {
            timeStr = [NSString stringFromDate:date format:@"yyyy-MM-dd"];
        }
    }
    
    self.askCodeBtn.hidden = !(model.bignameDto.attestationType == 2);

    
    // 头像、名称、头衔、简介
    [self.infoView.iconImgV.iconImg sd_setImageWithURL:[NSURL URLWithString:model.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.infoView.nameLabel.text = model.bignameDto.userName;
    self.infoView.timeLabel.text = timeStr;
    self.infoView.userId = model.bignameDto.userId;
    self.infoView.islive = model.bignameDto.isLive;
    self.infoView.attestationType = model.bignameDto.attestationType;

    // 关注按钮
    self.focusBtn.dakaId = model.bignameDto.userId;
    self.focusBtn.isFocus = [[FMUserDataSyncManager sharedManager] isDakaNoticed:model.bignameDto.userId];
    self.focusBtn.hidden = [[FMUserDefault getUserId] isEqualToString:self.model.bignameDto.userId];
    
    [self setNoteContentUI:model];
   
}

- (void)setNoteContentUI:(FMNoteModel *)model {
    /********************************************************** 中间部分逻辑 ************************************************************/
    // 标题部分
    self.titleLB.font = [FMHelper scaleBoldFont:23];
    NSMutableParagraphStyle *textTitleStyle = [[NSMutableParagraphStyle alloc] init];
    textTitleStyle.lineSpacing = 3;
    if (model.noteTitle.length) {
        NSDictionary *textTitleDic = @{NSParagraphStyleAttributeName : textTitleStyle,NSFontAttributeName : [FMHelper scaleBoldFont:23]};
        CGSize titleSize = [model.noteTitle boundingRectWithSize:CGSizeMake(UI_SCREEN_WIDTH - 30, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:textTitleDic context:nil].size;
        NSMutableAttributedString *attrString = [[NSMutableAttributedString alloc] initWithString:model.noteTitle attributes:textTitleDic];
        self.titleLB.attributedText = attrString;
        self.titleLB.hidden = NO;
        [self.titleLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.infoView.mas_bottom).offset(15);
            make.height.equalTo(@(ceil(titleSize.height + 1)));
        }];
    } else {
        self.titleLB.hidden = YES;
        [self.titleLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.infoView.mas_bottom).offset(0);
            make.height.equalTo(@(CGFLOAT_MIN));
        }];
    }
    
    // 摘要部分，显示summry字段
    self.summaryLB.font = [FMHelper scaleFont:18];
    if (model.noteType.integerValue == 3 || model.noteType.integerValue == 1)  {
        if(model.noteSummary.length) {
            // 去除首位两端无用的换行符，空格符
            NSDictionary *textTitleDic = @{NSParagraphStyleAttributeName : textTitleStyle,NSFontAttributeName : [FMHelper scaleFont:18], NSForegroundColorAttributeName : UIColor.up_textPrimaryColor};
            NSString *summry = [model.noteSummary stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"\r\n "]];
            NSMutableAttributedString *attrString = [[NSMutableAttributedString alloc] initWithString:summry attributes:textTitleDic];
            self.summaryLB.attributedText = attrString;
            self.summaryBg.hidden = NO;
            [self.summaryLB mas_updateConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(@30);
                make.right.equalTo(@-30);
                make.top.equalTo(self.titleLB.mas_bottom).offset(35);
            }];
            self.summaryLB.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 60;
        } else {
            self.summaryBg.hidden = YES;
            [self.summaryLB mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.titleLB.mas_bottom).offset(0);
            }];
        }
    } else {
        self.summaryBg.hidden = YES;
        [self.summaryLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLB.mas_bottom).offset(0);
        }];
    }
    
    self.payGuideLB.text = @"";
    self.payGuideLB.hidden = YES;
    if (!self.summaryBg.isHidden) {
        [self.payGuideLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.summaryLB.mas_bottom).offset(15);
        }];
    } else {
        [self.payGuideLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.summaryLB.mas_bottom).offset(0);
        }];
    }

    // 支付按钮，自己没有权限读才有
    CGFloat vipinfoViewHeight = [FMHelper isBigFont] ? 98 : 85;

    if (model.authority.integerValue != 1) {
        self.audioPlayView.hidden = YES;
        [self.audioPlayView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@0);
            make.top.equalTo(self.payGuideLB.mas_bottom).offset(0);
        }];
        
        if (model.vipGroup.length) {
            self.payBtn.hidden = YES;
            self.payGuideLabel.hidden = YES;
            self.payGuideLabel.attributedText = [[NSAttributedString alloc] initWithString:@""];
            [self.payGuideLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.payBtn.mas_bottom).offset(0);
            }];
            self.vipInfoView.hidden = NO;
            self.vipInfoView.dakaId = model.bignameDto.userId;
            if (model.vipGroupDesc > 0) {
                // 会员专享
                [self.payBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.audioPlayView.mas_bottom).offset(15);
                    make.height.equalTo(@(vipinfoViewHeight + 16));
                }];
                [self.vipInfoView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.equalTo(@(vipinfoViewHeight + 16));
                }];
            } else {
                // 付费文章
                [self.payBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.audioPlayView.mas_bottom).offset(15);
                    make.height.equalTo(@(vipinfoViewHeight));
                }];
                [self.vipInfoView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.equalTo(@(vipinfoViewHeight));
                }];
            }
            self.vipInfoView.vipGroupDesc = model.vipGroupDesc;
            self.vipInfoView.price = [model.notePrice integerValue];
        } else {
            self.vipInfoView.hidden = YES;
            self.payBtn.hidden = NO;
            [self.payBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.audioPlayView.mas_bottom).offset(15);
                make.height.equalTo(@(45));
            }];
            
            if (model.authority.integerValue == -1) { // 暂停购买
                [self.payBtn setTitle:@"暂停购买" forState:UIControlStateNormal];
                [self.payBtn setBackgroundColor:ColorWithHex(0xe6e6e6)];
                [self.payBtn setTitleColor:ColorWithHex(0x5a5a5a) forState:UIControlStateNormal];
                self.payBtn.enabled = NO;
                
                self.payGuideLabel.hidden = YES;
                self.payGuideLabel.attributedText = [[NSAttributedString alloc] initWithString:@""];
                [self.payGuideLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.payBtn.mas_bottom).offset(0);
                }];
            } else if (model.authority.integerValue == 0) { // 需要付费
                self.payGuideLabel.hidden = NO;
                [self.payBtn setTitle:@"点击解锁完整笔记" forState:UIControlStateNormal];
                self.payGuideLabel.attributedText = [[NSString stringWithFormat:@"支付%@金币阅读精华部分", model.notePrice] attrStrWithMatchColor:FMNavColor pattern:@"\\d+(\\.\\d+)?金币" textFont:FontWithSize(14)];
                [self.payGuideLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.payBtn.mas_bottom).offset(10);
                }];
            } else {
                [self.payBtn setTitle:model.payText forState:UIControlStateNormal];
                self.payGuideLabel.hidden = YES;
                self.payGuideLabel.attributedText = [[NSAttributedString alloc] initWithString:@""];
                [self.payGuideLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(self.payBtn.mas_bottom).offset(0);
                }];
            }
        }
    } else {
        self.vipInfoView.hidden = YES;
        self.vipPerButton.hidden = (model.vipGroup.length == 0);
        
        if (self.model.contentAudio.length) {
            self.audioPlayView.hidden = NO;
            self.audioPlayView.detailModel = model;
            [self.audioPlayView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(@45);
                if (model.vipGroup.length > 0) {
                    make.top.equalTo(self.payGuideLB.mas_bottom).offset(15 + 37);
                } else {
                    make.top.equalTo(self.payGuideLB.mas_bottom).offset(15);
                }
            }];
        } else {
            self.audioPlayView.hidden = YES;
            [self.audioPlayView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(@0);
                if (model.vipGroup.length > 0) {
                    make.top.equalTo(self.payGuideLB.mas_bottom).offset(37);
                } else {
                    make.top.equalTo(self.payGuideLB.mas_bottom).offset(0);
                }
            }];
        }
            
        if (self.vipPerButton.hidden == NO) {
            // 投顾会员专享策略
            [self.vipPerButton setTitle:[NSString stringWithFormat:@"投顾会员专享策略:%@",model.vipGroupDesc] forState:UIControlStateNormal];
            [self.vipPerButton setImage:ImageWithName(@"note_vip_auth") forState:UIControlStateNormal];
            [self.vipPerButton layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:4];
            self.vipPerButton.hidden = NO;
            NSArray *colors =  @[(__bridge  id)ColorWithHex(0xFCE4C5).CGColor,(__bridge  id)ColorWithHex(0xF0BC80).CGColor];
            self.vipPerButton.bounds = CGRectMake(0, 0, UI_SCREEN_WIDTH - 30, 37);
            [self.vipPerButton drawCAGradientWithcolors:colors startPoint:CGPointMake(0, 0) endPoint:CGPointMake(0, 1)];
            [self.vipPerButton bringSubviewToFront:self.vipPerButton.titleLabel];
            [self.vipPerButton bringSubviewToFront:self.vipPerButton.imageView];
            UI_View_Radius(self.vipPerButton, 5);
            [self.vipPerButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.payGuideLB.mas_bottom).offset(10);
            }];
            [self.payBtn setTitle:@"" forState:UIControlStateNormal];
            self.payBtn.hidden = YES;
            [self.payBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.audioPlayView.mas_bottom).offset(0);
                make.height.equalTo(@(17));
            }];
        } else {
            [self.vipPerButton mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.payGuideLB.mas_bottom);
            }];
            [self.payBtn setTitle:@"" forState:UIControlStateNormal];
            self.payBtn.hidden = YES;
            [self.payBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.audioPlayView.mas_bottom).offset(0);
                make.height.equalTo(@(0));
            }];
        }
        
        self.payGuideLabel.hidden = YES;
        self.payGuideLabel.attributedText = [[NSAttributedString alloc] initWithString:@""];
        [self.payGuideLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.payBtn.mas_bottom).offset(0);
        }];
    }
}

#pragma mark -- http
- (void)wxPayGoodsId:(NSString *)goodsId {
    [HttpRequestTool wxPayNoteWithNoteId:self.model.noteId goodsId:goodsId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self wechatPay:dic];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)httpForPayNote:(NSString *)payType goodsId:(NSString *)goodsId{
    WEAKSELF
    [HttpRequestTool payNoteWithNoteId:self.model.noteId type:payType  goodsId:goodsId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] isKindOfClass:[NSDictionary class]]) {
            [SVProgressHUD dismiss];
            if ([dic[@"data"][@"discountPrice"] floatValue] > 0) {
                NSString *discountPrice = [NSString stringWithFormat:@"%@",dic[@"data"][@"discountPrice"]];
                FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
                popView.minusNum = discountPrice.integerValue;
                [popView show];
            } else {
                FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
                [popView show];
            }
            __weakSelf.model.noteContent = dic[@"data"][@"noteContent"];
            __weakSelf.model.authority = @"1";
            
            if ([payType isEqualToString:@"1"] && self.model.freeDesc.length == 0) {
                // 金币支付，更新本地的金币余额
                FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                userModel.coin = userModel.coin - [dic[@"data"][@"payPrice"] integerValue];
                [MyKeyChainManager save:kUserModel data:userModel];
            }

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if ([__weakSelf.delegate respondsToSelector:@selector(noteDetailHeaderCellPayBtnDidClicked: serviceTime:)]) {
                    [__weakSelf.delegate noteDetailHeaderCellPayBtnDidClicked:__weakSelf serviceTime:[dic[@"systemTime"] longLongValue]];
                }
            });
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - Private
- (void)wxPayNoteSuccess:(NSNotification *)noti {
    NSDictionary *dic = noti.userInfo;
    NSString *noteVideo = dic[@"noteVideo"];
    if (noteVideo.length) {
        return;
    }
    
    NSString *noteId = dic[@"noteId"];
    if ([noteId isEqualToString:self.model.noteId]) {
        FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
        [popView show];
        
        self.model.noteContent = dic[@"noteContent"];
        self.model.authority = @"1";
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if ([self.delegate respondsToSelector:@selector(noteDetailHeaderCellPayBtnDidClicked: serviceTime:)]) {
                [self.delegate noteDetailHeaderCellPayBtnDidClicked:self serviceTime:[dic[@"systemTime"] longLongValue]];
            }
        });
    }
}

/// 调起wx支付
- (void)wechatPay:(NSDictionary *)dic {
    NSDictionary *data = dic[@"data"][@"prepay"];
    NSString *wechatAppidForPay = [FMUserDefault getSeting:AppInit_PayAppid];
    [WXApi registerApp:wechatAppidForPay universalLink:UNIVERSAL_LINK];
    
    if (![WXApi isWXAppInstalled]) {
        [SVProgressHUD showInfoWithStatus:@"您还没有安装微信，请先安装"];
        return;
    }
    
    if (![WXApi isWXAppSupportApi]) {
        [SVProgressHUD showInfoWithStatus:@"您的微信版本太低，建议更新"];
        return;
    }
    
    NSString *stamp  = data[@"timestamp"];
    PayReq *req = [[PayReq alloc] init];
    req.partnerId = data[@"partnerid"];
    req.prepayId = data[@"prepayid"];
    req.timeStamp = stamp.intValue;
    req.nonceStr = data[@"noncestr"];
    req.package = @"Sign=WXPay";
    req.sign = data[@"sign"];
    
    [WXApi sendReq:req completion:^(BOOL success) {
        if (success) {
            [SVProgressHUD dismiss];
            
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict setObject:data[@"out_trade_no"] forKey:@"outTradeNo"];
            [dict setObject:self.model.noteId forKey:@"noteId"];
            [dict setObject:[FMUserDefault getUserId] forKey:@"userId"];
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults setObject:dict forKey:kUserDefault_IsPayNote];
            [defaults synchronize];
        } else {
            [SVProgressHUD showErrorWithStatus:@"打开微信异常，请重试"];
        }
    }];
}

- (void)vipBtnClick:(UIButton *)sender {
    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
        FMMemberCenterProductViewController *vc = [[FMMemberCenterProductViewController alloc] init];
        vc.bigcastId = self.model.bignameDto.userId;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
}

- (void)payBtnClicked:(UIButton *)btn {
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return ;
    }
    
    if ([self.model.sign isEqualToString:@"教学"]) {
        NSInteger days = daysDifferenceBetweenTimes(self.model.noteTime, [FMUserDefault getServerSystemTime]);
        if (days >= 7) {
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:self.model.noteTime / 1000.0];
            NSString *time = [date dateStringWithFormatString:@"yyyy-MM-dd"];
            NSString *reminder = [NSString stringWithFormat:@"该教学是%@号发布，距今已过%zd天，是否仍要解锁？", time, days];
            ShowConfirm([FMHelper getCurrentVC], reminder, nil, @"取消", @"确定", nil, ^{
                [self judgePayNeedVIP];
            });
        } else {
            [self judgePayNeedVIP];
        }
    } else {
        [self judgePayNeedVIP];
    }
}

- (void)judgePayNeedVIP {
    if (self.model.ex_needVIP) {
        [FMHelper showVIPAlertWithType:VIPReadTypeNote needVip:-1 authority:self.model.authority price:[self.model.notePrice floatValue] showAlert:YES clickSure:nil clickCancel:nil clickSureDismiss:YES];
    } else {
        [self gotoPay];
    }
}

NSInteger daysDifferenceBetweenTimes(NSInteger time1, NSInteger time2) {
    // 将时间戳转换为秒
    NSTimeInterval interval1 = time1 / 1000.0;
    NSTimeInterval interval2 = time2 / 1000.0;

    // 创建 NSDate 对象
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:interval1];
    NSDate *date2 = [NSDate dateWithTimeIntervalSince1970:interval2];

    // 计算两个日期之间的时间差
    NSTimeInterval timeDifference = [date2 timeIntervalSinceDate:date1];

    // 将时间差转换为天数
    NSInteger daysDifference = timeDifference / (24 * 60 * 60);

    return daysDifference;
}



- (void)gotoPay {
    if (self.model.notePrice.integerValue >= 30) {
        [HttpRequestTool getRandomDiscountStart:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                NSDictionary *discountInfo = [NSDictionary dictionaryWithDictionary:dic[@"data"]];
                if ([discountInfo[@"isOpen"] boolValue]) {
                    [self configPaymentView:discountInfo];
                } else {
                    [self configPaymentView:nil];
                }
            }
        }];
    } else {
        [self configPaymentView:nil];
    }
}

- (void)configPaymentView:(NSDictionary *)discountInfo {
    WEAKSELF
    FMNoteModel *model = self.model;
    
    if (model.enablePayModel.count == 0) {
        // 没有可用的支付方式
        [[FMPayTool payTool] noEnablePayModelWithErrorCode:model.noPayModelCode.integerValue errorText:model.noPayModelText];
        return;
    }
    NSString *title = model.noteTitle.length > 0 ? model.noteTitle : model.noteSummary;
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.model.bignameDto.userId certCode:self.model.bignameDto.certCode clickView:self.payBtn confirmOperation:^{
        EnablePayModel *payModel = [NSArray modelArrayWithClass:[EnablePayModel class] json:model.enablePayModel].firstObject;
        payModel.bignameId = model.bignameDto.userId;
        payModel.consumeType = 1;
        payModel.contentId = model.noteId.integerValue;
        payModel.discountInfo = discountInfo;
        
        EnablePayModel *wxPayModel = [[EnablePayModel alloc] init];
        wxPayModel.name = @"微信";
        wxPayModel.type = PaymentTypeWechat;
        wxPayModel.consumeType = 1;
        wxPayModel.bignameId = model.bignameDto.userId;
        wxPayModel.contentId = model.noteId.integerValue;
        
        PaymentView *payView = [PaymentView showWithEnablePayList:@[payModel, wxPayModel] payPrice:model.notePrice productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
            if (selectedModel.type == PaymentTypeWechat) {
                [__weakSelf wxPayGoodsId:selectedModel.couponId];
            } else {
                [__weakSelf httpForPayNote:[NSString stringWithFormat:@"%zd", selectedModel.type] goodsId:selectedModel.couponId];
            }
        } dismissBlock:^{
        }];
                
        NSDictionary *couponDic = [FMUserDefault getUnArchiverDataForKey:@"BeginnerUserCoupon"];
        FMCouponTableModel *couponModel = couponDic[[FMUserDefault getUserId]];
        NSDictionary *dic = [FMUserDefault getUnArchiverDataForKey:@"CouponGuideTypeStep1"];
        NSNumber *isStep1 = dic[[FMUserDefault getUserId]];
         if (couponModel.couponId.length > 0 && !isStep1.boolValue) {
             FMCouponUseGuideVC *vc = [[FMCouponUseGuideVC alloc] initWithGuideType:CouponGuideTypeStep1];
             vc.view.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
             [[FMAppDelegate shareApp].window addSubview:vc.view];
             
             __weak PaymentView *weakPayView = payView;
             payView.getCouponInfo = ^{
                 vc.targetRect = [weakPayView couponBgViewCoverWindowFrame];
             };
         }
        __weak PaymentView *weakPayView = payView;
        payView.couponSelected = ^{
            NSDictionary *couponDic = [FMUserDefault getUnArchiverDataForKey:@"BeginnerUserCoupon"];
            FMCouponTableModel *couponModel = couponDic[[FMUserDefault getUserId]];
            NSDictionary *dic = [FMUserDefault getUnArchiverDataForKey:@"CouponGuideTypeStep4"];
            NSNumber *isStep4 = dic[[FMUserDefault getUserId]];
            if (couponModel.couponId.length > 0 && !isStep4.boolValue) {
                FMCouponUseGuideVC *vc = [[FMCouponUseGuideVC alloc] initWithGuideType:CouponGuideTypeStep4];
                vc.view.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
                [[FMAppDelegate shareApp].window addSubview:vc.view];
                vc.targetRect = [weakPayView headerViewCoverWindowFrame];
            }
        };
        
    }];
}

// 更新关注相关UI，（关注按钮，关注数量）
- (void)updateUIWithFollowStatus {
    self.focusBtn.selected = [[FMUserDataSyncManager sharedManager] isDakaNoticed:self.model.bignameDto.userId];
    if (self.focusBtn.selected) {
        UI_View_BorderRadius(self.focusBtn, 15.0, 1, ColorWithHex(0xd6d6d6));
        [self.focusBtn setBackgroundColor:FMWhiteColor];
        [self.focusBtn setImage:nil forState:UIControlStateNormal];
        [self.focusBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:0];
    } else {
        UI_View_BorderRadius(self.focusBtn, 15.0, 1, FMNavColor);
        [self.focusBtn setBackgroundColor:FMNavColor];
        [self.focusBtn setImage:[UIImage imageNamed:@"add_focus"] forState:UIControlStateNormal];
        [self.focusBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:4];
    }
}

- (void)focusNotification:(NSNotification *)noti {
    NSDictionary *userIfo = noti.userInfo;
    if ([self.model.bignameDto.userId isEqualToString:userIfo[@"userid"]]) {
        [self updateUIWithFollowStatus];
    }
}

@end
