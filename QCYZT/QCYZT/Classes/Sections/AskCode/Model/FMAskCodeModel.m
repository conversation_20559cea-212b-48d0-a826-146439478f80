//
//  FMAskCodeModel.m
//  QCYZT
//
//  Created by shumi on 2022/12/6.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMAskCodeModel.h"
#import "FMUPDataTool.h"

@implementation QuestionPerm

@end

@implementation FMAskCodeModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"askCodeId" : @"id"};
}

+ (NSDictionary *)modelContainerPropertyGenericClass{
    return @{
             @"questionPerm" : QuestionPerm.class,
             @"bignameDto" : FMBigCastCommonModel.class,
            };
}

- (NSMutableAttributedString *)questionContentAttrStr {
    if (!_questionContentAttrStr.length && _questionContent.length) {
        _questionContentAttrStr = [self highlightStockNamesInText:_questionContent withStockArr:[[FMUPDataTool shareManager] getAllStocks]];
    }
        
    return _questionContentAttrStr;
}


- (NSMutableAttributedString *)highlightStockNamesInText:(NSString *)text withStockArr:(NSArray <DBStockModel *>*)stockArr {
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:text];
    attributedString.yy_font = [FMHelper scaleFont:16];
    attributedString.yy_color = UIColor.up_textPrimaryColor;
    attributedString.yy_lineSpacing = 5.0f;

    NSMutableString *mutableText = [text mutableCopy];
    
    NSLog(@"开始识别");
    for (DBStockModel *stockModel in stockArr) {
        NSRange searchRange = NSMakeRange(0, [mutableText length]);
        NSRange foundRange;
        while (searchRange.location < mutableText.length) {
            searchRange.length = mutableText.length - searchRange.location;
            foundRange = [mutableText rangeOfString:stockModel.name options:NSCaseInsensitiveSearch range:searchRange];
            if (foundRange.location != NSNotFound) {
                [attributedString yy_setTextHighlightRange:foundRange color:ColorWithHex(0x0077ff) backgroundColor:nil tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
                    [UPRouterUtil goMarketStock:stockModel.setcode code:stockModel.code];
                }];
                
                // Replace matched string in mutableText with a placeholder
                NSString *placeholder = [@"" stringByPaddingToLength:stockModel.name.length withString:@" " startingAtIndex:0];
                [mutableText replaceCharactersInRange:foundRange withString:placeholder];
                
                searchRange.location = foundRange.location + foundRange.length;
            } else {
                break;
            }
        }
    }
    NSLog(@"结束识别");
    
    return attributedString;
}



@end
