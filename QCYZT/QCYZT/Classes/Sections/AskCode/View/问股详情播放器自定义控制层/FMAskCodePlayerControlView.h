//
//  FMAskCodePlayerControlView.h
//  QCYZT
//
//  Created by shumi on 2022/10/20.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "ZFPlayerControlView.h"
@class FMAskCodeModel;
NS_ASSUME_NONNULL_BEGIN

@protocol FMAsCodePlayerControlDelegate <NSObject>
- (void)detailPayViewPayBtnClick:(UIButton *)sender;
- (void)videoReWatch:(UIButton *)sender;
@end

@interface FMAskCodePlayerControlView : ZFPlayerControlView
@property (nonatomic, strong) FMAskCodeModel *model;
@property (nonatomic,weak) id<FMAsCodePlayerControlDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
