//
//  FMAskCodePlayView.m
//  QCYZT
//
//  Created by th on 17/1/9.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMAskCodePlayView.h"
#import "FMAskCodeModel.h"
#import "WeakMutableArray.h"
#import "NSObject+FBKVOController.h"
#import "FMPlayerManager.h"

#define kWaveViewWidth      145
#define kVoiceViewHeight    45
#define kVoiceViewX         58
#define kSliderViewWidth    1

@interface FMAskCodePlayView()

@property (nonatomic,weak) UIButton *playBtn;
@property (nonatomic,weak) UIView *voiceGrayView;
@property (nonatomic,weak) UIView *voiceWhiteView;
@property (nonatomic, strong) UIView *sliderView;

// 播放免责声明新增属性
@property (nonatomic, copy) NSString *disclaimerUrl;
@property (nonatomic, assign) BOOL isPlayingDisclaimer;

@end

@implementation FMAskCodePlayView

- (void)dealloc {
    FMLog(@"%s", __func__);
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
    // 停止所有播放
    if ([[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:self.model.answerOssUrl] ||
        [[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:self.disclaimerUrl]) {
        [[MP3PlayTool shareMusicPlay] musicStop];
        self.voiceGrayView.frame = CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight);
        self.voiceWhiteView.frame = CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight);
        self.playBtn.selected = NO;
        self.sliderView.hidden = YES;
    }
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        [self setUp];
    }
    return self;
}

- (void)setUp {    
    UIImageView *voiceImageV = [[UIImageView alloc] init];
    [self addSubview:voiceImageV];
    [voiceImageV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(0);
    }];
    voiceImageV.image = [UIImage imageNamed:@"bluebg"];
    
    UIButton *playBtn = [[UIButton alloc] init];
    [self addSubview:playBtn];
    [playBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(0);
        make.left.equalTo(10);
        make.height.width.equalTo(45);
    }];
    [playBtn setImage:[UIImage imageNamed:@"startbf"] forState:UIControlStateNormal];
    [playBtn setImage:[UIImage imageNamed:@"stopbf"] forState:UIControlStateSelected];
    [playBtn addTarget:self action:@selector(playBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    self.playBtn = playBtn;
    
    UIView *voiceGrayView = [[UIView alloc] initWithFrame:CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight)];
    [self addSubview:voiceGrayView];
    voiceGrayView.backgroundColor = [UIColor whiteColor];
    voiceGrayView.alpha = 0.2;
    self.voiceGrayView = voiceGrayView;
    
    UIView *voiceWhiteView = [[UIView alloc] initWithFrame:CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight)];
    [self addSubview:voiceWhiteView];
    voiceWhiteView.backgroundColor = [UIColor whiteColor];
    self.voiceWhiteView = voiceWhiteView;
    
    UIImageView *voiceWaveImageV = [[UIImageView alloc] init];
    [self addSubview:voiceWaveImageV];
    [voiceWaveImageV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(@0);
        make.left.equalTo(@(kVoiceViewX));
        make.width.equalTo(@(kWaveViewWidth));
    }];
    voiceWaveImageV.image = [UIImage imageNamed:@"toumingjindu"];
    voiceWaveImageV.userInteractionEnabled = YES;
    UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(panSlider:)];
    [voiceWaveImageV addGestureRecognizer:pan];
    
    UIView *sliderView = [UIView new];
    [self addSubview:sliderView];
    sliderView.frame = CGRectMake(kVoiceViewX, 0, kSliderViewWidth, kVoiceViewHeight);
    sliderView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHexAlpha(0xffffff, 0.5), FMWhiteColor, FMWhiteColor, FMWhiteColor, FMWhiteColor, ColorWithHexAlpha(0xffffff, 0.5)] withFrame:sliderView.bounds direction:GradientDirectionTopToBottom];
    sliderView.hidden = YES;
    self.sliderView = sliderView;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(paySuccess) name:kQuestionPaySuccess object:nil];
}

- (void)setModel:(FMAskCodeModel *)model {
    if (model == nil) {
        return;
    }
    _model = model;
    
    if ([MP3PlayTool shareMusicPlay].playStatus == MP3PlayToolPlayStatusStopped) {    // 如果MP3PlayTool处理初始状态，设置URL加载音频
        [MP3PlayTool shareMusicPlay].currentPlayUrl = _model.answerOssUrl;
        [self setSelfAsObserver];
    } else { // 如果MP3PlayTool已经设置了URL
        if ([[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:_model.answerOssUrl]){ // 如果URL相同，同步UI，并添加self为观察者
            self.voiceGrayView.frame = CGRectMake(kVoiceViewX, 0, kWaveViewWidth*[MP3PlayTool shareMusicPlay].loadProgress, kVoiceViewHeight);
            self.voiceWhiteView.frame = CGRectMake(kVoiceViewX, 0, kWaveViewWidth*[MP3PlayTool shareMusicPlay].playProgress, kVoiceViewHeight);
            self.sliderView.frame = CGRectMake(kVoiceViewX + kWaveViewWidth*[MP3PlayTool shareMusicPlay].playProgress, 0, kSliderViewWidth, kVoiceViewHeight);
            self.sliderFrameChangeBlock([MP3PlayTool shareMusicPlay].playProgress);

            switch ([MP3PlayTool shareMusicPlay].playStatus) {
                case MP3PlayToolPlayStatusPlaying:
                {
                    self.playBtn.selected = YES;
                    self.sliderView.hidden = NO;
                    break;
                }
                case MP3PlayToolPlayStatusPaused:
                {
                    self.playBtn.selected = NO;
                    self.sliderView.hidden = NO;
                    break;
                }
                case MP3PlayToolPlayStatusStopped:
                {
                    self.playBtn.selected = NO;
                    self.sliderView.hidden = YES;
                    break;
                }
                    
                default:
                    break;
            }
            [self setSelfAsObserver];
        } else { // 如果URL不同，恢复UI
            self.voiceGrayView.frame = CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight);
            self.voiceWhiteView.frame = CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight);
            self.playBtn.selected = NO;
            self.sliderView.hidden = YES;
        }
    }
}

- (void)setSelfAsObserver {
    // 将self添加到可变数组（可变数组是dic的value，key是url）中，并将self设置为观察者
    NSString *currentPlayUrl = [MP3PlayTool shareMusicPlay].currentPlayUrl;
    if (!currentPlayUrl.length) {
        return;
    }
    WeakMutableArray *arr = [[MP3PlayTool shareMusicPlay].urlDic objectForKey:currentPlayUrl];
    if (arr.allCount) {
        if (![arr containsObject:self]) {
            [arr addObject:self];
            [self observeKeyPath];
        }
    } else {
        WeakMutableArray *tmpArr = [[WeakMutableArray alloc] init];
        [tmpArr addObject:self];
        [[MP3PlayTool shareMusicPlay].urlDic setObject:tmpArr forKey:currentPlayUrl];
        [self observeKeyPath];
    }
}

- (void)addPlayProgressObserver {
    WEAKSELF;
    [self.KVOController observe:[MP3PlayTool shareMusicPlay] keyPath:@"playProgress" options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if ([[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:__weakSelf.disclaimerUrl]) {
            return;
        }
        
//        NSLog(@"问股播放--- %f", [[MP3PlayTool shareMusicPlay] playProgress]);
        if (![[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:__weakSelf.model.answerOssUrl]) {
            __weakSelf.playBtn.selected = NO;
            __weakSelf.sliderView.hidden = YES;
            __weakSelf.voiceWhiteView.frame = CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight);
            return;
        }
        
        if ([change objectForKey:NSKeyValueChangeNewKey]) {
            CGFloat progress = [[change valueForKey:NSKeyValueChangeNewKey] floatValue];
            if (!isnan(progress) && progress >= 0) {
                if (progress >= 1.0) {
                    progress = 1.0;
                }
                
                __weakSelf.voiceWhiteView.frame = CGRectMake(kVoiceViewX, 0, kWaveViewWidth*progress, kVoiceViewHeight);
                __weakSelf.sliderView.frame = CGRectMake(kVoiceViewX + kWaveViewWidth*progress, 0, kSliderViewWidth, kVoiceViewHeight);
                __weakSelf.sliderFrameChangeBlock(progress);
            }
        }
    }];
}

- (void)addLoadProgressObserver {
    WEAKSELF
    // 监听加载进度
    [self.KVOController observe:[MP3PlayTool shareMusicPlay] keyPath:@"loadProgress" options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if ([[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:__weakSelf.disclaimerUrl]) {
            return;
        }
        
        if (![[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:__weakSelf.model.answerOssUrl]) {
            __weakSelf.voiceGrayView.frame = CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight);
            return;
        }
        CGFloat progress = [[change valueForKey:NSKeyValueChangeNewKey] floatValue];
        if (!isnan(progress) && __weakSelf.voiceGrayView.width != kWaveViewWidth) {
            __weakSelf.voiceGrayView.frame = CGRectMake(kVoiceViewX, 0, kWaveViewWidth*progress, kVoiceViewHeight);
        }
    }];
}

- (void)addPlayStatusObserver {
    WEAKSELF
    // 监听播放状态
    [self.KVOController observe:[MP3PlayTool shareMusicPlay] keyPath:@"playStatus" options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        MP3PlayToolPlayStatus playStatus = [[change valueForKey:NSKeyValueChangeNewKey] integerValue];
        if ([[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:__weakSelf.disclaimerUrl]) {
            if (playStatus == MP3PlayToolPlayStatusStopped) {
                __weakSelf.playBtn.selected = NO;
                __weakSelf.sliderView.hidden = YES;
                
                __weakSelf.voiceWhiteView.frame = CGRectMake(kVoiceViewX, 0, 0, kVoiceViewHeight);
                [MP3PlayTool shareMusicPlay].currentPlayUrl = __weakSelf.model.answerOssUrl;
            }
            return;
        }

        
        switch (playStatus) {
            case MP3PlayToolPlayStatusPlaying:
            {
                __weakSelf.playBtn.selected = YES;
                __weakSelf.sliderView.hidden = NO;
                break;
            }
            case MP3PlayToolPlayStatusPaused:
            {
                __weakSelf.playBtn.selected = NO;
                break;
            }
            case MP3PlayToolPlayStatusStopped:
            {
                __weakSelf.playBtn.selected = NO;
                __weakSelf.sliderView.hidden = YES;

                // 播放完毕后自动播放免责声明。 由于MP3PlayTool中是调用seekToBeginAndPause进入的MP3PlayToolPlayStatusStopped，所以需要加延时
                dispatch_async(dispatch_get_main_queue(), ^{
                    [__weakSelf playDisclaimer];
                });
                break;
            }
                
            default:
                break;
        }
    }];
}

- (void)observeKeyPath {
    [self addPlayProgressObserver];
    [self addLoadProgressObserver];
    [self addPlayStatusObserver];
}

- (void)removePlayProgressObserver {
    [self.KVOController unobserve:[MP3PlayTool shareMusicPlay] keyPath:@"playProgress"];
}

- (void)setTotalTime:(CGFloat)totalTime {
    _totalTime = totalTime;
    
    if ([[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:_model.answerOssUrl]) {
        [MP3PlayTool shareMusicPlay].totalTime = totalTime;
    }
}

#pragma mark - Notification
- (void)paySuccess {
    if ([MP3PlayTool shareMusicPlay].player.rate == 0) {
        WEAKSELF;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [__weakSelf playBtnClicked:__weakSelf.playBtn];
        });
    }
}

#pragma mark - Private
- (void)playBtnClicked:(UIButton *)btn {
    // 如果正在播放免责声明，停止播放
    if (self.isPlayingDisclaimer) {
        self.isPlayingDisclaimer = NO;
        [[MP3PlayTool shareMusicPlay] musicStop];
    }
    
    [[FMGlobalPlayTool shareTool] dismissGlobalPlayView];
    
    // 设置锁屏信息和代理
    [MP3PlayTool shareMusicPlay].title = _model.questionContent;
    [MP3PlayTool shareMusicPlay].artist = _model.answerUsername;
    [MP3PlayTool shareMusicPlay].lockImage = nil;
    [MP3PlayTool shareMusicPlay].lockCanDrag = NO;
    
    if (!self.playBtn.selected) {
        if (![[MP3PlayTool shareMusicPlay].currentPlayUrl isEqualToString:_model.answerOssUrl]) {
            [MP3PlayTool shareMusicPlay].currentPlayUrl = _model.answerOssUrl;
            [self setSelfAsObserver];
        }
        
        [[MP3PlayTool shareMusicPlay] musicPlay];
    } else {
        [[MP3PlayTool shareMusicPlay] musicPause];
    }
}

- (void)panSlider:(UIPanGestureRecognizer *)sender {
    if (self.sliderView.hidden || self.isPlayingDisclaimer) {
        return;
    }
    CGPoint point =  [sender translationInView:sender.view];
    [sender setTranslation:CGPointZero inView:sender.view];
    
    self.sliderView.x += point.x;
    CGFloat seekProgress = (self.sliderView.x - kVoiceViewX) / kWaveViewWidth;
    if (seekProgress < 0) {
        seekProgress = 0;
    } else if (seekProgress > 1) {
        seekProgress = 1;
    }
    self.voiceWhiteView.frame = CGRectMake(kVoiceViewX, 0, kWaveViewWidth*seekProgress, kVoiceViewHeight);
    self.sliderView.frame = CGRectMake(kVoiceViewX + kWaveViewWidth*seekProgress, 0, kSliderViewWidth, kVoiceViewHeight);
    self.sliderFrameChangeBlock(seekProgress);
//    NSLog(@"滑动 %f， %f", point.x, self.sliderView.x);
    
    if (sender.state == UIGestureRecognizerStateBegan) {
        [self removePlayProgressObserver];  // 拖动过程中移除观察
    } else if (sender.state == UIGestureRecognizerStateEnded) {
        WEAKSELF
        [[MP3PlayTool shareMusicPlay] seekToTimeWithProgress:seekProgress completionBlock:^{
            [__weakSelf addPlayProgressObserver];
        }];
    }
}

// 新增方法：播放免责声明
- (void)playDisclaimer {
    if (!self.disclaimerUrl.length) {
        self.disclaimerUrl = @"https://file.djc8888.com/audio/2025-04-17/990e64cb0395186d990e64cb0395186d.mp3";
    }
    
    self.isPlayingDisclaimer = YES;
    NSLog(@"播放免责声明");
    [MP3PlayTool shareMusicPlay].currentPlayUrl = self.disclaimerUrl;
    [[MP3PlayTool shareMusicPlay] musicPlay];
}

@end

