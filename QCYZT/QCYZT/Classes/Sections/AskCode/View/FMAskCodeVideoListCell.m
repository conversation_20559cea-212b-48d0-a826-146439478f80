//
//  FMAskCodeVideoListCell.m
//  QCYZT
//
//  Created by zeng on 2021/11/15.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAskCodeVideoListCell.h"
#import "FMAskCodeModel.h"
@interface FMAskCodeVideoListCell()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *watchListenLabel;
@property (nonatomic, strong) UIButton *praiseBtn;
@property (nonatomic, strong) ZLTagLabel *timeLB;

@property (nonatomic, strong) UIView *bottomLine;

@end

@implementation FMAskCodeVideoListCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.backgroundColor = self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:16] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(@18);
    }];
    self.titleLabel = titleLabel;
    
    DakaInfoNewView *infoView = [[DakaInfoNewView alloc] init];
    [self.contentView addSubview:infoView];
    [infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(titleLabel.mas_bottom).offset(12);
//        make.height.equalTo(@40);
    }];
    self.infoView = infoView;
    
    UIView *gesView = [[UIView alloc] init];
    [self.contentView addSubview:gesView];
    [gesView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(infoView.timeLabel);
        make.height.equalTo(@20);
    }];
    gesView.userInteractionEnabled = YES;
    [gesView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
        // 屏蔽点击跳转到详情
    }]];

    UIButton *praiseBtn = [[UIButton alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] normalTextColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor title:@"点赞" image:FMImgInBundle(@"笔记/点赞数") target:self action:@selector(praiseBtnClicked)];
    [gesView addSubview:praiseBtn];
    [praiseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-10);
        make.centerY.equalTo(@0);
        make.left.equalTo(@0);
    }];
    [praiseBtn setTitleColor:FMNavColor forState:UIControlStateDisabled];
    [praiseBtn setImage:ImageWithName(@"new_praised") forState:UIControlStateDisabled];
    self.praiseBtn = praiseBtn;
    
    UILabel *watchListenLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:watchListenLabel];
    [watchListenLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(gesView);
        make.right.equalTo(gesView.mas_left).offset(-15);
    }];
    self.watchListenLabel = watchListenLabel;
    
    UIImageView *videoImg = [[UIImageView alloc] init];
    videoImg.userInteractionEnabled = YES;
    [self.contentView addSubview:videoImg];
    [videoImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(titleLabel);
        make.top.equalTo(infoView.mas_bottom).offset(15);
        make.bottom.equalTo(@-20);
        make.height.equalTo(@((UI_SCREEN_WIDTH - 30) * 194.0 / 345));
    }];
    videoImg.contentMode = UIViewContentModeScaleAspectFill;
    videoImg.clipsToBounds = YES;
    self.videoImg = videoImg;
    
    UIImageView *playImg = [[UIImageView alloc] init];
    playImg.image = [UIImage imageNamed:@"askcode_list_video"];
    [videoImg addSubview:playImg];
    [playImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.equalTo(videoImg);
        make.size.equalTo(@(CGSizeMake(45, 45)));
    }];
    
    ZLTagLabel *timeLB = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:FMWhiteColor backgroundColor:ColorWithHexAlpha(0x000000, 0.6) numberOfLines:1];
    timeLB.textAlignment = NSTextAlignmentCenter;
    timeLB.widthPadding = 12.0;
    UI_View_Radius(timeLB, 10);
    [videoImg addSubview:timeLB];
    [timeLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(-10));
        make.bottom.equalTo(@(-10));
        make.height.equalTo(@(20));
    }];
    self.timeLB = timeLB;
    
    self.bottomLine = [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.bottom.equalTo(@0);
        make.height.equalTo(@0.5);
    }];
    self.bottomLine.backgroundColor = UIColor.fm_sepline_color;
}

- (void)praiseBtnClicked {
    if ([FMHelper checkLoginStatus]) {
        [HttpRequestTool questionPraiseWithQuestionId:self.askModel.askCodeId start:^{
            self.praiseBtn.userInteractionEnabled = NO;
        } failure:^{
            self.praiseBtn.userInteractionEnabled = YES;
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            self.praiseBtn.userInteractionEnabled = YES;

            if ([dic[@"status"] isEqualToString:@"1"]) {
                [[FMUserDataSyncManager sharedManager] likeQuestion:self.askModel.askCodeId];
                self.askModel.satisfiedNums = [NSString stringWithFormat:@"%ld",self.askModel.satisfiedNums.integerValue + 1];
                
                NSString *praiseNumStr = nil;
                if (self.askModel.satisfiedNums.integerValue < 10000) {
                    praiseNumStr = [NSString stringWithFormat:@"%@", self.askModel.satisfiedNums];
                } else {
                    double wan = self.askModel.satisfiedNums.integerValue / 10000.0;
                    praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
                }
                [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
                [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0f];
                
                self.praiseBtn.enabled = NO;
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

- (void)setAskModel:(FMAskCodeModel *)askModel {
    _askModel = askModel;
    
    NSMutableAttributedString *titleAttrStr = [askModel.questionContent attrStrWithMatchColor:FMSearchOrangeColor pattern:self.searchKeyWord textFont:[FMHelper scaleFont:16]];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 5.0f;
    [titleAttrStr addAttributes:@{NSParagraphStyleAttributeName : style} range:NSMakeRange(0, titleAttrStr.length)];
    self.titleLabel.attributedText = titleAttrStr;
    
    [self.infoView.iconImgV.iconImg sd_setImageWithURL:[NSURL URLWithString:askModel.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.infoView.nameLabel.text = askModel.bignameDto.userName;
    self.infoView.userId = askModel.bignameDto.userId;
    // 非投顾账户
    self.infoView.islive = askModel.bignameDto.isLive;
    self.infoView.attestationType = askModel.bignameDto.attestationType;
    
//    long timeTemp = (askModel.questionAnswerFlag.integerValue == 1 && askModel.checkStatus.integerValue == 1) ? askModel.answerTime : askModel.questionTime;
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:askModel.answerTime / 1000];
    NSString *timeStr = nil;
    if ([nowDate isToday]) {
        timeStr = [NSString stringFromDate:nowDate format:@"HH:mm 回答"];
    } else {
        if ([nowDate isThisYear]) {
            timeStr = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm 回答"];
        } else {
            timeStr = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd 回答"];
        }
    }
    self.infoView.timeLabel.text = timeStr;
    self.timeLB.text = [NSString stringWithFormat:@"%@",askModel.answerVideoTime];
    [self.videoImg sd_setImageWithURL:[NSURL URLWithString:askModel.questionImg] placeholderImage:[UIImage imageNamed:@"home_shortCut_bigPlaceholder"]];

    if (askModel.listenerNums.integerValue > 0) {
        NSString *readNumStr = nil;
        if (askModel.listenerNums.integerValue < 10000) {
            readNumStr = [NSString stringWithFormat:@"%@人看过",askModel.listenerNums];
        } else {
            double wan = askModel.listenerNums.integerValue / 10000.0;
            readNumStr = [NSString stringWithFormat:@"%.1f万人看过", wan];
        }
        self.watchListenLabel.text = readNumStr;
    } else {
        self.watchListenLabel.text = @"";
    }
    
    if (askModel.satisfiedNums.integerValue > 0) {
        NSString *praiseNumStr = nil;
        if (askModel.satisfiedNums.integerValue < 10000) {
            praiseNumStr = [NSString stringWithFormat:@"%@", askModel.satisfiedNums];
        } else {
            double wan = askModel.satisfiedNums.integerValue / 10000.0;
            praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
        }
        [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
    } else {
        [self.praiseBtn setTitle:@"点赞" forState:UIControlStateNormal];
    }
    [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5];
    self.praiseBtn.enabled = ![[FMUserDataSyncManager sharedManager] isQuestionLiked:askModel.askCodeId];
}



- (void)setIsLastCell:(BOOL)isLastCell {
    _isLastCell = isLastCell;
    
    self.bottomLine.hidden = isLastCell;
}

@end
