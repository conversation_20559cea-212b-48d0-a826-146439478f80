//
//  FMAskCodeStockResultTableView.m
//  QCYZT
//
//  Created by shumi on 2024/1/25.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMAskCodeStockResultTableView.h"

@interface FMAskCodeStockResultTabCell : UITableViewCell
@property (nonatomic, strong) FMSearchStockModel *model;
@property (nonatomic, strong) UILabel *stockLB;

@end

@implementation FMAskCodeStockResultTabCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}


- (void)setupUI {
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    UILabel *stockLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1];
    [self.contentView addSubview:stockLB];
    [stockLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(20);
    }];
    self.stockLB = stockLB;
}

- (void)setModel:(FMSearchStockModel *)model {
    _model = model;
    self.stockLB.text = [NSString stringWithFormat:@"%@  %@",model.name,model.code];
}

@end


@interface FMAskCodeStockResultTableView ()<UITableViewDataSource,UITableViewDelegate>

@end

@implementation FMAskCodeStockResultTableView

- (instancetype)initWithFrame:(CGRect)frame style:(UITableViewStyle)style {
    self = [super initWithFrame:frame style:style];
    if (self) {
        self.separatorStyle = UITableViewCellSeparatorStyleNone;
        self.delegate = self;
        self.dataSource = self;
        self.bounces = NO;
        [self registerCellClass:[FMAskCodeStockResultTabCell class]];
        self.tableFooterView = [UIView new];
    }
    return self;
}

//- (void)layoutSubviews {
//    [super layoutSubviews];
//    NSLog(@"当前contentSize:%@",NSStringFromCGSize(self.contentSize));
//
//}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMAskCodeStockResultTabCell *cell = [tableView reuseCellClass:[FMAskCodeStockResultTabCell class]];
    cell.model = self.dataArr[indexPath.row];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 35;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    FMSearchStockModel *model = self.dataArr[indexPath.row];
    if (self.stockSelectBlock) {
        self.stockSelectBlock(model);
    }
}

- (void)setDataArr:(NSArray<FMSearchStockModel *> *)dataArr {
    _dataArr = dataArr;
    [self reloadData];
}

@end
