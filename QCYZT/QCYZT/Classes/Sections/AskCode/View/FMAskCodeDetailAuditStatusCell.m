//
//  FMAskCodeDetailAuditStatusCell.m
//  QCYZT
//
//  Created by macPro on 2019/12/17.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import "FMAskCodeDetailAuditStatusCell.h"
#import "FMAskCodeListCell.h"
#import "AskCodeAnswerViewController.h"

@interface FMAskCodeDetailAuditStatusCell()

@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *reasonLabel;
@property (nonatomic, strong) UIButton *answerBtn;

@end

@implementation FMAskCodeDetailAuditStatusCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = ColorWithHex(0xf1a742);
    
    UILabel *statusLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:16] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1];
    [self.contentView addSubview:statusLabel];
    [statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@15);
    }];
    self.statusLabel = statusLabel;
    
    UILabel *reasonLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:16] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:0];
    [self.contentView addSubview:reasonLabel];
    [reasonLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(statusLabel.mas_bottom).offset(6);
    }];
    self.reasonLabel = reasonLabel;

    UIButton *answerBtn = [[UIButton alloc] init];
    [self.contentView addSubview:answerBtn];
    [answerBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(reasonLabel.mas_bottom).offset(15);
        make.width.equalTo(@120);
        make.height.equalTo(@40);
        make.bottom.equalTo(@-15);
    }];
    answerBtn.backgroundColor = FMNavColor;
    answerBtn.layer.cornerRadius = 20;
    answerBtn.layer.masksToBounds = YES;
    [answerBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
    [answerBtn setTitle:@"重新回答" forState:UIControlStateNormal];
    answerBtn.titleLabel.textColor = [UIColor whiteColor];
    [answerBtn addTarget:self action:@selector(answerAgain) forControlEvents:UIControlEventTouchUpInside];
    self.answerBtn = answerBtn;
    
    UIView *sepline = [[UIView alloc] init];
    [self.contentView addSubview:sepline];
    [sepline mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@0);
        make.height.equalTo(@0.5);
    }];
    sepline.backgroundColor = UIColor.fm_sepline_color;
}

- (void)setModel:(FMAskCodeModel *)model {
    _model = model;
    if (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == -1) {
        self.hidden = NO;
        self.statusLabel.text = @"审核状态：已驳回";
        self.reasonLabel.text = [NSString stringWithFormat:@"驳回理由：%@", model.rejectReason];
    } else {
        self.hidden = YES;
    }
}

- (void)answerAgain {
    AskCodeAnswerViewController *askCodeVC = [[AskCodeAnswerViewController alloc] init];
    askCodeVC.questionId = self.model.askCodeId;
    [[FMHelper getCurrentVC].navigationController pushViewController:askCodeVC animated:YES];
}


@end
