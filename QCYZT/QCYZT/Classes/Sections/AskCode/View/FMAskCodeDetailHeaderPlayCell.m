//
//  FMAskCodeDetailHeaderPlayCell.m
//  QCYZT
//
//  Created by th on 17/1/9.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMAskCodeDetailHeaderPlayCell.h"
#import "FMAskCodePlayView.h"
#import "FMAskCodeModel.h"
#import "FMPayTool.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "HttpRequestTool+Pay.h"
#import "FMPaySuccessPopView.h"

@interface FMAskCodeDetailHeaderPlayCell()

@property (nonatomic, strong) UIImageView *notPlayImageV; //不可以播放时显示

@property (nonatomic, strong) FMAskCodePlayView *playView; // 可以播放时显示

@property (nonatomic, strong) UILabel *durationLB; // 时长

@property (nonatomic, strong) UILabel *listionPriceLB; // 偷听价格Label

@end

@implementation FMAskCodeDetailHeaderPlayCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)dealloc {
    FMLog(@"%s---%p", __func__, self);
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
        
    [self.contentView addSubview:self.notPlayImageV];
    [self.notPlayImageV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.width.equalTo(250);
        make.height.equalTo(45);
        make.centerY.equalTo(0);
    }];
    
    UIView *whiteView = [UIView new];
    whiteView.backgroundColor = FMWhiteColor;
    [self.contentView insertSubview:whiteView belowSubview:self.notPlayImageV];
    [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.notPlayImageV.mas_left).offset(15);
        make.width.equalTo(40);
        make.top.bottom.equalTo(self.notPlayImageV);
    }];
    
    [self.contentView addSubview:self.playView];
    [self.playView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(0);
        make.left.width.height.equalTo(self.notPlayImageV);
    }];
    
    // 时长
    UILabel *durationLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [self.contentView addSubview:durationLabel];
    [durationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.notPlayImageV.mas_right).offset(-10);
        make.centerY.equalTo(0);
    }];
    self.durationLB = durationLabel;
}

- (UIImageView *)notPlayImageV {
    if(!_notPlayImageV) {
        UIImageView *notPlayImageV = [[UIImageView alloc] init];
        notPlayImageV.image = [UIImage imageNamed:@"askcode_list_blueBg"];
        notPlayImageV.userInteractionEnabled = YES;
        [notPlayImageV addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(payQuestion)]];
        _notPlayImageV = notPlayImageV;
        
        UILabel *listionPriceLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [notPlayImageV addSubview:listionPriceLabel];
        [listionPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(55);
            make.centerY.equalTo(0);
        }];
        self.listionPriceLB = listionPriceLabel;
    }
    
    return _notPlayImageV;
}

- (FMAskCodePlayView *)playView {
    if (!_playView) {
        FMAskCodePlayView *playView = [[FMAskCodePlayView alloc] init];
        WEAKSELF
        playView.sliderFrameChangeBlock = ^(CGFloat progress) {
            __weakSelf.durationLB.text = [NSString stringWithFormat:@"%d\"", (__weakSelf.model.questionAnswerLength.intValue - (int)(__weakSelf.model.questionAnswerLength.intValue * progress))];
        };
        _playView = playView;
    }
    return _playView;
}

- (void)setModel:(FMAskCodeModel *)model {
    _model = model;
    
    self.listionPriceLB.text = @"";
    self.playView.hidden = YES;
    self.notPlayImageV.hidden = YES;
    
    if (model.questionPerm.type.integerValue != 1) {
        self.playView.model = model;
        self.playView.totalTime = model.questionAnswerLength.floatValue;
        self.playView.hidden= NO;
    } else {
        if (model.listenPriceStr.integerValue > 0 ) {
            self.notPlayImageV.image = [UIImage imageNamed:@"askcode_list_blueBg"];
            self.listionPriceLB.text = [NSString stringWithFormat:@"%.f金币付费偷听", [model.listenPriceStr floatValue]];
            self.notPlayImageV.hidden = NO;
        } else {
            self.playView.model = model;
            self.playView.totalTime = [model.questionAnswerLength floatValue];
            self.playView.hidden = NO;
        }
    }

    // 语音长度
    if (model.questionAnswerLength.length) {
        self.durationLB.text = [NSString stringWithFormat:@"%@\"", model.questionAnswerLength];
    }
}

- (void)payQuestion { 
    if ([FMHelper checkLoginStatus]) {
        [self gotoPay];
    }
}

- (void)gotoPay {
    // 判断有几种支付方式，如果是一种，直接弹框提醒，如果是多种，弹框选择
    WEAKSELF
    FMAskCodeModel *model = self.model;
    if (model.enablePayModel.count == 0) {
        // 没有可用的支付方式
        [[FMPayTool payTool] noEnablePayModelWithErrorCode:model.noPayModelCode.integerValue errorText:model.noPayModelText];
        return;
    }
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.model.bignameDto.userId certCode:self.model.bignameDto.certCode clickView:self.notPlayImageV confirmOperation:^{
        
        EnablePayModel *payModel = [NSArray modelArrayWithClass:[EnablePayModel class] json:model.enablePayModel].firstObject;
        payModel.bignameId = model.bignameDto.userId;
        payModel.consumeType = 4;
        payModel.contentId = model.askCodeId.integerValue;
        [PaymentView showWithEnablePayModel:payModel payPrice:model.listenPriceStr productName:model.questionContent bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
            [__weakSelf httpForPayAskCode:[NSString stringWithFormat:@"%zd", selectedModel.type] couponId:selectedModel.couponId];
        } dismissBlock:^{
        }];
    }];
}

- (void)httpForPayAskCode:(NSString *)payType couponId:(NSString *)couponId {
    WEAKSELF
    [HttpRequestTool payQuestionWithQuestionid:self.model.askCodeId type:payType couponId:couponId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] isKindOfClass:[NSDictionary class]]) {
            [SVProgressHUD dismiss];
            FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
            popView.jumpIndex = 2;
            [popView show];
            
            __weakSelf.model.questionPerm.type = @"2";
            __weakSelf.model.answerOssUrl = dic[@"data"][@"answerOssUrl"];
            
            if ([payType isEqualToString:@"1"]) {
                // 金币支付，更新本地的金币余额
                FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                userModel.coin = userModel.coin - __weakSelf.model.listenPriceStr.integerValue;
                [MyKeyChainManager save:kUserModel data:userModel];
            }
            
            if (__weakSelf.model.askCodeId.length) {
                [[NSNotificationCenter defaultCenter] postNotificationName:kQuestionPaySuccess object:nil userInfo:@{@"questionId":__weakSelf.model.askCodeId}];
            }
            
        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            });
        }
    }];
}

@end


