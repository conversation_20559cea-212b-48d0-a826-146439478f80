//
//  FMAskCodeListTopCell.m
//  FMMarket
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 16/12/29.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "FMAskCodeRecommendDakaCell.h"
#import "FMRecommendModel.h"
#import "FMBigCastHomePageViewController.h"

@interface FMAskCodeRecommendDakaCell()

@property (weak, nonatomic) UIImageView *headImgV; // 头像

@property (weak, nonatomic) UIImageView *vipImage;  // vip图标

@property (weak, nonatomic) UILabel *nameLB; // 姓名

@property (weak, nonatomic) UILabel *userTitleLabel; // 头衔

@property (weak, nonatomic) UILabel *profileLabel; // 简介


@property (nonatomic,weak) UIView *leftSepLine;

@end

static const CGFloat headImgWH = 40;

@implementation FMAskCodeRecommendDakaCell

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.backgroundColor = [UIColor whiteColor];
    
    // 头像
    UIImageView *headImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:headImgV];
    [headImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@13);
        make.width.height.equalTo(@(headImgWH));
    }];
    headImgV.contentMode = UIViewContentModeScaleAspectFill;
    headImgV.userInteractionEnabled = YES;
    [headImgV addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(enterToDakaHomePage)]];
    self.headImgV = headImgV;
    
    // 名称
    UILabel *nameLB = [[UILabel alloc] init];
    [self.contentView addSubview:nameLB];
    [nameLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(headImgV.mas_right).offset(10);
        make.top.equalTo(headImgV);
    }];
    nameLB.font = [UIFont systemFontOfSize:17];
    nameLB.textColor = FMZeroColor;
    self.nameLB = nameLB;
    
    UIImageView *hotImg = [[UIImageView alloc] init];
    [self.contentView addSubview:hotImg];
    [hotImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(nameLB);
        make.left.equalTo(nameLB.mas_right).offset(3);
        make.right.lessThanOrEqualTo(@(-10));
        make.width.height.equalTo(@15);
    }];
    hotImg.image = [UIImage imageNamed:@"火热"];
    
    // 头衔
    UILabel *userTitleLabel = [[UILabel alloc] init];
    [self.contentView addSubview:userTitleLabel];
    [userTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLB);
        make.top.equalTo(nameLB.mas_bottom).offset(3);
        make.right.equalTo(@(-15));
    }];
    userTitleLabel.textColor = FMNavColor;
    userTitleLabel.font = [UIFont systemFontOfSize:15];
    self.userTitleLabel = userTitleLabel;
    
    // 简介
    UILabel *profileLabel = [[UILabel alloc] init];
    [self.contentView addSubview:profileLabel];
    [profileLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(headImgV);
        make.top.equalTo(headImgV.mas_bottom).offset(8);
        make.right.equalTo(@(-13));
    }];
    profileLabel.textColor = UIColorFromRGB(0x666666);
    profileLabel.font = [UIFont systemFontOfSize:13];
    profileLabel.numberOfLines = 3;
    profileLabel.preferredMaxLayoutWidth = UIScreenWidth * 0.5 - 30;
    self.profileLabel = profileLabel;
    
    // 提问按钮
    WEAKSELF;
    UIButton *askBtn = [[UIButton alloc] init];
    [self.contentView addSubview:askBtn];
    [askBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@95);
        make.height.equalTo(@27);
        make.centerX.equalTo(__weakSelf.contentView);
        make.top.equalTo(profileLabel.mas_bottom).offset(8);
        make.bottom.equalTo(@(-15));
    }];
    askBtn.layer.cornerRadius = 3.0f;
    askBtn.layer.borderWidth = 1.0f;
    askBtn.layer.borderColor = FMNavColor.CGColor;
    askBtn.exclusiveTouch = YES;
    [askBtn setTitle:@"提问" forState:UIControlStateNormal];
    [askBtn setTitleColor:FMNavColor forState:UIControlStateNormal];
    askBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [askBtn addTarget:self action:@selector(askBtnDidClicked) forControlEvents:UIControlEventTouchUpInside];
    self.askBtn = askBtn;
    
    UIView *leftSepLine = [[UIView alloc] init];
    [self.contentView addSubview:leftSepLine];
    [leftSepLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.equalTo(@0);
        make.width.equalTo(@0.5);
    }];
    leftSepLine.backgroundColor = FMSepLineColor;
    self.leftSepLine = leftSepLine;
    
    UIView *bottomSepLine = [[UIView alloc] init];
    [self.contentView addSubview:bottomSepLine];
    [bottomSepLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@0);
        make.height.equalTo(@0.5);
    }];
    bottomSepLine.backgroundColor = FMSepLineColor;
}

- (void)setModel:(FMRecommendModel *)model {
    _model = model;
    NSDictionary *dic = model.contents;
    
    NSString *strURl = [dic[@"user_ico"] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    [self.headImgV aliCircleImageWithURL:strURl imgaeWidth:headImgWH imgaeHeigth:headImgWH circleRadius:headImgWH * 0.5];
    
    self.nameLB.text = dic[@"user_name"];
    if ([dic[@"good_at"] length]) {
        NSArray *arr = [dic[@"good_at"] componentsSeparatedByString:@","];
        self.userTitleLabel.text = [NSString stringWithFormat:@"%@", arr.firstObject];
        self.userTitleLabel.hidden = NO;
    } else {
        self.userTitleLabel.hidden = YES;
    }
    
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 3;
    NSDictionary *attr = @{NSParagraphStyleAttributeName:style};
    if([model.recommended_msg length]) {
        NSMutableAttributedString *attrString = [[NSMutableAttributedString alloc] initWithString:model.recommended_msg attributes:attr];
        self.profileLabel.attributedText = attrString;
        self.profileLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    } else {
        self.profileLabel.text = @"";
    }
}

- (void)setIndex:(NSIndexPath *)index {
    if (index.item == 0) {
        self.leftSepLine.hidden = YES;
    } else {
        self.leftSepLine.hidden = NO;
    }
}

- (void)askBtnDidClicked {
    if (self.askBtnClickBlock) {
        self.askBtnClickBlock();
    }
}

- (void)enterToDakaHomePage {
    NSDictionary *dic = self.model.contents;
    
    UIViewController *selfVC = [self viewController];
    FMBigCastHomePageViewController *homeVC = [[FMBigCastHomePageViewController alloc] init];
    homeVC.userId = dic[@"userid"];
    homeVC.hidesBottomBarWhenPushed = YES;
    [selfVC.navigationController pushViewController:homeVC animated:YES];
}


@end
