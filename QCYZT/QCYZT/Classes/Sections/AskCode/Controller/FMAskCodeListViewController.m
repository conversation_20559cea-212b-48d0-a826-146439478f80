//
//  FMAskCodeListViewController.m
//  QCYZT
//
//  Created by zeng on 2021/11/15.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAskCodeListViewController.h"
#import "FMAskCodeListCell.h"
#import "FMAskCodeVideoListCell.h"
#import "FMAskCodeDetailViewController.h"
#import "FMAskCodeModel.h"
#import "FMAskCodeRecommendCell.h"
#import "FMRecommendModel.h"
#import "FMUPDataTool.h"
#import "HomeTableViewHeader.h"
#import "HttpRequestTool+Home.h"

@interface FMAskCodeListViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIButton *questionBtn2;

@property (nonatomic, strong) NSArray <FMAskCodeModel *>*recommendDatas;
@property (nonatomic, strong) NSMutableArray <FMAskCodeModel *> *dataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;

@property (nonatomic, assign) BOOL needsRefresh;
@property (nonatomic, assign) BOOL isRemind;

@end

@implementation FMAskCodeListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = self.tableView.backgroundColor = UIColor.up_contentBgColor;

    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 20;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    
    if (self.askCodeListType == AskCodeListTypeMyAsked) {
        if ([FMHelper isLogined]) {
            [self.tableView.mj_header beginRefreshing];
        } else {
            UIButton *questionBtn2 = [[UIButton alloc] initWithFrame:CGRectMake(27.5, 230, UI_SCREEN_WIDTH - 55, 45) font:[FMHelper scaleFont:17] normalTextColor:FMWhiteColor backgroundColor:FMNavColor title:@"我要提问" image:ImageWithName(@"askcode_wytw") target:self action:@selector(askQuestion)];
            [self.view addSubview:questionBtn2];
            [self.view bringSubviewToFront:questionBtn2];
            self.questionBtn2 = questionBtn2;
            UI_View_Radius(questionBtn2, 22.5);
            [questionBtn2 layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:10];
            self.questionBtn2.hidden = YES;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if (![FMHelper isLogined] ) {
                    self.questionBtn2.hidden = NO;
                    if (self.dataArr.count == 0) {
                        [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无内容" attributes:nil offsetY:60];
                        self.tableView.mj_footer.hidden = YES;
                    } else {
                        [self.tableView dismissNoDataView];
                        self.tableView.mj_footer.hidden = NO;
                    }
                }
            });
        }
    } else {
        [self.tableView.mj_header beginRefreshing];
    }
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(questionPaySuccess:) name:kQuestionPaySuccess object:nil];
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(praisedNotification:) name:kAskCodePriasedNotification object:nil];
    if (self.askCodeListType == AskCodeListTypeSelfSelected) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kFMUpdateSelfStockDatabaseNotification object:nil];
    } else if (self.askCodeListType == AskCodeListTypeMyAnswered) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(submitAnswerSuccess:) name:kSubmitAnswerSuccess object:nil];
    } else if (self.askCodeListType == AskCodeListTypeMyAsked) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kAskCodeSuccess object:nil];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        self.page = 1;
        self.currentPage = self.page;
        [self requestData];
    }
}

#pragma mark - UITableView  Delegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 2;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        return self.recommendDatas.count;
    }
    
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        FMAskCodeRecommendCell *cell = [tableView reuseCellClass:[FMAskCodeRecommendCell class]];
        cell.askModel = self.recommendDatas[indexPath.row];
        cell.isLastCell = (indexPath.row == self.recommendDatas.count - 1);
        return cell;
    }
    
    FMAskCodeModel *model = self.dataArr[indexPath.row];
    if (model.contentFileType.integerValue == 1 && (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1)) {
        //视频问股
        FMAskCodeVideoListCell *cell = [tableView reuseCellClass:[FMAskCodeVideoListCell class]];
        cell.askModel = model;
        cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
        return cell;
    } else {
        //语音问股
        FMAskCodeListCell *cell = [tableView reuseCellClass:[FMAskCodeListCell class]];
        cell.askCodeListType = self.askCodeListType;
        cell.askModel = self.dataArr[indexPath.row];
        cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeRecommendCell class]) configuration:^(FMAskCodeRecommendCell *cell) {
            cell.askModel = self.recommendDatas[indexPath.row];
            cell.isLastCell = (indexPath.row == self.recommendDatas.count - 1);
        }];
    }
    
    FMAskCodeModel *model = self.dataArr[indexPath.row];
    if (model.contentFileType.integerValue == 1 && (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1)) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeVideoListCell class]) configuration:^(FMAskCodeVideoListCell *cell) {
            cell.askModel = self.dataArr[indexPath.row];
        }];
    } else {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeListCell class]) configuration:^(FMAskCodeListCell *cell) {
            cell.askCodeListType = self.askCodeListType;
            cell.askModel = self.dataArr[indexPath.row];
        }];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if (indexPath.section == 0) {
        FMAskCodeDetailViewController *vc = [[FMAskCodeDetailViewController alloc] init];
        FMAskCodeModel *model = self.recommendDatas[indexPath.row];
        vc.questionId = [NSString stringWithFormat:@"%@",model.askCodeId];
        [self.navigationController pushViewController:vc animated:YES];
    } else {
        FMAskCodeModel *model = self.dataArr[indexPath.row];
        if (self.askCodeListType == AskCodeListTypeMyAnswered) {
            if ((model.questionAnswerFlag.integerValue == 1) || model.questionAnswerFlag.integerValue == 0) {
                [ProtocolJump jumpWithUrl:model.contentAction];
            }
        } else if (self.askCodeListType == AskCodeListTypeMyAsked) {
            if (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1) {
                [ProtocolJump jumpWithUrl:model.contentAction];
            }
        } else {
            if (model.questionAnswerFlag.integerValue == 1) {
                [ProtocolJump jumpWithUrl:model.contentAction];
            }
        }
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (self.askCodeListType == AskCodeListTypeAll) {
        if (section == 1 && self.dataArr.count && self.recommendDatas.count) {
            HomeTableViewHeader *header = [[HomeTableViewHeader alloc] init];
            header.backgroundColor = FMWhiteColor;
            header.lab.text = @"最新问股";
            header.moreBtn.hidden = YES;
            header.topLine.hidden = YES;
            [header.lab mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(@13);
            }];
            return header;
        }
    }
    
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (self.askCodeListType == AskCodeListTypeAll) {
        if (section == 1 && self.dataArr.count && self.recommendDatas.count) {
            return 36;
        }
    }
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 0 && self.recommendDatas.count) {
        return 10;
    }
    return CGFLOAT_MIN;
}

#pragma mark - HTTP
- (void)requestRecommendData {
    [HttpRequestTool getAskCodeRecommonDataWithstart:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.recommendDatas = [NSArray modelArrayWithClass:[FMAskCodeModel class] json:dic[@"data"]];
            [self.recommendDatas bk_each:^(FMAskCodeModel *obj) {
                obj.bignameDto.userName = [NSString stringWithFormat:@"%@老师", obj.bignameDto.userName];
            }];
            [self.tableView reloadData];
        }
    }];
}

- (void)requestData {
    
    NSString *listType = [NSString stringWithFormat:@"%zd", self.askCodeListType];
    NSString *keyword = nil;
    if (self.askCodeListType == AskCodeListTypeSelfSelected) {
        keyword = [FMUPDataTool selfStockDataFormaterCodeColonName];
    }
    [HttpRequestTool getAskCodeListWithPage:self.page pageSize:self.pageSize listType:listType.integerValue keyWords:keyword start:^{
    } failure:^{
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self endRefreshForSuccess];

            // 根据total总数 确定分页的页码
            NSInteger pageNum = (([dic[@"total"] integerValue] + self.pageSize - 1) / self.pageSize);
            
            if (self.page == 1) {
                [self.tableView.mj_footer resetNoMoreData];
                [self.dataArr removeAllObjects];
            }

            NSArray *dataArr = [NSArray modelArrayWithClass:[FMAskCodeModel class] json:dic[@"data"]];
            [self removeRepeatDataWithArray:dataArr];
            
            if (self.page == pageNum) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            
            if (self.dataArr.count == 0) {
                if (![FMHelper isLogined] && !self.isRemind) {
                    self.isRemind = YES;
                    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                        [self requestData];
                    }];
                }
                [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无内容" attributes:nil offsetY:60];
                self.tableView.mj_footer.hidden = YES;
                
                if (self.askCodeListType == AskCodeListTypeMyAsked) {
                    self.questionBtn2.hidden = NO;
                    [self.tableView bringSubviewToFront:self.questionBtn2];
                }
            } else {
                [self.tableView dismissNoDataView];
                self.tableView.mj_footer.hidden = NO;
                self.questionBtn2.hidden = YES;
            }
            [self.tableView reloadData];
        } else {
            [self endRefreshForFailure];
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - Notification
- (void)praisedNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i <self.dataArr.count; i++) {
        FMAskCodeModel *model = self.dataArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            [[FMUserDataSyncManager sharedManager] likeQuestion:questionId];
            model.satisfiedNums = [NSString stringWithFormat:@"%ld",model.satisfiedNums.integerValue + 1];
            [self.tableView reloadData];
            break;
        }
    }
}

- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)questionPaySuccess:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i <self.dataArr.count; i++) {
        FMAskCodeModel *model = self.dataArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            model.questionPerm.type = @"3";
            [self.tableView reloadData];
            break;
        }
    }
}

- (void)submitAnswerSuccess:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i<self.dataArr.count; i++) {
        FMAskCodeModel *model = self.dataArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            model.questionAnswerFlag = @"1";
            model.checkStatus = @"1";
            model.contentAction = [NSString stringWithFormat:@"qcyzt://questionStock?id=%@", model.askCodeId];
            [self.tableView reloadData];
            break;
        }
    }
}

#pragma mark - Private
- (void)headerAction {
    self.page = 1;
    [self requestData];
    if (self.askCodeListType == AskCodeListTypeAll) {
        [self requestRecommendData];
    }
}

- (void)footerAction {
    self.page++;
    [self requestData];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMAskCodeModel *model in array) {
        [dic setObject:model forKey:model.askCodeId];
    }
    
    for (FMAskCodeModel *model in self.dataArr.reverseObjectEnumerator) {
        if ([dic valueForKey:model.askCodeId]) {
            [self.dataArr removeObject:model];
        }
    }
    
    [self.dataArr addObjectsFromArray:array];
}

- (void)askQuestion {    
    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
        
    }];
}


#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMAskCodeListCell class]];
        [_tableView registerCellClass:[FMAskCodeVideoListCell class]];
        [_tableView registerCellClass:[FMAskCodeRecommendCell class]];
        [_tableView registerViewClass:[HomeTableViewHeader class]];
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
        _tableView.mj_footer.hidden = YES;
    }
    return _tableView;
}

- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    return _dataArr;
}

@end
