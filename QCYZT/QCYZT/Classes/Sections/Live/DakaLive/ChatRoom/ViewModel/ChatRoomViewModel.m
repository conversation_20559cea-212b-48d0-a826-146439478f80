//
//  ChatRoomViewModel.m
//  QCYZT
//
//  Created by shumi on 2022/3/21.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "ChatRoomViewModel.h"
#import "HttpRequestTool+Live.h"
#import "HttpRequestTool+Pay.h"
#import "LiveMessageBaseModel.h"
#import "FMDaKaLiveConst.h"
#import "WebSocketManager.h"
#import "FMLiveDetailModel.h"
#import "EnablePayModel.h"
#import "FMPayTool.h"
#import "PaymentView.h"
#import "FMPlayerManager.h"
#import "FMProgressHUD.h"

@interface ChatRoomViewModel ()<WebSocketManagerDelegate>
//@property (nonatomic,assign) NSInteger page;
@property (nonatomic,assign) NSInteger pageSize;
@property (nonatomic,assign) NSInteger lastMsgTime;
@property (nonatomic,assign) NSInteger currentMsgTime;

/// 消息类型数组
@property (nonatomic, strong) NSArray *messageTypeArray;
@property (nonatomic, assign) CGFloat baseTime;  //穿插时间显示参考值


@end

@implementation ChatRoomViewModel

- (instancetype)init {
    if (self = [super init]) {
        self.pageSize = 20;
    }
    return self;
}

- (void)dealloc {
    // 断开websocket
    FMLog(@"WebSocketManager 断开链接");
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [[WebSocketManager shared] closeWebSocket];
}


/// 链接websocket
- (void)prepareLinkWebsocket {
    [WebSocketManager shared].socketUrl = [NSString stringWithFormat:@"%@/api/v2/ws/live?roomid=%ld",kDakaInit,self.detailModel.liveId];
    if (![WebSocketManager shared].isConnect) {
        [[WebSocketManager shared] connectServer];
    } else {
        // WS已经链接  小窗切换到详情时 WS是链接状态
        if(self.webSocketConnectSuccess) {
            self.webSocketConnectSuccess();
        }
    }
    [WebSocketManager shared].delegate = self;
}

/// 下拉加载历史数据
- (void)headerActionSuccessBlock:(void (^)(BOOL stop,NSInteger lastMsgTime, NSInteger size))successBlock FailureBlock:(void (^)())failureBlock {
    if (self.isOnlySeeDaka) {
        self.lastMsgTime = 0;
        [self requestOnlyDakaMessagesWithSuccessBlock:successBlock failureBlock:failureBlock];
    } else {
        [self requestDataSuccessBlock:successBlock FailureBlock:failureBlock];
    }
}

- (void)requestDataSuccessBlock:(void (^)(BOOL stop, NSInteger lastMsgTime, NSInteger size))successBlock FailureBlock:(void (^)())failureBlock {
    [HttpRequestTool getLiveHistoryMessageWithRoomId:[NSString stringWithFormat:@"%ld",self.detailModel.liveId] lastMsgTime:self.lastMsgTime pageSize:self.pageSize start:^{
    } failure:^{
        if (failureBlock) {
            failureBlock();
        }
        [self endRefreshForFailure];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"OnlySeeDakaOrSeeAllStatus" object:nil];
    } success:^(NSDictionary *dic) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"OnlySeeDakaOrSeeAllStatus" object:nil];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if ([dic[@"data"] isKindOfClass:[NSDictionary class]]) {
                BOOL hasMessage = self.frameModelArray.count != 0;
                if (self.lastMsgTime == 0) {
                    [self.frameModelArray removeAllObjects];
                }
                // 置顶消息
                if ([dic[@"data"][@"topMsg"] isKindOfClass:[NSDictionary class]]) {
                    if (!self.topMessageFrameModel) {
                        [self configTopMsg:dic[@"data"][@"topMsg"] isTop:YES];
                    }
                }
                // 历史消息
                if ([dic[@"data"][@"msgList"] isKindOfClass:[NSArray class]]) {
                    NSArray *msglist = [NSArray modelArrayWithClass:[LiveMessageBaseModel class] json:dic[@"data"][@"msgList"]];
                    /// 直播中的直播间 插入免责声明消息和打招呼消息
                    if (self.lastMsgTime == 0 && !hasMessage) {
                        NSMutableArray *msgListArr = [NSMutableArray arrayWithArray:msglist];
                        if (self.detailModel.liveStatus == FMLiveStatusLiveing) {
                            // 先添加免责声明消息
                            [msgListArr addObject:[self addDisclaimerMsg]];
                            // 再添加打招呼消息
                            [msgListArr addObject:[self addQuickMsg]];
                        }

                        NSDictionary *objectDic = @{@"userName" : [NSString stringWithFormat:@"%@",[FMUserDefault getNickName]],@"roomid": [NSString stringWithFormat:@"%ld",self.detailModel.liveId],@"msgFrom":[FMUserDefault getUserId],@"websocketMsgType":@"ENTERMSG"};
                        LiveMessageBaseModel *msgModel = [LiveMessageBaseModel modelWithDictionary:objectDic];
                        [msgListArr addObject:msgModel];
                        msglist = [NSArray arrayWithArray:msgListArr];
                    }
                                    
                    __block NSMutableArray *array = [NSMutableArray array];
                    NSMutableArray *dakaMsgArray = [NSMutableArray array];
                    __block NSInteger  loadCompletedCount = 0;
                    WEAKSELF
                    for (NSInteger i = 0; i < msglist.count; i ++) {
                        LiveMessageBaseModel *model = msglist[i];
                        LiveMessageFrameModel *frameModel = [[LiveMessageFrameModel alloc] init];
                        frameModel.loaderCompletedBlock = ^{
                            loadCompletedCount ++;
                            if (loadCompletedCount == msglist.count) {
                                if (successBlock) {
                                    successBlock((msglist.count < __weakSelf.pageSize), __weakSelf.lastMsgTime, array.count);
                                    // 此处array会引起循环引用  用weak时在这里拿到的是nil  所以手动置空一下
                                    array = nil;
                                }
                                [__weakSelf endRefreshForSuccess];
                            }
                        };
                        frameModel.liveType = self.detailModel.liveType;
                        frameModel.model = model;
                        [array addObject:frameModel];
                        if (model.webSocketMessageType == WebSocketMessageTypeDKMESSAGE) {
                            [dakaMsgArray addObject:frameModel];
                        }
                    }
                    
                    if (self.frameModelArray.count == 0) {
                        [self.frameModelArray addObjectsFromArray:array];
                    } else {
                        [self.frameModelArray insertObjects:array atIndexes:[NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, [array count])]];
                    }
                    /**
                     插入时间间隔消息
                     1.逆序找出第一条和系统时间相差五分中的时间
                     2.以这条消息为基准 对比这条消息的前一条消息 相差五分钟的进行展示时间
                     */
                    __block  LiveMessageFrameModel *currentFrameModel;
                    [self.frameModelArray enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(LiveMessageFrameModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                        if (obj.model.webSocketMessageType == WebSocketMessageTypeDKMESSAGE ||
                            obj.model.webSocketMessageType == WebSocketMessageTypeUSERMESSAGE) {
                            if (currentFrameModel) {
                                if ((currentFrameModel.model.createTime - obj.model.createTime) > 5 * 60 * 1000) {
                                    currentFrameModel = obj;
                                    currentFrameModel.showTime = YES;
                                }
                            } else {
                                // 找到第一条和系统时间相差五分钟的消息
                                if ((self.detailModel.currTime - obj.model.createTime) > 5 * 60 * 1000) {
                                    currentFrameModel = obj;
                                    currentFrameModel.showTime = YES;
                                    self.baseTime = currentFrameModel.model.createTime;
                                }
                            }
                        }
                    }];
                }
            }
        }
    }];
}

/// 只看大咖消息
- (void)requestOnlyDakaMessagesWithSuccessBlock:(void (^)(BOOL stop, NSInteger lastMsgTime, NSInteger size))successBlock
                                   failureBlock:(void (^)())failureBlock {
    NSString *roomId = [NSString stringWithFormat:@"%ld", self.detailModel.liveId];
    [HttpRequestTool getLiveOnlyDakaMessageWithRoomId:roomId start:^{
    } failure:^{
        if (failureBlock) {
            failureBlock();
        }
        [self endRefreshForFailure];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"OnlySeeDakaOrSeeAllStatus" object:nil];
    } success:^(NSDictionary *dic) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"OnlySeeDakaOrSeeAllStatus" object:nil];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self.frameModelArray removeAllObjects];
            // 处理大咖消息列表
            if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                NSArray *msglist = [NSArray modelArrayWithClass:[LiveMessageBaseModel class] json:dic[@"data"]];
                __block NSMutableArray *array = [NSMutableArray array];
                __block NSInteger loadCompletedCount = 0;
                WEAKSELF
                for (NSInteger i = 0; i < msglist.count; i++) {
                    LiveMessageBaseModel *model = msglist[i];
                    LiveMessageFrameModel *frameModel = [[LiveMessageFrameModel alloc] init];
                    frameModel.loaderCompletedBlock = ^{
                        loadCompletedCount++;
                        if (loadCompletedCount == msglist.count) {
                            if (successBlock) {
                                successBlock(YES, __weakSelf.lastMsgTime, array.count);
                                array = nil;
                            }
                            [__weakSelf endRefreshForSuccess];
                        }
                    };
                    frameModel.liveType = self.detailModel.liveType;
                    frameModel.model = model;
                    [array addObject:frameModel];
                }
                if (self.frameModelArray.count == 0) {
                    [self.frameModelArray addObjectsFromArray:array];
                } else {
                    [self.frameModelArray insertObjects:array atIndexes:[NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, array.count)]];
                }
                // 插入时间间隔消息（同之前逻辑）
                __block  LiveMessageFrameModel *currentFrameModel;
                [self.frameModelArray enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(LiveMessageFrameModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                    if (obj.model.webSocketMessageType == WebSocketMessageTypeDKMESSAGE ||
                        obj.model.webSocketMessageType == WebSocketMessageTypeUSERMESSAGE) {
                        if (currentFrameModel) {
                            if ((currentFrameModel.model.createTime - obj.model.createTime) > 5 * 60 * 1000) {
                                currentFrameModel = obj;
                                currentFrameModel.showTime = YES;
                            }
                        } else {
                            // 找到第一条和系统时间相差五分钟的消息
                            if ((self.detailModel.currTime - obj.model.createTime) > 5 * 60 * 1000) {
                                currentFrameModel = obj;
                                currentFrameModel.showTime = YES;
                                self.baseTime = currentFrameModel.model.createTime;
                            }
                        }
                    }
                }];
            }
        }
    }];
}

/// 直播间付费消息
- (void)liveChatRoomPayMessageWithModel:(LiveMessageFrameModel *)frameModel successBlock:(void (^)(LiveMessageFrameModel *frameModel))successBlock {
    if ([FMHelper checkLoginStatus]) {
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
        enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
        enablePayModel.name = @"金币";
        enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
        enablePayModel.type = PaymentTypeCoin;
        self.detailModel.enablePayModel = @[enablePayModel];
        NSString *dakaId = self.detailModel.bignameDto.userId;
        NSString *price = [NSString stringWithFormat:@"%ld",frameModel.model.messagePrice];
        NSString *title = @"直播付费消息";
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:dakaId certCode:self.detailModel.bignameDto.certCode clickView:nil confirmOperation:^{
            [PaymentView showWithEnablePayModel:[enablePayModel copy] payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
                // 3.支付
                [HttpRequestTool liveChatRoomPayMessageWithMessageId:frameModel.model.messageId roomId:self.detailModel.liveId  start:^{
                } failure:^{
                    [SVProgressHUD showErrorWithStatus:@"网络不给力"];
                } success:^(NSDictionary *dic) {
                    if ([dic[@"status"] isEqualToString:@"1"]) {
                        // 金币支付，更新本地的金币余额
                        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                        userModel.coin = userModel.coin - price.integerValue;
                        [MyKeyChainManager save:kUserModel data:userModel];
                        
                        LiveMessageBaseModel *model = [LiveMessageBaseModel modelWithDictionary:dic[@"data"][@"dkmsg"]];
                        model.websocketMsgType = @"DKMSG";
                        LiveMessageFrameModel *newframeModel = [[LiveMessageFrameModel alloc] init];
                        WEAKSELF
                        __weak LiveMessageFrameModel *weakSelfModel = newframeModel;
                        newframeModel.loaderCompletedBlock = ^{
                            [__weakSelf.frameModelArray replaceObjectAtIndex:[__weakSelf.frameModelArray indexOfObject:frameModel] withObject:weakSelfModel];
                            if (successBlock) {
                                successBlock(weakSelfModel);
                            }
                        };
                        newframeModel.liveType = self.detailModel.liveType;
                        newframeModel.model = model;
                        [SVProgressHUD showSuccessWithStatus:@"支付成功!"];
                    } else {
                        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                    }
                }];
            } dismissBlock:^{
            }];
        }];
    }
}

- (void)endRefreshForFailure {
    if (!self.isOnlySeeDaka) {
        self.lastMsgTime = self.currentMsgTime;
    }
}

//成功状态下停止refresh
- (void)endRefreshForSuccess {
    if (!self.isOnlySeeDaka) {
        if (self.frameModelArray.count) {
            LiveMessageBaseModel *model;
            NSInteger i = 0;
            while (model.createTime == 0) {
                model = self.frameModelArray[i].model;
                i ++;
            }
            self.lastMsgTime = model.createTime;
        }
        self.currentMsgTime = self.lastMsgTime;
    }
}


/// 构造进入直播间打招呼消息
- (LiveMessageBaseModel *)addQuickMsg {
    LiveMessageBaseModel *msgModel = [[LiveMessageBaseModel alloc] init];
    msgModel.userIco = self.detailModel.bignameDto.userIco;
    msgModel.bigname = self.detailModel.bignameDto.userName;
    msgModel.bignameId = self.detailModel.bignameDto.userId.integerValue;
    msgModel.roomid = self.detailModel.liveId;
    msgModel.createTime = [FMHelper getTimestamp];
    msgModel.attestationType = self.detailModel.bignameDto.attestationType;
    msgModel.content = [NSString stringWithFormat:@"<p>欢迎<span style=\"color: #0074FA ;\">%@</span>进入直播间,来<u>打个招呼吧></u></p>",[FMUserDefault getNickName]];
    msgModel.websocketMsgType = @"GreetingMessage";
    // 设置消息类型，确保能正确识别为投顾消息，这样会有主播身份标识
    msgModel.msgType = 2; // 投顾消息类型
    // 设置一个唯一的消息ID，避免HTML解析时的ID冲突
    msgModel.messageId = [FMHelper getTimestamp] + 2000000; // 使用时间戳+偏移作为唯一ID

    return  msgModel;
}

/// 构造免责声明消息
- (LiveMessageBaseModel *)addDisclaimerMsg {
    LiveMessageBaseModel *msgModel = [[LiveMessageBaseModel alloc] init];
    msgModel.userIco = self.detailModel.bignameDto.userIco;
    msgModel.bigname = self.detailModel.bignameDto.userName;
    msgModel.bignameId = self.detailModel.bignameDto.userId.integerValue;
    msgModel.roomid = self.detailModel.liveId;
    msgModel.createTime = [FMHelper getTimestamp] - 1; // 确保时间戳比打招呼消息早1毫秒
    msgModel.attestationType = self.detailModel.bignameDto.attestationType;
    msgModel.content = @"<p>风险提示：投资有风险，入市需谨慎！投顾观点仅供参考学习，不构成投资建议，据此操作风险自担！<br/>本直播内容版权归四川大决策投资顾问有限公司所有，未经书面许可，严禁转载。</p>";
    msgModel.websocketMsgType = @"DisclaimerMessage";
    // 设置消息类型，确保能正确识别为投顾消息，这样会有主播身份标识
    msgModel.msgType = 2; // 投顾消息类型
    // 设置一个唯一的消息ID，避免HTML解析时的ID冲突
    msgModel.messageId = [FMHelper getTimestamp] + 1000000; // 使用时间戳+偏移作为唯一ID

    return msgModel;
}

/// 发送消息
- (void)insertNewMessage:(NSString *)content  SuccessBlock:(void (^)())successBlock FailureBlock:(void (^)())failureBlock{
    NSMutableDictionary *messageDic = [NSMutableDictionary dictionary];
    [messageDic setObject:[NSString stringWithFormat:@"%@",content] forKey:@"content"];
    [messageDic setObject:[FMUserDefault getNickName] forKey:@"userName"];
    [messageDic setObject:[FMUserDefault getUserFace] forKey:@"userIco"];
    [messageDic setObject:@"1" forKey:@"msgType"];
    FMUserModel *userModel = [FMUserDefault getUnArchiverDataForKey:kUserModel];
    [messageDic setObject:[NSNumber numberWithBool:userModel.hasVip] forKey:@"hasVip"];
    [messageDic setObject:[FMUserDefault getUserId] forKey:@"msgFrom"];
    [messageDic setObject:self.detailModel.bignameDto.userId forKey:@"msgTo"];
    [messageDic setObject:[NSString stringWithFormat:@"%ld",self.detailModel.liveId] forKey:@"roomid"];
    [messageDic setObject:[NSNumber numberWithInteger:[FMUserDefault getAttestationType]] forKey:@"attestationType"];
    NSString *jsonStr = [JsonTool jsonStringFromDicOrArr:messageDic];
    UIView *hudBackView = [FMPlayerManager shareManager].player.isFullScreen ? [FMPlayerManager shareManager].player.controlView : [FMHelper getCurrentVC].view;

    [HttpRequestTool sendLiveMessageWithMessage:jsonStr start:^{
        [FMProgressHUD showProgressHUDViewInView:hudBackView];
    } failure:^{
        [FMProgressHUD showTextOnlyInView:hudBackView withText:@"发送失败,请检查您的网络"];
        if (failureBlock) {
            failureBlock();
        }
    } success:^(NSDictionary *dic) {
        [FMProgressHUD hiddenProgressHUDViewInView:hudBackView];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD showSuccessWithStatus:@"发布成功！审核通过后展示"];
//            if ([dic[@"data"] isKindOfClass:[NSDictionary class]]) {
//                LiveMessageBaseModel *model = [LiveMessageBaseModel modelWithDictionary:dic[@"data"]];
//                LiveMessageFrameModel *frameModel = [[LiveMessageFrameModel alloc] init];
//                __weak LiveMessageFrameModel *weakSelfModel = frameModel;
//                WEAKSELF
//                frameModel.loaderCompletedBlock = ^{
//                    // 处理收到重复的消息
//                    [__weakSelf dealdUplicationMsg:weakSelfModel];
//                    if (successBlock) {
//                        successBlock();
//                    }
//                };
//                frameModel.liveType = self.detailModel.liveType;
//                if ((model.createTime - self.baseTime) > 5 * 60 * 1000) {
//                    frameModel.showTime = YES;
//                    self.baseTime = model.createTime;
//                }
//                frameModel.model = model;
//            }
        }
    }];
}

/// 收到新消息刷新列表
- (void)receivenewMsgReloadTabView:(NSString *)messageType {
    if ([messageType isEqualToString:@"USERMSGYES"] || [messageType isEqualToString:@"DKMSG"]) {
        if (self.isOnlySeeDaka) {
            if ([messageType isEqualToString:@"DKMSG"]) {
                if (self.reload) {
                    self.reload(messageType);
                }
            }
        } else {
            if (self.reload) {
                self.reload(messageType);
            }
        }
    } else {
        if (self.reload) {
            self.reload(messageType);
        }
    }
}

/// 发送通知更新页面
- (void)postNotificationInfo:(NSString *)messageType msg:(NSDictionary *)messageDic {
    if ([messageType isEqualToString:@"ACTIVITY_MSG"]) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationLiveRoomActiviteInfo object:nil];
    } else if ([messageType isEqualToString:@"STOCK_MSG"]) {
        // 相关股票信息
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationLiveRelatedStockUpdate object:messageDic];
    } else if ([messageType isEqualToString:@"ONLINE"] ||
               [messageType isEqualToString:@"STARTTITLE"] ||
               [messageType isEqualToString:@"USERZAN"] ||
               [messageType isEqualToString:@"LIVESTOP"]) {
        // 在线数  直播间名称 更新 点赞数 停播
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationLiveRoomInfoUpdate object:messageDic];
    } else if ([messageType isEqualToString:@"NOTICEMSG"]) {
        // 投顾关注数更新
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationLiveRoomInfoUpdate object:messageDic];
    } else if ([messageType isEqualToString:@"USERGIFT"]) {
        // 直播间收到礼物消息
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationLiveSendGiftMessage object:messageDic];
    }
}

/// 配置置顶消息
/// - Parameters:
///   - messageDic: 消息体
///   - isTop: 置顶YES  取消置顶 NO
- (void)configTopMsg:(NSDictionary *)messageDic isTop:(BOOL)isTop {
    if (isTop) {
        LiveMessageBaseModel *topMessage = [LiveMessageBaseModel modelWithDictionary:messageDic];
        if (topMessage.content.length > 0 ||
            topMessage.noteTitle.length > 0 ||
            topMessage.linkContent.length > 0) {
            topMessage.isTopMessage = YES;
            LiveMessageFrameModel *frameModel = [[LiveMessageFrameModel alloc] init];
            __weak LiveMessageFrameModel *weakSelfModel = frameModel;
            WEAKSELF
            frameModel.loaderCompletedBlock = ^{
                __weakSelf.topMessageFrameModel = weakSelfModel;
            };
            topMessage.websocketMsgType = @"DKMSG";
            frameModel.liveType = self.detailModel.liveType; // 视频直播还是文字直播(UI样式上有区分)
            frameModel.model = [topMessage mutableCopy];
            if (self.updateTopMessage) {
                self.updateTopMessage(frameModel);
            }
        }
    } else {
        self.topMessageFrameModel = nil;
        if (self.updateTopMessage) {
            self.updateTopMessage(nil);
        }
    }
    if (self.reload) {
        self.reload(@"DKMSG");
    }
}

/// 处理重复消息
- (void)dealdUplicationMsg:(LiveMessageFrameModel *)frameModel {
    __block  BOOL isContain = NO;
    /// 用一个临时数组存储一下  遍历临时数据 去决定是否添加数据源
    if (self.frameModelArray.count == 0) {
        [self.frameModelArray addObject:frameModel];
    }
    NSMutableArray *tempArray = [NSMutableArray arrayWithArray:self.frameModelArray];
    [tempArray enumerateObjectsWithOptions:0 usingBlock:^(LiveMessageFrameModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (frameModel.model.webSocketMessageType == WebSocketMessageTypeDKMESSAGE ||
            frameModel.model.webSocketMessageType == WebSocketMessageTypeUSERMESSAGE ||
            frameModel.model.webSocketMessageType == WebSocketMessageTypeUSERGIFTMESSAGE) {
            if (obj.model.messageId == frameModel.model.messageId) {
                isContain = YES;
            }
        } else if (frameModel.model.webSocketMessageType == WebSocketMessageTypeENTERMESSAGE) {
            // 两个消息都为 进入直播间的提示消息 且来自一人则算做重复消息
            if (obj.model.msgFrom == frameModel.model.msgFrom
                && obj.model.messageId == 0) {
                isContain = YES;
            }
        }
        // 遍历完毕 确定是否有重复消息
        if (idx == tempArray.count - 1) {
            if (!isContain) {
                // 按照弹幕需求 收到WS消息并且过滤重复消息后 在加入到弹幕轨道中
                [[NSNotificationCenter defaultCenter] postNotificationName:LiveBarrageUpdate object:frameModel];
                [self.frameModelArray addObject:frameModel];
            }
        }
    }];
}

#pragma mark - websocket接收消息
- (void)webSocketManagerDidReceiveMessageWithString:(NSString *)string {
    NSDictionary *stringDic = [JsonTool dicOrArrFromJsonString:string];
    NSDictionary *objectDic = [NSDictionary dictionaryWithDictionary:stringDic[@"object"]];
    NSString *messageType = stringDic[@"websocketMsgType"];
    
    if ([objectDic[@"roomid"] integerValue] != self.detailModel.liveId) {
        return;
    }
    
    if (![self.messageTypeArray containsObject:messageType]) {
        return;
    }
    
    NSMutableDictionary *messageDic = [NSMutableDictionary dictionaryWithDictionary:stringDic[@"object"]];
    [messageDic setObject:messageType forKey:@"websocketMsgType"];
    
    // 处理不同类型的消息
    if ([messageType isEqualToString:@"SET_DK_TOP_MSG"] || [messageType isEqualToString:@"CANCEL_DK_TOP_MSG"]) {
        // 消息置顶
        BOOL isTop = [messageType isEqualToString:@"SET_DK_TOP_MSG"];
        [self configTopMsg:messageDic isTop:isTop];
        // 接收新消息刷新列表
        [self receivenewMsgReloadTabView:messageType];
    } else if ([messageType isEqualToString:@"ACTIVITY_MSG"] ||
               [messageType isEqualToString:@"STOCK_MSG"] ||
               [messageType isEqualToString:@"ONLINE"] ||
               [messageType isEqualToString:@"STARTTITLE"] ||
               [messageType isEqualToString:@"USERZAN"] ||
               [messageType isEqualToString:@"LIVESTOP"]) {
        [self postNotificationInfo:messageType msg:messageDic];
        return;
    } else if ([messageType isEqualToString:@"USERMSGREJECT"]) {
        // 消息撤回
        for (LiveMessageFrameModel *frameModel in self.frameModelArray) {
            if (frameModel.model.messageId == [messageDic[@"id"] integerValue]) {
                [self.frameModelArray removeObject:frameModel];
                break;
            }
        }
        // 接收新消息刷新列表
        [self receivenewMsgReloadTabView:messageType];
    } else {
        // 仅在不为“只看大咖”模式下处理非大咖消息
        if (!self.isOnlySeeDaka) {
            LiveMessageFrameModel *frameModel = [[LiveMessageFrameModel alloc] init];
            frameModel.liveType = self.detailModel.liveType;
            LiveMessageBaseModel *model = [LiveMessageBaseModel modelWithDictionary:messageDic];
            if ((model.createTime - self.baseTime) > 5 * 60 * 1000) {
                frameModel.showTime = YES;
                self.baseTime = model.createTime;
            }
            // 处理收到重复的消息
            __weak LiveMessageFrameModel *weakSelfModel = frameModel;
            WEAKSELF
            frameModel.loaderCompletedBlock = ^{
                // 处理收到的重复消息
                [__weakSelf dealdUplicationMsg:weakSelfModel];
                // 收到新消息刷新列表
                [__weakSelf receivenewMsgReloadTabView:messageType];
            };
            frameModel.model = model;
            // 关注投顾提示消息 送礼物消息
            if ([messageType isEqualToString:@"NOTICEMSG"] || [messageType isEqualToString:@"USERGIFT"]) {
                [self postNotificationInfo:messageType msg:messageDic];
            }
            // 收到了回复我的消息
            if (model.quoteUserId == [[FMUserDefault getUserId] integerValue]) {
                [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationLiveReplyMessage object:messageDic];
            }
        }
        // 如果是“只看大咖”模式，则仅处理大咖消息（例如: "DKMSG"）
        else {
            if ([messageType isEqualToString:@"DKMSG"]) {
                LiveMessageFrameModel *frameModel = [[LiveMessageFrameModel alloc] init];
                frameModel.liveType = self.detailModel.liveType;
                LiveMessageBaseModel *model = [LiveMessageBaseModel modelWithDictionary:messageDic];
                if ((model.createTime - self.baseTime) > 5 * 60 * 1000) {
                    frameModel.showTime = YES;
                    self.baseTime = model.createTime;
                }
                // 处理收到重复的消息
                __weak LiveMessageFrameModel *weakSelfModel = frameModel;
                WEAKSELF
                frameModel.loaderCompletedBlock = ^{
                    // 处理收到的重复消息
                    [__weakSelf dealdUplicationMsg:weakSelfModel];
                    // 收到新消息刷新列表
                    [__weakSelf receivenewMsgReloadTabView:messageType];
                };
                frameModel.model = model;
                // 关注投顾提示消息 送礼物消息
                if ([messageType isEqualToString:@"NOTICEMSG"] || [messageType isEqualToString:@"USERGIFT"]) {
                    [self postNotificationInfo:messageType msg:messageDic];
                }
                // 收到了回复我的消息
                if (model.quoteUserId == [[FMUserDefault getUserId] integerValue]) {
                    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationLiveReplyMessage object:messageDic];
                }
            }
        }
    }
}


/// WS链接成功
- (void)webSocketManagerConnectSuccess {
    if(self.webSocketConnectSuccess) {
        self.webSocketConnectSuccess();
    }
}

/// websocket连接失败 以及重连失败 则获取一下最新的直播信息
- (void)webSocketManagerConnectFailed {
    if (self.refreshLiveInfo) {
        self.refreshLiveInfo();
    }
}

- (NSMutableArray<LiveMessageFrameModel *> *)frameModelArray {
    if (!_frameModelArray) {
        _frameModelArray = [NSMutableArray array];
    }
    return _frameModelArray;
}

- (NSArray *)messageTypeArray {
    if (!_messageTypeArray) {
        _messageTypeArray = @[@"ENTERMSG",@"USERMSGYES",@"DKMSG",@"SET_DK_TOP_MSG",@"CANCEL_DK_TOP_MSG",@"STOCK_MSG",@"LIVESTOP",@"NOTICEMSG",@"ONLINE",@"STARTTITLE",@"USERZAN",@"USERMSGREJECT",@"ACTIVITY_MSG",@"USERGIFT"];
    }
    return _messageTypeArray;
}

@end
