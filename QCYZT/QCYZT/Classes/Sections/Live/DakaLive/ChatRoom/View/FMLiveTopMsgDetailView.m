//
//  FMLiveTopMsgDetailView.m
//  QCYZT
//
//  Created by shumi on 2023/3/20.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMLiveTopMsgDetailView.h"
#import "LiveMessageTextView.h"
#import "LiveMessageFrameModel.h"
#import "ChatRoomViewModel.h"
#import "FMLiveDetailModel.h"
#import "FMPayTool.h"
#import "PaymentView.h"
#import "HttpRequestTool+Live.h"
#import "HttpRequestTool+Pay.h"
#import "HZPhotoBrowser.h"

@interface FMLiveTopMsgDetailView ()
@property (nonatomic, strong) UIScrollView *scrollView;

/// 消息显示容器View
@property (nonatomic, strong) UIView *messageContentView;
/// 引用消息UI容器
@property (nonatomic, strong) UIView *quoteView;
/// 引用消息内容文本
@property (nonatomic, strong) UILabel *quoteLabel;
@property (nonatomic, strong) UIView *quoteLine;

/// 消息文本
@property (nonatomic, strong) LiveMessageTextView *textView;
/// 超链接文本
@property (nonatomic, strong) YYLabel *linkTextLB;
/// 笔记试图容器
@property (nonatomic, strong) UIView *noteContentView;
/// 笔记标题
@property (nonatomic, strong) UILabel *noteTitleLabel;
/// 笔记图标
@property (nonatomic, strong) UIImageView *noteIcon;

@end

@implementation FMLiveTopMsgDetailView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
        [self setupUI];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    UI_View_BorderRadius(self.messageContentView, 2, 0.5, UIColor.fm_sepline_color);
}

- (void)setupUI {
    
    
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    UI_View_Radius(scrollView, 8);
    scrollView.backgroundColor = UIColor.up_contentBgColor;
    [self addSubview:scrollView];
    [scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.equalTo(self);
        make.size.equalTo(CGSizeMake(UI_SCREEN_WIDTH - 75, (UI_SCREEN_WIDTH -75) * 49 / 30));
    }];
    self.scrollView = scrollView;
    
    UIView *messageContentView = [[UIView alloc] init];
    messageContentView.backgroundColor = UIColor.up_contentBgColor;
    [scrollView addSubview:messageContentView];
    self.messageContentView = messageContentView;
    
    // 引用消息容器
    UIView *quoteView = [[UIView alloc] init];
    quoteView.backgroundColor = FMClearColor;
    [self.messageContentView addSubview:quoteView];
    self.quoteView = quoteView;
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = ColorWithHex(0xd6d6d6);
    [quoteView addSubview:line];
    self.quoteLine = line;
    
    // 引用消息
    UILabel *quoteLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:14] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:0];
    [quoteView addSubview:quoteLabel];
    self.quoteLabel = quoteLabel;

    /// 内容消息
    LiveMessageTextView *textView = [[LiveMessageTextView alloc] init];
    WEAKSELF
    textView.payMessageClickBlock = ^{
        // 直播间消息付费
        [__weakSelf messagePayment];
    };
    textView.imagePreviewBlock = ^(NSInteger index) {
        /// 图片预览
        HZPhotoBrowser *photoCtrl = [[HZPhotoBrowser alloc] init];
        NSMutableArray *imageUrlArray = [NSMutableArray array];
        for (NSInteger i = 0; i < __weakSelf.frameModel.imgsAddress.count; i ++) {
            NSDictionary *dic = __weakSelf.frameModel.imgsAddress[i];
            [imageUrlArray addObject:dic[@"url"]];
        }
        photoCtrl.imageArray = imageUrlArray;
        photoCtrl.currentImageIndex = (int)index;
        [photoCtrl show];
    };
    textView.font = FontWithSize(15);
    [self.messageContentView addSubview:textView];
    self.textView = textView;
    
    // 超链接
    YYLabel *linkTextLB = [[YYLabel alloc] init];
    linkTextLB.textColor = ColorWithHex(0x0076FF);
    linkTextLB.numberOfLines = 0;
    linkTextLB.preferredMaxLayoutWidth = 235;
    linkTextLB.userInteractionEnabled = YES;
    [self.messageContentView addSubview:linkTextLB];
    self.linkTextLB = linkTextLB;
    
    // 笔记试图容器
    UIView *noteContentView = [[UIView alloc] init];
    noteContentView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    [self.messageContentView addSubview:noteContentView];
    self.noteContentView = noteContentView;
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://note?id=%ld", __weakSelf.frameModel.model.noteid]];
    }];
    [noteContentView addGestureRecognizer:tap];
    
    // 笔记标题
    UILabel *noteTitleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:13] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:2];
    [noteContentView addSubview:noteTitleLabel];
    self.noteTitleLabel = noteTitleLabel;
    
    // 笔记图标
    UIImageView *noteIcon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"live_note_icon"]];
    [noteContentView addSubview:noteIcon];
    self.noteIcon = noteIcon;
    
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:ImageWithName(@"alert_close") forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(closeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(scrollView.mas_bottom).offset(10);
        make.centerX.equalTo(self);
        make.size.equalTo(CGSizeMake(30, 30));
    }];
}

/// 私密消息支付
- (void)messagePayment {
    if ([FMHelper checkLoginStatus]) {
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
        enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
        enablePayModel.name = @"金币";
        enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
        enablePayModel.type = PaymentTypeCoin;
        self.detailModel.enablePayModel = @[enablePayModel];
        NSString *dakaId = self.detailModel.bignameDto.userId;
        NSString *price = [NSString stringWithFormat:@"%ld",self.frameModel.model.messagePrice];
        NSString *title = @"直播付费消息";
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:dakaId certCode:self.detailModel.bignameDto.certCode clickView:nil confirmOperation:^{
            [PaymentView showWithEnablePayModel:enablePayModel payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
                // 3.支付
                [HttpRequestTool liveChatRoomPayMessageWithMessageId:self.frameModel.model.messageId roomId:self.detailModel.liveId  start:^{
                } failure:^{
                    [SVProgressHUD showErrorWithStatus:@"网络不给力"];
                } success:^(NSDictionary *dic) {
                    if ([dic[@"status"] isEqualToString:@"1"]) {
                        // 金币支付，更新本地的金币余额
                        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                        userModel.coin = userModel.coin - price.integerValue;
                        [MyKeyChainManager save:kUserModel data:userModel];

                        LiveMessageBaseModel *model = [LiveMessageBaseModel modelWithDictionary:dic[@"data"][@"dkmsg"]];
                        model.websocketMsgType = @"DKMSG";
                        LiveMessageFrameModel *newframeModel = [[LiveMessageFrameModel alloc] init];
                        WEAKSELF
                        __weak LiveMessageFrameModel *weakSelfModel = newframeModel;
                        newframeModel.loaderCompletedBlock = ^{
                            __weakSelf.frameModel = weakSelfModel;
                            if (__weakSelf.messagePaymentBlock) {
                                __weakSelf.messagePaymentBlock(weakSelfModel);
                            }
                        };
                        newframeModel.liveType = self.detailModel.liveType;
                        newframeModel.model = model;
                        [SVProgressHUD showSuccessWithStatus:@"支付成功!"];
                    } else {
                        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                    }
                }];
            } dismissBlock:^{
            }];
        }];
        
    }
}

- (void)closeBtnClick {
    self.hidden = YES;
}

- (void)setFrameModel:(LiveMessageFrameModel *)frameModel {
    _frameModel = frameModel;
    
    // 引用消息
    self.quoteView.frame = frameModel.quoteViewF;
    self.quoteLine.frame = CGRectMake(0, 0, 2, frameModel.quoteViewF.size.height);
    self.quoteLabel.frame = CGRectMake(12, 0, frameModel.quoteViewF.size.width - 12, frameModel.quoteViewF.size.height);
    self.quoteLabel.text = frameModel.model.quoteContent;
    
    // 文本
    self.textView.frame = frameModel.textF;
    self.textView.attributedText = frameModel.model.contentAttrString;
    self.textView.linkModelArray = frameModel.linkModelArray;
    self.textView.imgArr = frameModel.imgsAddress;

    // 超链接
    if (frameModel.model.linkContent.length > 0) {
        NSMutableAttributedString *lintextAttr = [[NSMutableAttributedString alloc] initWithString:frameModel.model.linkContent];
        lintextAttr.yy_font = FontWithSize(15.0);
        
        YYTextDecoration *decoration = [YYTextDecoration decorationWithStyle:YYTextLineStyleSingle width:@(1) color:ColorWithHex(0x0076FF)];
        [lintextAttr setYy_textUnderline:decoration];
        
        [lintextAttr yy_setTextHighlightRange:NSMakeRange(0, lintextAttr.length) color:ColorWithHex(0x0076FF) backgroundColor:FMClearColor tapAction:^(UIView *containerView, NSAttributedString *text, NSRange range, CGRect rect) {
            [ProtocolJump jumpWithUrl:frameModel.model.link];
        }];
        self.linkTextLB.frame = frameModel.linkTextF;
        self.linkTextLB.attributedText = lintextAttr;
    } else {
        self.linkTextLB.frame = CGRectZero;
    }
   
    // 笔记标题
    self.noteTitleLabel.frame = frameModel.noteTitleF;
    self.noteTitleLabel.text = frameModel.model.noteTitle;
    
    // 笔记icon
    self.noteIcon.frame = frameModel.noteIconF;
    self.noteContentView.frame = frameModel.noteContentF;
    
    self.messageContentView.frame = CGRectMake(20, 20, UI_SCREEN_WIDTH - 75 - 40, frameModel.contentF.size.height);
    self.scrollView.contentSize = CGSizeMake(UI_SCREEN_WIDTH - 75, CGRectGetMaxY(frameModel.contentF));
}

@end
