//
//  FMPurchasedMonthlyServiceVC.m
//  QCYZT
//
//  Created by zeng on 2024/9/20.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMPurchasedMonthlyServiceVC.h"
#import "FMPurchasedMonthlyCell.h"
#import "HttpRequestTool+Strategy.h"
#import "FMIndexMallIndexDetailViewController.h"
#import "FMStockStrategyPayModel.h"
#import "HttpRequestTool+Pay.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "FMStockStrategyPayPopView.h"
#import "FMPayTool.h"

@interface FMPurchasedMonthlyServiceVC ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *datas;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;


@end

@implementation FMPurchasedMonthlyServiceVC


- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = self.tableView.backgroundColor = UIColor.up_contentBgColor;

    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 20;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 0, UI_SAFEAREA_BOTTOM_HEIGHT, 0));
    }];
    
    [self.tableView.mj_header beginRefreshing];
}

#pragma mark - UITableViewDataSource/Delegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.datas.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMPurchasedMonthlyCell *cell = [tableView reuseCellClass:[FMPurchasedMonthlyCell class]];
    cell.model = self.datas[indexPath.row];
    WEAKSELF
    cell.payBlock = ^(FMPurchasedMonthlyModel * _Nonnull model) {
        [__weakSelf requestBuyInfoWithMonthlyModel:model];
    };
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];

}

#pragma mark - HTTP
- (void)requestData {
    [HttpRequestTool requestPurchasedIndexListWithPage:self.page pageSize:self.pageSize start:^{
    } failure:^{
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self endRefreshForSuccess];
            if (self.page == 1) {
                [self.tableView.mj_footer resetNoMoreData];
                [self.datas removeAllObjects];
            }

            NSArray<FMPurchasedMonthlyModel *> *dataArr = [NSArray modelArrayWithClass:[FMPurchasedMonthlyModel class] json:dic[@"data"]];
            if (dataArr.count < self.pageSize) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            } else {
                self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
            }
            [self.datas addObjectsFromArray:dataArr];
            
            if (!self.datas.count) {
                [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无内容" attributes:nil offsetY:200];
                self.tableView.mj_footer.hidden = YES;
            } else {
                [self.tableView dismissNoDataView];
                self.tableView.mj_footer.hidden = NO;
            }

            [self.tableView reloadData];
        } else {
            [self endRefreshForFailure];
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)requestBuyInfoWithMonthlyModel:(FMPurchasedMonthlyModel *)monthlyModel {
    if (monthlyModel.consumeContentid.length) {
        if (monthlyModel.consumeType == 25) {
            [HttpRequestTool requestStrategyBuyInfoWithTemplateId:[monthlyModel.consumeContentid integerValue] start:^{
            } failure:^{
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    NSArray *payInfoModels = [NSArray modelArrayWithClass:[FMStockStrategyPayModel class] json:dic[@"data"]];
                    [self showPayPopViewWithPayInfoModels:payInfoModels monthlyModel:monthlyModel];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        } else if (monthlyModel.consumeType == 26) {
            [self showPayViewWithName:monthlyModel.name price:monthlyModel.price goodsId:monthlyModel.goodsId monthlyModel:monthlyModel];
        }
    }
}

- (void)showPayPopViewWithPayInfoModels:(NSArray <FMStockStrategyPayModel *> *)payInfoModels monthlyModel:(FMPurchasedMonthlyModel *)monthlyModel {
    FMStockStrategyPayPopView *popView = [[FMStockStrategyPayPopView alloc] initWithDataArr:payInfoModels title:monthlyModel.name chooseBlock:^(FMStockStrategyPayModel * _Nonnull model) {
        [self requestPayWithModel:model monthlyModel:monthlyModel];
    }];
    [popView show];
}

- (void)requestPayWithModel:(FMStockStrategyPayModel *)model monthlyModel:(FMPurchasedMonthlyModel *)monthlyModel {
    [self showPayViewWithName:model.templateName price:model.price goodsId:model.goodsId monthlyModel:monthlyModel];
}

- (void)showPayViewWithName:(NSString *)goodsName price:(CGFloat)goodsPrice goodsId:(NSString *)goodsId monthlyModel:(FMPurchasedMonthlyModel *)monthlyModel {
    if ([FMHelper checkLoginStatus]) {
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:nil certCode:nil clickView:nil confirmOperation:^{
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
            enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
            enablePayModel.name = @"金币";
            enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
            enablePayModel.type = PaymentTypeCoin;
            enablePayModel.consumeType = monthlyModel.consumeType;
            NSString *price = [NSString stringWithFormat:@"%.0f", goodsPrice];
            [PaymentView showWithEnablePayModel:enablePayModel payPrice:price productName:goodsName bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
                if (monthlyModel.consumeType == 25) {
                    [self payStrategy:goodsId price:price];
                } else if (monthlyModel.consumeType == 26) {
                    [self payUnlockPosition:goodsId price:price];
                }
            } dismissBlock:^{
            }];
        }];
    }
}

- (void)payStrategy:(NSString *)goodsId price:(NSString *)price {
    [HttpRequestTool payStrategyWithGoodsId:goodsId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD showSuccessWithStatus:@"支付成功"];
            // 金币支付，更新本地的金币余额
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            userModel.coin = userModel.coin - price.integerValue;
            [MyKeyChainManager save:kUserModel data:userModel];
            
            [self.tableView.mj_header beginRefreshing];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)payUnlockPosition:(NSString *)goodsId price:(NSString *)price {
    [HttpRequestTool payUnlockPositionWithGoodsId:goodsId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD showSuccessWithStatus:@"支付成功"];
            // 金币支付，更新本地的金币余额
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            userModel.coin = userModel.coin - price.integerValue;
            [MyKeyChainManager save:kUserModel data:userModel];
            
            [self.tableView.mj_header beginRefreshing];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

#pragma mark - Private
- (void)headerAction {
    self.page = 1;
    [self requestData];
}

- (void)footerAction {
    self.page++;
    [self requestData];
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        [_tableView registerCellClass:[FMPurchasedMonthlyCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.estimatedRowHeight = 80.0f;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
        _tableView.mj_footer.hidden = YES;
    }
    
    return _tableView;
}

- (NSMutableArray *)datas {
    if (!_datas) {
        _datas = [NSMutableArray array];
    }
    
    return _datas;
}

@end
