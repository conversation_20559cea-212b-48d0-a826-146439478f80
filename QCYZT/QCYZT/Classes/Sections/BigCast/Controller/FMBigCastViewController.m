//
//  FMBigCastViewController.m
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2016/12/18.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "FMBigCastViewController.h"
#import "BigCastTabCell.h"
#import "BigCastModel.h"
#import "BannerCoViewCell.h"
#import "FMBigCastCommonModel.h"
#import "HttpRequestTool+Home.h"
#import "HttpRequestTool+Daka.h"

@interface FMBigCastViewController () <UITableViewDelegate,UITableViewDataSource,UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout,UIScrollViewDelegate> {
    NSTimer *_timer;
}

@property (nonatomic, strong) UITableView *bigCastTab;
@property (nonatomic, strong) UIButton *searchButton;
@property (nonatomic, strong) UICollectionView *bannerCo;
@property (nonatomic, strong) NSMutableArray *bannerDataArr;
@property (nonatomic, strong) NSMutableArray *listDataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;
@property (nonatomic, strong) UIView *bannerHeadView;
@property (nonatomic, strong) UIPageControl * pageControl;

@property (nonatomic, assign) BOOL needsRefresh;

@end

@implementation FMBigCastViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"投顾";
    self.view.backgroundColor = FMBgColor;
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:self.searchButton];

    [self.view addSubview:self.bigCastTab];
    [self.bigCastTab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    [self setInitData];
    [self.bigCastTab.mj_header beginRefreshing];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(focusSuccess:) name:kFocusNumChanged object:nil];
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        [self setInitData];
        [self getBigCastListData];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self stopTimer];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark -- timer

- (void)startTimer {
    if (!_timer) {
        _timer = [NSTimer timerWithTimeInterval:5 target:self selector:@selector(changeImage) userInfo:nil repeats:YES];
        NSRunLoop *runLoop = [NSRunLoop currentRunLoop];
        [runLoop addTimer:_timer forMode:NSDefaultRunLoopMode];
    }
}

- (void)stopTimer {
    if (_timer) {
        [_timer invalidate];
        _timer = nil;
    }
}

-(void)changeImage {

    static CGFloat speed;
    if (self.bannerCo.contentOffset.x == UI_SCREEN_WIDTH*(floor([self.bannerDataArr count]/3)-1)) {
        speed = -UI_SCREEN_WIDTH;
    }
    
    if (self.bannerCo.contentOffset.x  == 0) {
        speed = UI_SCREEN_WIDTH;
    }
    self.bannerCo.contentOffset = CGPointMake(self.bannerCo.contentOffset.x+speed, 0);
    _pageControl.currentPage = self.bannerCo.contentOffset.x/UI_SCREEN_WIDTH;
    
}

- (void)focusSuccess:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *userid = userInfo[@"dakaId"];
    for (int i = 0; i<self.listDataArr.count; i++) {
        BigCastModel *model = self.listDataArr[i];
        if ([model.userid isEqualToString:userid]) {
            [self.bigCastTab reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForRow:i inSection:0]] withRowAnimation:UITableViewRowAnimationNone];
            break;
        }
    }
}

#pragma mark -- UIScrollViewDegelate
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    if ([scrollView isKindOfClass:[UICollectionView class]]) {
        _pageControl.currentPage = self.bannerCo.contentOffset.x/UI_SCREEN_WIDTH;
    }
}
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    if (floor([self.bannerDataArr count]/3) > 1) {
        [self stopTimer];
    }
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (floor([self.bannerDataArr count]/3) > 1) {
        [self startTimer];
    }
}

#pragma mark -- UITabViewDelegate & Datasource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.listDataArr count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    BigCastTabCell *cell = [self.bigCastTab dequeueReusableCellWithIdentifier:@"BigCastTabCell" forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    if ([self.listDataArr count] > 0) {
        cell.model = self.listDataArr[indexPath.row];
    }
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    BigCastModel *model = self.listDataArr[indexPath.row];
    return [tableView fd_heightForCellWithIdentifier:@"BigCastTabCell" cacheByKey:model.user_profiles configuration:^(BigCastTabCell *cell) {
        cell.model = model;
    }];
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 106.0f;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    BigCastTabCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@", cell.userId]];
}

#pragma mark -- UICollectionViewDelegate & Datasource
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return floor([self.bannerDataArr count]/3);
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    BannerCoViewCell *cell = [self.bannerCo dequeueReusableCellWithReuseIdentifier:@"BannerCoViewCell" forIndexPath:indexPath];
    if ([self.bannerDataArr count] > 2) {
        NSArray *threeArray = [self.bannerDataArr subarrayWithRange:NSMakeRange(3*indexPath.item, 3)];
        cell.dataArray = threeArray;
    }
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return CGSizeMake(UI_SCREEN_WIDTH, 240*UI_RELATIVE_WIDTH);
}


#pragma mark -- 数据请求

- (void)getRecommendWithType {
    WEAKSELF
    [HttpRequestTool getRecommendWithPositionId:@"200101" page_no:0 page_size:0 start:^{
        
    } failure:^{
        
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMBigCastCommonModel class] json:dic[@"data"]];
            if (floor([dataArr count]/3) > 0) {
                self.bigCastTab.tableHeaderView = self.bannerHeadView;
                //一共有几个点
                if (floor([dataArr count]/3) > 1) {
                    self.pageControl.numberOfPages = floor([dataArr count]/3);
                    [self startTimer];
                } else {
                    self.pageControl.numberOfPages = 0;
                    [self stopTimer];
                }
                [__weakSelf.bannerDataArr removeAllObjects];
                [__weakSelf.bannerDataArr addObjectsFromArray:dataArr];
                [__weakSelf.bannerCo reloadData];
            } else {
                self.bigCastTab.tableHeaderView = [UIView new];
            }
        } 
    }];
}

- (void)getBigCastListData {
    WEAKSELF;
    [HttpRequestTool getDakaListWithWithPage:self.page pageSize:self.pageSize orderType:@"2" userType:nil keyWord:nil goodAt:nil listType:@"2" start:^{
        
    } failure:^{
        [__weakSelf endRefreshForFailure];
        
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self endRefreshForSuccess];

            if (__weakSelf.page == 1) {
                [__weakSelf.bigCastTab.mj_footer resetNoMoreData];
                [__weakSelf.listDataArr removeAllObjects];
            }
            
            NSArray *dataArr = [NSArray modelArrayWithClass:[BigCastModel class] json:dic[@"data"]];
            if (dataArr.count < self.pageSize) {
                [__weakSelf.bigCastTab.mj_footer endRefreshingWithNoMoreData];
            }
            [__weakSelf dealDuplicateDataWithArr:dataArr];
            
            __weakSelf.bigCastTab.mj_footer.hidden = !__weakSelf.listDataArr.count;
            [__weakSelf.bigCastTab reloadData];
        } else {
            [__weakSelf endRefreshForFailure];

            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)endRefreshForFailure {
    [self.bigCastTab.mj_footer endRefreshing];
    [self.bigCastTab.mj_header endRefreshing];
    self.page = self.currentPage;
}

//成功状态下停止refresh
- (void)endRefreshForSuccess {
    [self.bigCastTab.mj_footer endRefreshing];
    [self.bigCastTab.mj_header endRefreshing];
    self.currentPage = self.page;
}

#pragma mark - Private
- (void)setInitData {
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;
}

- (void)dealDuplicateDataWithArr:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (BigCastModel *model in array) {
        [dic setObject:model forKey:model.userid];
    }
    
    for (BigCastModel *model in self.listDataArr.reverseObjectEnumerator) {
        if ([dic valueForKey:model.userid]) {
            [self.listDataArr removeObject:model];
        }
    }
    
    [self.listDataArr addObjectsFromArray:array];
}

- (void)jumpToSearchStockView {
    [ProtocolJump jumpWithUrl:@"qcyzt://search"];
}

#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

#pragma mark -- lazy

- (UITableView *)bigCastTab {
    if (!_bigCastTab) {
        _bigCastTab = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        _bigCastTab.backgroundColor = FMBgColor;
        [_bigCastTab registerClass:[BigCastTabCell class] forCellReuseIdentifier:@"BigCastTabCell"];
        _bigCastTab.tableFooterView = [UIView new];
    }
    return _bigCastTab;
}

- (void)headerAction {
    self.page = 1;
    [self getBigCastListData];
    [self getRecommendWithType];
}

- (void)footerAction {
    self.page++;
    [self getBigCastListData];
}

- (UIView *)bannerHeadView {
    if (!_bannerHeadView) {
        _bannerHeadView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 240*UI_RELATIVE_WIDTH)];
        [_bannerHeadView addSubview:self.bannerCo];
        [_bannerHeadView addSubview:self.pageControl];
    }
    return _bannerHeadView;
}

- (UICollectionView *)bannerCo {
    if (!_bannerCo) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        [layout setScrollDirection:UICollectionViewScrollDirectionHorizontal];
        layout.minimumInteritemSpacing = 0;
        layout.minimumLineSpacing = 0;
        _bannerCo = [[UICollectionView alloc]initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 240*UI_RELATIVE_WIDTH) collectionViewLayout:layout];
        _bannerCo.backgroundColor = FMBgColor;
        _bannerCo.bounces = NO;
        _bannerCo.showsHorizontalScrollIndicator = NO;
        _bannerCo.pagingEnabled = YES;
        [_bannerCo registerClass:[BannerCoViewCell class] forCellWithReuseIdentifier:@"BannerCoViewCell"];
        
        _bannerCo.delegate = self;
        _bannerCo.dataSource = self;
    }
    return _bannerCo;
}

- (UIPageControl *)pageControl {
    if (!_pageControl) {
        
        _pageControl = [[UIPageControl alloc]initWithFrame:CGRectMake(UI_SCREEN_WIDTH/2.0-100, 240*UI_RELATIVE_WIDTH-20, 200, 20)];
        //改变当前点的颜色
        _pageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
        //改变非当前点的颜色
        _pageControl.pageIndicatorTintColor = ColorWithHex(0xf49898);
        //设置当前页
        _pageControl.currentPage = 0;
        _pageControl.enabled = NO;
    }
    return _pageControl;
}

- (NSMutableArray *)bannerDataArr {
    if (!_bannerDataArr) {
        _bannerDataArr = [[NSMutableArray alloc]init];
    }
    return _bannerDataArr;
}

- (NSMutableArray *)listDataArr {
    if (!_listDataArr) {
        _listDataArr = [NSMutableArray array];
    }
    
    return _listDataArr;
}

- (UIButton *)searchButton {
    if (!_searchButton) {
        UIImage *sicon = [UIImage imageNamed:@"search"];
        _searchButton = [UIButton buttonWithType:UIButtonTypeSystem];
        _searchButton.frame = CGRectMake(UI_SCREEN_WIDTH-(sicon.size.width+15), 0, sicon.size.width, UI_SAFEAREA_TOP_HEIGHT);
        [_searchButton setImage:sicon forState:UIControlStateNormal];
        _searchButton.backgroundColor = [UIColor clearColor];
        _searchButton.layer.borderWidth = 0;
        [_searchButton addTarget:self action:@selector(jumpToSearchStockView) forControlEvents:UIControlEventTouchUpInside];
    }
    return _searchButton;
}


@end
