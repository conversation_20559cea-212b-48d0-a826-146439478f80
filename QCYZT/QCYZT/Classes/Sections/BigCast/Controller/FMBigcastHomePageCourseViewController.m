//
//  FMBigcastHomePageCourseViewController.m
//  QCYZT
//
//  Created by zeng on 2021/11/28.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMBigcastHomePageCourseViewController.h"
#import "CourseDetailViewController.h"
#import "CourseAlbumDetailViewController.h"
#import "CourseCell.h"
#import "CourseAlbumCell.h"
#import "CourseListModel.h"
#import "CourseAlbumListModel.h"
#import "FMSearchAlbumCourseSectionHeader.h"
#import "HttpRequestTool+Course.h"

@interface FMBigcastHomePageCourseViewController ()

@property (nonatomic, strong) NSMutableArray *dataArr;

/// 系列课程数组
@property (nonatomic, strong) NSMutableArray *albumArr;
/// 优质课程数组
@property (nonatomic, strong) NSMutableArray *courseArr;
@end

@implementation FMBigcastHomePageCourseViewController

#pragma mark - 生命周期
- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [self configTableView];
    [self requestData];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(getRefreshNotification:) name:kCollectNumChanged object:nil];
}

- (void)dealloc {
    FMLog(@"%s", __func__);
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Private
- (void)configTableView {
    self.tableView.backgroundColor = self.view.backgroundColor = UIColor.up_contentBgColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.tableView registerCellClass:[CourseAlbumCell class]];
    [self.tableView registerCellClass:[CourseCell class]];
    [self.tableView registerViewClass:[FMSearchAlbumCourseSectionHeader class]];
    [self.dataArr addObject:self.albumArr];
    [self.dataArr addObject:self.courseArr];
}

#pragma mark - Notification
- (void)getRefreshNotification:(NSNotification *)noti {
    [self requestData];
}


#pragma mark - HTTP
- (void)requestData {
    
    [HttpRequestTool getDaKaCourseListWithCourseAuthorid:[self.authorId integerValue] start:^{
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [self dealData:dic];
    }];
}

- (void)dealData:(NSDictionary *)dic {
    if ([dic[@"status"] isEqualToString:@"1"]) {
        // 数据处理
        NSArray *albumArr = nil;
        NSArray *courseArr = nil;
        [self.courseArr removeAllObjects];
        [self.albumArr removeAllObjects];
        if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
            courseArr = [NSArray modelArrayWithClass:[CourseListModel class] json:dic[@"data"]];
        } else if ([dic[@"data"] isKindOfClass:[NSDictionary class]]) {
            albumArr = [NSArray modelArrayWithClass:[CourseAlbumListModel class] json:dic[@"data"][@"seriesList"]];
            courseArr = [NSArray modelArrayWithClass:[CourseListModel class] json:dic[@"data"][@"singleList"]];
        }
        if (albumArr.count) {
            [self.albumArr addObjectsFromArray:albumArr];
        }
        if (courseArr.count) {
            [self.courseArr addObjectsFromArray:courseArr];
        }
        
        if (!self.courseArr.count && !self.albumArr.count) {
            [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无课程" attributes:nil offsetY:80];
        } else {
            [self.tableView dismissNoDataView];
        }

        [self.tableView reloadData];
    } else {
        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
    }
}

#pragma mark - UITableView  DataSoure
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *arr = self.dataArr[section];
    return arr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *arr = self.dataArr[indexPath.section];

    if (arr == self.albumArr) {
        CourseAlbumCell *cell = [tableView reuseCellClass:[CourseAlbumCell class]];
        cell.model = arr[indexPath.row];
        cell.isLastCell = indexPath.row == arr.count - 1;
        return cell;
    } else if (arr == self.courseArr) {
        CourseCell *cell = [tableView reuseCellClass:[CourseCell class]];
        cell.model = arr[indexPath.row];
        cell.isLastCell = indexPath.row == arr.count - 1;
        cell.userIcon.userInteractionEnabled = NO;
        return cell;
    }
    
    return [UITableViewCell new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    NSArray *arr = self.dataArr[indexPath.section];
    if (arr == self.albumArr) {
        CGFloat height = [tableView fd_heightForCellWithIdentifier:NSStringFromClass([CourseAlbumCell class]) configuration:^(CourseAlbumCell *cell) {
            cell.model = self.albumArr[indexPath.row];
        }];
        return (height > 170) ? height : 170;
    } else if (arr == self.courseArr) {
        CGFloat height = [tableView fd_heightForCellWithIdentifier:NSStringFromClass([CourseCell class]) configuration:^(CourseCell *cell) {
            cell.model = self.courseArr[indexPath.row];
        }];
        return (height > 120) ? height : 120;
    }
    
    return CGFLOAT_MIN;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    NSArray *arr = self.dataArr[indexPath.section];
    if (arr == self.albumArr) {
        CourseAlbumListModel *model = arr[indexPath.row];
        if ([model.type isEqualToString:@"2"]) {
            [ProtocolJump jumpWithUrl:model.action];
        } else if ([model.type isEqualToString:@"1"]) {
            CourseAlbumDetailViewController *vc = [[CourseAlbumDetailViewController alloc] init];
            vc.albumId = model.courseId;
            [self.navigationController pushViewController:vc animated:YES];
        }
    } else if (arr == self.courseArr) {
        CourseDetailViewController *detailVC = [[CourseDetailViewController alloc] init];
        CourseListModel *model = arr[indexPath.row];
        detailVC.courseId = model.courseId;
        [self.navigationController pushViewController:detailVC animated:YES];
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    FMSearchAlbumCourseSectionHeader *header = [tableView reuseViewClass:[FMSearchAlbumCourseSectionHeader class]];
    NSArray *titleArr = @[@"系列课程", @"优质课程"];
    header.titleLabel.text = titleArr[section];
    
    return header;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    NSArray *arr = self.dataArr[section];
    if (arr.count) {
        return 45.0f;
    } else {
        return CGFLOAT_MIN;
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *view = [UIView new];
    view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 0 && self.courseArr.count) {
        return 10;
    }
    return CGFLOAT_MIN;
}

#pragma mark - Getter/Setter
- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    return _dataArr;
}

- (NSMutableArray *)albumArr {
    if (!_albumArr) {
        _albumArr = [NSMutableArray array];
    }
    return _albumArr;
}

- (NSMutableArray *)courseArr {
    if (!_courseArr) {
        _courseArr = [NSMutableArray array];
    }
    return _courseArr;
}


@end
