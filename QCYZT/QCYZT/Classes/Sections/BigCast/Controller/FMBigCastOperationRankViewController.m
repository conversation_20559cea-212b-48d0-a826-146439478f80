//
//  FMBigCastOperationRankViewController.m
//  QCYZT
//
//  Created by zeng on 2023/3/21.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMBigCastOperationRankViewController.h"
#import "FMBigCastOperationRankCell.h"
#import "FMBigCastOperationRankChampionCell.h"
#import "HttpRequestTool+Daka.h"
#import "FMBigCastOperationRankModel.h"
#import "FMBigCastOperatationRankNumView.h"

@interface FMBigCastOperationRankViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIView *navView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *tableHeaderView;
@property (nonatomic, strong) ZLTagLabel *tagLabel;

@property (nonatomic, strong) NSArray *dataArr;

@end

@implementation FMBigCastOperationRankViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = ColorWithHex(0xf65045);
    
    [self.view addSubview:self.tagLabel];
    [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(-UI_SAFEAREA_BOTTOM_HEIGHT);
    }];
    self.tagLabel.hidden = YES;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(0);
        make.bottom.equalTo(self.tagLabel.mas_top);
    }];
    self.tableView.hidden = YES;
    
    [self.view addSubview:self.navView];
    self.navView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SAFEAREA_TOP_HEIGHT);
    
    [self requestData];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
}

- (void)requestData {
    [HttpRequestTool getDakaOperationListWithStart:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            self.tableView.hidden = NO;
            self.tagLabel.hidden = NO;
            self.navView.backgroundColor = ColorWithHexAlpha(0xf65045, 0);
            
            self.dataArr = [NSArray modelArrayWithClass:[FMBigCastOperationRankModel class] json:dic[@"data"]];
            [self.tableView reloadData];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat offsetY = scrollView.contentOffset.y;
    CGFloat alpha = offsetY / 376;
    self.navView.backgroundColor = ColorWithHexAlpha(0xf65045, alpha);
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row == 0) {
        FMBigCastOperationRankChampionCell *cell = [tableView reuseCellClass:[FMBigCastOperationRankChampionCell class]];
        cell.onlyOneCell = self.dataArr.count == 1;
        cell.model = self.dataArr[indexPath.row];
        return cell;
    }
    FMBigCastOperationRankCell *cell = [tableView reuseCellClass:[FMBigCastOperationRankCell class]];
    cell.rank = indexPath.row + 1;
    cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
    cell.model = self.dataArr[indexPath.row];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row == 0) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMBigCastOperationRankChampionCell class]) configuration:^(FMBigCastOperationRankChampionCell *cell) {
            cell.onlyOneCell = self.dataArr.count == 1;
            cell.model = self.dataArr[indexPath.row];
        }];
    }
    return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMBigCastOperationRankCell class]) configuration:^(FMBigCastOperationRankCell *cell) {
        cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
        cell.model = self.dataArr[indexPath.row];
    }];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [[UIView alloc] init];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [[UIView alloc] init];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        [_tableView registerCellClass:[FMBigCastOperationRankCell class]];
        [_tableView registerCellClass:[FMBigCastOperationRankChampionCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = FMClearColor;
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 20)];
        _tableView.tableHeaderView = self.tableHeaderView;
        _tableView.bounces = NO;
    }
    
    return _tableView;
}

- (UIView *)tableHeaderView {
    if (!_tableHeaderView) {
        _tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_Relative_WidthValue(376))];
        _tableHeaderView.clipsToBounds = YES;
        
        UIImageView *imgV = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_Relative_WidthValue(425))];
        imgV.image = ImageWithName(@"daka_operation_top");
        [_tableHeaderView addSubview:imgV];
    }
    
    return _tableHeaderView;
}

- (UIView *)navView {
    if (!_navView) {
        _navView = [UIView new];
        _navView.backgroundColor = ColorWithHexAlpha(0xf65045, 1);
        
        UIView *contentView = [[UIView alloc] init];
        [_navView addSubview:contentView];
        [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@0);
            make.top.equalTo(@(UI_STATUS_HEIGHT));
            make.height.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        
        UIButton *backArrow = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"return") target:self action:@selector(backArrowClicked)];
        [contentView addSubview:backArrow];
        [backArrow mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.bottom.equalTo(@0);
            make.width.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(18) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [contentView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(@0);
        }];
        titleLabel.text = @"神操作排行榜";

    }
    
    return _navView;
}

- (ZLTagLabel *)tagLabel {
    if (!_tagLabel) {
        _tagLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
        NSMutableAttributedString *attrstr = [[NSMutableAttributedString alloc] initWithString:@"风险提示：以上内容仅供参考，过往表现不能代表未来表现，也不能覆盖同期客户全部表现，个别客户特定时间区间的历史最佳收益不代表全部客户的历史业绩表现，不构成投资建议。据此操作，风险自担。投资有风险，入市需谨慎。四川大决策证券投资顾问有限公司(经营证券期货业务许可证号915101067130530143)"];
        NSMutableParagraphStyle *style = [NSMutableParagraphStyle new];
        style.lineSpacing = 3;
        [attrstr addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, attrstr.length)];
        _tagLabel.attributedText = attrstr;
        _tagLabel.heightPadding = 25;
    }
    
    return _tagLabel;
}


@end
