//
//  FMPersonDataInfoVC.m
//  QCYZT
//
//  Created by shumi on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPersonDataInfoVC.h"
#import "BigCastDetailModel.h"
#import "FMPersonDataBaseInfoCell.h"
#import "FMPersonAuthInfoTabCell.h"
#import "FMPersonDataServiceTabCell.h"
#import "FMMemberCenterProductViewController.h"
#import "FMPersonCertificateTabCell.h"

@interface FMPersonDataInfoVC ()<UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIImageView *topBgImgV;
@property (nonatomic, strong) UIView *headerView;
@property (nonatomic, strong) UIView *bottomView;
@end

@implementation FMPersonDataInfoVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = FMWhiteColor;
    [self.view addSubview:self.topBgImgV];
    [self.topBgImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@0);
        make.height.equalTo(@(UI_Relative_WidthValue(280)));
    }];
    
    NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
    // 是否打开开通vip入口
    BOOL bignameVipTag = [initDic[@"bignameVipTag"] boolValue];
    if (self.model.allowOpenVip > 0 && self.model.attestationType.integerValue == 2 && ![FMHelper getIAPPayStatus]) {
        if (bignameVipTag) {
            [self.view addSubview:self.bottomView];
            [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.bottom.left.right.equalTo(@(0));
                make.height.equalTo(@(65 + UI_SAFEAREA_BOTTOM_HEIGHT));
            }];
        }
    }
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@(UI_SAFEAREA_TOP_HEIGHT));
        if (self.model.allowOpenVip.integerValue > 0 && self.model.attestationType.integerValue == 2 && ![FMHelper getIAPPayStatus]) {
            if (bignameVipTag) {
                make.bottom.equalTo(self.bottomView.mas_top);
            } else {
                make.bottom.equalTo(@(0));
            }
        } else {
            make.bottom.equalTo(@(-UI_SAFEAREA_BOTTOM_HEIGHT));
        }
    }];
    
    self.tableView.tableHeaderView = self.headerView;    
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;

    [super viewWillAppear:animated];
        
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
}


#pragma mark - private
- (void)vipBtnClick {
    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
        FMMemberCenterProductViewController *vc = [[FMMemberCenterProductViewController alloc] init];
        vc.bigcastId = self.model.userid;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - tableViewDelegate

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 4;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        FMPersonDataBaseInfoCell *cell = [tableView reuseCellClass:[FMPersonDataBaseInfoCell class]];
        cell.model = self.model;
        return cell;
    } else if (indexPath.section == 1) {
        if (self.model.attestationType.integerValue > 0) {
            FMPersonAuthInfoTabCell *cell = [tableView reuseCellClass:[FMPersonAuthInfoTabCell class]];
            cell.model = self.model;
            return cell;
        }
    } else if (indexPath.section == 2) {
        if (self.model.certCode) {
            FMPersonCertificateTabCell *cell = [tableView reuseCellClass:[FMPersonCertificateTabCell class]];
            cell.model = self.model;
            return cell;
        }
    } else {
        NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
        // 是否打开开通vip入口
        BOOL bignameVipTag = [initDic[@"bignameVipTag"] boolValue];
        if (self.model.allowOpenVip > 0 && self.model.attestationType.integerValue == 2 && ![FMHelper getIAPPayStatus]) {
            if (bignameVipTag) {
                FMPersonDataServiceTabCell *cell = [tableView reuseCellClass:[FMPersonDataServiceTabCell class]];
                cell.model = self.model;
                return cell;
            }
        }
    }
    return [UITableViewCell new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMPersonDataBaseInfoCell class]) configuration:^(FMPersonDataBaseInfoCell *cell) {
            cell.model = self.model;
        }];
    } else if (indexPath.section == 1) {
        return self.model.attestationType.integerValue > 0 ? 120 : CGFLOAT_MIN;
    } else if (indexPath.section == 2) {
        return self.model.certCode.length > 0 ? 55 : CGFLOAT_MIN;
    } else {
        NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
        // 是否打开开通vip入口
        BOOL bignameVipTag = [initDic[@"bignameVipTag"] boolValue];
        if (self.model.allowOpenVip > 0 && self.model.attestationType.integerValue == 2 && ![FMHelper getIAPPayStatus]) {
            if (bignameVipTag && self.model.infoIntroduction.length > 0) {
                return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMPersonDataServiceTabCell class]) configuration:^(FMPersonDataServiceTabCell *cell) {
                    cell.model = self.model;
                }];
            }
        }
    }
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *view = [UIView new];
    if (section == 3) {
        view.backgroundColor = ColorWithHex(0xF7F7F7);
    }
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return (section == 3) ? 8.0f : CGFLOAT_MIN;
}


#pragma mark - lazy
- (UIImageView *)topBgImgV {
    if (!_topBgImgV) {
        _topBgImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"bigcast_info_bg")];
        _topBgImgV.userInteractionEnabled = YES;
        
        UIView *navView = [[UIView alloc] init];
        [_topBgImgV addSubview:navView];
        [navView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@0);
            make.top.equalTo(@(UI_STATUS_HEIGHT));
            make.height.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        navView.backgroundColor = FMClearColor;
        
        UIButton *backArrow = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"bigcast_homepage_back") target:self action:@selector(backArrowClicked)];
        [navView addSubview:backArrow];
        [backArrow mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.bottom.equalTo(@0);
            make.width.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(18) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [navView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(@0);
        }];
        titleLabel.text = @"个人资料";
    }
    
    return _topBgImgV;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[UIView alloc] init];
        _bottomView.backgroundColor = FMWhiteColor;
        
        UIButton *vipBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [vipBtn setTitle:@"开通VIP服务" forState:UIControlStateNormal];
        [vipBtn setTitleColor:ColorWithHex(0x7b3000) forState:UIControlStateNormal];
        vipBtn.titleLabel.font = FontWithSize(16);
        [vipBtn addTarget:self action:@selector(vipBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [_bottomView addSubview:vipBtn];
        [vipBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@(0));
            make.top.equalTo(@(10));
            make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH - 175, 45)));
        }];
        NSArray *colors =  @[(__bridge  id)ColorWithHex(0xFFE0A7).CGColor,(__bridge  id)ColorWithHex(0xFFC14D).CGColor];
        vipBtn.bounds = CGRectMake(0, 0, UI_SCREEN_WIDTH - 175, 45);
        [vipBtn drawCAGradientWithcolors:colors];
        [vipBtn bringSubviewToFront:vipBtn.titleLabel];
        UI_View_Radius(vipBtn, 45 / 2.0);
    }
    return _bottomView;;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        _tableView.backgroundColor = FMClearColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMPersonDataBaseInfoCell class]];
        [_tableView registerCellClass:[FMPersonAuthInfoTabCell class]];
        [_tableView registerCellClass:[FMPersonDataServiceTabCell class]];
        [_tableView registerCellClass:[FMPersonCertificateTabCell class]];
        _tableView.tableFooterView = [UIView new];
    }
    return _tableView;
}

- (UIView *)headerView {
    if (!_headerView) {
        _headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 116)];
        
        UIView *headerImgBgView = [[UIView alloc] init];
        headerImgBgView.backgroundColor = FMClearColor;
        [_headerView addSubview:headerImgBgView];
        [headerImgBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@(0));
            make.top.equalTo(@(16));
            make.size.equalTo(@(CGSizeMake(100, 100)));
        }];
        UI_View_BorderRadius(headerImgBgView, 50, 1, ColorWithHex(0xF7CCCD));
        
        UIImageView *headerImg = [[UIImageView alloc] init];
        headerImg.contentMode = UIViewContentModeScaleAspectFill;
        [headerImg sd_setImageWithURL:[NSURL URLWithString:self.model.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
        [headerImgBgView addSubview:headerImg];
        [headerImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.centerY.equalTo(@(0));
            make.size.equalTo(@(CGSizeMake(90, 90)));
        }];
        UI_View_Radius(headerImg, 45);
    }
    return _headerView;
}

@end
