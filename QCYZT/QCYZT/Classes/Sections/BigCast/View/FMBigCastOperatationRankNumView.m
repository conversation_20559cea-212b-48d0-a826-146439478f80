//
//  FMBigCastOperatationRankNumView.m
//  QCYZT
//
//  Created by zeng on 2023/6/5.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMBigCastOperatationRankNumView.h"

@interface FMBigCastOperatationRankNumCell : UITableViewCell

@property (nonatomic, strong) UIView *iconBgView;
@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) ZLTagView *tagView;
@property (nonatomic, strong) UILabel *rankNumLabel;

@property (nonatomic, strong) UIButton *followBtn;

@end

@implementation FMBigCastOperatationRankNumCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    [self.contentView addSubview:self.iconBgView];
    [self.iconBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(16);
        make.centerY.equalTo(0);
        make.width.height.equalTo(40);
    }];
    [self.iconBgView addSubview:self.iconImgV];
    [self.iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(0);
        make.width.height.equalTo(37);
    }];
    
    [self.contentView addSubview:self.followBtn];
    [self.followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(self.iconBgView);
        make.width.equalTo(87);
        make.height.equalTo(33);
    }];
        
    UIView *view = [[UIView alloc] init];
    [view addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(0);
    }];
    [view addSubview:self.rankNumLabel];
    [self.rankNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.right.equalTo(0);
        make.left.equalTo(self.nameLabel.mas_right).offset(10);
    }];
    
    UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionEqualSpacing spacing:6 arrangedSubviews:@[view, self.tagView]];
    [self addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBgView.mas_right).offset(7.5);
        make.centerY.equalTo(self.iconBgView);
        make.right.equalTo(self.followBtn.mas_left).offset(-10);
    }];
            
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(17);
    }];
}

#pragma mark - Private
- (void)followBtnClicked {
//    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@&secondLevel=2", self.model.bignameDto.userId]];
}

- (UIView *)iconBgView {
    if (!_iconBgView) {
        _iconBgView = [UIView new];
        UI_View_Radius(_iconBgView, 20);
        _iconBgView.backgroundColor = ColorWithHex(0xf2ebeb);
    }
    
    return _iconBgView;
}


- (UIImageView *)iconImgV {
    if (!_iconImgV) {
        _iconImgV = [UIImageView new];
        UI_View_Radius(_iconImgV, 18.5);
    }
    
    return _iconImgV;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _nameLabel;
}

- (UILabel *)rankNumLabel {
    if (!_rankNumLabel) {
        _rankNumLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _rankNumLabel;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] init];
        _tagView.tagLabelFont = FontWithSize(11);
        _tagView.tagLabelWidthPadding = 8.0f;
        _tagView.tagLabelHeightPadding = 4.0f;
        _tagView.middlePadding = 5;
        _tagView.tagLabelTextColor = ColorWithHex(0xa56112);
        _tagView.tagLabelCornerRadius = 2.0f;
        _tagView.numberofLines = 1;
        _tagView.tagLabelBgColor = ColorWithHex(0xfeddb5);
        _tagView.titleArray = @[@"测试", @"数据降低"];
    }
    
    return _tagView;
}

- (UIButton *)followBtn {
    if (!_followBtn) {
        _followBtn = [[UIButton alloc] initWithFrame:CGRectZero font:BoldFontWithSize(14) normalTextColor:FMWhiteColor backgroundColor:[UIColor lz_gradientColors:@[ColorWithHex(0xfc5219), ColorWithHex(0xfc0002)] withFrame:CGRectMake(0, 0, 87, 33) direction:GradientDirectionLeftToRight] title:@"追随老师" image:nil target:self action:@selector(followBtnClicked)];
        UI_View_Radius(_followBtn, 5.0f);
    }
    
    return _followBtn;
}

@end

static const CGFloat kCellHeight = 72.5;

@interface FMBigCastOperatationRankNumView()<UIGestureRecognizerDelegate, UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIButton *closeBtn;
@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, assign) CGFloat tableViewHeight;
@property (nonatomic, strong) NSArray *dataArr;

@end

@implementation FMBigCastOperatationRankNumView

- (instancetype)initWithDataArr:(NSArray *)dataArr {
    self = [super init];
    if (self) {
        self.dataArr = dataArr;
        self.tableViewHeight = kCellHeight * dataArr.count;
        if (self.tableViewHeight > 350) {
            self.tableViewHeight = 350;
        }
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    UIView *bgView = [[UIView alloc] init];
    [self addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    bgView.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
    
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, self.tableViewHeight + 50 + UI_SAFEAREA_BOTTOM_HEIGHT)];
    contentView.backgroundColor = FMWhiteColor;
    [self addSubview:contentView];
    [contentView layerAndBezierPathWithRect:contentView.bounds cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight];
    self.contentView = contentView;
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:FMZeroColor backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@13);
        make.height.equalTo(@24);
    }];
    titleLabel.text = @"投顾登榜情况";
    
    
    UIImage *originalImage = ImageWithName(@"daka_operation_upArrow");
    UIImage *rotatedImage = [UIImage imageWithCGImage:originalImage.CGImage scale:originalImage.scale orientation:UIImageOrientationDown];
    UIButton *closeBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:ColorWithHex(0x888888) backgroundColor:FMWhiteColor title:@"收起" image:rotatedImage target:self action:@selector(dismiss)];
    [contentView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(titleLabel);
    }];
    self.closeBtn = closeBtn;
    
    [contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@49.3);
        make.height.equalTo(@0.7);
    }];
    
    [contentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@50);
        make.height.equalTo(@(self.tableViewHeight));
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.closeBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:6];
}

- (void)show {
    [[UIApplication sharedApplication].keyWindow addSubview:self];
    self.frame = [UIScreen mainScreen].bounds;
    [UIView animateWithDuration:0.3 animations:^{
        self.contentView.frame = CGRectMake(0, UI_SCREEN_HEIGHT - self.tableViewHeight - 50 - UI_SAFEAREA_BOTTOM_HEIGHT, UI_SCREEN_WIDTH, self.tableViewHeight + 50 + UI_SAFEAREA_BOTTOM_HEIGHT);
    }];
}

- (void)dismiss {
    [UIView animateWithDuration:0.3 animations:^{
        self.contentView.frame = CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, self.tableViewHeight + 50 + UI_SAFEAREA_BOTTOM_HEIGHT);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

#pragma mark - tableViewDelegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMBigCastOperatationRankNumCell *cell = [tableView reuseCellClass:[FMBigCastOperatationRankNumCell class]];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return kCellHeight;;
}

#pragma mark - setter/getter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:nil];
        _tableView.separatorInset = UIEdgeInsetsMake(0, 0, 0, 0);
        _tableView.separatorColor = FMSepLineColor;
        [_tableView registerCellClass:[FMBigCastOperatationRankNumCell class]];
        _tableView.tableFooterView = [UIView new];
        _tableView.bounces = NO;
    }
    return _tableView;
}

@end
