//
//  FMPersonAuthInfoTabCell.m
//  QCYZT
//
//  Created by shumi on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPersonAuthInfoTabCell.h"
#import "BigCastDetailModel.h"

@interface FMPersonAuthInfoTabCell ()
@property (nonatomic, strong) UIImageView *authIconImg;
@property (nonatomic, strong) UILabel *authTitleLB;
@property (nonatomic, strong) UILabel *authSubTitleLB;
@property (nonatomic, strong) UILabel *certificateNum;
@end

@implementation FMPersonAuthInfoTabCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    UILabel *title = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor numberOfLines:1];
    title.text = @"Ta的认证";
    [self.contentView addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(13));
        make.left.equalTo(@(15));
        make.height.equalTo(@(24));
    }];
    
    UIView *topline = [[UIView alloc] init];
    topline.backgroundColor = ColorWithHex(0xececec);
    [self.contentView addSubview:topline];
    [topline mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(50));
        make.centerX.equalTo(@(0));
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH, 1)));
    }];
    
    UIImageView *authIconImg = [[UIImageView alloc] init];
    [self.contentView addSubview:authIconImg];
    [authIconImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(title.mas_bottom).offset(24);
        make.left.equalTo(@(15));
        make.size.equalTo(@(CGSizeMake(50, 50)));
    }];
    self.authIconImg = authIconImg;
    
    UILabel *authTitleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor numberOfLines:1];
    [self.contentView addSubview:authTitleLB];
    [authTitleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(authIconImg);
        make.left.equalTo(authIconImg.mas_right).offset(10);
        make.height.equalTo(@(23));
    }];
    self.authTitleLB = authTitleLB;
    
    UILabel *authSubTitleLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x888888) backgroundColor:FMWhiteColor numberOfLines:1];
    [self.contentView addSubview:authSubTitleLB];
    [authSubTitleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(authTitleLB);
        make.right.equalTo(@(-15));
        make.bottom.equalTo(authIconImg.mas_bottom);
    }];
    self.authSubTitleLB = authSubTitleLB;
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = ColorWithHex(0xececec);
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(authIconImg.mas_bottom).offset(10);
        make.centerX.equalTo(@(0));
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH, 1)));
        make.bottom.equalTo(@(0));
    }];
    
}

- (void)setModel:(BigCastDetailModel *)model {
    _model = model;
    self.authIconImg.hidden = (model.attestationType == 0);
    self.authTitleLB.hidden = (model.attestationType == 0);
    self.authSubTitleLB.hidden = (model.attestationType == 0);
    if (model.attestationType.integerValue == 1) {
        self.authIconImg.image = ImageWithName(@"persoon_attestation2");
        self.authTitleLB.text = @"专栏认证";
        self.authSubTitleLB.text = @"名家专栏，独到观点输出者";
    } else if (model.attestationType.integerValue == 2) {
        self.authIconImg.image = ImageWithName(@"persoon_attestation1");
        self.authTitleLB.text = @"投顾认证";
        self.authSubTitleLB.text = @"具备投资执业资格证的投资专家";
    } else {
        self.authIconImg.image = ImageWithName(@"persoon_attestation3");
        self.authTitleLB.text = @"机构认证";
        self.authSubTitleLB.text = @"官方入驻的机构账号或媒体官方账号";
    }

}

@end
