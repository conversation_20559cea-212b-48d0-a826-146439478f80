//
//  FMPersonCertificateTabCell.m
//  QCYZT
//
//  Created by shumi on 2022/7/7.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPersonCertificateTabCell.h"
#import "BigCastDetailModel.h"

@interface FMPersonCertificateTabCell ()
@property (nonatomic, strong) UILabel *certificateNum;
@end

@implementation FMPersonCertificateTabCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    UILabel *certificate = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x555555) backgroundColor:FMWhiteColor numberOfLines:1];
    certificate.text = @"资格证编号";
    [self.contentView addSubview:certificate];
    [certificate mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@(0));
        make.left.equalTo(@(15));
        make.height.equalTo(@(23));
    }];
    
    UILabel *certificateNum = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [self.contentView addSubview:certificateNum];
    [certificateNum mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(certificate);
        make.right.equalTo(@(-15));
        make.left.equalTo(certificate.mas_right).offset(10);
    }];
    self.certificateNum = certificateNum;
}

- (void)setModel:(BigCastDetailModel *)model {
    _model = model;
    self.certificateNum.text = model.certCode;
}

@end
