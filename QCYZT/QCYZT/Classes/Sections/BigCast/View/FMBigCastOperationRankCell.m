//
//  FMBigCastOperationRankCell.m
//  QCYZT
//
//  Created by zeng on 2023/3/21.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMBigCastOperationRankCell.h"
#import "SGAdvertScrollView.h"
#import "FMNoteDetailViewController.h"

@interface FMBigCastOperationRankCell()<SGAdvertScrollViewDelegate, UIGestureRecognizerDelegate>

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UIView *containerView;

@property (nonatomic, strong) UIImageView *rankImgV;
@property (nonatomic, strong) ZLTagLabel *rankLabel;
@property (nonatomic, strong) UIView *iconBgView;
@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) ZLTagView *tagView;
@property (nonatomic, strong) UIButton *followBtn;
@property (nonatomic, strong) UILabel *stockNameLabel;
@property (nonatomic, strong) UILabel *stockCodeLabel;
@property (nonatomic, strong) UILabel *stockRateLabel;
@property (nonatomic, strong) UILabel *stockRateDescLabel;
@property (nonatomic, strong) UILabel *goodNewsLabel;

// 评论View
@property (nonatomic, strong) UIView *commentView;
@property (nonatomic, strong) SGAdvertScrollView *commentScrollView;

@end

@implementation FMBigCastOperationRankCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.contentView.backgroundColor = ColorWithHex(0xF65045);
    
    [self.contentView addSubview:self.bgView];
    self.bgView.backgroundColor = ColorWithHex(0xf4a483);
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(0);
        make.bottom.equalTo(0);
    }];
    
    [self.bgView addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(12.5);
        make.right.equalTo(-12.5);
        make.top.equalTo(12.5);
        make.bottom.equalTo(0);
    }];
    
    [self.bgView addSubview:self.rankImgV];
    [self.rankImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.containerView).offset(-9);
        make.width.equalTo(33);
        make.height.equalTo(32);
    }];
    self.rankImgV.hidden = YES;
    
    [self.containerView addSubview:self.rankLabel];
    [self.rankLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(0);
        make.height.equalTo(20);
    }];
    self.rankLabel.hidden = YES;
    
    [self.containerView addSubview:self.iconBgView];
    [self.iconBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(16);
        make.width.height.equalTo(40);
    }];
    
    [self.iconBgView addSubview:self.iconImgV];
    [self.iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(0);
        make.width.height.equalTo(37);
    }];
    
    UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionEqualSpacing spacing:5 arrangedSubviews:@[self.nameLabel, self.tagView]];
    [self.containerView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBgView.mas_right).offset(7.5);
        make.centerY.equalTo(self.iconBgView);
        make.right.equalTo(-115);
    }];
            
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(17);
    }];
    
    [self.containerView addSubview:self.followBtn];
    [self.followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(self.iconBgView);
        make.width.equalTo(87);
        make.height.equalTo(33);
    }];
    
    [self.containerView addSubview:self.stockNameLabel];
    [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBgView);
        make.top.equalTo(self.iconBgView.mas_bottom).offset(15.5);
    }];
    
    [self.containerView addSubview:self.stockCodeLabel];
    [self.stockCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBgView);
        make.top.equalTo(self.stockNameLabel.mas_bottom).offset(4);
    }];
    
    [self.containerView addSubview:self.stockRateLabel];
    [self.stockRateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.followBtn);
        make.bottom.equalTo(self.stockNameLabel).offset(2);
    }];
    
    [self.containerView addSubview:self.stockRateDescLabel];
    [self.stockRateDescLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.followBtn);
        make.bottom.equalTo(self.stockCodeLabel);
    }];
    
    [self.containerView addSubview:self.goodNewsLabel];
    [self.goodNewsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBgView);
        make.right.equalTo(self.followBtn);
        make.top.equalTo(self.stockCodeLabel.mas_bottom).offset(15);
    }];
    
    [self.containerView addSubview:self.commentView];
    [self.commentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.goodNewsLabel.mas_bottom).offset(15);
        make.left.right.equalTo(0);
        make.height.equalTo(28);
        make.bottom.equalTo(-15);
    }];
    self.commentView.backgroundColor = [UIColor lz_gradientColors:@[FMClearColor, ColorWithHexAlpha(0x0074fa, 0.12), FMClearColor] withFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH - 55, 28) direction:GradientDirectionLeftToRight];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (self.isLastCell) {
        [self.bgView layerAndBezierPathWithRect:self.bgView.bounds cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerBottomLeft|UIRectCornerBottomRight];
    } else {
        [self.bgView layerAndBezierPathWithRect:self.bgView.bounds cornerRadii:CGSizeMake(0, 0) byRoundingCorners:UIRectCornerBottomLeft|UIRectCornerBottomRight];
    }
    if (self.rank == 2) {
        self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xe5f3ff), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    } else if (self.rank == 3) {
        self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffeadb), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    } else {
        self.containerView.backgroundColor = FMWhiteColor;
    }
    [self.rankLabel layerAndBezierPathWithRect:self.rankLabel.bounds cornerRadii:CGSizeMake(5, 5) byRoundingCorners:UIRectCornerBottomRight];
    for (ZLTagLabel *tagLabel in self.tagView.subviews) {
        tagLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfeddb5), ColorWithHex(0xfdc48d)] withFrame:tagLabel.bounds direction:GradientDirectionLeftToRight];
    }
}

- (void)setRank:(NSInteger)rank {
    _rank = rank;
    if (rank == 2) {
        self.rankLabel.hidden = YES;
        self.rankImgV.hidden = NO;
        self.rankImgV.image = ImageWithName(@"daka_operation_second");
        self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xD2E1EE), ColorWithHex(0x8DAFCA)] withFrame:CGRectMake(0, 0, 40, 40) direction:GradientDirectionTopToBottom];
    } else if (rank == 3) {
        self.rankLabel.hidden = YES;
        self.rankImgV.hidden = NO;
        self.rankImgV.image = ImageWithName(@"daka_operation_third");
        self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffe7d4), ColorWithHex(0xffc196)] withFrame:CGRectMake(0, 0, 40, 40) direction:GradientDirectionTopToBottom];
    } else {
        self.rankImgV.hidden = YES;
        self.rankLabel.hidden = NO;
        self.rankLabel.text = [NSString stringWithFormat:@"%zd", rank];
        self.iconBgView.backgroundColor = ColorWithHex(0xF2EBEB);
    }
}

- (void)setIsLastCell:(BOOL)isLastCell {
    _isLastCell = isLastCell;
    
    if (isLastCell) {
        [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(-10);
        }];
    } else {
        [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(0);
        }];
    }
}

- (void)setModel:(FMBigCastOperationRankModel *)model {
    _model = model;
    
    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:model.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.nameLabel.text = model.bignameDto.userName;
    NSArray *goodAts = [model.bignameDto.userGoodAt componentsSeparatedByString:@"、"];
    self.tagView.titleArray = goodAts;
    self.tagView.hidden = !model.bignameDto.userGoodAt.length;
    self.stockNameLabel.text = model.stockName;
    self.stockCodeLabel.text = model.stockCode;
    self.stockRateLabel.text = model.increase.length ? [NSString stringWithFormat:@"+%@%%", model.increase] : @"";
    self.stockRateDescLabel.text = [NSString stringWithFormat:@"推送价%@—了结价%@", model.buyPrice.length ? model.buyPrice : @"", model.sellPrice.length ? model.sellPrice : @""];

    if (model.recommendedContent.length) {
        self.goodNewsLabel.hidden = NO;
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@" %@", model.recommendedContent]];
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        UIImage *xbImg = ImageWithName(@"daka_operation_xb");
        attachment.image = xbImg;
        attachment.bounds = CGRectMake(0, -4, xbImg.size.width, xbImg.size.height);
        NSAttributedString *subStr = [NSAttributedString attributedStringWithAttachment:attachment];
        [attrStr insertAttributedString:subStr atIndex:0];
        self.goodNewsLabel.attributedText = attrStr;
        [self.goodNewsLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.stockCodeLabel.mas_bottom).offset(15);
        }];
    } else {
        self.goodNewsLabel.hidden = YES;
        self.goodNewsLabel.attributedText = [[NSAttributedString alloc] initWithString:@""];
        [self.goodNewsLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.stockCodeLabel.mas_bottom).offset(0);
        }];
    }
    
    if (model.noteComments.count) {
        NSMutableArray *titles = [NSMutableArray array];
        [model.noteComments enumerateObjectsUsingBlock:^(FMCommentModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:obj.commentContent];
            attrStr.yy_font = FontWithSize(12);
            attrStr.yy_color = ColorWithHex(0x0074fa);
            [titles addObject:attrStr];
        }];
        self.commentScrollView.titleAttrStrs = titles;
        self.commentScrollView.signImages = [model.noteComments valueForKeyPath:@"commenterIco"];
        [self.commentView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(28);
            make.top.equalTo(self.goodNewsLabel.mas_bottom).offset(15);
        }];
        self.commentView.hidden = NO;
    } else {
        [self.commentView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(0);
            make.top.equalTo(self.goodNewsLabel.mas_bottom).offset(0);
        }];
        self.commentView.hidden = YES;
    }
}

#pragma mark - Private
- (void)followBtnClicked {
    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@&secondLevel=2", self.model.bignameDto.userId]];
}

- (void)jumpToStockDetail {
    UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:self.model.stockCode];
    if (matchInfo) {
        [UPRouterUtil goMarketStock:matchInfo.setCode code:matchInfo.code];
    }
}

#pragma mark - SGAdvertScrollViewDelegate
- (void)advertScrollView:(SGAdvertScrollView *)advertScrollView didSelectedItemAtIndex:(NSInteger)index {
    FMNoteDetailViewController *vc = [[FMNoteDetailViewController alloc] init];
    vc.noteId = self.model.noteId;
    vc.scrollToComment = YES;
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

#pragma mark - UIGestureRecognizerDelegate
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    if ([touch.view isDescendantOfView:self.commentView]) {
        return NO;
    }
    
    return YES;
}

#pragma mark - Getter/Setter
- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [UIView new];
    }
    
    return _bgView;
}

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [UIView new];
        UI_View_Radius(_containerView, 10);
        _containerView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(followBtnClicked)];
        tap.delegate = self;
        [_containerView addGestureRecognizer:tap];
    }
    
    return _containerView;
}

- (UIImageView *)rankImgV {
    if (!_rankImgV) {
        _rankImgV = [UIImageView new];
    }
    
    return _rankImgV;
}

- (ZLTagLabel *)rankLabel {
    if (!_rankLabel) {
        _rankLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x8b6666) backgroundColor:ColorWithHex(0xf2ebeb) numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _rankLabel.widthPadding = 13;
    }
    
    return _rankLabel;
}

- (UIView *)iconBgView {
    if (!_iconBgView) {
        _iconBgView = [UIView new];
        UI_View_Radius(_iconBgView, 20);
    }
    
    return _iconBgView;
}

- (UIImageView *)iconImgV {
    if (!_iconImgV) {
        _iconImgV = [UIImageView new];
        UI_View_Radius(_iconImgV, 18.5);
    }
    
    return _iconImgV;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _nameLabel;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] init];
        _tagView.tagLabelFont = FontWithSize(11);
        _tagView.tagLabelWidthPadding = 8.0f;
        _tagView.tagLabelHeightPadding = 4.0f;
        _tagView.middlePadding = 5;
        _tagView.tagLabelTextColor = ColorWithHex(0xa56112);
        _tagView.tagLabelCornerRadius = 2.0f;
        _tagView.numberofLines = 1;
        _tagView.tagLabelBgColor = ColorWithHex(0xfeddb5);
    }
    
    return _tagView;
}

- (UIButton *)followBtn {
    if (!_followBtn) {
        _followBtn = [[UIButton alloc] initWithFrame:CGRectZero font:BoldFontWithSize(14) normalTextColor:FMWhiteColor backgroundColor:[UIColor lz_gradientColors:@[ColorWithHex(0xfc5219), ColorWithHex(0xfc0002)] withFrame:CGRectMake(0, 0, 87, 33) direction:GradientDirectionLeftToRight] title:@"追随老师" image:nil target:self action:@selector(followBtnClicked)];
        UI_View_Radius(_followBtn, 5.0f);
    }
    
    return _followBtn;
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        _stockNameLabel.userInteractionEnabled = YES;
        WEAKSELF
        [_stockCodeLabel bk_whenTapped:^{
            [__weakSelf jumpToStockDetail];
        }];
    }
    
    return _stockNameLabel;
}

- (UILabel *)stockCodeLabel {
    if (!_stockCodeLabel) {
        _stockCodeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x666666) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        _stockCodeLabel.userInteractionEnabled = YES;
        WEAKSELF
        [_stockCodeLabel bk_whenTapped:^{
            [__weakSelf jumpToStockDetail];
        }];
    }
    
    return _stockCodeLabel;
}


- (UILabel *)stockRateLabel {
    if (!_stockRateLabel) {
        _stockRateLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(25) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];;
    }
    
    return _stockRateLabel;
}


- (UILabel *)stockRateDescLabel {
    if (!_stockRateDescLabel) {
        _stockRateDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];;
    }
    
    return _stockRateDescLabel;
}


- (UILabel *)goodNewsLabel {
    if (!_goodNewsLabel) {
        _goodNewsLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(13) textColor:ColorWithHex(0xb40001) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        _goodNewsLabel.userInteractionEnabled = YES;
        WEAKSELF
        [_goodNewsLabel bk_whenTapped:^{
            if (__weakSelf.model.noteId.length) {
                [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://note?id=%@", __weakSelf.model.noteId]];
            }
        }];
    }
    
    return _goodNewsLabel;
}

- (UIView *)commentView {
    if (!_commentView) {
        _commentView = [UIView new];
        
        _commentScrollView = [SGAdvertScrollView new];
        _commentScrollView.scrollTimeInterval = 6.0f;
        _commentScrollView.backgroundColor = FMClearColor;
        _commentScrollView.signImageSize = CGSizeMake(18, 18);
        _commentScrollView.signImageCornerRaidus = 9.0;
        _commentScrollView.signImagePlaceholder = ImageWithName(@"userCenter_dltx");
        _commentScrollView.imageTextAlignment = NSTextAlignmentCenter;
        _commentScrollView.imageTextPadding = 5.0f;
        _commentScrollView.delegate = self;
        [_commentView addSubview:_commentScrollView];
        [_commentScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsMake(0, 15, 0, 15));
        }];
    }
    
    return _commentView;
}

@end
