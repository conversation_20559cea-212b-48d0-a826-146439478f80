//
//  TagListView.h
//  TagListDemo
//
//  Created by 涂威 on 2017/7/28.
//  Copyright © 2017年 涂威. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface TagListView : UIView

/**
 item 间距
 */
@property (nonatomic, assign) CGFloat itemSpacing; // >=0

/**
 行间距
 */
@property (nonatomic, assign) CGFloat rowSpacing; // >=0

/**
 内容内边距
 */
@property (nonatomic, assign) UIEdgeInsets containerInsets;

/**
 item 的内间距
 */
@property (nonatomic, assign) CGFloat paddingX; // >=0
@property (nonatomic, assign) CGFloat paddingY; // >=0

/**
 item圆角
 */
@property (nonatomic, assign) CGFloat itemCornerRadius;

/**
 item边宽
 */
@property (nonatomic, assign) CGFloat itemBorderWidth;

/**
 item边框颜色
 */
@property (nonatomic, strong) UIColor *itemBorderColor;

/**
 item 背景色
 */
@property (nonatomic, strong) UIColor *itemBackgroundColor;

/**
 item 文字颜色
 */
@property (nonatomic, strong) UIColor *itemTextColor;

/**
 item 字体
 */
@property (nonatomic, strong) UIFont *itemFont;

/**
 item 行数（等于0时，自动换行）
 */
@property (nonatomic, assign) NSInteger numberOfRows; // >= 0, 默认为0

/**
 根据内容计算的高度
 */
@property (nonatomic, assign, readonly) CGFloat containerHeight;

/**
 标题数组，元素为字符串
 */
@property (nonatomic, strong) NSArray *tagList;

/**
 点击回调
 */
@property (nonatomic, copy) void (^clickBlock)(NSString *tagName);

@end
