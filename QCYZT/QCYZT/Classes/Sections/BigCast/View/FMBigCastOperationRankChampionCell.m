//
//  FMBigCastOperationRankChampionCell.m
//  QCYZT
//
//  Created by zeng on 2023/3/21.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMBigCastOperationRankChampionCell.h"
#import "SGAdvertScrollView.h"
#import "FMNoteDetailViewController.h"

@interface FMBigCastOperationRankChampionCell()<SGAdvertScrollViewDelegate, UIGestureRecognizerDelegate>

//
@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UIView *containerView;

@property (nonatomic, strong) UILabel *topLabel;
@property (nonatomic, strong) UIImageView *rankImgV;
// 投顾相关
@property (nonatomic, strong) UIView *iconBgView;
@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) ZLTagView *tagView;
// 股票相关
@property (nonatomic, strong) UILabel *stockNameLabel;
@property (nonatomic, strong) UILabel *stockCodeLabel;
@property (nonatomic, strong) UILabel *stockRateLabel;
@property (nonatomic, strong) UILabel *stockRateDescLabel;
// 喜报
@property (nonatomic, strong) UILabel *goodNewsLabel;
// 追随按钮
@property (nonatomic, strong) UIButton *followBtn;

// 评论View
@property (nonatomic, strong) UIView *commentView;
@property (nonatomic, strong) SGAdvertScrollView *commentScrollView;

@end

@implementation FMBigCastOperationRankChampionCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.contentView.backgroundColor = ColorWithHex(0xF65045);
    
    [self.contentView addSubview:self.bgView];
    self.bgView.backgroundColor = ColorWithHex(0xf4a483);
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(0);
        make.bottom.equalTo(0);
    }];
    
    [self.bgView addSubview:self.topLabel];
    [self.topLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(8);
        make.centerX.equalTo(0);
    }];
    
    [self.bgView addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(12.5);
        make.right.equalTo(-12.5);
        make.top.equalTo(self.topLabel.mas_bottom).offset(6);
        make.bottom.equalTo(0);
    }];
    
    [self.bgView addSubview:self.rankImgV];
    [self.rankImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView).offset(10);
        make.top.equalTo(self.containerView).offset(-5);
        make.width.equalTo(37);
        make.height.equalTo(50);
    }];
    
    [self.containerView addSubview:self.iconBgView];
    [self.iconBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.rankImgV.mas_right).offset(32);
        make.top.equalTo(16);
        make.width.height.equalTo(45);
    }];
    self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffebbe), ColorWithHex(0xffd681)] withFrame:CGRectMake(0, 0, 45, 45) direction:GradientDirectionTopToBottom];
    
    [self.iconBgView addSubview:self.iconImgV];
    [self.iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(0);
        make.width.height.equalTo(42);
    }];
    
    UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionEqualSpacing spacing:6 arrangedSubviews:@[self.nameLabel, self.tagView]];
    [self.containerView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBgView.mas_right).offset(7.5);
        make.centerY.equalTo(self.iconBgView);
        make.right.equalTo(-15);
    }];
            
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(17);
    }];
    
    [self.containerView addSubview:self.stockNameLabel];
    [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(self.iconBgView.mas_bottom).offset(17);
    }];
    
    [self.containerView addSubview:self.stockCodeLabel];
    [self.stockCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockNameLabel);
        make.top.equalTo(self.stockNameLabel.mas_bottom).offset(4);
    }];
    
    [self.containerView addSubview:self.stockRateLabel];
    [self.stockRateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.bottom.equalTo(self.stockNameLabel).offset(2);
    }];
    
    [self.containerView addSubview:self.stockRateDescLabel];
    [self.stockRateDescLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.stockRateLabel);
        make.bottom.equalTo(self.stockCodeLabel);
    }];
    
    [self.containerView addSubview:self.goodNewsLabel];
    [self.goodNewsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockNameLabel);
        make.right.equalTo(-15);
        make.top.equalTo(self.stockCodeLabel.mas_bottom).offset(16);
    }];
        
    [self.containerView addSubview:self.followBtn];
    [self.followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.goodNewsLabel.mas_bottom).offset(16);
        make.centerX.equalTo(0);
        make.width.equalTo(150);
        make.height.equalTo(40);
    }];
    
    [self.containerView addSubview:self.commentView];
    [self.commentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.followBtn.mas_bottom).offset(15);
        make.left.right.equalTo(0);
        make.height.equalTo(28);
        make.bottom.equalTo(-15);
    }];
    self.commentView.backgroundColor = [UIColor lz_gradientColors:@[FMClearColor, ColorWithHexAlpha(0x0074fa, 0.12), FMClearColor] withFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH - 55, 28) direction:GradientDirectionLeftToRight];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (self.onlyOneCell) {
        [self.bgView layerAndBezierPathWithRect:self.bgView.bounds cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerAllCorners];
    } else {
        [self.bgView layerAndBezierPathWithRect:self.bgView.bounds cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight];
    }
    self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffebc9), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    for (ZLTagLabel *tagLabel in self.tagView.subviews) {
        tagLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfeddb5), ColorWithHex(0xfdc48d)] withFrame:tagLabel.bounds direction:GradientDirectionLeftToRight];
    }
}

- (void)setOnlyOneCell:(BOOL)onlyOneCell {
    _onlyOneCell = onlyOneCell;
    
    if (onlyOneCell) {
        [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(-10);
        }];
    } else {
        [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(0);
        }];
    }
}

- (void)setModel:(FMBigCastOperationRankModel *)model {
    _model = model;
    
    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:model.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.nameLabel.text = model.bignameDto.userName;
    NSArray *goodAts = [model.bignameDto.userGoodAt componentsSeparatedByString:@"、"];
    self.tagView.titleArray = goodAts;
    self.tagView.hidden = !model.bignameDto.userGoodAt.length;
    self.stockNameLabel.text = model.stockName;
    self.stockCodeLabel.text = model.stockCode;
    self.stockRateLabel.text = model.increase.length ? [NSString stringWithFormat:@"+%@%%", model.increase] : @"";
    self.stockRateDescLabel.text = [NSString stringWithFormat:@"推送价%@—了结价%@", model.buyPrice.length ? model.buyPrice : @"", model.sellPrice.length ? model.sellPrice : @""];
    
    if (model.recommendedContent.length) {
        self.goodNewsLabel.hidden = NO;
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@" %@", model.recommendedContent]];
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        UIImage *xbImg = ImageWithName(@"daka_operation_xb");
        attachment.image = xbImg;
        attachment.bounds = CGRectMake(0, -4, xbImg.size.width, xbImg.size.height);
        NSAttributedString *subStr = [NSAttributedString attributedStringWithAttachment:attachment];
        [attrStr insertAttributedString:subStr atIndex:0];
        self.goodNewsLabel.attributedText = attrStr;
        [self.goodNewsLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.stockCodeLabel.mas_bottom).offset(16);
        }];
    } else {
        self.goodNewsLabel.hidden = YES;
        self.goodNewsLabel.attributedText = [[NSAttributedString alloc] initWithString:@""];
        [self.goodNewsLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.stockCodeLabel.mas_bottom).offset(0);
        }];
    }
    
    if (model.noteComments.count) {
        NSMutableArray *titles = [NSMutableArray array];
        [model.noteComments enumerateObjectsUsingBlock:^(FMCommentModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:obj.commentContent];
            attrStr.yy_font = FontWithSize(12);
            attrStr.yy_color = ColorWithHex(0x0074fa);
            [titles addObject:attrStr];
        }];
        self.commentScrollView.titleAttrStrs = titles;
        self.commentScrollView.signImages = [model.noteComments valueForKeyPath:@"commenterIco"];
        [self.commentView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(28);
            make.top.equalTo(self.followBtn.mas_bottom).offset(15);
        }];
        self.commentView.hidden = NO;
    } else {
        [self.commentView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(0);
            make.top.equalTo(self.followBtn.mas_bottom).offset(0);
        }];
        self.commentView.hidden = YES;
    }
}

#pragma mark - Private
- (void)followBtnClicked {
    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@&secondLevel=2", self.model.bignameDto.userId]];
}

- (void)jumpToStockDetail {
    UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:self.model.stockCode];
    if (matchInfo) {
        [UPRouterUtil goMarketStock:matchInfo.setCode code:matchInfo.code];
    }
}

#pragma  mark - SGAdvertScrollViewDelegate
- (void)advertScrollView:(SGAdvertScrollView *)advertScrollView didSelectedItemAtIndex:(NSInteger)index {
    FMNoteDetailViewController *vc = [[FMNoteDetailViewController alloc] init];
    vc.noteId = self.model.noteId;
    vc.scrollToComment = YES;
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

#pragma mark - UIGestureRecognizerDelegate
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    if ([touch.view isDescendantOfView:self.commentView]) {
        return NO;
    }
    
    return YES;
}

#pragma mark - Getter/Setter
- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [UIView new];
    }
    
    return _bgView;
}

- (UILabel *)topLabel {
    if (!_topLabel) {
        _topLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(11) textColor:ColorWithHex(0x924518) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _topLabel.text = @"注：涨跌幅数值考虑中间操作的影响";
    }
    
    return _topLabel;
}

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [UIView new];
        UI_View_Radius(_containerView, 10);
        _containerView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(followBtnClicked)];
        tap.delegate = self;
        [_containerView addGestureRecognizer:tap];
    }
    
    return _containerView;
}

- (UIImageView *)rankImgV {
    if (!_rankImgV) {
        _rankImgV = [UIImageView new];
        _rankImgV.image = ImageWithName(@"daka_operation_champion");
    }
    
    return _rankImgV;
}

- (UIView *)iconBgView {
    if (!_iconBgView) {
        _iconBgView = [UIView new];
        UI_View_Radius(_iconBgView, 20);
    }
    
    return _iconBgView;
}

- (UIImageView *)iconImgV {
    if (!_iconImgV) {
        _iconImgV = [UIImageView new];
        UI_View_Radius(_iconImgV, 21);
    }
    
    return _iconImgV;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(17) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _nameLabel;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] init];
        _tagView.tagLabelFont = FontWithSize(11);
        _tagView.tagLabelWidthPadding = 8.0f;
        _tagView.tagLabelHeightPadding = 4.0f;
        _tagView.middlePadding = 5;
        _tagView.tagLabelTextColor = ColorWithHex(0xa56112);
        _tagView.tagLabelCornerRadius = 2.0f;
        _tagView.numberofLines = 1;
        _tagView.tagLabelBgColor = ColorWithHex(0xfeddb5);
    }
    
    return _tagView;
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        _stockNameLabel.userInteractionEnabled = YES;
        WEAKSELF
        [_stockNameLabel bk_whenTapped:^{
            [__weakSelf jumpToStockDetail];
        }];
    }
    
    return _stockNameLabel;
}

- (UILabel *)stockCodeLabel {
    if (!_stockCodeLabel) {
        _stockCodeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x666666) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        _stockCodeLabel.userInteractionEnabled = YES;
        WEAKSELF
        [_stockCodeLabel bk_whenTapped:^{
            [__weakSelf jumpToStockDetail];
        }];
    }
    
    return _stockCodeLabel;
}


- (UILabel *)stockRateLabel {
    if (!_stockRateLabel) {
        _stockRateLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(25) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];;
    }
    
    return _stockRateLabel;
}


- (UILabel *)stockRateDescLabel {
    if (!_stockRateDescLabel) {
        _stockRateDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];;
    }
    
    return _stockRateDescLabel;
}


- (UILabel *)goodNewsLabel {
    if (!_goodNewsLabel) {
        _goodNewsLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(13) textColor:ColorWithHex(0xb40001) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        _goodNewsLabel.userInteractionEnabled = YES;
        WEAKSELF
        [_goodNewsLabel bk_whenTapped:^{
            if (__weakSelf.model.noteId.length) {
                [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://note?id=%@", __weakSelf.model.noteId]];
            }
        }];
    }
    
    return _goodNewsLabel;
}

- (UIButton *)followBtn {
    if (!_followBtn) {
        _followBtn = [[UIButton alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) normalTextColor:FMWhiteColor backgroundColor:[UIColor lz_gradientColors:@[ColorWithHex(0xfc5219), ColorWithHex(0xfc0002)] withFrame:CGRectMake(0, 0, 150, 40) direction:GradientDirectionLeftToRight] title:@"追随老师" image:nil target:self action:@selector(followBtnClicked)];
        _followBtn.layer.cornerRadius = 5;
        _followBtn.layer.shadowColor = ColorWithHexAlpha(0xfc0002, 0.5).CGColor;
        _followBtn.layer.shadowOffset = CGSizeMake(0,5);
        _followBtn.layer.shadowOpacity = 1;
        _followBtn.layer.shadowRadius = 10;
    }
    
    return _followBtn;
}

- (UIView *)commentView {
    if (!_commentView) {
        _commentView = [UIView new];
        
        _commentScrollView = [SGAdvertScrollView new];
        _commentScrollView.scrollTimeInterval = 6.0f;
        _commentScrollView.backgroundColor = FMClearColor;
        _commentScrollView.signImageSize = CGSizeMake(18, 18);
        _commentScrollView.signImageCornerRaidus = 9.0;
        _commentScrollView.signImagePlaceholder = ImageWithName(@"userCenter_dltx");
        _commentScrollView.imageTextAlignment = NSTextAlignmentCenter;
        _commentScrollView.imageTextPadding = 5.0f;
        _commentScrollView.delegate = self;
        [_commentView addSubview:_commentScrollView];
        [_commentScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsMake(0, 15, 0, 15));
        }];
    }
    
    return _commentView;
}

@end
