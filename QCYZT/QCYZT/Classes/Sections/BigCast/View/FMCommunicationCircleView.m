//
//  FMCommunicationCircleView.m
//  QCYZT
//
//  Created by <PERSON> on 2018/8/7.
//  Copyright © 2018年 LZKJ. All rights reserved.
//

#import "FMCommunicationCircleView.h"
#import "SDCycleScrollView.h"
#import "UIImage+circleImage.h"
#import "FMDakaCommunicationLatePopView.h"

@interface FMCommunicationCircleView ()<SDCycleScrollViewDelegate>

@property (nonatomic, strong) NSDictionary *dataDic;

@property (nonatomic, strong) SDCycleScrollView *circleScrollView;

@property (nonatomic, strong) UIPageControl *pageControl;

@end

@implementation FMCommunicationCircleView


- (id)initWithData:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        [self setUpUI:dic];
    }
    return self;
}


- (void)setUpUI:(NSDictionary *)dataDic{
    self.dataDic = dataDic;
    self.backgroundColor = [UIColor whiteColor];
    
    [self addSubview:self.circleScrollView];
    [self.circleScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(UI_Relative_WidthValue(10.0)));
        make.left.equalTo(self.mas_left).offset(15.0);
        make.right.equalTo(self.mas_right).offset(-15);
        make.height.equalTo(@(UI_Relative_WidthValue(105)));
    }];
    NSMutableArray *coverImageArr = [[NSMutableArray array] init];
    NSArray *listArr = dataDic[@"list"];
    for (NSDictionary *dic in listArr) {
        [coverImageArr addObject:dic[@"coverImage"]];
    }
    self.circleScrollView.imageURLStringsGroup = [NSArray arrayWithArray:coverImageArr];
    UI_View_Radius(self.circleScrollView, 5);
    
    [self addSubview:self.pageControl];
    [self.pageControl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.circleScrollView.mas_bottom).offset(UI_Relative_WidthValue(-5));
        make.centerX.equalTo(self.mas_centerX);
        make.height.equalTo(@(UI_Relative_WidthValue(5)));
    }];
    self.pageControl.numberOfPages = self.circleScrollView.imageURLStringsGroup.count;
}


#pragma mark - cycleScrolldelegate

- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index{
    NSDictionary *dic = self.dataDic[@"list"][index];
    if (dic) {
        NSString *isOutOfSale = [NSString stringWithFormat:@"%@", dic[@"isOutOfSale"]];
        if ([isOutOfSale integerValue]) {
            FMDakaCommunicationLatePopView *popView = [[FMDakaCommunicationLatePopView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT)];
            [popView show];
        } else {
            [ProtocolJump jumpWithUrl:self.dataDic[@"list"][index][@"action"]];
        }
    }
}

- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didScrollToIndex:(NSInteger)index{
    self.pageControl.currentPage = index;
}

#pragma mark - lazy

- (SDCycleScrollView *)circleScrollView{
    if (!_circleScrollView) {
        _circleScrollView = [SDCycleScrollView cycleScrollViewWithFrame:CGRectZero delegate:self placeholderImage:[UIImage imageNamed:@"sphc_placeholder"]];
        _circleScrollView.backgroundColor = [UIColor clearColor];
        _circleScrollView.hidesForSinglePage = YES;
        _circleScrollView.showPageControl = NO;
        _circleScrollView.autoScrollTimeInterval = 5.0;
        _circleScrollView.bannerImageViewContentMode = UIViewContentModeScaleAspectFill;
    }
    return _circleScrollView;
}

- (UIPageControl *)pageControl{
    if (!_pageControl) {
        _pageControl = [[UIPageControl alloc] init];
        _pageControl.hidesForSinglePage = YES;
        _pageControl.pageIndicatorTintColor = FMGreyColor;
        _pageControl.currentPageIndicatorTintColor = ColorWithHex(0xedb0b2);
    }
    return _pageControl;
}


@end
