//
//  FMPersonDataBaseInfoCell.m
//  QCYZT
//
//  Created by shumi on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPersonDataBaseInfoCell.h"
#import "BigCastDetailModel.h"

@interface FMPersonDataBaseInfoCell ()

@property (nonatomic, strong) UILabel *goodAt;

@end

@implementation FMPersonDataBaseInfoCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    self.backgroundColor = FMClearColor;
    
    UILabel *nickTitle = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x555555) backgroundColor:FMClearColor numberOfLines:1];
    nickTitle.text = @"昵称";
    [self.contentView addSubview:nickTitle];
    [nickTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(16));
        make.left.equalTo(@(15));
        make.height.equalTo(@(23));
    }];
    
    UILabel *nickLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [self.contentView addSubview:nickLB];
    [nickLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(nickTitle);
        make.right.equalTo(@(-15));
    }];
    self.nickNameLB = nickLB;
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = ColorWithHex(0xececec);
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(nickTitle.mas_bottom).offset(16);
        make.centerX.equalTo(@(0));
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH - 30, 1)));
    }];
    
    UILabel *profile = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x555555) backgroundColor:FMClearColor numberOfLines:1];
    profile.text = @"简介";
    [self.contentView addSubview:profile];
    [profile mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nickTitle);
        make.top.equalTo(line.mas_bottom).offset(16);
    }];
    
    UILabel *profileLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentRight];
    [self.contentView addSubview:profileLB];
    [profileLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(profile);
        make.left.equalTo(profile.mas_right).offset(23);
        make.right.equalTo(@(-15));
        make.bottom.equalTo(@(-24 - 36));
    }];
    self.profileLB = profileLB;
    
    UIView *line1 = [[UIView alloc] init];
    line1.backgroundColor = ColorWithHex(0xececec);
    [self.contentView addSubview:line1];
    [line1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(profileLB.mas_bottom).offset(16);
        make.centerX.equalTo(@(0));
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH - 30, 1)));
    }];
 
    UILabel *goodAt = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x555555) backgroundColor:FMClearColor numberOfLines:1];
    goodAt.text = @"风格";
    [self.contentView addSubview:goodAt];
    [goodAt mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(profile);
        make.top.equalTo(line1.mas_bottom).offset(16);
    }];
    self.goodAt = goodAt;
//
    UILabel *goodAtLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x33333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [self.contentView addSubview:goodAtLB];
    [goodAtLB mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(line1.mas_bottom).offset(16);
        make.right.equalTo(@(-15));
    }];
    self.goodAtLB = goodAtLB;
    
    UIView *bottomView = [[UIView alloc] init];
    bottomView.backgroundColor = ColorWithHex(0xF7F7F7);
    [self.contentView addSubview:bottomView];
    [bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.left.right.equalTo(@(0));
        make.height.equalTo(@(8));
    }];
    
}

- (void)setModel:(BigCastDetailModel *)model {
    _model = model;
    self.nickNameLB.text = self.model.userName;
    self.profileLB.text = self.model.userProfiles.length > 0 ? self.model.userProfiles : @"这个人很懒,什么都没有留下";
    self.goodAt.hidden = model.userGoodAt.length == 0;
    self.goodAtLB.hidden = model.userGoodAt.length == 0;
    if (model.userGoodAt.length > 0) {
        self.goodAtLB.text = model.userGoodAt;
        [self.profileLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(@(-24 - 36 - 16));
        }];
    } else {
        [self.profileLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(@(-24));
        }];
    }
}

@end
