//
//  FMPersonDataServiceTabCell.m
//  QCYZT
//
//  Created by shumi on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPersonDataServiceTabCell.h"
#import "BigCastDetailModel.h"

@interface FMPersonDataServiceTabCell ()
@property (nonatomic, strong) UILabel *title;
@property (nonatomic, strong) UILabel *content;
@property (nonatomic, strong) UIView *line;
@end

@implementation FMPersonDataServiceTabCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    UILabel *title = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor numberOfLines:1];
    [self.contentView addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(13));
        make.left.equalTo(@(15));
        make.height.equalTo(@(24));
    }];
    self.title = title;
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = ColorWithHex(0xececec);
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(title.mas_bottom).offset(13);
        make.centerX.equalTo(@(0));
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH, 1)));
    }];
    self.line = line;
    
    UILabel *contentLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor numberOfLines:0];
    [self.contentView addSubview:contentLB];
    [contentLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(line.mas_bottom).offset(15);
        make.centerX.equalTo(@(0));
        make.width.equalTo(@(UI_SCREEN_WIDTH - 30));
        make.bottom.equalTo(@(-15));
    }];
    self.content = contentLB;
    
}

- (void)setModel:(BigCastDetailModel *)model {
    _model = model;
    if (model.infoIntroduction.length > 0) {
        self.title.hidden = NO;
        self.line.hidden = NO;
        self.content.hidden = NO;
        
        self.title.text = @"服务介绍";
        self.content.text = model.infoIntroduction;
    } else {
        self.title.hidden = YES;
        self.line.hidden = YES;
        self.content.hidden = YES;
    }
}

@end
