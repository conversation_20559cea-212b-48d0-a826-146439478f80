//
//  FMBigCastHomepageBottomView.m
//  QCYZT
//
//  Created by zeng on 2025/7/25.
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMBigCastHomepageBottomView.h"
#import "BigCastDetailModel.h"
#import "ProtocolJump.h"
#import "FMHelper.h"

@interface FMBigCastHomepageBottomView()

@property (nonatomic, strong) UIStackView *stackView;

// 私信按钮
@property (nonatomic, strong) UIButton *msgBtn;
// 问股按钮
@property (nonatomic, strong) UIButton *askBtn;

@property (nonatomic, strong) UIImageView *imgV;

@end

@implementation FMBigCastHomepageBottomView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.backgroundColor = UIColor.up_contentBgColor;

    // 创建私信按钮
    UIButton *msgBtn = [[UIButton alloc] init];
    [msgBtn setTitle:@"私信老师" forState:UIControlStateNormal];
    [msgBtn setTitleColor:UIColor.up_riseColor forState:UIControlStateNormal];
    msgBtn.titleLabel.font = FontWithSize(16);
    UI_View_BorderRadius(msgBtn, 20, 1, UIColor.up_riseColor);
    msgBtn.backgroundColor = UIColor.clearColor;
    [msgBtn addTarget:self action:@selector(msgBtnClick) forControlEvents:UIControlEventTouchUpInside];
    self.msgBtn = msgBtn;

    // 创建问股按钮
    UIButton *askBtn = [[UIButton alloc] init];
    [askBtn setTitle:@"向老师问股" forState:UIControlStateNormal];
    [askBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    askBtn.titleLabel.font = FontWithSize(16);
    UI_View_BorderRadius(askBtn, 20, 1, UIColor.up_riseColor);
    askBtn.backgroundColor = UIColor.up_riseColor;
    [askBtn addTarget:self action:@selector(askBtnClick) forControlEvents:UIControlEventTouchUpInside];
    self.askBtn = askBtn;

    // 创建StackView
    UIStackView *stackView = [[UIStackView alloc] initWithArrangedSubviews:@[msgBtn, askBtn]];
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.distribution = UIStackViewDistributionFillEqually;
    stackView.spacing = 15;
    [self addSubview:stackView];
    self.stackView = stackView;

    // 设置StackView约束
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(-(UI_SAFEAREA_BOTTOM_HEIGHT + 10));
        make.centerY.equalTo(0);
        make.height.equalTo(40);
    }];
    
    UIImageView *imgV = [[UIImageView alloc] initWithImage:ImageWithName(@"bigcast_homepage_privateLetter")];
    [self addSubview:imgV];
    [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(msgBtn);
        make.top.equalTo(msgBtn.mas_top).offset(-9);
    }];
    self.imgV = imgV;
}

#pragma mark - Actions
- (void)msgBtnClick {
    // 直接跳转到私信页面
    if (self.model.userid.length > 0) {
        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://privateLetter?id=%@", self.model.userid]];
    }
}

- (void)askBtnClick {
    if ([self.delegate respondsToSelector:@selector(bigCastHomepageBottomViewAskBtnDidClicked:btn:)]) {
        [self.delegate bigCastHomepageBottomViewAskBtnDidClicked:self btn:self.askBtn];
    }
}

#pragma mark - Setter
- (void)setModel:(BigCastDetailModel *)model {
    _model = model;
    [self updateUI];
}

- (void)updateUI {
    if (!self.model) return;

    // 私信按钮显示逻辑 - 按照用户提供的逻辑
    BOOL msgBtnHidden = (!(self.model.letterPrice.integerValue > 0) && (self.model.attestationType.integerValue == 2)) || [FMHelper getIAPPayStatus];
    self.msgBtn.hidden = msgBtnHidden;
    self.imgV.hidden = msgBtnHidden;

    // 问股按钮显示逻辑 - 根据原来的逻辑，当有answerPrice且是投顾时显示
    BOOL shouldShowAskBtn = [self.model.answerPrice integerValue] > 0 && self.model.attestationType.integerValue == 2;
    self.askBtn.hidden = !shouldShowAskBtn;

    // 更新问股按钮标题
    if (shouldShowAskBtn) {
        NSString *askTitle = [NSString stringWithFormat:@"向老师问股(%@金币)", self.model.answerPrice];
        [self.askBtn setTitle:askTitle forState:UIControlStateNormal];
    }

    // 更新私信按钮标题
    if (!msgBtnHidden && self.model.letterPrice.integerValue > 0) {
        NSString *msgTitle = [NSString stringWithFormat:@"私信老师(%zd金币)", self.model.letterPrice.integerValue];
        [self.msgBtn setTitle:msgTitle forState:UIControlStateNormal];
    }
}

@end
