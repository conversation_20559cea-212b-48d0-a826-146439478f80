//
//  FMDakaHomePageBannerView.m
//  QCYZT
//
//  Created by zeng on 2022/11/18.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMDakaHomePageBannerView.h"
#import "SDCycleScrollView.h"
#import "UIImage+circleImage.h"
#import "CustomPageControl.h"

@interface FMDakaHomePageBannerView ()<SDCycleScrollViewDelegate>

@property (nonatomic, strong) SDCycleScrollView *circleScrollView;
@property (nonatomic, strong) CustomPageControl *pageControl;

@end

@implementation FMDakaHomePageBannerView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.backgroundColor = UIColor.up_contentBgColor;
    
    [self addSubview:self.circleScrollView];
    [self.circleScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(@0);
        make.left.right.mas_equalTo(self);
    }];
    UI_View_Radius(self.circleScrollView, 5);
    
    [self.circleScrollView addSubview:self.pageControl];
    [self.pageControl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(@(UI_Relative_WidthValue(-5)));
        make.left.right.equalTo(@0);
        make.height.equalTo(@(UI_Relative_WidthValue(3)));
    }];
}

- (void)setDetailModel:(BigCastDetailModel *)detailModel {
    _detailModel = detailModel;
    self.pageControl.hidden = (self.detailModel.banners.count < 2);
    self.circleScrollView.imageURLStringsGroup = [self.detailModel.banners valueForKeyPath:@"banner"];
    self.pageControl.numberOfPags = self.circleScrollView.imageURLStringsGroup.count;
}


#pragma mark - cycleScrolldelegate

- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index{
    BigCastDetailBannerModel *model = self.detailModel.banners[index];
    if ([model isKindOfClass:[BigCastDetailBannerModel class]]) {
        [ProtocolJump jumpWithUrl:model.action];
    }
}

- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didScrollToIndex:(NSInteger)index{
    self.pageControl.currentPags = index;
}

#pragma mark - lazy

- (SDCycleScrollView *)circleScrollView{
    if (!_circleScrollView) {
        _circleScrollView = [SDCycleScrollView cycleScrollViewWithFrame:CGRectZero delegate:self placeholderImage:[UIImage imageNamed:@"home_banner_placeholder"]];
        _circleScrollView.backgroundColor = [UIColor clearColor];
        _circleScrollView.hidesForSinglePage = YES;
        _circleScrollView.showPageControl = NO;
        _circleScrollView.autoScrollTimeInterval = 5.0;
        _circleScrollView.bannerImageViewContentMode = UIViewContentModeScaleAspectFill;
    }
    return _circleScrollView;
}

- (CustomPageControl *)pageControl{
    if (!_pageControl) {
        _pageControl = [[CustomPageControl alloc] init];
        _pageControl.currentPags = 0;
        _pageControl.backPageColor = ColorWithHexAlpha(0xffffff, 0.5);
        _pageControl.selectedColor = ColorWithHex(0xffffff);
    }
    return _pageControl;
}


@end
