//
//  AllBigCastListModel.h
//  QCYZT
//
//  Created by shumi on 2022/6/29.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "BigCastDetailModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface AllBigCastListModel : NSObject
@property (nonatomic,copy) NSString *name;
@property (nonatomic, strong) NSArray<BigCastDetailModel *> *list;
@property (nonatomic,assign) BOOL isSelected;
@property (nonatomic,assign) BOOL isLast;

@end

NS_ASSUME_NONNULL_END
