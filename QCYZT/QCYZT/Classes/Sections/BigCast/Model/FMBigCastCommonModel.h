//
//  FMBigCastCommonModel.h
//  QCYZT
//  投顾基本信息通用模型
//  Created by shum<PERSON> on 2022/12/1.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DakaBannersModel : NSObject
@property (nonatomic,copy) NSString *action;
@property (nonatomic,copy) NSString *banner;
@end

@interface FMBigCastCommonModel : NSObject
/// 投顾id
@property (nonatomic,copy) NSString *userId;
/// 投顾姓名
@property (nonatomic,copy) NSString *userName;
/// 投顾简介
@property (nonatomic,copy) NSString *userProfiles;
/// 投顾头像
@property (nonatomic,copy) NSString *userIco;
/// 投顾创作数
@property (nonatomic,assign) NSInteger userWriteNums;
/// 投顾粉丝数
@property (nonatomic,assign) NSInteger userNoticerNums;
/// 投顾头衔
@property (nonatomic,copy) NSString *userTitle;
/// 投顾擅长
@property (nonatomic,copy) NSString *userGoodAt;
/// 作者认证类型 1专栏  2投顾  3机构
@property (nonatomic,assign) NSInteger attestationType;
/// 投顾详情跳转链接
@property (nonatomic,copy) NSString *userDetailAction;
/// 作者直播列表 
@property (nonatomic, strong) NSArray *isLive;
/// 证书编号
@property (nonatomic,copy) NSString *certCode;
/// 是否允许开通Vip
@property (nonatomic, assign) BOOL allowOpenVip;
/// 投顾介绍
@property (nonatomic,copy) NSString *bignameDetail;
/// 问股数
@property (nonatomic, copy) NSString *userAnswerNums;
/// 笔记数
@property (nonatomic,copy) NSString *userNoteNums;


#pragma mark - 扩展辅助字段
/// 相关内容的时间
@property (nonatomic,assign) long contentTime;
/// 是否为视频笔记或者直播笔记
@property (nonatomic,assign) BOOL isVideoNote;
/// 问股提问价格
@property (nonatomic,copy) NSString *answerPrice;

@property (nonatomic, strong) NSArray <DakaBannersModel *> *banners;

/// 点击头像的协议跳转
@property (nonatomic,copy) NSString *headerViewAction;
/// 点击作者信息视图的协议跳转(不含图片区域)
@property (nonatomic,copy) NSString *infoViewAction;

@end

NS_ASSUME_NONNULL_END
