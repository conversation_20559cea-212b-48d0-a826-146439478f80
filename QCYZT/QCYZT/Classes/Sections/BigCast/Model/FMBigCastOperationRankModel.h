//
//  FMBigCastOperationRankModel.h
//  QCYZT
//
//  Created by zeng on 2023/3/22.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FMBigCastCommonModel.h"
#import "FMCommentModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface FMBigCastOperationRankModel : NSObject

@property (nonatomic , copy) NSString              * buyPrice;
@property (nonatomic , copy) NSString              * increase;
@property (nonatomic , copy) NSString              * noteId;
@property (nonatomic , copy) NSString              * recommendedContent;
@property (nonatomic , copy) NSString              * sellPrice;
@property (nonatomic , copy) NSString              * stockCode;
@property (nonatomic , copy) NSString              * stockName;

@property (nonatomic, strong) FMBigCastCommonModel *bignameDto;

@property (nonatomic, strong) NSArray<FMCommentModel *> *noteComments;

@end

NS_ASSUME_NONNULL_END
