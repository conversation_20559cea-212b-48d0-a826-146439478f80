//
//  FMPrivateLetterCommentView.m
//  QCYZT
//
//  Created by zeng on 2025/5/18.
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMPrivateLetterCommentView.h"
#import "IQKeyboardManager.h"
#import "NSString+characterJudge.h"
#import "NSString+emoji.h"

static const NSInteger kWordLimit = 500;

@interface FMPrivateLetterCommentView() <UITextViewDelegate>

@property (nonatomic, strong) UIButton *publishBtn;

@end

@implementation FMPrivateLetterCommentView

- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.backgroundColor = UIColor.up_contentBgColor;
    
    UIButton *publishBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(16) normalTextColor:FMWhiteColor backgroundColor:FMClearColor title:@"发送" image:nil target:self action:@selector(publish)];
    [self addSubview:publishBtn];
    [publishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(@(-7.5));
        make.right.equalTo(@(-15));
        make.width.equalTo(@60);
        make.height.equalTo(40);
    }];
    UI_View_Radius(publishBtn, 20);
    [publishBtn setBackgroundImage:[UIImage imageWithColor:ColorWithHex(0xababab)] forState:UIControlStateDisabled];
    [publishBtn setBackgroundImage:[UIImage imageWithColor:UIColor.up_riseColor] forState:UIControlStateNormal];
    publishBtn.enabled = NO;
    self.publishBtn = publishBtn;

    FMTextView *textView = [[FMTextView alloc] init];
    [self addSubview:textView];
    [textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(7.5);
        make.left.equalTo(15);
        make.right.equalTo(publishBtn.mas_left).offset(-10);
        make.bottom.equalTo(-7.5);
        make.height.equalTo(40);
    }];
    textView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    UI_View_Radius(textView, 5);
    textView.textContainerInset = UIEdgeInsetsMake(10, 3, 10, 3);
    textView.delegate = self;
    textView.font = FontWithSize(15);
    textView.textColor = UIColor.fm_market_nav_text_zeroColor;
    textView.placehoder = @"您还可以发送2次私信";
    self.textView = textView;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(uptheme_themeDidUpdate) name:kUPThemeDidChangeNotification object:nil];
}


- (void)publish {
    if ([FMHelper checkLoginStatus]) {
        if ([self.textView.text isBlankString]) {
            [SVProgressHUD showInfoWithStatus:@"输入内容不能为空"];
            return;
        }
        WEAKSELF;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (__weakSelf.publishBlock) {
                __weakSelf.publishBlock(__weakSelf.textView.text, ^{
                    __weakSelf.textView.text = @"";
                    [__weakSelf textViewDidChange:__weakSelf.textView];
                });
            }
        });
    }
}

#pragma mark - TextView Delegate
- (void)textViewDidChange:(UITextView *)textView {
    if (textView.text.length <= 0) {
        self.publishBtn.enabled = NO;
    } else {
        self.publishBtn.enabled = YES;
        if (textView.text.length > kWordLimit) {
            textView.text = [textView.text substringToIndex:kWordLimit];
            [SVProgressHUD showInfoWithStatus:[NSString stringWithFormat:@"最多输入%zd个字", kWordLimit]];
        }
    }
    
    if ([textView.text isContainEmoji]) {
        [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
        return;
    }
    
    CGSize calculteSize = CGSizeMake(textView.width, CGFLOAT_MAX);
    CGSize size = [textView sizeThatFits:calculteSize];
    
    // 计算单行文本高度
    CGFloat lineHeight = textView.font.lineHeight;
    // 计算文本内边距的影响
    CGFloat verticalInset = textView.textContainerInset.top + textView.textContainerInset.bottom;
    // 计算最大可显示的完整行数（默认显示5行）
    NSInteger maxLines = 5;
    // 计算基于行数的最大高度
    CGFloat maxHeight = lineHeight * maxLines + verticalInset;
    
    CGFloat tmpHeight = MIN(maxHeight, MAX(40, size.height));
    [self.textView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(tmpHeight);
    }];

    if (self.heightDidChangeBlock) {
        self.heightDidChangeBlock();
    }

    if (size.height <= maxHeight) {
        textView.scrollEnabled = NO;
    } else {
        textView.scrollEnabled = YES;
        // 当文本高度超过最大高度时，保证滚动到可见位置
        [textView scrollRangeToVisible:NSMakeRange(textView.text.length, 0)];
    }
}

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if ([text isContainEmoji]) {
        [textView resignFirstResponder];
        [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
        return NO;
    }
    return YES;;
}


@end
