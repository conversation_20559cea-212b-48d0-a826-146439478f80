//
//  FMPrivateLetterImageCell.m
//  QCYZT
//
//  Created by zeng on 2022/6/30.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterImageCell.h"
#import "HZPhotoBrowser.h"
#import "FMPrivateLetterImageFrameModel.h"

@interface FMPrivateLetterImageCell() <HZPhotoBrowserDelegate>

// 图片
@property (nonatomic, strong) UIImageView *imageV;

@end

@implementation FMPrivateLetterImageCell

- (void)setUp {
    [super setUp];
    
    UIImageView *imageV = [[UIImageView alloc] init];
    imageV.contentMode = UIViewContentModeScaleAspectFill;
    [self.containerView addSubview:imageV];
    UI_View_Radius(imageV, 5.0f);
    self.imageV = imageV;
    imageV.userInteractionEnabled = YES;
    [imageV addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(imageClicked)]];
}

- (void)imageClicked {
    if (self.imageV.image) {
        HZPhotoBrowser *browserVc = [[HZPhotoBrowser alloc] init];
        browserVc.sourceImagesContainerView = self.containerView; // 原图的父控件
        browserVc.imageCount = 1; // 图片总数
        browserVc.currentImageIndex = 0;
        browserVc.delegate = self;
        [browserVc show];
    }
}

#pragma mark - photobrowser代理方法
- (UIImage *)photoBrowser:(HZPhotoBrowser *)browser placeholderImageForIndex:(NSInteger)index {
    UIImage *img = (UIImage *)[self.imageV image];
    return img;
}

- (NSURL *)photoBrowser:(HZPhotoBrowser *)browser highQualityImageURLForIndex:(NSInteger)index {
    return [NSURL URLWithString:self.frameModel.model.picUrl];
}

- (void)setFrameModel:(FMPrivateLetterImageFrameModel *)frameModel {
    [super setFrameModel:frameModel];
    
    FMPrivateLetterModel *model = frameModel.model;
    
    [self.imageV sd_setImageWithURL:[NSURL URLWithString:model.picUrl] placeholderImage:ImageWithName(@"sphc_placeholder")];
    self.imageV.frame = frameModel.imageVF;
}

@end
