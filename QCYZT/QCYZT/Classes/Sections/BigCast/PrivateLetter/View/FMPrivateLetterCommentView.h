//
//  FMPrivateLetterCommentView.h
//  QCYZT
//
//  Created by zeng on 2025/5/18.
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FMTextView.h"

NS_ASSUME_NONNULL_BEGIN

typedef void (^PublishBlock)(NSString *content, void(^success)(void));

@interface FMPrivateLetterCommentView : UIView

//@property (nonatomic, copy) NSString *placeholderText;
@property (nonatomic, strong) FMTextView *textView;

/// 发布按钮点击回调
@property (nonatomic, copy) PublishBlock publishBlock;
@property (nonatomic, copy) void(^heightDidChangeBlock)(void);

@end

NS_ASSUME_NONNULL_END
