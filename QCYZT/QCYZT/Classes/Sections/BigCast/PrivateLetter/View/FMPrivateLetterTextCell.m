//
//  FMPrivateLetterTextCell.m
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterTextCell.h"
#import "FMPrivateLetterTextFrameModel.h"

@interface FMPrivateLetterTextCell()

// 文本
@property (nonatomic, strong) UITextView *textView;


@end

@implementation FMPrivateLetterTextCell

- (void)setUp {
    [super setUp];
        
    UITextView *textView = [[UITextView alloc] init];
    textView.scrollEnabled = NO;
    textView.backgroundColor = FMClearColor;
    textView.userInteractionEnabled = NO;
    // 设置上下间距
    textView.textContainerInset = UIEdgeInsetsZero; // 必须设置，否则显示不准，原来为（8, 0, 8, 0)
    // 设置左右间距
    textView.textContainer.lineFragmentPadding = 0;
    
    [self.containerView addSubview:textView];
    self.textView = textView;
}

- (void)setFrameModel:(FMPrivateLetterTextFrameModel *)frameModel {
    [super setFrameModel:frameModel];
    
    self.textView.attributedText = frameModel.attributedContent;
    self.textView.frame = frameModel.textF;
}

@end
