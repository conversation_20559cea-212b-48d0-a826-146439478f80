//
//  FMBigcastPrivateLetterOrderListCell.m
//  QCYZT
//
//  Created by zeng on 2022/7/18.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMBigcastPrivateLetterOrderListCell.h"
#import "ZLTagLabel.h"

@interface FMBigcastPrivateLetterOrderListCell()

@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UIImageView *authenticationImgV;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) ZLTagLabel *redNumLabel;

@end

@implementation FMBigcastPrivateLetterOrderListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    UIImageView *iconImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:iconImgV];
    [iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.left.equalTo(@15);
        make.width.height.equalTo(@50);
    }];
    UI_View_Radius(iconImgV, 25);
    self.iconImgV = iconImgV;
    
    UIImageView *authenticationImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:authenticationImgV];
    [authenticationImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(iconImgV.mas_right).offset(2);
        make.bottom.equalTo(iconImgV.mas_bottom).offset(2);
        make.width.height.equalTo(@17);
    }];
    authenticationImgV.image = ImageWithName(@"MemberCenter_AttestationTypeConsultant");
    self.authenticationImgV = authenticationImgV;
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(17) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1];
    [self.contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@14);
        make.left.equalTo(iconImgV.mas_right).offset(10);
        make.height.equalTo(@24);
        make.width.priorityLow();
    }];
    self.titleLabel = titleLabel;
    
    UILabel *subTitleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1];
    [self.contentView addSubview:subTitleLabel];
    [subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel);
        make.top.equalTo(titleLabel.mas_bottom).offset(3);
        make.height.equalTo(@20);
        make.width.priorityLow();
    }];
    self.subTitleLabel = subTitleLabel;
    
    UILabel *timeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [self.contentView addSubview:timeLabel];
    [timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(titleLabel);
        make.left.greaterThanOrEqualTo(titleLabel.mas_right).offset(20);
    }];
    self.timeLabel = timeLabel;

    ZLTagLabel *tagLabel = [[ZLTagLabel alloc] init];
    [self.contentView addSubview:tagLabel];
    [tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(subTitleLabel);
        make.height.equalTo(@17);
        make.width.greaterThanOrEqualTo(@17);
    }];
    tagLabel.font = [UIFont systemFontOfSize:12];
    tagLabel.widthPadding = 8;
    tagLabel.layer.cornerRadius = 8.5f;
    tagLabel.clipsToBounds = YES;
    tagLabel.backgroundColor = UIColor.up_riseColor;
    tagLabel.textColor = UIColor.up_contentBgColor;
    tagLabel.textAlignment = NSTextAlignmentCenter;
    self.redNumLabel = tagLabel;
    
    [subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.lessThanOrEqualTo(tagLabel.mas_left).offset(-20);
    }];
    
    [self addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.bottom.equalTo(@0);
        make.height.equalTo(@0.7);
    }];
}

- (void)setModel:(FMBigcastPrivateLetterOrderListModel *)model {
    _model = model;

    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:model.speekerIco] placeholderImage:ImageWithName(@"spch")];

    self.authenticationImgV.hidden = !model.isVip;
    
    if (model.speekerName.length) {
        self.titleLabel.text = model.speekerName;
    } else {
        self.titleLabel.text = @"";
    }
    
    if (model.content.length) {
        self.subTitleLabel.text = model.content;
    } else {
        self.subTitleLabel.text = @"暂无消息";
    }
    
    if (model.createTime) {
        NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.createTime/1000];
        NSString *timeStr = nil;
        if ([date isToday]) {
            timeStr = [NSString stringFromDate:date format:@"HH:mm"];
        } else {
            if ([date isThisYear]) {
                timeStr = [NSString stringFromDate:date format:@"MM-dd HH:mm"];
            } else {
                timeStr = [NSString stringFromDate:date format:@"yyyy-MM-dd"];
            }
        }
        self.timeLabel.text = timeStr;
    } else {
        self.timeLabel.text = @"";
    }
    
    if (model.unreadNum > 0) {
        self.redNumLabel.hidden = NO;
        if (model.unreadNum > 99) {
            self.redNumLabel.text = [NSString stringWithFormat:@"99+"];
        } else {
            self.redNumLabel.text = [NSString stringWithFormat:@"%zd", model.unreadNum];
        }
    } else {
        self.redNumLabel.hidden = YES;
    }
}

@end
