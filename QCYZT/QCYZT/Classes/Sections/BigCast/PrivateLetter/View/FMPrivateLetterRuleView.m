//
//  FMPrivateLetterRuleView.m
//  QCYZT
//
//  Created by zeng on 2022/6/29.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterRuleView.h"

@interface FMPrivateLetterRuleView()

@property (nonatomic, strong) UIImageView *bgImgV;


@end

@implementation FMPrivateLetterRuleView

- (void)show {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    [keyWindow addSubview:self];
}

- (void)dismiss {
    [self removeFromSuperview];
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT)]) {
        [self setUp];
    }
    
    return self;
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesBegan:touches withEvent:event];
    
    [self dismiss];
}

- (void)setUp {
    self.backgroundColor = FMClearColor;
    
    UIView *blackView = [[UIView alloc] init];
    [self addSubview:blackView];
    [blackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-11);
        make.top.equalTo(@(UI_STATUS_HEIGHT + 44));
        make.width.equalTo(@255);
    }];
    UI_View_Radius(blackView, 5.0);
    blackView.backgroundColor = ColorWithHex(0x434343);
    
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [blackView addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@15);
    }];
    label.text = @"私信规则";
    
    UILabel *label2 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [blackView addSubview:label2];
    [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(label.mas_bottom).offset(7.5);
        make.bottom.equalTo(@-15);
    }];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 4.0f;
    style.paragraphSpacing = 6.0f;
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:@"1、支付成功后，私信权利24小时有效\n2、若24小时后，老师未回复消息，金币将原路退回账户"];
    [attrStr addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, attrStr.length)];
    label2.attributedText = attrStr;
}

- (void)drawRect:(CGRect)rect {
    [super drawRect:rect];
    
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    CGContextMoveToPoint(ctx, UI_SCREEN_WIDTH - 31, UI_STATUS_HEIGHT + 44);
    CGContextAddLineToPoint(ctx, UI_SCREEN_WIDTH - 25, UI_STATUS_HEIGHT + 36);
    CGContextAddLineToPoint(ctx, UI_SCREEN_WIDTH - 19, UI_STATUS_HEIGHT + 44);
    CGContextClosePath(ctx);
    
    CGContextSetFillColorWithColor(ctx, ColorWithHex(0x43434).CGColor);
    CGContextFillPath(ctx);
}

@end
