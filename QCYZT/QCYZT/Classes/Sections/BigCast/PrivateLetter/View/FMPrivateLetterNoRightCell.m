//
//  FMPrivateLetterNoRightCell.m
//  QCYZT
//
//  Created by zeng on 2022/6/29.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterNoRightCell.h"
#import "FMPayTool.h"
#import "HttpRequestTool+Pay.h"
#import "FMMemberCenterProductViewController.h"
#import "PaymentView.h"

@interface FMPrivateLetterNoRightCell ()

@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UIButton *coinBtn;
//@property (nonatomic, strong) UIButton *memberBtn;

@end

@implementation FMPrivateLetterNoRightCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp  {
    self.contentView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    
    [self.contentView addSubview:self.iconImgV];
    [self.iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@50);
    }];
    
    [self.contentView addSubview:self.coinBtn];
    [self.coinBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(self.iconImgV.mas_bottom).offset(25);
        make.width.equalTo(@(UI_SCREEN_WIDTH - 75));
        make.height.equalTo(@45);
        make.bottom.equalTo(@-30);
    }];
    
//    [self.contentView addSubview:self.memberBtn];
//    [self.memberBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerX.equalTo(@0);
//        make.top.equalTo(self.coinBtn.mas_bottom).offset(15);
//        make.width.equalTo(@(UI_SCREEN_WIDTH - 75));
//        make.height.equalTo(@45);
//        make.bottom.equalTo(@-30);
//    }];
}

- (void)coinBtnClicked {
    if (self.model.enablePayModel.count == 0) {
        // 没有可用的支付方式
        [[FMPayTool payTool] noEnablePayModelWithErrorCode:self.model.noPayModelCode errorText:self.model.noPayModelText];
        return;
    }
    
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.model.bignameId certCode:self.model.cert_code clickView:self.coinBtn confirmOperation:^{
        EnablePayModel *payModel = [NSArray modelArrayWithClass:[EnablePayModel class] json:self.model.enablePayModel].firstObject;
        payModel.bignameId = self.model.bignameId;
        payModel.consumeType = 3;
        payModel.contentId = self.model.currentOrderId.integerValue;
        [PaymentView showWithEnablePayModel:payModel payPrice:[NSString stringWithFormat:@"%.0f", self.model.price] productName:[NSString stringWithFormat:@"与%@的私聊", self.model.bignameName] bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
            [self requestPay:selectedModel];
        } dismissBlock:^{
        }];
    }];
}

- (void)requestPay:(EnablePayModel *)payModel {
    [HttpRequestTool requestPrivateLetterPayWithBigcastId:self.model.bignameId type:[NSString stringWithFormat:@"%zd", payModel.type]
                                                  goodsId:payModel.couponId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD showSuccessWithStatus:@"支付成功"];
            NSLog(@"私信支付成功");
            
            if (self.paySuccessBlock) {
                self.paySuccessBlock([NSString stringWithFormat:@"%@", dic[@"data"][@"id"]], 2);
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)memberBtnClicked {
    FMMemberCenterProductViewController *vc = [[FMMemberCenterProductViewController alloc] init];
    vc.bigcastId = self.model.bignameId;
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

- (void)setModel:(FMPrivateLetterBigcastHomeModel *)model {
    _model = model;
    
    [self.coinBtn setTitle:[NSString stringWithFormat:@"%.0f金币 付费私聊", model.price] forState:UIControlStateNormal];
//    self.memberBtn.hidden = !model.allowOpenVip;
}

- (UIImageView *)iconImgV {
    if (!_iconImgV) {
        _iconImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"PrivateLetter_NoRight")];
    }
    
    return _iconImgV;
}

- (UIButton *)coinBtn {
    if (!_coinBtn) {
        _coinBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(16) normalTextColor:FMNavColor backgroundColor:UIColor.up_contentBgColor title:@"付费私聊" image:nil target:self action:@selector(coinBtnClicked)];
        UI_View_BorderRadius(_coinBtn, 22.5, 1.0, FMNavColor);
    }
    
    return _coinBtn;
}

//- (UIButton *)memberBtn {
//    if (!_memberBtn) {
//        _memberBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(16) normalTextColor:FMWhiteColor backgroundColor:FMNavColor title:@"开通会员 免费私聊" image:nil target:self action:@selector(memberBtnClicked)];
//        UI_View_Radius(_memberBtn, 22.5);
//    }
//
//    return _memberBtn;
//}

@end
