//
//  FMPrivateLetterRateTabCell.m
//  QCYZT
//
//  Created by shumi on 2024/1/24.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMPrivateLetterRateTabCell.h"
#import "FMRatingView.h"
#import "HttpRequestTool+UserCenter.h"

@interface FMPrivateLetterRateTabCell ()
@property (nonatomic, strong) FMRatingView *rate;

@end

@implementation FMPrivateLetterRateTabCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    self.contentView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    
    FMRatingView *rate = [[FMRatingView alloc] initWithFrame:CGRectMake(15, 0, UI_SCREEN_WIDTH - 30, 155)];
    WEAKSELF
    rate.submitFeedBack = ^(NSString * _Nonnull content) {
        [__weakSelf subminLetterSubmit:content];
    };
    rate.selectedRate = ^{
        [__weakSelf subminLetterSubmit:@""];
    };
    [self.contentView addSubview:rate];
    self.rate = rate;
}

- (void)setStarLevel:(NSInteger)starLevel {
    _starLevel = starLevel;
    self.rate.rate = self.starLevel;
}

// 问股意见反馈
- (void)subminLetterSubmit:(NSString *)content {
    if(content.length == 0) {
        [HttpRequestTool cancelHttpRequestWithUrlString:kAPI_Letter_FeedBack];
    }
    [HttpRequestTool letterFeedBackWithQuestionId:self.letterId StarLevel:self.rate.rate feedback:content start:^{
        if (content.length > 0) {
            [SVProgressHUD show];
        }
    } failure:^{
        if (content.length > 0) {
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        }
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if (content.length > 0) {
                [SVProgressHUD showSuccessWithStatus:@"提交成功!"];
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self.rate.feedbackView dismiss];
                });
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

@end
