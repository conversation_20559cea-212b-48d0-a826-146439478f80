//
//  FMPrivateLetterViewController.m
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterViewController.h"
#import "FMPrivateLetterTextFrameModel.h"
#import "FMPrivateLetterTextCell.h"
#import "HttpRequestTool+UserCenter.h"
#import "FMDetailBottomView.h"
#import "FMPrivateLetterNoRightCell.h"
#import "FMPrivateLetterRuleView.h"
#import "WebSocketManager.h"
#import "FMPrivateLetterImageCell.h"
#import "FMPrivateLetterImageFrameModel.h"
#import "FMPrivateLetterSendMsgModel.h"
#import "FMMsgRequestAllDataStaus.h"
#import "FMPrivateLetterRateTabCell.h"
#import "FMPrivateLetterCommentView.h"
#import "IQKeyboardManager.h"

@interface FMPrivateLetterViewController ()<UITableViewDelegate, UITableViewDataSource, FMDetailBottomViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *frameModels;
@property (nonatomic, assign) NSUInteger pageSize;

@property (nonatomic, strong) FMDetailBottomView *bottomView;
@property (nonatomic, strong) FMPrivateLetterCommentView *commentView;
@property (nonatomic, strong) FMPrivateLetterRuleView *ruleView;
@property (nonatomic,assign) BOOL isShowRateView;
@property (nonatomic, strong) FMPrivateLetterModel *rateLetterModel;
@property (nonatomic, strong) MASConstraint *commentViewBottomConstraint;

@end

@implementation FMPrivateLetterViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColor.up_contentBgColor;
    self.title = self.model.bignameName;
        
    [self.view addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@0);
        make.height.equalTo(@(DetailBottomViewHeight));
    }];
    self.bottomView.hidden = YES;
    
    [self.view addSubview:self.commentView];
    [self.commentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        self.commentViewBottomConstraint = make.bottom.equalTo(-UI_SAFEAREA_BOTTOM_HEIGHT);
    }];
    self.commentView.hidden = YES;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@0);
        make.bottom.equalTo(self.commentView.mas_top);
    }];
    self.tableView.hidden = YES;

    self.pageSize = 20;
    [self requestData];
    
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithImage:FMImgInBundle(@"导航/亮黑暗灰问号") style:UIBarButtonItemStylePlain target:self action:@selector(showRuleView)];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(rechargeSuccess) name:kRechargeSuccess object:nil];
    [self registerKeyboardNotifications];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
        
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
    
    [IQKeyboardManager sharedManager].enable = NO;
    [UPSecurityKeyboardAdjustManager sharedManager].enable = NO;
    
    [self judgeDisplayStatusWithClear:NO];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self.commentView.textView resignFirstResponder];
            
    [self refreshReadStatus];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    
    [IQKeyboardManager sharedManager].enable = YES;
    [UPSecurityKeyboardAdjustManager sharedManager].enable = YES;
}

- (void)dealloc {
    // 断开websocket
    FMLog(@"WebSocketManager 断开链接");
    [[WebSocketManager shared] closeWebSocket];
    [self unregisterKeyboardNotifications];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - UITableView Delegate/DataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (!self.model.currentOrderId.length && self.model.price > 0) { // 会话已被关闭且投顾允许私信
        return self.isShowRateView ?  self.frameModels.count + 2  : self.frameModels.count + 1;
    }
    return self.frameModels.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.isShowRateView) {
        if (indexPath.row == self.frameModels.count + 1) {
            FMPrivateLetterNoRightCell *cell = [tableView reuseCellClass:[FMPrivateLetterNoRightCell class]];
            cell.model = self.model;
            WEAKSELF
            cell.paySuccessBlock = ^(NSString * currentOrderId, NSInteger currentSentence) {
                __weakSelf.model.currentOrderId = currentOrderId;
                __weakSelf.model.currentSentence = currentSentence;
                [__weakSelf judgeDisplayStatusWithClear:YES];
            };
            return cell;
        }
        if (indexPath.row == self.frameModels.count) {
            FMPrivateLetterRateTabCell *cell = [tableView reuseCellClass:[FMPrivateLetterRateTabCell class]];
            cell.letterId = self.rateLetterModel.msgId.integerValue;
            cell.starLevel = self.rateLetterModel.starLevel;
            return cell;
        }
    } else {
        if (indexPath.row == self.frameModels.count) {
            FMPrivateLetterNoRightCell *cell = [tableView reuseCellClass:[FMPrivateLetterNoRightCell class]];
            cell.model = self.model;
            WEAKSELF
            cell.paySuccessBlock = ^(NSString * currentOrderId, NSInteger currentSentence) {
                __weakSelf.model.currentOrderId = currentOrderId;
                __weakSelf.model.currentSentence = currentSentence;
                [__weakSelf judgeDisplayStatusWithClear:YES];
            };
            return cell;
        }
    }
    
    FMPrivateLetterFrameModel *frameModel = self.frameModels[indexPath.row];
    FMPrivateLetterModel *model = frameModel.model;
    
    FMPrivateLetterBaseCell *cell = nil;
    switch (model.messageType) {
        case PrivateLetterBodyType_Text:
            cell = [tableView reuseCellClass:[FMPrivateLetterTextCell class]];
            break;
            
        case PrivateLetterBodyType_Image:
            cell = [tableView reuseCellClass:[FMPrivateLetterImageCell class]];
            break;
            
        default:
            cell = [tableView reuseCellClass:[FMPrivateLetterTextCell class]];
            break;
    }
    cell.frameModel = self.frameModels[indexPath.row];
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.isShowRateView) {
        if (indexPath.row == self.frameModels.count + 1) {
            return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMPrivateLetterNoRightCell class]) configuration:nil];
        }
        if (indexPath.row == self.frameModels.count) {
            return 155;
        }
    } else {
        if (indexPath.row == self.frameModels.count) {
            return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMPrivateLetterNoRightCell class]) configuration:nil];
        }
    }
  
    FMPrivateLetterFrameModel *frameModel = self.frameModels[indexPath.row];
    return frameModel.cellHeight;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - websocket接收消息
- (void)webSocketManagerDidReceiveMessageWithString:(NSString *)string {
    NSDictionary *stringDic = [JsonTool dicOrArrFromJsonString:string];
    NSDictionary *contentDic = [JsonTool dicOrArrFromJsonString:stringDic[@"content"]];
    NSString *msgType = stringDic[@"msgType"];
    if ([msgType isEqualToString:@"BIGNAME_MESSAGE"]) {
        FMPrivateLetterModel *model = [FMPrivateLetterModel modelWithDictionary:contentDic];
        for (FMPrivateLetterFrameModel *frameModel in self.frameModels) {
            if ([frameModel.model.msgId isEqualToString:model.msgId]) {
                return;
            }
        }
        if (!self.model.currentOrderId.length) {
            // 支付私信后先收到自动回复的消息，此时currentOrderId为空
            // 延迟0.5s在支付成功的block里面会给currentOrderId赋值，此时自动滚动到最后一条数据也不会有问题
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                NSLog(@"支付私信后收到默认信息");
                model.speekerType = 2;
                model.bubbleType = PrivateLetterBubbleType_OtherSend;
                [self addOneFrameModelWithModel:model];
            });
            return;
        }
        model.speekerType = 2;
        model.bubbleType = PrivateLetterBubbleType_OtherSend;
        [self addOneFrameModelWithModel:model];
    } else if ([msgType isEqualToString:@"ORDER_END"]) {
        if ([self.model.currentOrderId isEqualToString:[NSString stringWithFormat:@"%@", contentDic[@"orderId"]]]) {
            self.model.currentSentence = 0;
            self.model.vipSentence = 0;
            self.model.currentOrderId = @"";
            
            for (NSInteger i = self.frameModels.count - 1; i >= 0; i --) {
                FMPrivateLetterFrameModel *frameModel = self.frameModels[i];
                if (frameModel.model.speekerType == 2) {
                    frameModel.model.status = 2;
                    break;
                }
            }
            
            [self judgeDisplayStatusWithClear:YES];
            
            [self tableViewScrollToBottomWithAnimated:YES];
        }
    }
}

#pragma mark - NSNotification
- (void)rechargeSuccess {
    [HttpRequestTool requestPrivateLetterHomeWithBigcastId:self.model.bignameId start:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            FMPrivateLetterBigcastHomeModel *model = [FMPrivateLetterBigcastHomeModel modelWithDictionary:dic[@"data"]];
            self.model = model;
        }
    }];
}

#pragma mark - RequestData
- (void)requestData {
    FMPrivateLetterFrameModel *firstFrameModel = self.frameModels.firstObject;
    [HttpRequestTool requestPrivateLetterListWithBigcastId:self.model.bignameId pageSize:self.pageSize lastMessageId:firstFrameModel.model.msgId start:^{
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        [self.tableView.mj_header endRefreshing];
    } success:^(NSDictionary *dic) {
        [self.tableView.mj_header endRefreshing];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            // 建立webSocket链接
            if (![WebSocketManager shared].isConnect) {
                FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                [WebSocketManager shared].socketUrl = [NSString stringWithFormat:@"%@/api/v2/ws/letter?userId=%@&bignameId=%@",kDakaInit,userModel.userId,self.model.bignameId];
                [[WebSocketManager shared] connectServer];
                [WebSocketManager shared].delegate = self;
            }
            
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMPrivateLetterModel class] json:dic[@"data"]];
            [self dealFramesWithArr:dataArr];
            
            [self judgeDisplayStatusWithClear:YES];

            if (!firstFrameModel) { // 第一次加载
                [self tableViewScrollToBottomWithAnimated:NO];
                
                [self refreshReadStatus];
            } else { // 下拉
                self.tableView.hidden = NO;
                if (dataArr.count) { // 下拉有数据，偏移一点点
                    CGRect cellRect = [self.tableView rectForRowAtIndexPath:[NSIndexPath indexPathForRow:dataArr.count - 1 inSection:0]];
                    [self.tableView setContentOffset:CGPointMake(0, cellRect.origin.y + cellRect.size.height - MJRefreshHeaderHeight)];
                }
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)refreshReadStatus {
    if (self.model.bignameId && self.frameModels.count) {
        FMPrivateLetterFrameModel *frameModel = self.frameModels.lastObject;
        [HttpRequestTool requestPrivateLetterRefreshReadWithBigcastId:self.model.bignameId messageId:frameModel.model.msgId start:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [FMMsgRequestAllDataStaus requestMsgUnreadDataWithBlock:^{
                }];
            }
        }];
    }
}

#pragma mark - Private
- (void)judgeDisplayStatusWithClear:(BOOL)clear {
    if (self.model.currentOrderId.length) { // 订单id存在，会话未关闭
        self.bottomView.hidden = YES;
        self.commentView.hidden = NO;
        
        if (clear) {
            self.commentView.textView.text = @"";
        }
        if (self.model.currentSentence == 0) {
            self.commentView.textView.placehoder = @"您此次私聊机会已用完";
            self.commentView.userInteractionEnabled = NO;
        } else {
            self.commentView.textView.placehoder = [NSString stringWithFormat:@"您此次私聊可发送%zd句", self.model.currentSentence];
            self.commentView.userInteractionEnabled = YES;
        }
    } else {// 订单id不存在，显示付费私聊
        self.bottomView.hidden = NO;
        self.commentView.hidden = YES;
        
        [self.bottomView.commentBtn setTitle:@"付费私聊" forState:UIControlStateNormal];
        self.bottomView.commentBtn.userInteractionEnabled = NO;
        [self.tableView reloadData];
    }
    
    FMPrivateLetterFrameModel *firstModel = self.frameModels.lastObject;
    if (firstModel.model.status == 2) {
        for (NSInteger i = self.frameModels.count - 1; i >= 0; i --) {
            FMPrivateLetterFrameModel *frameModel = self.frameModels[i];
            if (frameModel.model.speekerType == 2 && frameModel.model.status == 2) {
                self.rateLetterModel = frameModel.model;
                /// 是否显示评分cell
                self.isShowRateView = [CountDownShareInstance shareInstance].serverTime  - frameModel.model.createTime <= 3 * 24 * 60 * 60 * 1000;
                break;
            }
        }
    }
    
    [self.tableView reloadData];
}

- (void)showRuleView {
    if (self.ruleView.superview) {
        [self.ruleView dismiss];
    } else {
        [self.ruleView show];
    }
}

- (void)dealFramesWithArr:(NSArray *)arr {
    NSMutableArray *tmpFrameArr = [NSMutableArray array];
    for (NSInteger i = arr.count - 1; i >= 0; i--) {
        FMPrivateLetterModel *model = arr[i];
        if (model.speekerType == 1) {
            model.bubbleType = PrivateLetterBubbleType_MeSend;
        } else if (model.speekerType == 2) {
            model.bubbleType = PrivateLetterBubbleType_OtherSend;
        }
        
        FMPrivateLetterFrameModel *frameModel = [self createFrameModelWithMessageType:model.messageType];
        if (i == arr.count - 1) {
            frameModel.hiddenTime = NO;
        } else {
            FMPrivateLetterModel *preModel = arr[i+1];
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.createTime/1000];
            NSDate *preDate = [NSDate dateWithTimeIntervalSince1970:preModel.createTime/1000];
            frameModel.hiddenTime = [self isHiddenTimeWithDate:date preDate:preDate]; // 5分钟内不展示
        }
        
        if (i == 0) {
            frameModel.isLast = YES;
        } else {
            frameModel.isLast = NO;
        }
        
        frameModel.model = model;
        [tmpFrameArr addObject:frameModel];
    }
    
    // 如果是下拉刷新，修改第一条的时间显示
    if (self.frameModels.count != 0) {
        FMPrivateLetterFrameModel *firstFrameModel = self.frameModels.firstObject;
        FMPrivateLetterModel *firstModel = firstFrameModel.model;
        FMPrivateLetterFrameModel *preFrameModel = tmpFrameArr.lastObject;
        FMPrivateLetterModel *preModel = preFrameModel.model;
        preFrameModel.isLast = NO;
        NSDate *date = [NSDate dateWithTimeIntervalSince1970:firstModel.createTime/1000];
        NSDate *preDate = [NSDate dateWithTimeIntervalSince1970:preModel.createTime/1000];
        firstFrameModel.hiddenTime = [self isHiddenTimeWithDate:date preDate:preDate];
        firstFrameModel.model = firstFrameModel.model;
    }
    
    
    [self insertObjects:tmpFrameArr toArr:self.frameModels atIndex:0];
}

- (void)insertObjects:(NSArray *)objects toArr:(NSMutableArray *)arr atIndex:(NSUInteger)index {
    NSUInteger i = index;
    for (id obj in objects) {
        [arr insertObject:obj atIndex:i++];
    }
}

- (BOOL)isTimeExpiredWithDate:(NSDate *)date preDate:(NSDate *)preDate {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    int unit = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute;
    NSDateComponents *cmps = [calendar components:unit fromDate:preDate toDate:date options:0];
    
    return cmps.minute >= 1 || cmps.hour > 0 || cmps.day > 0 || cmps.month > 0 || cmps.year > 0;
}

- (FMPrivateLetterFrameModel *)createFrameModelWithMessageType:(PrivateLetterBodyType)messageType {
    FMPrivateLetterFrameModel *frameModel = nil;
    switch (messageType) {
        case PrivateLetterBodyType_Text:
            frameModel = [[FMPrivateLetterTextFrameModel alloc] init];
            break;
        case PrivateLetterBodyType_Image:
            frameModel = [[FMPrivateLetterImageFrameModel alloc] init];
            break;
            
        default:
            frameModel = [[FMPrivateLetterTextFrameModel alloc] init];
            break;
    }
    frameModel.bigcastHomeModel = self.model;
    return frameModel;
}

// 增加一条数据
- (void)addOneFrameModelWithModel:(FMPrivateLetterModel *)messageModel {
    // 1.增加frame模型
    FMPrivateLetterFrameModel *preFrameModel = self.frameModels.lastObject;
    preFrameModel.isLast = NO;
    FMPrivateLetterModel *preModel = preFrameModel.model;
    preFrameModel.model = preModel;
    NSDate *preDate = [NSDate dateWithTimeIntervalSince1970:preModel.createTime/1000];
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:messageModel.createTime/1000];
    
    FMPrivateLetterFrameModel *frameModel = [self createFrameModelWithMessageType:messageModel.messageType];
    frameModel.isLast = YES;
    frameModel.hiddenTime = [self isHiddenTimeWithDate:date preDate:preDate];
    frameModel.model = messageModel;
    [self.frameModels addObject:frameModel];
    
    // 2.刷新tableView，并滚动到最下面
    [self.tableView reloadData];
    [self tableViewScrollToBottomWithAnimated:YES];
}

// 是否隐藏时间，距离上一条消息在5分钟之内不显示
- (BOOL)isHiddenTimeWithDate:(NSDate *)date preDate:(NSDate *)preDate {
    if (!preDate) {
        return NO;
    }
    NSCalendar *calendar = [NSCalendar currentCalendar];
    int unit = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute;
    NSDateComponents *cmps = [calendar components:unit fromDate:preDate toDate:date options:0];
    return (cmps.minute < 5 && cmps.hour == 0 && cmps.day == 0 && cmps.month == 0 && cmps.year == 0);
}

// 滚动到最后一条
- (void)tableViewScrollToBottomWithAnimated:(BOOL)animated {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.tableView.hidden = NO;
        if (!self.model.currentOrderId.length && self.model.price > 0) {
            [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:self.isShowRateView ?  self.frameModels.count + 1  : self.frameModels.count inSection:0] atScrollPosition:UITableViewScrollPositionBottom animated:animated];
        } else {
            if (self.frameModels.count) {
                [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:self.frameModels.count - 1 inSection:0] atScrollPosition:UITableViewScrollPositionBottom animated:animated];
            }
        }
    });
}

#pragma mark - Keyboard Notifications
- (void)registerKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

- (void)unregisterKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    NSDictionary *userInfo = [notification userInfo];
    CGRect keyboardFrame = [[userInfo objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    NSTimeInterval animationDuration = [[userInfo objectForKey:UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve animationCurve = [[userInfo objectForKey:UIKeyboardAnimationCurveUserInfoKey] intValue];

    CGFloat keyboardHeight = keyboardFrame.size.height;

    [self.view layoutIfNeeded];
    [UIView animateWithDuration:animationDuration
                          delay:0.0
                        options:(animationCurve << 16)
                     animations:^{
        self.commentViewBottomConstraint.offset = -keyboardHeight;
        [self.view layoutIfNeeded];
        [self tableViewScrollToBottomWithAnimated:YES]; // 键盘弹出时，滚动到底部，不需要动画
    } completion:nil];
}

- (void)keyboardWillHide:(NSNotification *)notification {
    NSDictionary *userInfo = [notification userInfo];
    NSTimeInterval animationDuration = [[userInfo objectForKey:UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve animationCurve = [[userInfo objectForKey:UIKeyboardAnimationCurveUserInfoKey] intValue];

    // Set the target offset for the commentView
    self.commentViewBottomConstraint.offset = -UI_SAFEAREA_BOTTOM_HEIGHT;

    if (self.isMovingFromParentViewController || self.isBeingDismissed) {
        // If the view is disappearing (e.g., during a pop gesture or dismissal),
        // apply the layout change immediately without animation to avoid conflicts
        // with the view controller's transition animation.
        [self.view layoutIfNeeded];
    } else {
        // For a normal keyboard hide (not during a VC transition),
        // animate the layout change.
        [self.view layoutIfNeeded]; // Ensure current layout state is captured before animation block for consistency
        [UIView animateWithDuration:animationDuration
                              delay:0.0
                            options:(animationCurve << 16 | UIViewAnimationOptionLayoutSubviews)
                         animations:^{
            [self.view layoutIfNeeded];
        } completion:nil];
    }
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(requestData)];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMPrivateLetterTextCell class]];
        [_tableView registerCellClass:[FMPrivateLetterImageCell class]];
        [_tableView registerCellClass:[FMPrivateLetterNoRightCell class]];
        [_tableView registerCellClass:[FMPrivateLetterRateTabCell class]];
        _tableView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        _tableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    }
    
    return _tableView;
}

- (FMDetailBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[FMDetailBottomView alloc] init];
        _bottomView.backgroundColor = UIColor.up_contentBgColor;
        [_bottomView.commentBtn setTitle:@"付费私聊" forState:UIControlStateNormal];
    }
    return _bottomView;
}

- (FMPrivateLetterCommentView *)commentView {
    if (!_commentView) {
        _commentView = [[FMPrivateLetterCommentView alloc] init];
        
        WEAKSELF
        _commentView.publishBlock = ^(NSString * _Nonnull content, void (^ _Nonnull success)(void)) {
            [HttpRequestTool requestPrivateLetterSendWithBigcastId:__weakSelf.model.bignameId orderId:__weakSelf.model.currentOrderId content:content start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD dismiss];
                    success();
                    
                    FMPrivateLetterSendMsgModel *sendModel = [FMPrivateLetterSendMsgModel modelWithDictionary:dic[@"data"]];
                    __weakSelf.model.currentOrderId = sendModel.orderId;
                    __weakSelf.model.currentSentence = sendModel.currentSentence;
                    __weakSelf.model.vipSentence = sendModel.vipSentence;
                    [__weakSelf judgeDisplayStatusWithClear:YES];
                    
                    FMPrivateLetterModel *model = [[FMPrivateLetterModel alloc] init];
                    model.speekerType = 1;
                    model.bubbleType = PrivateLetterBubbleType_MeSend;
                    model.messageType = PrivateLetterBodyType_Text;
                    model.content = content;
                    model.speekerIco = dic[@"data"][@"speekerIco"];
                    model.createTime = [dic[@"data"][@"createTime"] longLongValue];

                    [__weakSelf addOneFrameModelWithModel:model];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        };
        _commentView.heightDidChangeBlock = ^{
            [__weakSelf.view layoutIfNeeded];
            [__weakSelf tableViewScrollToBottomWithAnimated:YES];
        };
    }
    return _commentView;
}

- (NSMutableArray *)frameModels {
    if (!_frameModels) {
        _frameModels = [NSMutableArray array];
    }
    
    return _frameModels;
}

- (FMPrivateLetterRuleView *)ruleView {
    if (!_ruleView) {
        _ruleView = [[FMPrivateLetterRuleView alloc] init];
    }
    
    return _ruleView;
}

@end
