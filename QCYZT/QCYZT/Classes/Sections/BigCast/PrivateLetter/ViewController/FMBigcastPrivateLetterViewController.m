//
//  FMBigcastPrivateLetterViewController.m
//  QCYZT
//
//  Created by zeng on 2022/7/15.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMBigcastPrivateLetterViewController.h"
#import "FMPrivateLetterTextFrameModel.h"
#import "FMPrivateLetterTextCell.h"
#import "HttpRequestTool+UserCenter.h"
#import "FMDetailBottomView.h"
#import "FMCommentView.h"
#import "WebSocketManager.h"
#import "FMPrivateLetterImageCell.h"
#import "FMPrivateLetterImageFrameModel.h"
#import "FMPrivateLetterSendMsgModel.h"
#import "FMMsgRequestAllDataStaus.h"
#import "FMImagePickerVC.h"

@interface FMBigcastPrivateLetterViewController ()<UITableViewDelegate, UITableViewDataSource, FMDetailBottomViewDelegate, WebSocketManagerDelegate, UIActionSheetDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate>

@property (nonatomic, strong) UIView *topView;
@property (nonatomic, strong) UILabel *topLabel;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *frameModels;
@property (nonatomic, assign) NSUInteger pageSize;

@property (nonatomic, strong) UIButton *addImgBtn;
@property (nonatomic, strong) FMDetailBottomView *bottomView;
@property (nonatomic, strong) FMCommentView *commentView;

@end

@implementation FMBigcastPrivateLetterViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = FMWhiteColor;
    self.title = self.model.speekerName;
    
    [self.view addSubview:self.addImgBtn];
    [self.addImgBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.bottom.equalTo(@(-UI_SAFEAREA_BOTTOM_HEIGHT));
        make.width.equalTo(@30);
        make.height.equalTo(@(55));
    }];
    self.addImgBtn.hidden = YES;
    
    [self.view addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.addImgBtn.mas_right);
        make.right.equalTo(@0);
        make.bottom.equalTo(@0);
        make.height.equalTo(@(DetailBottomViewHeight));
    }];
    self.bottomView.delegate = self;
    self.bottomView.hidden = YES;
    
    [self.view addSubview:self.topView];
    [self.topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@0);
        make.height.equalTo(@45);
    }];
    self.topView.hidden = YES;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(self.topView.mas_bottom);
        make.bottom.equalTo(self.bottomView.mas_top);
    }];
    self.tableView.hidden = YES;

    self.pageSize = 20;
    [self requestData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self configNavRedColor];
}

- (void)dealloc {
    // 断开websocket
    FMLog(@"WebSocketManager 断开链接");
    [[WebSocketManager shared] closeWebSocket];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - UITableView Delegate/DataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.frameModels.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMPrivateLetterFrameModel *frameModel = self.frameModels[indexPath.row];
    FMPrivateLetterModel *model = frameModel.model;
    
    FMPrivateLetterBaseCell *cell = nil;
    switch (model.messageType) {
        case PrivateLetterBodyType_Text:
            cell = [tableView reuseCellClass:[FMPrivateLetterTextCell class]];
            break;
            
        case PrivateLetterBodyType_Image:
            cell = [tableView reuseCellClass:[FMPrivateLetterImageCell class]];
            break;
            
        default:
            break;
    }
    cell.frameModel = self.frameModels[indexPath.row];
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMPrivateLetterFrameModel *frameModel = self.frameModels[indexPath.row];
    return frameModel.cellHeight;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - FMDetailBottomViewDelegate
- (void)detailBottomViewCommentBtnDidClicked:(UIView *)bottomView btn:(UIButton *)commentBtn {
    bottomView.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        bottomView.userInteractionEnabled = YES;
    });

    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    [keyWindow addSubview:self.commentView];
    self.commentView.publisLb.textColor = FMNavColor;
    self.commentView.publisLb.text = @"发送私信";
    self.commentView.placeholderText = [NSString stringWithFormat:@"最多输入%zd字", self.commentView.limitWordNum];
    WEAKSELF
    self.commentView.publishBlock = ^(NSString *content) {
        [HttpRequestTool requestDakaPrivateLetterSendWithOrderId:__weakSelf.model.orderId content:content contentType:@"1" start:^{
            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [SVProgressHUD dismiss];
                __weakSelf.commentView.textView.text = @"";

                FMPrivateLetterModel *model = [[FMPrivateLetterModel alloc] init];
                model.speekerType = 2;
                model.bubbleType = PrivateLetterBubbleType_MeSend;
                model.messageType = PrivateLetterBodyType_Text;
                model.content = content;
                model.speekerIco = dic[@"data"][@"speekerIco"];
                model.createTime = [dic[@"data"][@"createTime"] longLongValue];

                [__weakSelf addOneFrameModelWithModel:model];
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    };
}

#pragma mark - websocket接收消息
- (void)webSocketManagerDidReceiveMessageWithString:(NSString *)string {
    NSDictionary *stringDic = [JsonTool dicOrArrFromJsonString:string];
    NSDictionary *contentDic = [JsonTool dicOrArrFromJsonString:stringDic[@"content"]];
    NSString *msgType = stringDic[@"msgType"];
    if ([msgType isEqualToString:@"USER_MESSAGE"]) {
        FMPrivateLetterModel *model = [FMPrivateLetterModel modelWithDictionary:contentDic];
        model.speekerType = 1;
        model.bubbleType = PrivateLetterBubbleType_OtherSend;
        model.messageType = PrivateLetterBodyType_Text;
        [self addOneFrameModelWithModel:model];
    }
}

#pragma mark - UIActionSheet Delegate
- (void)actionSheet:(UIActionSheet *)actionSheet clickedButtonAtIndex:(NSInteger)buttonIndex {
    UIImagePickerController *picker;
    if (buttonIndex == 0) {
        picker = [[UIImagePickerController alloc] init];
    } else {
        picker = [[FMImagePickerVC alloc] init];
    }
    picker.delegate = self;
    picker.allowsEditing = NO;
    picker.modalPresentationStyle = UIModalPresentationFullScreen;
    
    if (buttonIndex == 0) {
        AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
        if ((authStatus == AVAuthorizationStatusRestricted || authStatus ==AVAuthorizationStatusDenied)) {
            ShowConfirm(self, @"无法使用你的相机", @"大决策没有获得相机的使用权限，请在设置中开启「相机」", @"取消", @"开启权限", nil, ^{
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:nil];
            });
        } else if (authStatus == AVAuthorizationStatusNotDetermined) {
            // fix issue 466, 防止用户首次拍照拒绝授权时相机页黑屏
            [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
                if (granted) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        picker.sourceType = UIImagePickerControllerSourceTypeCamera;
                        [self presentViewController:picker animated:YES completion:nil];
                    });
                }
            }];
        } else {
            picker.sourceType = UIImagePickerControllerSourceTypeCamera;
            [self presentViewController:picker animated:YES completion:nil];
        }
    } else if (buttonIndex == 1) {
        picker.allowsEditing = NO;
        picker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
        [self presentViewController:picker animated:YES completion:nil];
    }
}

#pragma mark - UIImagePickController Delegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info
{
    [picker dismissViewControllerAnimated:NO completion:nil];
    
    // 此处必须延迟，否非无法显示转圈圈
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        UIImage *photo = [info objectForKey:UIImagePickerControllerOriginalImage];
        [self requestSendImage:photo];
    });
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    [picker dismissViewControllerAnimated:YES completion:nil];
}


#pragma mark - RequestData
- (void)requestData {
    FMPrivateLetterFrameModel *firstFrameModel = self.frameModels.firstObject;
    [HttpRequestTool requestDakaPrivateLetterRecordListWithOrderId:self.model.orderId start:^{
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        [self.tableView.mj_header endRefreshing];
    } success:^(NSDictionary *dic) {
        [self.tableView.mj_header endRefreshing];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            // 建立webSocket链接
            if (![WebSocketManager shared].isConnect) {
                FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                [WebSocketManager shared].socketUrl = [NSString stringWithFormat:@"%@/api/v2/ws/letter?orderId=%@&bignameId=%@",kDakaInit, self.model.orderId, userModel.userId];
                [[WebSocketManager shared] connectServer];
                [WebSocketManager shared].delegate = self;
            }
            self.tableView.hidden = NO;
            self.addImgBtn.hidden = NO;
            self.bottomView.hidden = NO;
            self.topView.hidden = NO;
            if (self.model.isVip) {
                self.topLabel.attributedText = [@"VIP用户 用户问题已解决？" attrStrWithMatchColor:ColorWithHex(0xff7200) pattern:@"VIP用户" textFont:FontWithSize(14)];
            } else {
                self.topLabel.attributedText = [[NSAttributedString alloc] initWithString:@"用户问题已解决？"];
            }
            
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMPrivateLetterModel class] json:dic[@"data"]];
            [self dealFramesWithArr:dataArr];
            [self.tableView reloadData];

            if (!firstFrameModel) { // 第一次加载
                [self tableViewScrollToBottomWithAnimated:NO];
            } else { // 下拉
                self.tableView.hidden = NO;
                if (dataArr.count) { // 下拉有数据，偏移一点点
                    CGRect cellRect = [self.tableView rectForRowAtIndexPath:[NSIndexPath indexPathForRow:dataArr.count - 1 inSection:0]];
                    [self.tableView setContentOffset:CGPointMake(0, cellRect.origin.y + cellRect.size.height - MJRefreshHeaderHeight)];
                }
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)stopChat {
    [PushMessageView showWithTitle:nil message:@"确定结束此次会话吗？" noticeImage:nil sureTitle:@"确定" cancelTitle:@"取消" clickSure:^{
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [HttpRequestTool requestDakaPrivateLetterStopWithOrderId:self.model.orderId start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"关闭成功"];
                    
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [self.navigationController popViewControllerAnimated:YES];
                    });
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        });
    } clickCancel:^{
        
    }];
}

- (void)requestSendImage:(UIImage *)iconImage {
    WEAKSELF;
    [SVProgressHUD show];
    [NetworkManager uploadImageWithOperations:nil withImageArray:@[iconImage] withUrlString:kAPI_System_Uplaod withSuccessBlock:^(NSDictionary *object) {
        if ([object[@"status"] isEqualToString:@"1"]) {
            NSDictionary *dict = [object[@"data"] firstObject];
            NSDictionary *imgDic = @{@"url" : dict[@"url"], @"width" : @(iconImage.size.width), @"height" : @(iconImage.size.height)};
            [HttpRequestTool requestDakaPrivateLetterSendWithOrderId:__weakSelf.model.orderId content:[JsonTool jsonStringFromDicOrArr:imgDic] contentType:@"2" start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD dismiss];

                    FMPrivateLetterModel *model = [[FMPrivateLetterModel alloc] init];
                    model.speekerType = 2;
                    model.bubbleType = PrivateLetterBubbleType_MeSend;
                    model.messageType = PrivateLetterBodyType_Image;
                    model.picUrl = dict[@"url"];
                    model.picWidth = iconImage.size.width;
                    model.picHeight = iconImage.size.height;
                    model.speekerIco = dic[@"data"][@"speekerIco"];
                    model.createTime = [dic[@"data"][@"createTime"] longLongValue];

                    [__weakSelf addOneFrameModelWithModel:model];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        } else {
            [SVProgressHUD showErrorWithStatus:object[@"errmessage"]];
        }
     } withFailurBlock:^(NSError *error) {
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } withUpLoadProgress:^(float progress) {
        
    }];
}

#pragma mark - Private
- (void)dealFramesWithArr:(NSArray *)arr {
    NSMutableArray *tmpFrameArr = [NSMutableArray array];
    for (NSInteger i = arr.count - 1; i >= 0; i--) {
        FMPrivateLetterModel *model = arr[i];
        if (model.speekerType == 1) { 
            model.bubbleType = PrivateLetterBubbleType_OtherSend;
        } else if (model.speekerType == 2) {
            model.bubbleType = PrivateLetterBubbleType_MeSend;
        }
        
        FMPrivateLetterFrameModel *frameModel = [self createFrameModelWithMessageType:model.messageType];
        if (i == arr.count - 1) {
            frameModel.hiddenTime = NO;
        } else {
            FMPrivateLetterModel *preModel = arr[i+1];
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.createTime/1000];
            NSDate *preDate = [NSDate dateWithTimeIntervalSince1970:preModel.createTime/1000];
            frameModel.hiddenTime = [self isHiddenTimeWithDate:date preDate:preDate]; // 5分钟内不展示
        }
        
        if (i == 0) {
            frameModel.isLast = YES;
        } else {
            frameModel.isLast = NO;
        }

        frameModel.model = model;
        [tmpFrameArr addObject:frameModel];
    }
    
    // 如果是下拉刷新，修改第一条的时间显示
    if (self.frameModels.count != 0) {
        FMPrivateLetterFrameModel *firstFrameModel = self.frameModels.firstObject;
        FMPrivateLetterModel *firstModel = firstFrameModel.model;
        FMPrivateLetterFrameModel *preFrameModel = tmpFrameArr.lastObject;
        FMPrivateLetterModel *preModel = preFrameModel.model;
        preFrameModel.isLast = NO;
        NSDate *date = [NSDate dateWithTimeIntervalSince1970:firstModel.createTime/1000];
        NSDate *preDate = [NSDate dateWithTimeIntervalSince1970:preModel.createTime/1000];
        firstFrameModel.hiddenTime = [self isHiddenTimeWithDate:date preDate:preDate];
        firstFrameModel.model = firstFrameModel.model;
    }
    
    
    [self insertObjects:tmpFrameArr toArr:self.frameModels atIndex:0];
}

- (void)insertObjects:(NSArray *)objects toArr:(NSMutableArray *)arr atIndex:(NSUInteger)index {
    NSUInteger i = index;
    for (id obj in objects) {
        [arr insertObject:obj atIndex:i++];
    }
}

- (BOOL)isTimeExpiredWithDate:(NSDate *)date preDate:(NSDate *)preDate {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    int unit = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute;
    NSDateComponents *cmps = [calendar components:unit fromDate:preDate toDate:date options:0];
    
    return cmps.minute >= 1 || cmps.hour > 0 || cmps.day > 0 || cmps.month > 0 || cmps.year > 0;
}

- (FMPrivateLetterFrameModel *)createFrameModelWithMessageType:(PrivateLetterBodyType)messageType {
    FMPrivateLetterFrameModel *frameModel = nil;
    switch (messageType) {
        case PrivateLetterBodyType_Text:
            frameModel = [[FMPrivateLetterTextFrameModel alloc] init];
            break;
        case PrivateLetterBodyType_Image:
            frameModel = [[FMPrivateLetterImageFrameModel alloc] init];
            break;
            
        default:
            frameModel = [[FMPrivateLetterTextFrameModel alloc] init];
            break;
    }
    
    frameModel.hiddenTime = NO; // 默认为NO
    return frameModel;
}

// 增加一条数据
- (void)addOneFrameModelWithModel:(FMPrivateLetterModel *)messageModel {
    // 1.增加frame模型
    FMPrivateLetterFrameModel *preFrameModel = self.frameModels.lastObject;
    preFrameModel.isLast = NO;
    FMPrivateLetterModel *preModel = preFrameModel.model;
    preFrameModel.model = preModel;
    NSDate *preDate = [NSDate dateWithTimeIntervalSince1970:preModel.createTime/1000];
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:messageModel.createTime/1000];
    
    FMPrivateLetterFrameModel *frameModel = [self createFrameModelWithMessageType:messageModel.messageType];
    frameModel.isLast = YES;
    frameModel.hiddenTime = [self isHiddenTimeWithDate:date preDate:preDate];
    frameModel.model = messageModel;
    [self.frameModels addObject:frameModel];
    
    // 2.刷新tableView，并滚动到最下面
    [self.tableView reloadData];
    [self tableViewScrollToBottomWithAnimated:YES];
}

// 是否隐藏时间，距离上一条消息在5分钟之内不显示
- (BOOL)isHiddenTimeWithDate:(NSDate *)date preDate:(NSDate *)preDate {
    if (!preDate) {
        return NO;
    }
    NSCalendar *calendar = [NSCalendar currentCalendar];
    int unit = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute;
    NSDateComponents *cmps = [calendar components:unit fromDate:preDate toDate:date options:0];
    return (cmps.minute < 5 && cmps.hour == 0 && cmps.day == 0 && cmps.month == 0 && cmps.year == 0);
}

// 滚动到最后一条
- (void)tableViewScrollToBottomWithAnimated:(BOOL)animated {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.tableView.hidden = NO;
        if (self.frameModels.count) {
            [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:self.frameModels.count - 1 inSection:0] atScrollPosition:UITableViewScrollPositionBottom animated:animated];
        }
    });
}

- (void)addImg {
    UIActionSheet *actionSheet = [[UIActionSheet alloc] initWithTitle:nil delegate:self cancelButtonTitle:@"取消" destructiveButtonTitle:nil otherButtonTitles:@"拍照", @"从相册选择", nil];
    [actionSheet showInView:self.view];
}

#pragma mark - Getter/Setter
- (UIView *)topView {
    if (!_topView) {
        _topView = [[UIView alloc] init];
        _topView.backgroundColor = FMWhiteColor;
        
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        label.text = @"用户问题已解决？";
        [_topView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.centerY.equalTo(@0);
        }];
        self.topLabel = label;
        
        UIButton *stopBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(14) normalTextColor:FMWhiteColor backgroundColor:FMNavColor title:@"结束会话" image:nil target:self action:@selector(stopChat)];
        [_topView addSubview:stopBtn];
        [stopBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(@-15);
            make.centerY.equalTo(@0);
            make.size.equalTo(@(CGSizeMake(87, 30)));
        }];
        UI_View_Radius(stopBtn, 15);
    }
    
    return _topView;;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMPrivateLetterTextCell class]];
        [_tableView registerCellClass:[FMPrivateLetterImageCell class]];
        _tableView.backgroundColor = ColorWithHex(0xeeeeee);
    }
    
    return _tableView;
}

- (UIButton *)addImgBtn {
    if (!_addImgBtn) {
        _addImgBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMWhiteColor title:nil image:ImageWithName(@"PrivateLetter_SendImg") target:self action:@selector(addImg)];
    }
    
    return _addImgBtn;
}

- (FMDetailBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[FMDetailBottomView alloc] init];
        [_bottomView.commentBtn setTitle:@"发送消息" forState:UIControlStateNormal];
    }
    return _bottomView;
}

- (FMCommentView *)commentView {
    if (!_commentView) {
        _commentView = [[FMCommentView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        _commentView.limitWordNum = 500;
    }
    return _commentView;
}

- (NSMutableArray *)frameModels {
    if (!_frameModels) {
        _frameModels = [NSMutableArray array];
    }
    
    return _frameModels;
}



@end
