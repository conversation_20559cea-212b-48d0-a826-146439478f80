//
//  FMBigcastPrivateLetterOrderListViewController.m
//  QCYZT
//
//  Created by zeng on 2022/7/18.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMBigcastPrivateLetterOrderListViewController.h"
#import "FMBigcastPrivateLetterOrderListModel.h"
#import "FMBigcastPrivateLetterOrderListCell.h"
#import "HttpRequestTool+UserCenter.h"
#import "FMBigcastPrivateLetterViewController.h"

@interface FMBigcastPrivateLetterOrderListViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *modelArr;

@end

@implementation FMBigcastPrivateLetterOrderListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"私信";
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 0, UI_SAFEAREA_BOTTOM_HEIGHT, 0));
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
    
    [self headerAction];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self configNavRedColor];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - UITableViewDelegate
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.modelArr.count;
}

-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMBigcastPrivateLetterOrderListCell *cell = [tableView reuseCellClass:[FMBigcastPrivateLetterOrderListCell class]];
    cell.model = self.modelArr[indexPath.row];
    return cell;
}

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 75.0f;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    FMBigcastPrivateLetterViewController *vc = [[FMBigcastPrivateLetterViewController alloc] init];
    vc.model = self.modelArr[indexPath.row];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)headerAction {
    [HttpRequestTool requestDakaPrivateLetterOrderListWithStart:^{
    } failure:^{
        [self.tableView.mj_header endRefreshing];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [self.tableView.mj_header endRefreshing];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self.modelArr removeAllObjects];
            
            NSArray *arr = [NSArray modelArrayWithClass:[FMBigcastPrivateLetterOrderListModel class] json:dic[@"data"]];
            [self.modelArr addObjectsFromArray:arr];
            [self.tableView reloadData];
            
            if (self.modelArr.count) {
                [self.view dismissNoDataView];
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
                self.tableView.mj_footer.hidden = NO;
            } else {
                [self.view showNoDataViewWithImage:ImageWithName(@"PrivateLetter_NoData") string:@"暂无待处理私聊" attributes:nil offsetY:120];
                self.tableView.mj_footer.hidden = YES;
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)footerAction {
    
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        _tableView.backgroundColor = FMBgColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMBigcastPrivateLetterOrderListCell class]];
    }
    
    return _tableView;
}

- (NSMutableArray *)modelArr {
    if (!_modelArr) {
        _modelArr = [NSMutableArray array];
    }
    
    return _modelArr;
}


@end
