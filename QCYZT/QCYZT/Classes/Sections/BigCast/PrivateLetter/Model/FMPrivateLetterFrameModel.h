//
//  FMPrivateLetterFrameModel.h
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FMPrivateLetterModel.h"
#import "FMPrivateLetterBigcastHomeModel.h"

static const CGFloat kTimeFontSize = 12.0f;  // 时间字体大小
static const CGFloat kTextInsetPadding = 10.0f; // container与内容的间距，如果需要的话
static const CGFloat kBubbleInsetPadding = 7.0f; // 气泡小揪揪的偏移
static const CGFloat kIconWidthHeight = 40.0f;  // 头像宽高
static const CGFloat kContainerToIconYPadding = 0; // container距离头像的Y偏移
static const CGFloat kContainerToIconXPadding = 7.0f; // container距离头像的X间距

@interface FMPrivateLetterFrameModel : NSObject

@property (nonatomic, strong) FMPrivateLetterBigcastHomeModel *bigcastHomeModel;

@property (nonatomic, assign, getter=isHiddenTime) BOOL hiddenTime; // 是否隐藏时间展示，必须在设置model前设置
@property (nonatomic, assign) BOOL isLast; // 是否是列表上最后一条聊天
@property (nonatomic, strong) FMPrivateLetterModel *model;
@property (nonatomic, copy, readonly) NSString *timeStr;

//// frame相关
// 时间
@property (nonatomic, assign, readonly) CGRect timeF;
// 气泡
@property (nonatomic, assign) CGRect bubbleF;
// 头像
@property (nonatomic, assign, readonly) CGRect iconF;
// 中间内容
@property (nonatomic, assign, readonly) CGRect containerF;
// cell高度
@property (nonatomic, assign, readonly) CGFloat cellHeight;


/// 计算containerF，子类覆盖
/// @param model 私信模型
/// @param iconF 头像frame
- (CGRect)calculateContainerViewFrameWithModel:(FMPrivateLetterModel *)model iconF:(CGRect)iconF;

@end


