//
//  FMPrivateLetterFrameModel.m
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterFrameModel.h"

static const CGFloat kCommonPadding = 18.0f;  // 通用间距
static const CGFloat kIconLeftOrRightPadding = 12.0f; // 头像左右距离父控件的间距

@interface FMPrivateLetterFrameModel()

@property (nonatomic, assign) CGRect timeF;
@property (nonatomic, copy) NSString *timeStr;
@property (nonatomic, assign) CGRect iconF;
@property (nonatomic, assign) CGRect containerF;
@property (nonatomic, assign) CGFloat cellHeight;

@end

@implementation FMPrivateLetterFrameModel

- (NSString *)getCommomDateFormatWithTimestamp:(double)timestamp {
    NSDate *date= [NSDate dateWithTimeIntervalSince1970:timestamp/1000];
    NSString *timeString = nil;
    if ([date isToday]) {
        timeString = [NSString stringFromDate:date format:@"HH:mm"];
    } else {
        if ([date isThisYear]) {
            timeString = [NSString stringFromDate:date format:@"MM-dd HH:mm"];
        } else {
            timeString = [NSString stringFromDate:date format:@"yyyy-MM-dd HH:mm"];
        }
    }
    
    return timeString;
}

- (void)setModel:(FMPrivateLetterModel *)model {
    _model = model;
        
    // 1.时间
    self.timeStr = [self getCommomDateFormatWithTimestamp:model.createTime];
    if (!self.isHiddenTime) {
        CGSize timeSize = [self.timeStr sizeWithFont:FontWithSize(kTimeFontSize) andSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)];
        CGFloat timeX = (UI_SCREEN_WIDTH - timeSize.width) * 0.5;
        CGFloat timeY = kCommonPadding;
        _timeF = CGRectMake(timeX, timeY, timeSize.width, timeSize.height);
    } else {
        _timeF = CGRectZero;
    }
    
    // 2.头像
    CGFloat iconY = CGRectGetMaxY(_timeF) + kCommonPadding;
    CGFloat iconX;
    if (model.bubbleType == PrivateLetterBubbleType_MeSend) {// 自己发的
        iconX = UI_SCREEN_WIDTH - kIconLeftOrRightPadding - kIconWidthHeight;
    } else { // 别人发的
        iconX = kIconLeftOrRightPadding;
    }
    _iconF = CGRectMake(iconX, iconY, kIconWidthHeight, kIconWidthHeight);
    
    // 3.container
    _containerF = [self calculateContainerViewFrameWithModel:model iconF:_iconF];
    
    // 4.cell的高度
    _cellHeight = MAX(CGRectGetMaxY(_containerF), CGRectGetMaxY(_iconF)) + (self.isLast ? kCommonPadding : 0);
}

- (void)setIsLast:(BOOL)isLast {
    _isLast = isLast;
    
    _cellHeight = MAX(CGRectGetMaxY(_containerF), CGRectGetMaxY(_iconF)) + (_isLast ? kCommonPadding : 0);
}

// 计算containerF，子类覆盖
- (CGRect)calculateContainerViewFrameWithModel:(FMPrivateLetterModel *)model iconF:(CGRect)iconF {
    return CGRectZero;
}

@end
