//
//  FMPrivateLetterModel.h
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 消息气泡类型
typedef NS_ENUM(NSUInteger, PrivateLetterBubbleType) {
    PrivateLetterBubbleType_MeSend = 1,      // 自己发送的
    PrivateLetterBubbleType_OtherSend    // 别人发送的
};


// 消息类型
typedef NS_ENUM(NSUInteger, PrivateLetterBodyType) {
    PrivateLetterBodyType_Text = 1,
    PrivateLetterBodyType_Image
};



@interface FMPrivateLetterModel : NSObject

@property (nonatomic , copy) NSString              * content;
@property (nonatomic , assign) long long              createTime;
@property (nonatomic , copy) NSString            *  msgId;
@property (nonatomic , assign) NSInteger              orderId;
@property (nonatomic , copy) NSString              * speekerIco;
@property (nonatomic , assign) NSInteger              speekerId;
@property (nonatomic , copy) NSString              * speekerName;
@property (nonatomic,assign) NSInteger starLevel;
@property (nonatomic,assign) NSInteger status;

@property (nonatomic,assign) BOOL isHello;

// 发言人类型：1：用户；2：投顾
@property (nonatomic, assign) NSInteger speekerType;
// 消息发送与接收
@property (nonatomic, assign) PrivateLetterBubbleType bubbleType;
// 消息类型
@property (nonatomic, assign) PrivateLetterBodyType messageType;

// 图片类型
@property (nonatomic, copy) NSString *picUrl;
@property (nonatomic, assign) CGFloat picWidth;
@property (nonatomic, assign) CGFloat picHeight;


@end

NS_ASSUME_NONNULL_END
