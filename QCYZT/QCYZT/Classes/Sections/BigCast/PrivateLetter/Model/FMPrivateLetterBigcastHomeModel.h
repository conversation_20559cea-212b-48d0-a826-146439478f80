//
//  FMPrivateLetterBigcastHomeModel.h
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FMPrivateLetterBigcastHomeModel : NSObject

@property (nonatomic , copy) NSString              * bignameId;
@property (nonatomic , copy) NSString              * bignameName;
@property (nonatomic, copy) NSString *bignameRealName;
@property (nonatomic , assign) CGFloat              price;
@property (nonatomic , strong) NSArray *historyRecord;
@property (nonatomic, assign) NSInteger currentSentence; // 当前订单剩余的发送话次数
@property (nonatomic, assign) NSInteger vipSentence; // 当前用户剩余vip发送次数
@property (nonatomic, copy) NSString *currentOrderId; // 当前订单id
@property (nonatomic, copy) NSString *cert_code;
@property (nonatomic, assign) BOOL allowOpenVip;
@property (nonatomic , assign) BOOL                 isVip;  // 是不是这个投顾的vip
/// 0过期    2已关闭
@property (nonatomic,assign) NSInteger status;


@property (nonatomic, copy) NSString *payText;              // 付款方式提示语
@property (nonatomic, copy) NSString *noPayModelText;       // 没有可用的付款方式的提示语
@property (nonatomic, assign) NSInteger noPayModelCode;       // 没有可用的付款方式的返回码（501金币不足，502没有月卡，503没有券）
@property (nonatomic, strong) NSArray *enablePayModel;      // 可用的支付方式


@end

NS_ASSUME_NONNULL_END
