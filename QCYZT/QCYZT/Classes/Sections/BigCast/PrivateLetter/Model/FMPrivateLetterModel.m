//
//  FMPrivateLetterModel.m
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterModel.h"

@implementation FMPrivateLetterModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"msgId" : @"id",
             @"messageType" : @"contentType"
    };
}

- (NSString *)picUrl {
    id contentDic = [JsonTool dicOrArrFromJsonString:self.content];
    if ([contentDic isKindOfClass:[NSDictionary class]]) {
        _picUrl = contentDic[@"url"];
    }
    
    return _picUrl.length ? _picUrl : @"";
}

- (CGFloat)picWidth {
    id contentDic = [JsonTool dicOrArrFromJsonString:self.content];
    if ([contentDic isKindOfClass:[NSDictionary class]]) {
        _picWidth = [contentDic[@"width"] floatValue];
    }
    
    return _picWidth > 0 ? _picWidth : 160.0f;
}

- (CGFloat)picHeight {
    id contentDic = [JsonTool dicOrArrFromJsonString:self.content];
    if ([contentDic isKindOfClass:[NSDictionary class]]) {
        _picHeight = [contentDic[@"height"] floatValue];
    }
    
    return _picHeight > 0 ? _picHeight : 160.0f;
}

@end
