//
//  EnablePayModel.h
//  QCYZT
//
//  Created by Mr.文 on 2017/9/18.
//  Copyright © 2017年 sdcf. All rights reserved.
//  

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, PaymentType) {
    PaymentTypeCoin = 1,  // 金币
    PaymentTypeCoupon = 3, // 通用券
    PaymentTypeSpecialCoupon = 4, // 专用券
    PaymentTypeWechat = 5 // 微信支付
};


@interface EnablePayModel : NSObject

// 描述
@property (nonatomic, copy) NSString *paydesc;
// name
@property (nonatomic, copy) NSString *name;
// 用户余额
@property (nonatomic, copy) NSString *num;
// 支付方式
@property (nonatomic, assign) PaymentType type;
// 免费群体 描述
@property (nonatomic, copy) NSString *freeDesc;
// 券id
@property (nonatomic, copy) NSString *couponId;
// 投顾id
@property (nonatomic,copy) NSString *bignameId;
// 1 笔记 2 问股 3 私信 4 偷听 5 直播 6 优质课程 7 系列课程  23 指标 24 模板 25 策略 26解套宝增值服务
@property (nonatomic,assign) NSInteger consumeType;
// 内容id
@property (nonatomic,assign) NSInteger contentId;
// 折扣信息
@property (nonatomic, strong) NSDictionary *discountInfo;

@property (nonatomic, assign) BOOL choosed; // 是否选中


@end
