//
//  PaymentView.h
//  QCYZT
//
//  Created by Mr.文 on 2017/9/18.
//  Copyright © 2017年 sdcf. All rights reserved.
//  支付方式选择弹框

#import <UIKit/UIKit.h>
#import "EnablePayModel.h"


@interface PaymentView : UIView
/// - Parameters:
///   - enablePayList: 支付方式模型数组
///   - price: 价格
///   - productName: 付费商品名
///   - bottomReminder: 底部提示内容
///   - sureAction: 确定回调
///   - dismissBlock: 消失回调
+ (instancetype)showWithEnablePayList:(NSArray *)enablePayList
                             payPrice:(NSString *)price
                          productName:(NSString *)productName
                       bottomReminder:(NSString *)bottomReminder
                            payAction:(void(^)(EnablePayModel *selectedModel))sureAction
                         dismissBlock:(void(^)())dismissBlock;

/// 传入单个支付模型构造支付view
+ (instancetype)showWithEnablePayModel:(EnablePayModel *)payModel
                              payPrice:(NSString *)price
                           productName:(NSString *)productName
                        bottomReminder:(NSString *)bottomReminder
                             payAction:(void(^)(EnablePayModel *selectedModel))sureAction
                          dismissBlock:(void(^)())dismissBlock;

- (CGRect)couponBgViewCoverWindowFrame;

- (CGRect)headerViewCoverWindowFrame;

@property (nonatomic,copy) void(^couponSelected) ();

@property (nonatomic,copy) void (^getCouponInfo) ();


@end
