//
//  PayMethodCell.m
//  QCYZT
//
//  Created by 涂威 on 2017/9/18.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "PayMethodCell.h"
#import "EnablePayModel.h"
#import "YTGNormalWebVC.h"

@interface PayMethodCell ()

@property (nonatomic, strong) UIImageView *markImageView;
@property (nonatomic, strong) UILabel *originalPriceLabel;  // 原始价格
@property (nonatomic, strong) UILabel *priceLabel;          // 价格(右上位置)
@property (nonatomic, strong) UIButton *freeSeeTipButton;  // 免费看提示label
@property (nonatomic, strong) UILabel *restLabel;           // 余额/可用(右下位置)
@property (nonatomic, strong) UILabel *payTypeLB;           // 支付方式(左上位置)
@property (nonatomic, strong) UILabel *rechargeLable;       // 描述(左下位置)
@property (nonatomic, strong) UIView  *line;

@end

@implementation PayMethodCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.layoutMargins = UIEdgeInsetsMake(0, 0, 0, 0);
        self.separatorInset = UIEdgeInsetsMake(0, 0, 0, 0);
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self addSuviews];
    }
    return self;
}

- (void)addSuviews {
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    [self.contentView addSubview:self.markImageView];
    [self.contentView addSubview:self.originalPriceLabel];
    [self.contentView addSubview:self.priceLabel];
    [self.contentView addSubview:self.freeSeeTipButton];
    [self.contentView addSubview:self.restLabel];
    [self.contentView addSubview:self.payTypeLB];
    [self.contentView addSubview:self.rechargeLable];
    [self.contentView addSubview:self.line];
    
    WEAKSELF
    [self.markImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@25);
        make.right.equalTo(@-15.f);
        make.centerY.equalTo(@(0));
    }];
    
    
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(__weakSelf.markImageView.mas_left).offset(-15);
        make.bottom.equalTo(__weakSelf.mas_centerY).offset(-5);
    }];
    
    [self.originalPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.priceLabel);
        make.right.equalTo(self.priceLabel.mas_left).offset(-10);
    }];

    [self.restLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(__weakSelf.markImageView.mas_left).offset(-15);
        make.top.equalTo(__weakSelf.mas_centerY).offset(5);
    }];
    
    [self.freeSeeTipButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.priceLabel.mas_right);
        make.centerY.equalTo(self.restLabel.mas_centerY);
        make.height.equalTo(@(30));
    }];
    
    [self.payTypeLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(__weakSelf.priceLabel);
        make.left.equalTo(@15);
        make.right.lessThanOrEqualTo(__weakSelf.priceLabel.mas_left).offset(-15);
    }];
    [self.payTypeLB setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.rechargeLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(__weakSelf.restLabel);
        make.left.equalTo(@15);
    }];
    
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(__weakSelf.contentView);
        make.height.equalTo(@0.5);
    }];
    
}

- (UIImageView *)markImageView {
    if (_markImageView == nil) {
        _markImageView = [[UIImageView alloc] init];
        _markImageView.image = [UIImage imageNamed:@"unchecked"];
    }
    return _markImageView;
}

- (UILabel *)priceLabel {
    if (_priceLabel == nil) {
        _priceLabel = [[UILabel alloc] init];
        _priceLabel.font = [UIFont systemFontOfSize:16];
        _priceLabel.textColor = UIColor.up_textPrimaryColor;
    }
    return _priceLabel;
}

- (UILabel *)originalPriceLabel {
    if (!_originalPriceLabel) {
        _originalPriceLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15.0) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1];
    }
    return _originalPriceLabel;
}

- (UIButton *)freeSeeTipButton {
    if (!_freeSeeTipButton) {
        _freeSeeTipButton = [[UIButton alloc] initWithFrame:CGRectZero font:BoldFontWithSize(14.0) backgroundColor:FMClearColor target:nil action:nil];
        [_freeSeeTipButton setTitleColor:ColorWithHex(0x522700) forState:UIControlStateNormal];
        UI_View_Radius(_freeSeeTipButton, 15.0);
    }
    return _freeSeeTipButton;
}

- (UILabel *)restLabel {
    if (_restLabel == nil) {
        _restLabel = [[UILabel alloc] init];
        _restLabel.font = [UIFont systemFontOfSize:15];
        _restLabel.textColor = FMDarkGreyColor;
    }
    return _restLabel;
}

- (UILabel *)payTypeLB {
    if (_payTypeLB == nil) {
        _payTypeLB = [[UILabel alloc] init];
        _payTypeLB.font = [UIFont systemFontOfSize:16];
        _payTypeLB.textColor = UIColor.up_textSecondaryColor;
    }
    return _payTypeLB;
}

- (UILabel *)rechargeLable {
    if (!_rechargeLable) {
        _rechargeLable = [[UILabel alloc]init];
        _rechargeLable.font = [UIFont systemFontOfSize:15];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(earnGoldCoins)];
        [_rechargeLable addGestureRecognizer:tap];
    }
    return _rechargeLable;
}

- (UIView *)line {
    if (!_line) {
        _line = [[UIView alloc]init];
        _line.backgroundColor = UIColor.fm_sepline_color;
    }
    return _line;
}

- (void)setModel:(EnablePayModel *)model {
    _model = model;
    
    self.rechargeLable.text = @"";
    self.rechargeLable.userInteractionEnabled = NO;
    self.payTypeLB.text = [NSString stringWithFormat:@"%@支付:",model.name];
    self.markImageView.image = model.choosed?[UIImage imageNamed:@"paytype_select"]:[UIImage imageNamed:@"unchecked"];
    
    if (model.type == PaymentTypeWechat) {
        NSString *priceStr = self.model.freeDesc.length > 0 ? @"0" : self.price;
        // 金币
        NSMutableAttributedString *priceAttr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@元",priceStr]];
        [priceAttr addAttributes:@{NSForegroundColorAttributeName : FMNavColor} range:NSMakeRange(0, priceStr.length)];
        self.priceLabel.attributedText = priceAttr;
        
        self.restLabel.hidden = YES;
        self.rechargeLable.hidden = YES;
        [self.payTypeLB mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.markImageView);
            make.left.equalTo(@15);
            make.right.lessThanOrEqualTo(self.priceLabel.mas_left).offset(-15);
        }];
        [self.priceLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.markImageView.mas_left).offset(-15);
            make.centerY.equalTo(self.markImageView);
        }];
        return;
    } else {
        NSString *priceStr = self.model.freeDesc.length > 0 ? @"0" : self.price;
        // 金币
        NSMutableAttributedString *priceAttr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@金币",priceStr]];
        [priceAttr addAttributes:@{NSForegroundColorAttributeName : FMNavColor} range:NSMakeRange(0, priceStr.length)];
        self.priceLabel.attributedText = priceAttr;
        
        
        self.restLabel.hidden = (self.model.freeDesc.length > 0);
        self.restLabel.text = [NSString stringWithFormat:@"余额:%@",model.num];
        self.rechargeLable.hidden = (self.model.freeDesc.length > 0);
        /// 金币不够
        if ([model.num floatValue] < [self.price floatValue]) {
            self.unCoinPay = YES;
            self.rechargeLable.text = @"做任务，赚金币>";
            self.rechargeLable.textColor = FMNavColor;
            self.rechargeLable.userInteractionEnabled = YES;
        } else {
            /// 开启了随机立减的配置 且没有选择卡券
            if ([self.model.discountInfo[@"isOpen"] boolValue] && !self.isSelectCounpon) {
                self.rechargeLable.text = self.model.discountInfo[@"description"];
                self.rechargeLable.textColor = ColorWithHex(0xD70008);
            }
        }
        
        [self.payTypeLB mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.priceLabel);
            make.left.equalTo(@15);
            make.right.lessThanOrEqualTo(self.priceLabel.mas_left).offset(-15);
        }];
        [self.priceLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.markImageView.mas_left).offset(-15);
            make.bottom.equalTo(self.mas_centerY).offset(-5);
        }];
    }
    
    if (self.model.freeDesc.length > 0) {
        // 免费看的群体 支付时显示的原始价格
        NSMutableAttributedString *originalPriceAttr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@金币",self.price]];
        [originalPriceAttr addAttribute:NSStrikethroughStyleAttributeName value:@(NSUnderlineStyleSingle) range:NSMakeRange(0, originalPriceAttr.length)];
        [originalPriceAttr addAttribute:NSStrikethroughColorAttributeName value:UIColor.up_textSecondaryColor range:NSMakeRange(0, originalPriceAttr.length)];
        self.originalPriceLabel.attributedText = originalPriceAttr;
        
        [self.freeSeeTipButton setTitle:self.model.freeDesc forState:UIControlStateNormal];
        NSArray *colors =  @[(__bridge  id)ColorWithHex(0xFFF1CC).CGColor,(__bridge  id)ColorWithHex(0xEBB92E).CGColor];
        CGSize size = [self.model.freeDesc sizeWithFont:BoldFontWithSize(14.0) andSize:CGSizeMake(CGFLOAT_MAX, 30)];
        [self.freeSeeTipButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@(size.width + 24));
        }];
        self.freeSeeTipButton.bounds = CGRectMake(0, 0, size.width + 24, 30);
        [self.freeSeeTipButton drawCAGradientWithcolors:colors];
        [self.freeSeeTipButton bringSubviewToFront:self.freeSeeTipButton.titleLabel];
    }

}

/// 赚金币
- (void)earnGoldCoins {
    if (self.gotoRechargeBlock) {
        self.gotoRechargeBlock();
    }
}

@end
