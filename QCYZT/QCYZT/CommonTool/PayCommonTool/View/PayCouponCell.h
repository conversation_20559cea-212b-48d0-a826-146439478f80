//
//  PayCouponCell.h
//  QCYZT
//
//  Created by Mr.文 on 2025-01-29.
//  Copyright © 2025年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

@class FMCouponTableModel;

typedef void(^CouponTapBlock)(void);

@interface PayCouponCell : UITableViewCell

/// 配置优惠券cell
/// @param selectedCoupon 当前选中的优惠券
/// @param availableCoupons 可用优惠券列表
/// @param tapBlock 点击回调
- (void)configureWithSelectedCoupon:(FMCouponTableModel *)selectedCoupon
                   availableCoupons:(NSArray<FMCouponTableModel *> *)availableCoupons
                           tapBlock:(CouponTapBlock)tapBlock;

/// 获取优惠券背景视图的frame（用于动画定位）
- (CGRect)couponBgViewFrame;

@end
