//
//  PayContentCell.m
//  QCYZT
//
//  Created by Mr.文 on 2025-01-29.
//  Copyright © 2025年 sdcf. All rights reserved.
//

#import "PayContentCell.h"

@interface PayContentCell ()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;

@end

@implementation PayContentCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    self.backgroundColor = UIColor.up_contentBgColor;
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // 标题标签
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero 
                                                    font:FontWithSize(16) 
                                               textColor:UIColor.up_textSecondaryColor 
                                         backgroundColor:FMClearColor 
                                           numberOfLines:1 
                                           textAlignment:NSTextAlignmentLeft];
    titleLabel.text = @"付费内容 : ";
    [self.contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.centerY.equalTo(0);
    }];
    self.titleLabel = titleLabel;
    
    // 内容标签
    UILabel *contentLabel = [[UILabel alloc] initWithFrame:CGRectZero 
                                                      font:FontWithSize(16) 
                                                 textColor:UIColor.up_textPrimaryColor 
                                           backgroundColor:FMClearColor 
                                             numberOfLines:1 
                                             textAlignment:NSTextAlignmentRight];
    [contentLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.contentView addSubview:contentLabel];
    [contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel.mas_right).offset(15);
        make.right.equalTo(-15);
        make.centerY.equalTo(titleLabel);
    }];
    self.contentLabel = contentLabel;
    
    // 分割线
    [self addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.bottom.equalTo(0);
        make.height.equalTo(0.5);
    }];
}

- (void)configureWithProductName:(NSString *)productName {
    self.contentLabel.text = productName ?: @"";
}

@end
