//
//  WKProcessPool+SharedProcessPool.m
//  QCYZT
//
//  Created by macPro on 2021/6/11.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "WKProcessPool+SharedProcessPool.h"

@implementation WKProcessPool (SharedProcessPool)


+ (WKProcessPool*)sharedProcessPool {
   static WKProcessPool* shared;
   static dispatch_once_t onceToken;
   dispatch_once(&onceToken, ^{
       shared = [[WKProcessPool alloc] init];
   });
   return shared;
}

@end
