//
//  UIButton+TouchArea.h
//  QCYZT
//
//  Created by zeng on 2022/5/19.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIButton (TouchArea)

// 设置按钮额外点击区域
@property (nonatomic, assign) UIEdgeInsets lz_touchAreaInsets;


// 设置点击时间间隔
@property (nonatomic, assign) NSTimeInterval lz_acceptEventInterval;
// 当前点击的时间，外部不需要设置
@property (nonatomic, assign) NSTimeInterval lz_acceptEventTime;
@end


NS_ASSUME_NONNULL_END




