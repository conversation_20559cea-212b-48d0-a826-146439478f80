//  UIButton+ImageTitleSpacing.h
//
//  Created by macPro on 2020/02/29
//  Copyright © 2020 macPro. All rights reserved.
//
#import "UIButton+ImageTitleSpacing.h"

@implementation UIButton (ImageTitleSpacingSet)

- (void)layoutButtonWithEdgInsetsStyle:(ButtonEdgeInsetsStyle)style
                     imageTitleSpacing:(CGFloat)space {
    // 1. 获取imageView和titleLabel的宽高
    CGFloat imageWidth = self.imageView.image.size.width;
    CGFloat imageHeight = self.imageView.image.size.height;
    CGFloat labelWidth = self.titleLabel.intrinsicContentSize.width;
    CGFloat labelHeight = self.titleLabel.intrinsicContentSize.height;

    // 2. 声明全局的imageEdgeInsets和labelEdgeInsets
    UIEdgeInsets imageEdgeInsets = UIEdgeInsetsZero;
    UIEdgeInsets labelEdgeInsets = UIEdgeInsetsZero;
    UIEdgeInsets contentEdgeInsets = UIEdgeInsetsZero;

    // 3. 根据传入的style及space确定imageEdgeInsets和labelEdgeInsets的值
    switch (style) {
        case ButtonEdgeInsetsStyleImageTop: {
            imageEdgeInsets = UIEdgeInsetsMake(-labelHeight - space / 2.0, (labelWidth / 2.0), 0, -(labelWidth / 2.0));
            labelEdgeInsets = UIEdgeInsetsMake(0, -(imageWidth / 2.0), -imageHeight - space / 2.0, (imageWidth / 2.0));
            contentEdgeInsets = UIEdgeInsetsMake(imageHeight + space / 2.0, -space / 2.0, labelHeight + space / 2.0, -space / 2.0);
        }
            break;
        case ButtonEdgeInsetsStyleImageLeft: {
            imageEdgeInsets = UIEdgeInsetsMake(0, -space / 2.0, 0, space / 2.0);
            labelEdgeInsets = UIEdgeInsetsMake(0, space / 2.0, 0, -space / 2.0);
            contentEdgeInsets = UIEdgeInsetsMake(0, space / 2.0, 0, space / 2.0);
        }
            break;
        case ButtonEdgeInsetsStyleImageBottom: {
            imageEdgeInsets = UIEdgeInsetsMake(0, (labelWidth / 2.0), -labelHeight - space / 2.0, -(labelWidth / 2.0));
            labelEdgeInsets = UIEdgeInsetsMake(-imageHeight - space / 2.0, -(imageWidth / 2.0), 0, (imageWidth / 2.0));
            contentEdgeInsets = UIEdgeInsetsMake(imageHeight + space / 2.0, -space / 2.0, labelHeight + space / 2.0, -space / 2.0);
        }
            break;
        case ButtonEdgeInsetsStyleImageRight: {
            imageEdgeInsets = UIEdgeInsetsMake(0, labelWidth + space / 2.0, 0, -(labelWidth + space / 2.0));
            labelEdgeInsets = UIEdgeInsetsMake(0, -(imageWidth + space / 2.0), 0, imageWidth + space / 2.0);
            contentEdgeInsets = UIEdgeInsetsMake(0, space / 2.0, 0, space / 2.0);
        }
            break;
        default:
            break;
    }

    // 4. 赋值
    self.titleEdgeInsets = labelEdgeInsets;
    self.imageEdgeInsets = imageEdgeInsets;
    self.contentEdgeInsets = contentEdgeInsets;
}

//-(void)layoutButtonWithEdgInsetsStyle:(ButtonEdgeInsetsStyle)style
//                    imageTitleSpacing:(CGFloat)space{
///**
// *  知识点：titleEdgeInsets是title相对于其上下左右的inset，跟tableView的contentInset是类似的，
// *  如果只有title，那它上下左右都是相对于button的，image也是一样；
// *  如果同时有image和label，那这时候image的上左下是相对于button，右边是相对于label的；title的上右下是相对于button，左边是相对于image的。
// */
//// 1. 获取lable和imageView的宽高
//    CGFloat imageWidth = self.imageView.frame.size.width;
//    CGFloat imageHeight = self.imageView.frame.size.height;
//    CGFloat labelWidth = (self.titleLabel.intrinsicContentSize.width > (self.width - space - imageWidth)) ? (self.width - space - imageWidth) : self.titleLabel.intrinsicContentSize.width ;
//    CGFloat labelHeight = self.titleLabel.intrinsicContentSize.height;
//    
//// 2. 声明全局的imageEdgeInsets和labelEdgeInsets：
//    UIEdgeInsets imageEdgeInsets = UIEdgeInsetsZero;
//    UIEdgeInsets labelEdgeInsets = UIEdgeInsetsZero;
//    UIEdgeInsets contentEdgeInsets = UIEdgeInsetsZero;
//    
//// 3. 根据传入的 style 及 space 确定 imageEdgeInsets和labelEdgeInsets的值
//    switch (style) {
//        case ButtonEdgeInsetsStyleImageTop:
//        {
//            imageEdgeInsets = UIEdgeInsetsMake(-labelHeight-space/2.0, 0, 0, -labelWidth);
//            labelEdgeInsets = UIEdgeInsetsMake(0, -imageWidth, -imageHeight-space/2.0, 0);
//        }
//            break;
//        case ButtonEdgeInsetsStyleImageLeft:
//        {
//            imageEdgeInsets = UIEdgeInsetsMake(0, -space/2.0, 0, space/2.0);
//            labelEdgeInsets = UIEdgeInsetsMake(0, space/2.0, 0, -space/2.0);
//            contentEdgeInsets = UIEdgeInsetsMake(0, space/2.0, 0, space/2.0);
//        }
//            break;
//        case ButtonEdgeInsetsStyleImageBottom:
//        {
//            imageEdgeInsets = UIEdgeInsetsMake(0, 0, -labelHeight-space/2.0, -labelWidth);
//            labelEdgeInsets = UIEdgeInsetsMake(-imageHeight-space/2.0, -imageWidth, 0, 0);
//        }
//            break;
//        case ButtonEdgeInsetsStyleImageRight:
//        {
//            imageEdgeInsets = UIEdgeInsetsMake(0, labelWidth+space/2.0, 0, -labelWidth-space/2.0);
//            labelEdgeInsets = UIEdgeInsetsMake(0, -imageWidth-space/2.0, 0, imageWidth+space/2.0);
//            contentEdgeInsets = UIEdgeInsetsMake(0, space/2.0, 0, space/2.0);
//        }
//            break;
//        default:
//            break;
//    }
//    // 4. 赋值
//    self.titleEdgeInsets = labelEdgeInsets;
//    self.imageEdgeInsets = imageEdgeInsets;
//    self.contentEdgeInsets = contentEdgeInsets;
//}
@end
