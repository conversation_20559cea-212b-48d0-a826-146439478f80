//  UIButton+ImageTitleSpacing.h
//
//  Created by macPro on 2020/02/29
//  Copyright © 2020 macPro. All rights reserved.
//
#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger, ButtonEdgeInsetsStyle) {
    ButtonEdgeInsetsStyleImageTop, //图片在上，文字在下
    ButtonEdgeInsetsStyleImageLeft, // 图片在左，文字在右
    ButtonEdgeInsetsStyleImageBottom, // 图片在下，文字在上
    ButtonEdgeInsetsStyleImageRight, // 图片在右，文字在左
};
@interface UIButton (ImageTitleSpacingSet)

/**
 @param style 选择button上图片文字位置关系
 @param space 图片文字间距
 */
- (void)layoutButtonWithEdgInsetsStyle:(ButtonEdgeInsetsStyle)style
                    imageTitleSpacing:(CGFloat)space;
@end

