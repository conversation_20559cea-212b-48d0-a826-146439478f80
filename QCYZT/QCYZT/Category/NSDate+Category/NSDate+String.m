//
//  NSDate+String.m
//  QCYZT
//
//  Created by <PERSON> on 2019/2/22.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import "NSDate+String.h"
#import "NSString+Date.h"

@implementation NSDate (String)

+ (NSDate *)dateFromFormatedString:(NSString *)formatedStr format:(NSString *)format {
    if (!format.length || !formatedStr.length) return nil;
    
    NSDateFormatter *dateFormatter = [format dateFormatterFromFormatStr];
//    dateFormatter.timeZone = [NSTimeZone timeZoneWithAbbreviation:@"UTC"];
    return [dateFormatter dateFromString:formatedStr];
}

+ (NSTimeInterval)getNowTimeStamp:(NSDate *)date {
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init] ;
    [formatter setDateStyle:NSDateFormatterMediumStyle];
    [formatter setTimeStyle:NSDateFormatterShortStyle];
    [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"]; // 设置想要的格式，hh与HH的区别:分别表示12小时制,24小时制
    
    //设置时区,这一点对时间的处理很重要
    NSTimeZone*timeZone=[NSTimeZone timeZoneWithName:@"Asia/Shanghai"];
    [formatter setTimeZone:timeZone];
    NSDate *dateNow = date;
    
    return [dateNow timeIntervalSince1970];
}

@end
