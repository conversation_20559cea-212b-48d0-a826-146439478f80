//
//  NSDate+ZL.h
//  QCYZT
//
//  Created by MacPro on 16/10/14.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface NSDate (ZL)

// 是否是同一天
- (BOOL)isSameDayWithDate:(NSDate *)date;

/**
 *  是否为今天
 */
- (BOOL)isToday;
/**
 *  是否为昨天
 */
- (BOOL)isYesterday;
/**
 *  是否为今年
 */
- (BOOL)isThisYear;

/**
 *  返回一个只有年月日的时间
 */
- (NSDate *)dateWithYMD;

/**
 *  获得与当前时间的差距
 */
- (NSDateComponents *)deltaWithNow;


/**
 *  由format字符串转化NSDate->NSString
 */
- (NSString *)dateStringWithFormatString:(NSString *)format;


@end





