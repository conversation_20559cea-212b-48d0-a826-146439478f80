//
//  UIColor+FMDarkModeChange.h
//  QCYZT
//
//  Created by zeng on 2024/12/25.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIColor (FMDarkModeChange)

// tabbar
@property(class, nonatomic, readonly) UIColor * fm_tabbar_textColor;
@property(class, nonatomic, readonly) UIColor * fm_tabbar_selectedTextColor;

// navigation
@property(class, nonatomic, readonly) UIColor * fm_nav_color;

// 笔记
@property(class, nonatomic, readonly) UIColor * fm_note_audio_borderColor;
@property(class, nonatomic, readonly) UIColor * fm_note_arrange_bgGradientColor1;
@property(class, nonatomic, readonly) UIColor * fm_note_arrange_bgGradientColor2;
@property(class, nonatomic, readonly) UIColor * fm_note_Topic_bgGradientColor1;
@property(class, nonatomic, readonly) UIColor * fm_note_Topic_bgGradientColor2;
@property(class, nonatomic, readonly) UIColor * fm_note_arrange_nameTextColor;
@property(class, nonatomic, readonly) UIColor * fm_note_tag_bgColor;
@property(class, nonatomic, readonly) UIColor * fm_note_tag_textColor;
@property(class, nonatomic, readonly) UIColor * fm_note_commenter_nameTextColor;
@property(class, nonatomic, readonly) UIColor * fm_note_listBottom_numColor;

@property(class, nonatomic, readonly) UIColor * fm_noteDetail_comment_bgColor;
@property(class, nonatomic, readonly) UIColor * fm_noteDetail_comment_textColor;

// 直播
@property(class, nonatomic, readonly) UIColor * fm_live_chatTextFromMe_BgColor;


// 个人中心
@property(class, nonatomic, readonly) UIColor * fm_userCenter_welcomeTextColor;

// 大咖
@property(class, nonatomic, readonly) UIColor * fm_daka_tag_bgColor;
@property(class, nonatomic, readonly) UIColor * fm_daka_tag_textColor1;
@property(class, nonatomic, readonly) UIColor * fm_daka_tag_textColor2;
@property(class, nonatomic, readonly) UIColor * fm_daka_tag_borderColor;

// 行情
@property(class, nonatomic, readonly) UIColor * fm_stock_calendar_textDisabledColor;

// 通用
@property(class, nonatomic, readonly) UIColor * fm_sepline_color;
@property(class, nonatomic, readonly) UIColor * fm_F7F7F7_232426;
@property(class, nonatomic, readonly) UIColor * fm_FFFFFF_2E2F33;
@property(class, nonatomic, readonly) UIColor * fm_F7F7F7_2E2F33;


@property(class, nonatomic, readonly) UIColor * fm_BFBFBF_888888;

@end

NS_ASSUME_NONNULL_END
