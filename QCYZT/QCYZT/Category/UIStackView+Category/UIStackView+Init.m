//
//  UIStackView+Init.m
//  QCYZT
//
//  Created by zeng on 2022/10/25.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "UIStackView+Init.h"

@implementation UIStackView (Init)

- (instancetype)initWithAxis:(UILayoutConstraintAxis)axis
                   alignment:(UIStackViewAlignment)alignment
                distribution:(UIStackViewDistribution)distribution
                     spacing:(CGFloat)spacing
            arrangedSubviews:(NSArray *)arrangedSubviews {
    if (arrangedSubviews) {
        self = [self initWithArrangedSubviews:arrangedSubviews];
    } else {
        self = [self init];
    }
    self.axis = axis;
    self.alignment = alignment;
    self.distribution = distribution;
    self.spacing = spacing;
    
    return self;
}

@end
