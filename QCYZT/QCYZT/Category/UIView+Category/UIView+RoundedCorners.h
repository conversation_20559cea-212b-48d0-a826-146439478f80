//
//  UIView+RoundedCorners.h
//  
//
//  Created by <PERSON> on 2020/8/10.
//  Copyright © 2020 macPro. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIView (RoundedCorners)

/// 设置任意边圆角
/// @param bounds 视图bounds
/// @param size   圆角size
/// @param corner 方位参数
- (void)layerAndBezierPathWithRect:(CGRect)bounds cornerRadii:(CGSize)size byRoundingCorners:(UIRectCorner)corner;

//绘制渐背景色
- (void)drawCAGradientWithcolors:(NSArray *)colors;

//指定渐变方向 绘制渐背景色
- (void)drawCAGradientWithcolors:(NSArray *)colors startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint;
@end

NS_ASSUME_NONNULL_END
