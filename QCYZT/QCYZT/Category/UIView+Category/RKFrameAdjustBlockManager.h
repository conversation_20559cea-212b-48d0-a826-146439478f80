//
//  RKFrameAdjustBlockManager.h
//  RKLayoutExample
//
//  Created by <PERSON> on 12/19/11.
//  Copyright (c) 2011 <PERSON>. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@interface RKFrameAdjustBlockManager : NSObject

@property (nonatomic, assign) BOOL insideFrameAdjustBlock;
@property (nonatomic, assign) CGRect frame;

+ (RKFrameAdjustBlockManager*)sharedRKFrameAdjustBlockManager;

@end
