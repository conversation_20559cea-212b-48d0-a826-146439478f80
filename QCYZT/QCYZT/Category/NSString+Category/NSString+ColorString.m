//
//  NSString+ColorString.m
//  QCYZT
//
//  Created by <PERSON> on 2018/7/12.
//  Copyright © 2018年 LZKJ. All rights reserved.
//

#import "NSString+ColorString.h"

@implementation NSString (ColorString)

+ (NSMutableAttributedString *)ls_changeCorlorWithColor:(UIColor *)color TotalString:(NSString *)totalStr SubStringArray:(NSArray *)subArray {
    
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:totalStr];
    for (NSString *rangeStr in subArray) {
        
        NSRange range = [totalStr rangeOfString:rangeStr options:NSBackwardsSearch];
        [attributedStr addAttribute:NSForegroundColorAttributeName value:color range:range];
    }
    
    return attributedStr;
}

- (NSMutableAttributedString *)attrStrWithSubStringColor:(UIColor *)color subStringArray:(NSArray *)subArray {
    if (!self.length) {
        return [[NSMutableAttributedString alloc] initWithString:@""];
    }
    NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:self];
    for (NSString *rangeStr in subArray) {
        NSRange range = [self rangeOfString:rangeStr options:NSBackwardsSearch];
        [attributedStr addAttribute:NSForegroundColorAttributeName value:color range:range];
    }
    
    return attributedStr;
}

- (NSMutableAttributedString *)attrStrWithMatchColor:(UIColor *)color pattern:(NSString *)pattern textFont:(UIFont *)textFont {
    if (!self.length) {
        return [[NSMutableAttributedString alloc] initWithString:@""];
    }
    
    NSMutableAttributedString *attrstr =  [[NSMutableAttributedString alloc] initWithString:self];
    if (!pattern.length) {
        return attrstr;
    }
    
    NSRegularExpression *regex = [[NSRegularExpression alloc] initWithPattern:pattern options:0 error:nil];
    NSArray *results = [regex matchesInString:self options:0 range:NSMakeRange(0, self.length)];
    for (NSTextCheckingResult *result in results) {
        [attrstr addAttribute:NSForegroundColorAttributeName value:color range:result.range];
        if (textFont) {
            [attrstr addAttribute:NSFontAttributeName value:textFont range:result.range];
        }
    }
    
    return attrstr;
}


@end
