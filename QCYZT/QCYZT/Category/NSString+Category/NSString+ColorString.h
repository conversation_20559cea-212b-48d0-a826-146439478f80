//
//  NSString+ColorString.h
//  QCYZT
//
//  Created by <PERSON> on 2018/7/12.
//  Copyright © 2018年 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface NSString (ColorString)

//指定部分字体颜色
+ (NSMutableAttributedString *)ls_changeCorlorWithColor:(UIColor *)color TotalString:(NSString *)totalStr SubStringArray:(NSArray *)subArray;

//指定部分字体颜色
- (NSMutableAttributedString *)attrStrWithSubStringColor:(UIColor *)color subStringArray:(NSArray *)subArray;

// 修改匹配到的字符的颜色
- (NSMutableAttributedString *)attrStrWithMatchColor:(UIColor *)color pattern:(NSString *)pattern textFont:(UIFont *)textFont;

@end



