//
//  NSString+Date.m
//  QCYZT
//
//  Created by <PERSON> on 2019/2/22.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import "NSString+Date.h"
#import "NSDate+ZL.h"

@implementation NSString (Date)

/**
 创建formate格式的NSDateFormatter,使用字典是为了节省NSDateFormatter的性能损耗
 */
- (NSDateFormatter *)dateFormatterFromFormatStr {
    NSMutableDictionary *threadDic = [[NSThread currentThread] threadDictionary];
    NSDateFormatter *dateFormatter = [threadDic objectForKey:self];
    if (!dateFormatter) {
        dateFormatter = [[NSDateFormatter alloc] init];
        [dateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"en_US_POSIX"]];
        dateFormatter.dateFormat = self;
//        dateFormatter.timeZone = [NSTimeZone timeZoneWithAbbreviation:@"UTC"];
        [threadDic setObject:dateFormatter forKey:self];
    }
    
    return dateFormatter;
    
}

+ (NSDate *)dateFromFormatedString:(NSString *)formatedStr format:(NSString *)format {
    if (!format.length || !formatedStr.length) return nil;
    
    NSDateFormatter *dateFormatter = [format dateFormatterFromFormatStr];
    return [dateFormatter dateFromString:formatedStr];
}


+ (NSString *)getNowTimeWithStarTime:(NSInteger)startTime endTime:(NSInteger)endTime {

    NSTimeInterval timeInterval = (endTime - startTime) / 1000.0;
    int days = (int)(timeInterval / (3600*24));
    int hours = (int)((timeInterval - days*3600*24)/3600);
    int minutes = (int)(timeInterval - days*3600*24 - hours*3600)/60;
    int seconds = timeInterval - days*3600*24 - hours*3600 -minutes*60;
    
    NSString *hoursStr;NSString *minutesStr;NSString *secondsStr;NSString *dayStr;
    //天
    if (days < 10) {
        dayStr = [NSString stringWithFormat:@"0%d", days];
    } else {
        dayStr = [NSString stringWithFormat:@"%d",days];
    }
    //小时
    if (hours < 10) {
        hoursStr = [NSString stringWithFormat:@"0%d",hours];
    } else {
        hoursStr = [NSString stringWithFormat:@"%d",hours];
    }
    //分钟
    if(minutes<10)
        minutesStr = [NSString stringWithFormat:@"0%d",minutes];
    else
        minutesStr = [NSString stringWithFormat:@"%d",minutes];
    //秒
    if(seconds < 10)
        secondsStr = [NSString stringWithFormat:@"0%d", seconds];
    else
        secondsStr = [NSString stringWithFormat:@"%d",seconds];
    if (hours<=0 && days <= 0 && minutes<=0 && seconds<=0) {
        return @"00  天  00  时  00  分  00  秒";
    }
    
    return [NSString stringWithFormat:@"%@  天  %@  时  %@  分  %@  秒",dayStr,hoursStr,minutesStr,secondsStr];
}

+ (NSDate *)getNowDateFromatAnDate:(NSDate *)anyDate

{
    //设置源日期时区
    NSTimeZone* sourceTimeZone = [NSTimeZone timeZoneWithAbbreviation:@"UTC"];//或GMT
    //设置转换后的目标日期时区
    NSTimeZone* destinationTimeZone = [NSTimeZone localTimeZone];
    //得到源日期与世界标准时间的偏移量
    NSInteger sourceGMTOffset = [sourceTimeZone secondsFromGMTForDate:anyDate];
    //目标日期与本地时区的偏移量
    NSInteger destinationGMTOffset = [destinationTimeZone secondsFromGMTForDate:anyDate];
    //得到时间偏移量的差值
    
    NSTimeInterval interval = destinationGMTOffset - sourceGMTOffset;
    //转为现在时间
    NSDate* destinationDateNow = [[NSDate alloc] initWithTimeInterval:interval sinceDate:anyDate];
    return destinationDateNow;
}

+(NSDate *)getLocalDateWithDate:(NSDate *)date {
    // 获得系统时区
    NSTimeZone *zone = [NSTimeZone systemTimeZone];
    //得到源日期与世界标准时间的偏移量
    NSInteger interval = [zone secondsFromGMTForDate: date];
    //返回以当前NSDate对象为基准，偏移多少秒后得到的新NSDate对象
    NSDate *localeDate = [date dateByAddingTimeInterval: interval];
    return localeDate;
}







#pragma mark - 时间戳转换
+ (NSString *)getDateFormatByTimestamp:(long long)timestamp liveType:(NSInteger)type
{
    if (timestamp > 0) {
        NSDate *timeDate = [NSDate dateWithTimeIntervalSince1970:timestamp / 1000];
        NSString *timeDateStr = [NSString stringFromDate:timeDate format:@"yyyy-MM-dd HH:mm:ss"];
        NSString *todayStr = [NSString stringFromDate:[NSDate date] format:@"MM-dd"];
        NSString *thisYeartStr = [NSString stringFromDate:[NSDate date] format:@"yyyy"];
        if ([thisYeartStr isEqualToString:[timeDateStr substringWithRange:NSMakeRange(0, 4)]]) {
            // 同一年
            if ([todayStr isEqualToString:[timeDateStr substringWithRange:NSMakeRange(5, 5)]]) {
                // 同一天
                if (type == 1) {
                    return [NSString stringFromDate:timeDate format:@"HH:mm"];
                }
                return [NSString stringFromDate:timeDate format:@"HH:mm:ss"];
            } else {
                // 不同天
                if (type == 1) {
                    return [NSString stringFromDate:timeDate format:@"MM-dd HH:mm"];
                }
                return [NSString stringFromDate:timeDate format:@"MM-dd HH:mm:ss"];
            }
        } else {
            if (type == 1) {
                return [NSString stringFromDate:timeDate format:@"yyyy-MM-dd HH:mm"];
            }
            return timeDateStr;
        }
    } else {
        return @"";
    }
}

+ (NSString *)getDateFormatByTimestamp:(long long)timestamp
{
    NSDate *dat = [NSDate dateWithTimeIntervalSinceNow:0];
    NSTimeInterval nowTimestamp = [dat timeIntervalSince1970] ;
    long long int timeDifference = nowTimestamp - timestamp;
    long long int secondTime = timeDifference;
    long long int minuteTime = secondTime / 60;
    long long int hoursTime = minuteTime / 60;
    long long int dayTime = hoursTime / 24;
    long long int monthTime = dayTime/30;
    long long int yearTime = monthTime/12;
    if (yearTime >= 1) {
        // 超过年
        NSDate *timeDate = [NSDate dateWithTimeIntervalSince1970:timestamp];
        return [NSString stringFromDate:timeDate format:@"yyyy/MM/dd HH:mm"];
    } else if (monthTime >= 1) {
        // 超过一个月
        NSDate *timeDate = [NSDate dateWithTimeIntervalSince1970:timestamp];
        return [NSString stringFromDate:timeDate format:@"MM/dd HH:mm"];
    } else if (dayTime >= 1) {
        // 超过一天
        NSDate *timeDate = [NSDate dateWithTimeIntervalSince1970:timestamp];
        return [NSString stringFromDate:timeDate format:@"MM/dd HH:mm"];
    } else if(1 <= hoursTime) {
        // 超过一小时
        return [NSString stringWithFormat:@"%lld小时前",hoursTime];
    } else {
        return @"刚刚";
    }
}

/// 快讯一图看懂,时间戳
+ (NSString *)getFlashNewsDateFormatByTimestamp:(long long)timestamp
{
    NSDate *targetDate = [NSDate dateWithTimeIntervalSince1970:timestamp];
    NSDate *now = [NSDate date];
    NSCalendar *calendar = [NSCalendar currentCalendar];

    // 判断是否为当天
    if ([calendar isDate:targetDate inSameDayAsDate:now]) {
        // 当天：显示相对时间
        NSTimeInterval timeDifference = [now timeIntervalSinceDate:targetDate];
        long long secondTime = (long long)timeDifference;
        long long minuteTime = secondTime / 60;
        long long hoursTime = minuteTime / 60;

        if (hoursTime >= 1) {
            // >=1小时：显示"*小时前"（舍小数取整）
            return [NSString stringWithFormat:@"%lld小时前", hoursTime];
        } else if (minuteTime >= 1) {
            // 1分钟-1小时：显示"*分钟前"
            return [NSString stringWithFormat:@"%lld分钟前", minuteTime];
        } else {
            // <1分钟：显示"刚刚"
            return @"刚刚";
        }
    }
    // 判断是否为昨天
    else if ([calendar isDateInYesterday:targetDate]) {
        // 昨天：显示"昨天+时分"
        NSString *timeStr = [NSString stringFromDate:targetDate format:@"HH:mm"];
        return [NSString stringWithFormat:@"昨天 %@", timeStr];
    }
    // 判断是否跨年
    else if ([calendar compareDate:targetDate toDate:now toUnitGranularity:NSCalendarUnitYear] != NSOrderedSame) {
        // 跨年：显示"年月日+时分"
        return [NSString stringFromDate:targetDate format:@"yyyy/MM/dd HH:mm"];
    }
    else {
        // 非当天/昨天且未跨年：显示"日期+时分"
        return [NSString stringFromDate:targetDate format:@"MM/dd HH:mm"];
    }
}

@end
