//
//  NSMutableAttributedString+TY.m
//  TYAttributedLabelDemo
//
//  Created by tanyang on 15/4/8.
//  Copyright (c) 2015年 tanyang. All rights reserved.
//

#import "NSMutableAttributedString+TY.h"

@implementation NSMutableAttributedString (TY)

#pragma mark - 文本颜色属性
- (void)addAttributeTextColor:(UIColor*)color
{
    [self addAttributeTextColor:color range:NSMakeRange(0, [self length])];
}

- (void)addAttributeTextColor:(UIColor*)color range:(NSRange)range
{
    if (color.CGColor)
    {
        [self removeAttribute:NSForegroundColorAttributeName range:range];
        
        [self addAttribute:NSForegroundColorAttributeName
                     value:(id)color.CGColor
                     range:range];
    }
    
}

#pragma mark - 文本字体属性
- (void)addAttributeFont:(UIFont *)font
{
    [self addAttributeFont:font range:NSMakeRange(0, [self length])];
}

- (void)addAttributeFont:(UIFont *)font range:(NSRange)range
{
    if (font)
    {
        [self removeAttribute:NSFontAttributeName range:range];
        
        CTFontRef fontRef = CTFontCreateWithName((CFStringRef)font.fontName, font.pointSize, nil);
        if (nil != fontRef)
        {
            [self addAttribute:NSFontAttributeName value:(__bridge id)fontRef range:range];
            CFRelease(fontRef);
        }
    }
}

#pragma mark - 文本字符间隔属性
- (void)addAttributeCharacterSpacing:(unichar)characterSpacing
{
    [self addAttributeCharacterSpacing:characterSpacing range:NSMakeRange(0, self.length)];
}

- (void)addAttributeCharacterSpacing:(unichar)characterSpacing range:(NSRange)range
{
    [self removeAttribute:(id)kCTKernAttributeName range:range];
    
    CFNumberRef num =  CFNumberCreate(kCFAllocatorDefault,kCFNumberSInt8Type,&characterSpacing);
    [self addAttribute:(id)kCTKernAttributeName value:(__bridge id)num range:range];
    CFRelease(num);
}

#pragma mark - 文本下划线属性
- (void)addAttributeUnderlineStyle:(CTUnderlineStyle)style
                 modifier:(CTUnderlineStyleModifiers)modifier
{
    [self addAttributeUnderlineStyle:style
                   modifier:modifier
                      range:NSMakeRange(0, self.length)];
}

- (void)addAttributeUnderlineStyle:(CTUnderlineStyle)style
                 modifier:(CTUnderlineStyleModifiers)modifier
                    range:(NSRange)range
{
    [self removeAttribute:(NSString *)kCTUnderlineColorAttributeName range:range];
    
    if (style != kCTUnderlineStyleNone) {
        [self addAttribute:(NSString *)kCTUnderlineStyleAttributeName
                     value:[NSNumber numberWithInt:(style|modifier)]
                     range:range];
    }
    
}

#pragma mark - 文本段落样式属性
- (void)addAttributeAlignmentStyle:(CTTextAlignment)textAlignment
                    lineSpaceStyle:(CGFloat)linesSpacing
                    lineBreakStyle:(CTLineBreakMode)lineBreakMode
{
    [self addAttributeAlignmentStyle:textAlignment lineSpaceStyle:linesSpacing lineBreakStyle:lineBreakMode range:NSMakeRange(0, self.length)];
}

- (void)addAttributeAlignmentStyle:(CTTextAlignment)textAlignment
                    lineSpaceStyle:(CGFloat)linesSpacing
                    lineBreakStyle:(CTLineBreakMode)lineBreakMode
                             range:(NSRange)range
{
    [self removeAttribute:(id)kCTParagraphStyleAttributeName range:range];
    
    // 创建文本对齐方式
    CTParagraphStyleSetting alignmentStyle;
    alignmentStyle.spec = kCTParagraphStyleSpecifierAlignment;//指定为对齐属性
    alignmentStyle.valueSize = sizeof(textAlignment);
    alignmentStyle.value = &textAlignment;
    
    // 创建文本行间距
    CTParagraphStyleSetting lineSpaceStyle;
    lineSpaceStyle.spec = kCTParagraphStyleSpecifierLineSpacingAdjustment;
    lineSpaceStyle.valueSize = sizeof(linesSpacing);
    lineSpaceStyle.value = &linesSpacing;
    
    //换行模式
    CTParagraphStyleSetting lineBreakStyle;
    lineBreakStyle.spec = kCTParagraphStyleSpecifierLineBreakMode;
    lineBreakStyle.value = &lineBreakMode;
    lineBreakStyle.valueSize = sizeof(lineBreakMode);
    
    // 创建样式数组
    CTParagraphStyleSetting settings[] = {alignmentStyle ,lineSpaceStyle ,lineBreakStyle};
    CTParagraphStyleRef paragraphStyle = CTParagraphStyleCreate(settings, sizeof(settings) / sizeof(settings[0]));	// 设置样式
    
    // 设置段落属性
    [self addAttribute:(id)kCTParagraphStyleAttributeName value:(id)CFBridgingRelease(paragraphStyle) range:range];
}

/// 中间省略的单行富文本
/// @param fixedSuffix 后缀固定显示文本
/// @param textWidth 显示宽度
/// @param font font
- (void)truncateMiddleOfStringWithFixedSuffix:(NSString *)suffix textWidth:(CGFloat)textWidth{
    // 获取固定后缀和省略号的宽度
    UIFont *font = [self attribute:NSFontAttributeName atIndex:0 effectiveRange:nil];
    NSDictionary *attributes = @{NSFontAttributeName: font};
    CGSize fixedSuffixSize = [suffix sizeWithAttributes:attributes];
    CGSize ellipsisSize = [@"..." sizeWithAttributes:attributes];
    CGFloat fixedAndEllipsisWidth = fixedSuffixSize.width + ellipsisSize.width;

    // 从原始字符串中删除固定后缀
    NSString *originalString = self.string;
    NSString *variablePart = [originalString substringToIndex:(originalString.length - suffix.length)];
    NSMutableString *result = [variablePart mutableCopy];

    // 计算可变部分的宽度
    CGFloat variablePartWidth = [result sizeWithAttributes:attributes].width;

    // 如果可变部分加上固定部分和省略号的宽度大于标签宽度，则截断并添加省略号
    while (variablePartWidth + fixedAndEllipsisWidth > textWidth && result.length > 0) {
        [result deleteCharactersInRange:NSMakeRange(result.length - 1, 1)];
        variablePartWidth = [result sizeWithAttributes:attributes].width;
    }

    // 添加省略号和固定后缀
    if (result.length < variablePart.length) {
        [result appendString:@"..."];
    }
    [result appendString:suffix];

    // 更新属性字符串
    [self replaceCharactersInRange:NSMakeRange(0, self.length) withString:result];
}

@end
