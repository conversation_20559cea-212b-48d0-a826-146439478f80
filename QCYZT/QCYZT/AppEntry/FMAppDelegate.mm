//
//  AppDelegate.m
//  QCYZT
//
//  Created by dangfm on 15/8/7.
//  Copyright (c) 2015年 dangfm. All rights reserved.
//
#import "FMPushHelper.h"
#import "FMAppDelegate.h"
#import "FMAppDelegate+UI.h"
#import "FMAppDelegate+AppService.h"
#import "FMIAPTool.h"
#import "CookieManagerTool.h"
#import <SDWebImage/SDImageCache.h>
#import "HttpRequestTool+MemberCenter.h"
#import "HttpRequestTool+Recharge.h"
#import "HttpRequestTool+NoteCourse.h"
#import "ZFLandscapeRotationManager.h"
#import "FMUPDataTool.h"
#import <Bugly/Bugly.h>
#import "FMMessageWindowVC.h"
#import "ZFLandscapeWindow.h"
#import "FMIndicatorImportUtil.h"
#import <UPMarket2/UPMarket2LandscapeUtil.h>
#import <UPMarket2/UPMarket2StockSettingStateManager.h>
#import "FMProgressHUD.h"
#import "UPMarketIndexManager.h"
#import "FMKeyboardDarkModeManager.h"


@interface FMAppDelegate ()<UIApplicationDelegate, AVPictureInPictureControllerDelegate>

@property (nonatomic, assign) BOOL isActiveFromWechatLogin; // 从微信登录授权后返回APP

@property (nonatomic, strong) UIApplication *application;
@property (nonatomic, strong) NSDictionary *launchOptions;
@property (nonatomic, strong) AVPictureInPictureController *pipVC;
@end

@implementation FMAppDelegate
/**
 *  单例
 */
+ (FMAppDelegate *)shareApp {
    return (FMAppDelegate *)[UIApplication sharedApplication].delegate;
}

/// 控制器载入时 所支持的方向
- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window {
    NSString *plistPath = [[NSBundle mainBundle] pathForResource:@"LandScapeVC" ofType:@"plist"];
    NSArray *landscapeVCs = [[NSArray alloc] initWithContentsOfFile:plistPath];
    ZFInterfaceOrientationMask orientationMask = [ZFLandscapeRotationManager supportedInterfaceOrientationsForWindow:window];
    if (orientationMask != ZFInterfaceOrientationMaskUnknow) {
        return (UIInterfaceOrientationMask)orientationMask;
    }
    /// 这里是非播放器VC支持的方向
    if ([landscapeVCs containsObject:NSStringFromClass([[FMHelper getCurrentVC] class])]) {
        if ([NSStringFromClass([[FMHelper getCurrentVC] class]) isEqualToString:@"UPMarket2SDILandscapeController"]) {
            return UIInterfaceOrientationMaskLandscapeLeft | UIInterfaceOrientationMaskLandscapeRight;
        }
        if ([UPMarket2LandscapeUtil isLandscape]) {
            return UIInterfaceOrientationMaskLandscapeLeft | UIInterfaceOrientationMaskLandscapeRight;
        }
        return UIInterfaceOrientationMaskPortrait | UIInterfaceOrientationMaskLandscapeLeft | UIInterfaceOrientationMaskLandscapeRight;
    } else if (window == [FMPopWindowManager shareManager].popWindow) {
        return UIInterfaceOrientationMaskPortrait | UIInterfaceOrientationMaskLandscapeLeft | UIInterfaceOrientationMaskLandscapeRight;
    } else if ([window isKindOfClass:[ZFLandscapeWindow class]]) {
        return UIInterfaceOrientationMaskLandscapeLeft | UIInterfaceOrientationMaskLandscapeRight;
    } else {
        return UIInterfaceOrientationMaskPortrait;
    }
}

#pragma mark -- 系统自带的方法
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    self.application = application;
    self.launchOptions = launchOptions;
    [WXApiManager sharedManager].delegate = self;

    [self judgeDarkModel];
    
    // 配置一些初始化数据
    [self configInitializationData];

    // 配置默认域名
    [self configDefaultRequestPrefix];

    // 调用静默登录
    [self requestSilentContent];

    // 判断显示隐私政策、启动广告等
    [self judgeShowPrivacyPolicyOrLaunchAd];

    // 清空启动图缓存
    [self clearLaunchScreenCache];

    // 内购，处理遗漏订单
    [[FMIAPTool sharIAPTool] addObserverToCheckOmitReceipt];

// 打开内存泄露查看器
//#if DEBUG
//    [[PLeakSniffer sharedInstance] installLeakSniffer];
//#endif

    [Bugly startWithAppId:BuglyId];

    //一创OCR 授权码
    NSUserDefaults * defaultsAuthCodeString = [NSUserDefaults standardUserDefaults];
    if ([defaultsAuthCodeString objectForKey:@"authCode"] == nil) {
        [defaultsAuthCodeString setObject:AuthCodeString forKey:@"authCode"];
    }

    [CookieManagerTool syncLocalCookiesToNSHTTPCookieStorage];


    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkChangedOnline:) name:kNetworkStatusChangedToOnlineNotification object:nil];

    // 启动键盘外观管理器监听
    [FMKeyboardDarkModeManager startMonitoring];

    return YES;
}

// 无网变有网，app启动时会进入一次
- (void)networkChangedOnline:(NSNotification *)noti {
    [FMPushHelper registerAPNS:self.application];

    [[UPMarketIndexManager share] setupForBusiness:UPMarketIndexBusinessAPP1];
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<NSString *,id> *)options {
    if (!url) return NO;
    if ([url.scheme isEqualToString:@"qcyzt"]) {
        [ProtocolJump jumpWithUrl:url.absoluteString];
        return YES;
    } else if ([url.scheme isEqualToString:@"qcy"]) {
        NSRange range = [url.absoluteString rangeOfString:@"qcy"]; // 只替换第一
        [ProtocolJump jumpWithUrl:[url.absoluteString stringByReplacingCharactersInRange:range withString:@"qcyzt"]];
        return YES;
    } else if ([url.scheme isEqualToString:@"file"] && [url.pathExtension isEqualToString:@"tn6"]) {
        [FMIndicatorImportUtil importIndicatorWithUrl:url];
        return YES;
    }

    NSString *absolutUrlStr = url.absoluteString;
    if ([absolutUrlStr rangeOfString:[FMUserDefault getSeting:AppInit_LoginAppid]].location != NSNotFound || [absolutUrlStr rangeOfString:[FMUserDefault getSeting:AppInit_PayAppid]].location != NSNotFound) {
        if ([absolutUrlStr rangeOfString:@"oauth"].location != NSNotFound) {
            self.isActiveFromWechatLogin = YES;
        }

        [WXApi registerApp: [FMUserDefault getSeting:AppInit_LoginAppid] universalLink:UNIVERSAL_LINK]; // 这里注册一下，否则不会走Onsep
        return  [WXApi handleOpenURL:url delegate:[WXApiManager sharedManager]];
    }
    return YES;
}


- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray * _Nullable))restorationHandler {
    // NSUserActivityTypeBrowsingWeb 由Universal Links唤醒的APP
    return [WXApi handleOpenUniversalLink:userActivity delegate:[WXApiManager sharedManager]];
}

#pragma mark 推送注册成功
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)pToken {
    [FMPushHelper registerDeviceToken:pToken];
}

#pragma mark 处理推送信息
-(void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler {
    // 处理静默推送
    if ([userInfo[@"aps"][@"content-available"] intValue] == 1) {
        NSLog(@"收到静默推送: %@", userInfo);
        // 更新数据或执行其他逻辑
    }
    completionHandler(UIBackgroundFetchResultNewData);
}

#pragma mark 注册推送失败
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
    // 一般是系统禁用了推送
    FMLog(@"Regist fail%@",error);
}

- (void)applicationDidReceiveMemoryWarning:(UIApplication *)application
{
    FMLog(@"低内存警告!");
    [[SDImageCache sharedImageCache] clearMemory];
    [[SDWebImageManager sharedManager] cancelAll];
    [[NSURLCache sharedURLCache] removeAllCachedResponses];
}

- (void)applicationWillResignActive:(UIApplication *)application {
    // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
    // Use this method to pause ongoing tasks, disable timers, and throttle down OpenGL ES frame rates. Games should use this method to pause the game.
    FMLog(@"APP将被挂起");
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    // 后台进入前台查询是否充值状态
    [HttpRequestTool queryRechargeResult];
    [HttpRequestTool queryPayMemberProductResult];
    [HttpRequestTool queryWxPayNoteResult];

    FMLog(@"APP即将激活");
    self.isActiveFromWechatLogin = NO;

    [self judgeDarkModel];
}

- (void)judgeDarkModel {
    if ([[FMUserDefault getUnArchiverDataForKey:DarkModeSetting] length]) {
        NSInteger index = [[FMUserDefault getUnArchiverDataForKey:DarkModeSetting] integerValue];
        if (index == 0) { // 自动
            if ([UIScreen mainScreen].traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
                [UPMarket2StockSettingStateManager updateSkinTheme:UPMarket2StockSettingOtherSkinThemeClassicBlack];
            } else {
                [UPMarket2StockSettingStateManager updateSkinTheme:UPMarket2StockSettingOtherSkinThemeSuccinctWhite];
            }
        } else if (index == 1) { // 亮色
            [UPMarket2StockSettingStateManager updateSkinTheme:UPMarket2StockSettingOtherSkinThemeSuccinctWhite];
        } else { // 暗色
            [UPMarket2StockSettingStateManager updateSkinTheme:UPMarket2StockSettingOtherSkinThemeClassicBlack];
        }
    } else {
        [UPMarket2StockSettingStateManager updateSkinTheme:UPMarket2StockSettingOtherSkinThemeSuccinctWhite];
    }

    // 更新键盘外观
    [FMKeyboardDarkModeManager updateAllKeyboardAppearances];
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(100 * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
        if ([AVPictureInPictureController isPictureInPictureSupported] && !self.pipVC.isPictureInPictureActive) {
            [self.pipVC startPictureInPicture];
        }
    });

    [FMUserDefault setArchiverData:[NSDate date] forKey:@"trackUserActivityLastBackgroundTime"];
    FMLog(@"APP已进入后台");
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
    if (self.pipVC.isPictureInPictureActive) {
        [self.pipVC stopPictureInPicture];
    }

    NSDate *lastBackgroundTime = [FMUserDefault getUnArchiverDataForKey:@"trackUserActivityLastBackgroundTime"];
    if (lastBackgroundTime) {
        NSTimeInterval timeSinceLastBackground = [[NSDate date] timeIntervalSinceDate:lastBackgroundTime];

        // 如果时间超过6小时（21600秒）
        if (timeSinceLastBackground > 21600) {
            [FMAnalyticTool trackUserActivity]; // 调用日活统计方法

            // 重置时间标记
            [FMUserDefault setArchiverData:nil forKey:@"trackUserActivityLastBackgroundTime"];
        }
    }
    FMLog(@"APP将进入前台");
}

- (void)pictureInPictureControllerWillStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController {
    NSLog(@"pictureInPictureControllerWillStart...");
}

- (void)pictureInPictureControllerDidStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController {
    // 回调画中画开始
//    [self.pipVC onStartPictureInPicture:nil];

}

- (void)pictureInPictureController:(AVPictureInPictureController *)pictureInPictureController
                                    failedToStartPictureInPictureWithError:(NSError *)error {
    // 画中画开始失败
//    [_delegate onStartPictureInPicture:error];
}

- (void)pictureInPictureControllerWillStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController {
    NSLog(@"pictureInPictureControllerWillStop...");
}

- (void)pictureInPictureControllerDidStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController {
    [self.pipVC stopPictureInPicture];

    // 回调画中画结束
//    [_delegate onStopPictureInPicture];
}

- (void)pictureInPictureController:(AVPictureInPictureController *)pictureInPictureController
restoreUserInterfaceForPictureInPictureStopWithCompletionHandler:(void (^)(BOOL restored))completionHandler {
    completionHandler(true);
    // 恢复全屏界面
}

#pragma mark - WXApiManagerDelegate
// 微信授权回调 - 统一处理
- (void)managerDidRecvAuthResponse:(SendAuthResp *)response {
    // 通过通知转发，确保登录页面能收到回调
    [[NSNotificationCenter defaultCenter] postNotificationName:kWXAuthResponse object:response];
}

// 微信小程序回调 - 统一处理
- (void)managerDidRecvLaunchMiniProgram:(WXLaunchMiniProgramResp *)response {
    // 通过通知转发，确保相关页面能收到回调
    [[NSNotificationCenter defaultCenter] postNotificationName:kWXLaunchMiniProgramResponse object:response];
}

// 从微信启动App
- (void)managerDidRecvLaunchFromWXReq:(LaunchFromWXReq *)request {
    // 通过通知转发
    [[NSNotificationCenter defaultCenter] postNotificationName:kWXLaunchFromWXReq object:request];

    // 原有逻辑保持不变
    NSURL *url = [NSURL URLWithString:request.message.messageExt];
    if (!url) return;
    if ([url.scheme isEqualToString:@"qcyzt"]) {
        [ProtocolJump jumpWithUrl:url.absoluteString];
    } else if ([url.scheme isEqualToString:@"qcy"]) {
        NSRange range = [url.absoluteString rangeOfString:@"qcy"]; // 只替换scheme上的
        [ProtocolJump jumpWithUrl:[url.absoluteString stringByReplacingCharactersInRange:range withString:@"qcyzt"]];
    }
}

- (void)managerDidRecvMessageResponse:(SendMessageToWXResp *)response {
    switch (response.errCode) {
        case WXSuccess: {
            [[NSNotificationCenter defaultCenter] postNotificationName:kShareSuccess object:nil];
        }
            break;

        case WXErrCodeUserCancel: {
            [[NSNotificationCenter defaultCenter] postNotificationName:kShareCancel object:nil];
        }
            break;

        default: {
            [[NSNotificationCenter defaultCenter] postNotificationName:kShareFailed object:nil];
        }
    }
}


/// 配置画中画
- (void)configPiP:(AVPlayerLayer *)layer {
//    //1.判断是否支持画中画功能
//    if ([AVPictureInPictureController isPictureInPictureSupported]) {
//        //2.开启权限
//        @try {
//            NSError *error = nil;
//            [[AVAudioSession sharedInstance] setCategory:AVAudioSessionOrientationBack error:&error];
//            [[AVAudioSession sharedInstance] setActive:YES error:&error];
//        } @catch (NSException *exception) {
//            FMLog(@"AVAudioSession发生错误");
//        }
//        self.pipVC = [[AVPictureInPictureController alloc] initWithPlayerLayer:layer];
//        self.pipVC.delegate = self;
//    }
}


@end

