//
//  FMAppDelegate+AppService.m
//  QCYZT
//
//  Created by zeng on 2021/8/10.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAppDelegate+AppService.h"
#import "FMAppDelegate+UI.h"
#import <AVFoundation/AVFoundation.h>
#import "FMAppDelegate+UI.h"
#import "HttpRequestTool.h"
#import "CDVURLProtocol.h"
#import "NSURLProtocol+WebKitSupport.h"
#import "HttpRequestTool+LoginRegister.h"
#import "HTMLParseTool.h"
#import "HttpRequestTool+Launch.h"
#import "IQKeyboardManager.h"
#import "UPRouterDispatcher.h"
#import "FMTaskConfigModel.h"
#import <MMKV/MMKV.h>
#import "WTVersion.h"
#import <sys/sysctl.h>
#import <sys/stat.h>
#import <dlfcn.h>
#import <mach-o/dyld.h>
#import "FMNetworkStatusMonitor.h"
#import "StatisticsManager.h"

@implementation FMAppDelegate (AppService)

// 配置一些初始化数据
- (void)configInitializationData {
    [NSURLProtocol registerClass:[CDVURLProtocol class]];
    
    // 安全检测
    [self setupSecurityProtection];

    // 开始监听网络状态
    [[FMNetworkStatusMonitor sharedMonitor] startMonitoring];

    //重置播放网络判断
    [FMUserDefault setSeting:@"supportTraffic" Value:@"0"];

    // 设置状态栏颜色为白色
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;

    // UPMarket配置
    [self upMarketConfig];

    // SVProgress设置
    [SVProgressHUD setDefaultMaskType:SVProgressHUDMaskTypeNone];
    [SVProgressHUD setBackgroundColor:ColorWithRGB(104,104,104)];
    [SVProgressHUD setForegroundColor:FMWhiteColor];
    [SVProgressHUD setMinimumDismissTimeInterval:3.0f];

    BOOL isNotFirstLaunch = [[FMUserDefault getSeting:AppNotFirstLaunch] boolValue];
    if (!isNotFirstLaunch) {
        [FMUserDefault setArchiverData:@(1) forKey:kLogoutNeedReminderSyncSelfStocks];
    } else {    //
        // 不是第一次启动时,检测联网权限是否打开. 第一次启动时 不检测, info.plist里的权限配置会自动检测
        [FMNetworkStatusMonitor sharedMonitor].shouldShowAuthorizationAlert = YES;
//        [[FMNetworkStatusMonitor sharedMonitor] checkNetworkAuthorization];
    }

    // 启动全局WebView
    [HTMLParseTool shareInstance];

    // IQKeyboardManager 基础配置
    [self keyboardConfig];

    [MMKV initializeMMKV:@"MMKV"];
    
    // 初始化统计管理器
    [StatisticsManager sharedManager];
}

- (void)upMarketConfig {
#if UP_BUILD_TEST || UP_BUILD_UAT
    if (![@"test" isEqualToString:[[NSUserDefaults standardUserDefaults] stringForKey:@"up_build_type"]]) {
        UPTAFLogFile(@"AppDelegate", @"Test app, Start clean up!");

        [self cleanEnvForTest];
    }

    [[NSUserDefaults standardUserDefaults] setObject:@"test" forKey:@"up_build_type"];
#else
    [[NSUserDefaults standardUserDefaults] setObject:@"pro" forKey:@"up_build_type"];
#endif

    // 记录app的打开时间
    [UPDeviceInfoManager updateLastLaunchTime];

    [UPRouter registerDelegate:[UPRouterDispatcher sharedInstance]];

#if DEBUG
//    [UPBundleResource setDebug:YES];
#endif

    // 初始化TAF
#if DEBUG || UP_BUILD_TEST || UP_BUILD_UAT
    [UPTAFLogger setDebug:true];
//    [UPMarketUILogUtil setDebug:true];
#else
#endif
    [UPTAFManager start];

    // 初始化行情服务
    //TODO: 使用uid
    [UPMarketManager startWithUserId:@""];

    // 初始化MarketUISDK
    [UPMarketUIManager start];

}

// 配置默认的请求地址
- (void)configDefaultRequestPrefix {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setValue:default_contentPrefix forKey:AppInit_contentPrefix];
    [defaults setValue:default_sharePrefix forKey:AppInit_ShareUrlPrefix];
    [defaults setValue:default_hqPrefix forKey:AppInit_HqPrefix];
    [defaults setValue:default_emasPrefix forKey:AppInit_EmasPrefix];
    [defaults synchronize];
}


- (void)keyboardConfig {
    // 设置键盘
    IQKeyboardManager *manager = [IQKeyboardManager sharedManager];
    //默认为YES，关闭为NO
    manager.enable = YES;
    //键盘弹出时，点击背景，键盘收回
    manager.shouldResignOnTouchOutside = YES;
    //如果YES，那么使用textField的tintColor属性为IQToolbar，否则颜色为黑色。默认是否定的。
    manager.shouldToolbarUsesTextFieldTintColor = NO;
    //如果YES，则在IQToolbar上添加textField的占位符文本。默认是肯定的。
    manager.shouldShowToolbarPlaceholder = YES;
    //设置IQToolbar按钮的文字
    manager.toolbarDoneBarButtonItemText = @"完成";
    //隐藏键盘上面的toolBar,默认是开启的
    manager.enableAutoToolbar = YES;
}

- (void)requestAppInitWithSuccess:(void(^)(void))successBlock {
    // 初始化重试计数
    self.appInitRetryCount = 0;
    [self performAppInitRequestWithSuccess:successBlock];
}

- (void)performAppInitRequestWithSuccess:(void(^)(void))successBlock {
    [HttpRequestTool getAppInitStart:^{
    } failure:^{
        [self handleAppInitFailureWithSuccess:successBlock];

        NSString *urlStr = @"/api/v2/silent/init.do";
        NSString *response = [JsonTool jsonStringFromDicOrArr:@[@{@"requestURL": urlStr, @"errmessage": @"网络不给力, 无返回", @"response" : @{}}]];
        [FMAnalyticTool trackRequestResultWithPageName:@"AppEnter" response:response];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSDictionary *data = dic[@"data"];
            NSDictionary *versionDic = data[@"version"];
            if (![versionDic[@"isClose"] boolValue]) {
                [self showVersionUpdateCheck:versionDic];
            }
            [FMUserDefault setAppInitInfoData:data];

            self.appInitRetryCount = 0;
            if (successBlock) {
                successBlock();
            }
        } else {
            [self handleAppInitFailureWithSuccess:successBlock];

            NSString *urlStr = @"/api/v2/silent/init.do";
            NSString *response = [JsonTool jsonStringFromDicOrArr:@[@{@"requestURL": urlStr, @"response" : dic}]];
            [FMAnalyticTool trackRequestResultWithPageName:@"AppEnter" response:response];
        }
    }];
}

- (void)handleAppInitFailureWithSuccess:(void(^)(void))successBlock {
    if (self.appInitRetryCount < 3) {
        self.appInitRetryCount++;
        [self performSelector:@selector(performAppInitRequestWithSuccess:) withObject:successBlock afterDelay:5.0];

        if (self.appInitRetryCount == 1) { // 只在初始化失败第一次时去判断一下版本号
            [WTVersion compareCurrentVersionWithAppStoreVersion:^(NSComparisonResult compareResult) {
                if (compareResult == NSOrderedAscending || compareResult == NSOrderedSame) {
                    // 比AppStore上版本小或者一样
                    [FMUserDefault setSeting:@"isNoAudit" Value:@"1"]; // 不是正在审核
                } else {
                    // 比AppStore上版本大
                    [FMUserDefault setSeting:@"isNoAudit" Value:@"0"]; // 正在审核
                }
            }];
        }
    } else {
        [SVProgressHUD showErrorWithStatus:@"App初始化失败"];
    }
}

/**
 APP启动要调用静默登录接口,通过该接口更新请求头cookie的session信息
 在成功的回调里再去调用获取首页广告的接口(请求头的cookie有正确的session时 首页弹窗广告才能正确返回)
 */
- (void)requestSilentContent {
    if ([FMHelper isLogined]) {
        [HttpRequestTool userLoginAuthByToken:^{
            [[FMPopWindowManager shareManager] getAppLaunchImageAdUrl:YES];
        } authTokenFailureBlock:^{
            [[FMPopWindowManager shareManager] getAppLaunchImageAdUrl:YES];
        }];
    } else {
        BOOL isNotFirstLaunch = [[FMUserDefault getSeting:AppNotFirstLaunch] boolValue];
        if (isNotFirstLaunch) {
            [[FMPopWindowManager shareManager] getAppLaunchImageAdUrl:YES];
        } else {
            [HttpRequestTool getAppDownLoadInfoWithStart:^{
            } failure:^{
            } success:^(NSDictionary *dic) {
            }];
        }
    }
}

#pragma mark - Security Methods

// 配置安全保护
- (void)setupSecurityProtection {
#if DEBUG
    return;
#endif

#ifdef DISABLE_SECURITY_CHECKS
    return;  // 允许临时禁用
#endif

    // 延迟执行安全检测，确保应用已完全初始化
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        @try {
            // 1. 添加安全检查
            if (![NetworkManager checkSecurityEnvironment]) {
                return;
            }

            // 2. 使用更安全的方式检测调试
            if ([self isBeingDebugged]) {
                [self handleSecurityViolation:@"检测到调试行为，应用将退出"];
                return;
            }

            // 3. 启动定时检测
            [self startSecurityTimer];

            // 4. 完整性检查
            [self checkAppIntegrity];
        } @catch (NSException *exception) {
            NSLog(@"安全保护设置异常: %@", exception);
        }
    });
}

// 使用sysctl检测调试状态 - 更安全的实现
- (BOOL)isBeingDebugged {
#if DEBUG
    return NO;  // 调试模式下不检测
#endif

    @try {
        // 使用sysctl检测当前进程是否被调试
        // 比ptrace更安全，不会导致应用崩溃
        int name[4];
        name[0] = CTL_KERN;
        name[1] = KERN_PROC;
        name[2] = KERN_PROC_PID;
        name[3] = getpid();

        struct kinfo_proc info;
        size_t info_size = sizeof(info);

        if (sysctl(name, 4, &info, &info_size, NULL, 0) == -1) {
            return NO;
        }

        return (info.kp_proc.p_flag & P_TRACED) != 0;
    } @catch (NSException *exception) {
        NSLog(@"调试检测异常: %@", exception);
        return NO;
    }
}

// 5. 启动定时安全检测
- (void)startSecurityTimer {
    // 随机间隔检测，增加逆向难度
    NSTimeInterval interval = 5.0 + (arc4random() % 10);

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(interval * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (![NetworkManager checkSecurityEnvironment] || [self isBeingDebugged]) {
            [self handleSecurityViolation:@"检测到安全风险，应用将退出"];
            return;
        }

        [self startSecurityTimer];
    });
}

// 修改应用完整性检查
- (void)checkAppIntegrity {
#if DEBUG
    return; // 调试模式不检查
#endif

    // 检查是否被篡改（这是实际的安全问题）
    BOOL isTampered = [self isAppTampered];

    // 只在应用被篡改时报警
    if (isTampered) {
        [self handleSecurityViolation:@"应用完整性校验失败"];
    }

    // 开发者签名检测仅做日志记录，不触发警告
#ifdef LOG_SECURITY_CHECKS
    BOOL isDevelopmentSigned = [self isAppDevelopmentSigned];
    if (isDevelopmentSigned) {
        NSLog(@"应用使用开发者证书签名");
    }
#endif
}

// 检查是否为开发者证书签名（非 App Store 分发）
- (BOOL)isAppDevelopmentSigned {
#if DEBUG
    return NO;
#endif

    NSString *provisionPath = [[NSBundle mainBundle] pathForResource:@"embedded" ofType:@"mobileprovision"];
    return [[NSFileManager defaultManager] fileExistsAtPath:provisionPath];
}

// 检查应用是否被篡改
- (BOOL)isAppTampered {
    // 检查关键文件的存在性和大小
    NSBundle *mainBundle = [NSBundle mainBundle];
    NSString *bundlePath = [mainBundle bundlePath];
    NSString *infoPath = [mainBundle pathForResource:@"Info" ofType:@"plist"];

    // 检查Info.plist是否存在
    if (![[NSFileManager defaultManager] fileExistsAtPath:infoPath]) {
        return YES;
    }

    // 检查是否可以在应用目录中创建文件（越狱环境可能允许）
    NSString *testPath = [bundlePath stringByAppendingPathComponent:@"integrity_test"];
    BOOL canCreateFile = [@"test" writeToFile:testPath atomically:YES encoding:NSUTF8StringEncoding error:nil];
    if (canCreateFile) {
        [[NSFileManager defaultManager] removeItemAtPath:testPath error:nil];
        return YES;
    }

    return NO;
}

// 7. 处理安全违规情况
- (void)handleSecurityViolation:(NSString *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"安全警告"
                                                                       message:message
                                                                preferredStyle:UIAlertControllerStyleAlert];

        [alert addAction:[UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            exit(0);  // 强制退出应用
        }]];

        [self.window.rootViewController presentViewController:alert animated:YES completion:nil];
    });
}



@end
