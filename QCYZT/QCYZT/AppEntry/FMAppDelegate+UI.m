//
//  FMAppDelegate+UI.m
//  QCYZT
//
//  Created by zeng on 2021/8/10.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAppDelegate+UI.h"
#import "ZLLaunchAdController.h"
#import "FMPrivacyPolicyView.h"
#import "VersionUpdateView.h"
#import "UIWindow+Category.h"
#import "FMFlaseLaunchViewController.h"

@implementation FMAppDelegate (UI)

// 判断显示隐私政策、启动广告等
- (void)judgeShowPrivacyPolicyOrLaunchAd {
    BOOL isNotFirstLaunch = [[FMUserDefault getSeting:AppNotFirstLaunch] boolValue];
    if (!isNotFirstLaunch) { // 第一次启动，显示隐私政策
        FMPrivacyPolicyView *privacyView = [[FMPrivacyPolicyView alloc] init];
        privacyView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
        __weak typeof(privacyView) weakPView = privacyView;
        privacyView.determineBlock = ^{
            [MyKeyChainManager delete:kUserModel];
            [FMUserDefault setSeting:AppNotFirstLaunch Value:@"1"]; // 只有点击过隐私政策确定之后，才算第一次启动完毕
            [[FMPopWindowManager shareManager] dismiss:weakPView];
            
            [self setAppWindow];
            [self setRootViewControllerWithBlock:nil];
        };
        [[FMPopWindowManager shareManager].mutableArr addObject:privacyView];
        [[FMPopWindowManager shareManager] show];
    } else { // 非第一次启动，显示广告
        [self setAppWindow];
        WEAKSELF
        [self setRootViewControllerWithBlock:^{
            NSString *imageBase64Str = [FMUserDefault getSeting:App_lunchImageBase64Data];
            NSString *action = [FMUserDefault getSeting:App_lunchImageAction];
            NSString *expireTime = [FMUserDefault getSeting:App_lunchImageExpireTime];
            if (imageBase64Str.length > 0) {
                NSDate *expireDate = [FMHelper stringToDate:expireTime];
                NSDate *currentDate = [NSDate date];
                if ([currentDate compare:expireDate] == NSOrderedAscending) {
                    [__weakSelf setLaunchImageAd:imageBase64Str action:action];
                }
            }
        }];
    }
}


// 设置window
- (void)setAppWindow {
    self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];
    self.window.backgroundColor = [UIColor whiteColor];
    [self.window makeKeyAndVisible];
    if (@available(iOS 13.0, *)) {
        self.window.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    } else {
        // Fallback on earlier versions
    }
    
    // 全局设置tableView适配
    if (@available(iOS 15.0, *)) {
        [UITableView appearance].sectionHeaderTopPadding = 0;
    }
    
    if (@available(iOS 11.0, *)){
        [[UIScrollView appearance] setContentInsetAdjustmentBehavior:UIScrollViewContentInsetAdjustmentNever];
        
        [[UITableView appearance] setEstimatedRowHeight:0];
        [[UITableView appearance] setEstimatedSectionFooterHeight:0];
        [[UITableView appearance] setEstimatedSectionHeaderHeight:0];
        [[UITableView appearance] setTableFooterView:[[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)]];
    }
    
    [[UIButton appearance] setExclusiveTouch:YES];
    
    //设置默认字体
    NSNumber *scale = [FMUserDefault getUnArchiverDataForKey:FONTSCALE];
    if (!scale) {
        [FMUserDefault setArchiverData:[NSNumber numberWithInt:1.0] forKey:FONTSCALE];
    }
    
    [SVProgressHUD setMaxSupportedWindowLevel:UIWindowLevelStatusBar + 100];
}

// 设置根控制器
- (void)setRootViewControllerWithBlock:(void(^)(void))block {
    // 先设置一个临时的假页面作为root
//    FMFlaseLaunchViewController *vc = [[FMFlaseLaunchViewController alloc] initWithNibName:@"FMFlaseLaunchViewController" bundle:nil];
//    self.window.rootViewController = vc;
    
    if (block) {
        block();
    }
    FMMainTabController *mainTab = [[FMMainTabController alloc] init];
    self.window.rootViewController = mainTab;
    self.main = mainTab;

    // 然后进行网络请求
    [self requestAppInitWithSuccess:nil];
}

// 清除启动图缓存
- (void)clearLaunchScreenCache {
    NSError *error;
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES);
    NSString *documentDirectory = [paths objectAtIndex:0];
    BOOL isDir = NO;
    NSString *component = [NSString stringWithFormat:@"%@/SplashBoard",documentDirectory];
    BOOL existed = [fileManager fileExistsAtPath:component isDirectory:&isDir];
    if (existed && isDir) {
        [NSFileManager.defaultManager removeItemAtPath:[NSString stringWithFormat:@"%@/Library/SplashBoard",NSHomeDirectory()] error:&error];
        if (error) {
            FMLog(@"Failed to delete launch screen cache: %@",error);
        }
    }
}

// 启动页广告点击事件
- (void)setLaunchImageAd:(NSString *)imageBase64Str action:(NSString *)action {
    NSData *imageData = [[NSData alloc] initWithBase64EncodedString:imageBase64Str options:NSDataBase64DecodingIgnoreUnknownCharacters];
    UIImage *image = [UIImage imageWithData:imageData];
    if (!image) {
        return;
    }

    __block UIWindow *window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    window.windowLevel = UIWindowLevelStatusBar + 1;

    ZLLaunchAdController *adVC = [[ZLLaunchAdController alloc] initWithSetAdImageBlock:^(UIImageView *imageView) {
        imageView.image = image;
        imageView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
    } eventBlock:^(ZLLaunchAdCallbackType callbackType) {
        switch (callbackType) {
            case ZLLaunchAdCallbackTypeClickAd: {
                // 先执行通用的操作
                window.hidden = YES;
                window = nil;

                // 然后执行跳转
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [ProtocolJump jumpWithUrl:action];
                });
                break;
            }
                
            case ZLLaunchAdCallbackTypeShowFinish:
            case ZLLaunchAdCallbackTypeClickSkipBtn:
            default: {
                // 倒计时广告结束后或者点击跳过的通用操作
                window.hidden = YES;
                window = nil;
                break;
            }
        }

    }];
    window.rootViewController = adVC;
    window.hidden = NO;
}

- (UIImage *)getLaunchImage {
    UIGraphicsBeginImageContextWithOptions(CGSizeMake(UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT), NO, UIScreen.mainScreen.scale);
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(ctx, FMWhiteColor.CGColor);
    CGContextFillRect(ctx, CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT));

    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    dict[NSFontAttributeName] = [UIFont systemFontOfSize:12.0f];
    dict[NSBackgroundColorAttributeName] = FMClearColor;
    dict[NSForegroundColorAttributeName] = ColorWithHex(0x999999);
    NSString *companyName = @"四川大决策证券投资顾问有限公司";
    CGSize size = [companyName sizeWithAttributes:dict];
    [companyName drawInRect:CGRectMake((UI_SCREEN_WIDTH - size.width) * 0.5, UI_SCREEN_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT - 15 - size.height, size.width, size.height) withAttributes:dict];
    
    UIImage *nameImg = ImageWithName(@"LaunchCompany");
    CGFloat nameImgW = 150;
    CGFloat nameImgH = 26;
    CGFloat nameImgX = (UI_SCREEN_WIDTH - nameImgW) * 0.5;
    CGFloat nameImgY = (UI_SCREEN_HEIGHT - nameImgH) * 0.5;
    [nameImg drawInRect:CGRectMake(nameImgX, nameImgY, nameImgW, nameImgH)];
    
    UIImage *logoImg = ImageWithName(@"LaunchLogo");
    CGFloat logoImgW = 75;
    CGFloat logoImgH = 75;
    CGFloat logoImgX = (UI_SCREEN_WIDTH - logoImgW) * 0.5;
    CGFloat logoImgY = nameImgY - 20 - logoImgH;
    [logoImg drawInRect:CGRectMake(logoImgX, logoImgY, logoImgW, logoImgH)];
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}



- (void)showVersionUpdateCheck:(NSDictionary *)versionDic {
    NSString *runLowestBuildInVersion = [NSString stringWithFormat:@"%@",versionDic[@"lowestCode"]]; // 允许运行的最低版本
    NSString *appStoreNewestBuildInVersion = [NSString stringWithFormat:@"%@",versionDic[@"versionNo"]]; // 服务器上表示的APP版本
    NSString *reminderUpdateBuildInVersion = [NSString stringWithFormat:@"%@",versionDic[@"remindVersionCode"]];//提示更新的版本字段，不能小于最低，不能大于最新
    
    if ([APP_VERSION compare:runLowestBuildInVersion options:NSNumericSearch] == NSOrderedAscending) { // 当前版本小于最低运行版本版本，强制更新
        [self showUpdateBoxViewWithVersionDic:versionDic section:1];
    } else if ([APP_VERSION compare:appStoreNewestBuildInVersion options:NSNumericSearch] == NSOrderedAscending) { // 当前版本小于最新版本，也就是在最低与最新中间
        if ([APP_VERSION compare:reminderUpdateBuildInVersion options:NSNumericSearch] != NSOrderedDescending) { // 如果当前版本小于等于提示更新版本，提示更新
            NSDate *dateNow = [NSDate date];
            NSDate *dateStore = [[NSUserDefaults standardUserDefaults] valueForKey:@"updateTime"];
            if (!dateStore) {  // 第一次存储，提示更新
                [[NSUserDefaults standardUserDefaults] setObject:dateNow forKey:@"updateTime"];
                [[NSUserDefaults standardUserDefaults] synchronize];
                [self showUpdateBoxViewWithVersionDic:versionDic section:0];
            } else if ([dateNow timeIntervalSinceDate:dateStore] >=  60 * 60 * 6) { // 以后相差6小时以上才再次提示
                [[NSUserDefaults standardUserDefaults] setObject:dateNow forKey:@"updateTime"];
                [[NSUserDefaults standardUserDefaults] synchronize];
                [self showUpdateBoxViewWithVersionDic:versionDic section:0];
            }
        }
    }
}

- (void)showUpdateBoxViewWithVersionDic:(NSDictionary *)versionDic section:(NSInteger)section {
    BOOL isMandatory = NO;
    if (section == 1) {
        //强制更新，没有取消按钮
        isMandatory = YES;
    }
    VersionUpdateView *view = [[VersionUpdateView alloc] initIsMandatory:isMandatory  content:versionDic[@"versionMemo"] backImg:ImageWithName(@"updateBgView")];
    [view show];
}


@end
