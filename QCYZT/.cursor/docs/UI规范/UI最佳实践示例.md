# UI最佳实践示例

## 基础组件使用示例

### Label 标准用法
```objc
// 主标题 - 粗体，主色调
UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero 
                                               font:BoldFontWithSize(16) 
                                          textColor:UIColor.up_textPrimaryColor 
                                    backgroundColor:FMClearColor 
                                      numberOfLines:1];
titleLabel.text = @"主标题";

// 副标题 - 普通字体，次色调
UILabel *subtitleLabel = [[UILabel alloc] initWithFrame:CGRectZero 
                                                  font:FontWithSize(14) 
                                             textColor:UIColor.up_textSecondaryColor 
                                       backgroundColor:FMClearColor 
                                         numberOfLines:2];
subtitleLabel.text = @"副标题内容";

// 布局
[self.contentView addSubview:titleLabel];
[self.contentView addSubview:subtitleLabel];
[titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(15);
    make.top.equalTo(10);
    make.right.equalTo(-15);
}];
[subtitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.right.equalTo(titleLabel);
    make.top.equalTo(titleLabel.mas_bottom).offset(5);
}];
```

### Button 标准用法
```objc
// 主要按钮 - 使用主色调
UIButton *primaryBtn = [[UIButton alloc] initWithFrame:CGRectZero
                                                 font:FontWithSize(16)
                                        normalTextColor:FMWhiteColor
                                        backgroundColor:FMNavColor
                                                 title:@"确定"
                                                 image:nil
                                                target:self
                                                action:@selector(primaryAction:)];
UI_View_Radius(primaryBtn, 5);

```

### ImageView 标准用法示例
```objc
// 头像ImageView - 圆形
UIImageView *avatarImageView = [[UIImageView alloc] init];
avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
[avatarImageView sd_setImageWithURL:[NSURL URLWithString:model.avatarUrl] 
                   placeholderImage:ImageWithName(@"avatar_placeholder")];
[self.contentView addSubview:avatarImageView];
[avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(@15);
    make.top.equalTo(@10);
    make.width.height.equalTo(@40);
}];
UI_View_Radius(avatarImageView, 20); // 圆形头像

// 图标ImageView - 固定尺寸
UIImageView *iconImageView = [[UIImageView alloc] initWithImage:ImageWithName(@"icon_arrow")];
iconImageView.contentMode = UIViewContentModeScaleAspectFit;
```

## TableView Cell 示例

### 标准Cell实现
```objc
// Cell初始化方法
- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 头像
    self.avatarImageView = [[UIImageView alloc] init];
    self.avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    [self.contentView addSubview:self.avatarImageView];
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@10);
        make.width.height.equalTo(@40);
    }];
    UI_View_Radius(self.avatarImageView, 20);
    
    // 主标题
    self.titleLabel = [[UILabel alloc] initWithFrame:CGRectZero
                                                font:BoldFontWithSize(16)
                                           textColor:UIColor.up_textPrimaryColor
                                     backgroundColor:FMClearColor
                                       numberOfLines:1];
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImageView.mas_right).offset(10);
        make.top.equalTo(@10);
        make.right.equalTo(@-15);
    }];
    
    // 副标题
    self.subtitleLabel = [[UILabel alloc] initWithFrame:CGRectZero
                                                   font:FontWithSize(14)
                                              textColor:UIColor.up_textSecondaryColor
                                        backgroundColor:FMClearColor
                                          numberOfLines:1];
    [self.contentView addSubview:self.subtitleLabel];
    [self.subtitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(5);
        make.bottom.equalTo(@-10);
    }];
    
    // 分割线
    [self.contentView addSepLineWithBlock:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.bottom.equalTo(@0);
        make.height.equalTo(@0.5);
    }];
}

// 数据设置
- (void)configureWithModel:(MyModel *)model {
    self.titleLabel.text = model.title;
    self.subtitleLabel.text = model.subtitle;
    [self.avatarImageView sd_setImageWithURL:[NSURL URLWithString:model.avatarUrl]
                            placeholderImage:ImageWithName(@"avatar_placeholder")];
}
```

### TableView控制器使用
```objc
// TableView初始化
- (void)setupTableView {
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero
                                                 style:UITableViewStyleGrouped
                                              delegate:self
                                            dataSource:self
                                        viewController:self];
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(@0);
    }];
    
    [self.tableView registerCellClass:[MyCustomCell class]];
}

// Cell配置
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    MyCustomCell *cell = [tableView reuseCellClass:[MyCustomCell class]];
    MyModel *model = self.dataArray[indexPath.row];
    [cell configureWithModel:model];
    return cell;
}
```

## 最佳实践要点

### 性能优化
1. **合理使用圆角**：避免在滚动视图中频繁设置圆角
2. **阴影优化**：考虑使用图片阴影替代Core Animation阴影
3. **图片缓存**：使用SDWebImage等库进行网络图片缓存

### 代码规范
1. **使用分类方法**：必须使用项目提供的UI组件分类方法
2. **统一命名**：遵循项目命名规范
3. **注释清晰**：为复杂布局添加必要注释

### 布局技巧
1. **优先使用StackView**：简化复杂布局
2. **约束优化**：避免约束冲突，使用优先级
3. **响应式设计**：考虑不同屏幕尺寸的适配 