# UI规范快速索引

## 🚀 字体宏速查

| 宏定义 | 用途 |
|--------|------|
| `FontWithSize(size)` | 普通字体 |
| `BoldFontWithSize(size)` | 粗体字体 |
| `MediumFontWithSize(size)` | 中等字体 |
| `kFontNumber(size)` | 数字字体 |
| `[FMHelper scaleFont:size]` | 支持大字模式普通字体 |
| `[FMHelper scaleBoldFont:size]` | 支持大字模式粗体 |

## 🎨 颜色宏速查

| 宏定义 | 用途 |
|--------|------|
| `ColorWithRGB(r, g, b)` | RGB颜色 |
| `ColorWithHex(0xRRGGBB)` | 十六进制颜色 |
| `ColorWithHexAlpha(0xRRGGBB, alpha)` | 带透明度十六进制 |
| `FMClearColor` / `FMWhiteColor` / `FMNavColor` | 基础颜色 |
| `UIColor.up_textPrimaryColor` / `UIColor.up_textSecondaryColor` | 文本颜色 |
| `UIColor.up_riseColor` / `UIColor.up_fallColor` / `UIColor.up_equalColor`  | 股价颜色 |

## 🔧 UI工具方法速查

| 方法 | 用途 |
|------|------|
| `UI_View_Radius(view, radius)` | 圆角 |
| `UI_View_Border(view, width, color)` | 边框 |
| `UI_View_BorderRadius(view, radius, width, color)` | 圆角+边框 |
| `[view addSepLineWithBlock:]` | 分割线 |
| `[view drawCAGradientWithcolors:startPoint:endPoint:]` | 渐变背景 |
| `ImageWithName(@"name")` | 本地图片 |

## 📱 UI组件方法速查

| 组件 | 分类初始化方法 |
|------|----------------|
| UILabel | `initWithFrame:font:textColor:backgroundColor:numberOfLines:` |
| UIButton | `initWithFrame:font:normalTextColor:backgroundColor:title:image:target:action:` |
| UITableView | `initWithFrame:style:delegate:dataSource:viewController:` |
| UIStackView | `initWithAxis:alignment:distribution:spacing:arrangedSubviews:` |

## 📅 日期处理速查

| 方法 | 用途 |
|------|------|
| `[date dateStringWithFormatString:]` | Date转字符串 |
| `[NSDate dateFromFormatedString:format:]` | 字符串转Date |
| `[dateStr dateWithFormat:target:]` | 格式转换 |

## 📐 Masonry简写速查

| 简写 | 等价于 |
|------|--------|
| `equalTo()` | `mas_equalTo` |
| `offset()` | `mas_offset` |
| `greaterThanOrEqualTo()` | `mas_greaterThanOrEqualTo` |
| `lessThanOrEqualTo()` | `mas_lessThanOrEqualTo` |

## 🔍 TableView方法速查

| 方法 | 用途 |
|------|------|
| `[tableView registerCellClass:]` | 注册Cell |
| `[tableView reuseCellClass:]` | 复用Cell |

## 📋 常见错误速查

| ❌ 错误 | ✅ 正确 |
|---------|---------|
| `[UIFont systemFontOfSize:]` | `FontWithSize()` |
| `[UIColor blackColor]` | `UIColor.up_textPrimaryColor` |
| `[[UILabel alloc] init]` | 使用分类初始化方法 |
| `[[NSDateFormatter alloc] init]` | `[date dateStringWithFormatString:]` |
| `make.left.mas_equalTo(@15)` | `make.left.equalTo(@15)` |
| `view.layer.cornerRadius = 5` | `UI_View_Radius(view, 5)` |

## 📖 详细文档导航

- **[UI组件API参考](UI组件API参考.md)** - 组件分类方法详细说明
- **[UI工具方法参考](UI工具方法参考.md)** - 工具方法详细说明
- **[UI最佳实践示例](UI最佳实践示例.md)** - 完整代码示例 