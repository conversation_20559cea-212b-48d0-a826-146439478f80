# UI工具方法参考

## 视图样式设置

### 圆角设置
```objc
// 仅设置圆角
UI_View_Radius(view, radius);

// 示例
UI_View_Radius(button, 5);
UI_View_Radius(imageView, 20);
```

### 边框设置
```objc
// 仅设置边框
UI_View_Border(view, borderWidth, borderColor);

// 示例
UI_View_Border(view, 1, UIColor.up_textSecondaryColor);
UI_View_Border(cardView, 0.5, FMSepLineColor);
```

### 圆角+边框组合
```objc
// 同时设置圆角和边框
UI_View_BorderRadius(view, radius, borderWidth, borderColor);

// 示例
UI_View_BorderRadius(cardView, 8, 1, FMSepLineColor);
UI_View_BorderRadius(inputView, 5, 1, UIColor.up_textSecondaryColor);
```

### 特定位置圆角
```objc
// 设置特定位置的圆角
[view layerAndBezierPathWithRect:view.bounds 
                    cornerRadii:CGSizeMake(radius, radius) 
              byRoundingCorners:corners];

// 常用圆角位置组合
UIRectCornerTopLeft | UIRectCornerTopRight      // 顶部圆角
UIRectCornerBottomLeft | UIRectCornerBottomRight // 底部圆角
UIRectCornerAllCorners                           // 全部圆角

// 示例：只设置顶部圆角。  需要该view的frame已经确定
[headerView layerAndBezierPathWithRect:headerView.bounds 
                          cornerRadii:CGSizeMake(10, 10) 
                    byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
```

## 分割线工具

### 标准分割线
```objc
// 使用分类方法添加分割线
UIView *sepLine = [containerView addSepLineWithBlock:^(MASConstraintMaker *make) {
    // 约束设置
}];
```

## 渐变背景

### 渐变背景设置
```objc
// 创建渐变背景
NSArray *colors = @[(__bridge id)startColor.CGColor, 
                   (__bridge id)endColor.CGColor];
[view drawCAGradientWithcolors:colors 
                    startPoint:startPoint 
                      endPoint:endPoint];
```

### 常见渐变方向
```objc
// 垂直渐变（从上到下）
CGPointMake(0, 0), CGPointMake(0, 1)

// 水平渐变（从左到右）  
CGPointMake(0, 0), CGPointMake(1, 0)

// 对角渐变
CGPointMake(0, 0), CGPointMake(1, 1)

// 示例：创建垂直渐变
NSArray *colors = @[(__bridge id)ColorWithHex(0xFFF4E8).CGColor, 
                   (__bridge id)ColorWithHex(0xFFDBAF).CGColor];
[gradientView drawCAGradientWithcolors:colors 
                            startPoint:CGPointMake(0, 0) 
                              endPoint:CGPointMake(0, 1)];
```

## 按钮增强功能

### 按钮背景色设置
```objc
// 设置按钮背景色（自动处理高亮效果）
[button setButtonBackGroundColor:normalColor];

// 设置按钮背景色和自定义高亮色
[button setButtonBackGroundColor:normalColor 
                  highLightColor:highlightColor];

// 示例
[confirmBtn setButtonBackGroundColor:FMNavColor];
[cancelBtn setButtonBackGroundColor:FMBgColor 
                     highLightColor:ColorWithHex(0xE0E0E0)];
```

## 阴影效果

### 常见阴影设置
```objc
// 卡片阴影效果
view.layer.shadowColor = ColorWithHexAlpha(0x000000, 0.1).CGColor;
view.layer.shadowOffset = CGSizeMake(0, 2);
view.layer.shadowOpacity = 1;
view.layer.shadowRadius = 4;

// 按钮阴影效果
button.layer.shadowColor = ColorWithHexAlpha(0x000000, 0.15).CGColor;
button.layer.shadowOffset = CGSizeMake(0, 1);
button.layer.shadowOpacity = 1;
button.layer.shadowRadius = 2;

// 浮动阴影效果
floatingView.layer.shadowColor = ColorWithHexAlpha(0x000000, 0.2).CGColor;
floatingView.layer.shadowOffset = CGSizeMake(0, 4);
floatingView.layer.shadowOpacity = 1;
floatingView.layer.shadowRadius = 8;
```

## 使用注意事项

1. **性能考虑**：频繁设置圆角和阴影会影响性能，适当使用
2. **一致性**：同类型效果使用相同的工具方法和参数
3. **可维护性**：使用宏定义而非硬编码数值
4. **兼容性**：注意不同iOS版本的兼容性
5. **内存管理**：合理使用渐变和阴影效果，避免过度使用 