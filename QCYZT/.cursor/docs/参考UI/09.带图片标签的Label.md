# 带图片标签的Label

### UI 设计图

<img src="images/带图片的Label.jpg" alt="带图片的 Label" style="zoom:50%;" />

### 页面结构分析

这是一个自适应高度的Cell，根据文本内容自动调整高度。结构分为4个Label：上面两个Label前面都需要展示图片（问题和回答的图标），使用YYLabel实现图文混排；底部包含日期（UILabel）和标签（传闻属实/不属实，使用ZLTagLabel实现）。

### 组件说明

- **YYLabel**: 用于实现图文混排效果，分别用于问题和回答内容的展示
- **UILabel**: 用于时间日期的显示
- **ZLTagLabel**: 自定义标签控件，用于显示"传闻属实"或"传闻不属实"状态
- **NSAttributedString**: 用于创建富文本，实现图片与文字的混排

### 代码示例

#### Model层
```objc
@interface FMStockDetailInteractionModel : NSObject

@property (nonatomic, copy) NSString *problemstatement; // 问题陈述
@property (nonatomic, copy) NSString *problemreply; // 问题回复
@property (nonatomic, assign) long long replydate; // 回复日期
@property (nonatomic, assign) NSInteger trueandfalse; // 真伪状态：1-属实，2-不属实

@end
```

#### View层
```objc
@interface FMStockDetailInteractionCell : UITableViewCell
@property (nonatomic, strong) FMStockDetailInteractionModel *model;
@end

@implementation FMStockDetailInteractionCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // 问题标签
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(15);
        make.right.equalTo(-15);
    }];
    
    // 回答内容
    [self.contentView addSubview:self.contentLabel];
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(10);
    }];
    
    // 日期标签
    [self.contentView addSubview:self.timeLabel];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(self.contentLabel.mas_bottom).offset(10);
        make.bottom.equalTo(-13.5);
    }];
    
    // 真伪标签
    [self.contentView addSubview:self.tagLabel];
    [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.timeLabel);
        make.right.equalTo(15);
    }];
    
    // 分割线
    [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.bottom.equalTo(0);
        make.height.equalTo(1);
    }];
}

- (void)setModel:(FMStockDetailInteractionModel *)model {
    // 创建问题图文混排
    UIImage *img1 = ImageWithName(@"stock_interactionQuestion");
    NSAttributedString *iconString1 = [NSAttributedString yy_attachmentStringWithContent:img1 
                                                                             contentMode:UIViewContentModeCenter 
                                                                         attachmentSize:img1.size 
                                                                            alignToFont:BoldFontWithSize(15) 
                                                                             alignment:YYTextVerticalAlignmentCenter];
    NSMutableAttributedString *titleAttrStr = [[NSMutableAttributedString alloc] initWithString:model.problemstatement.length ? model.problemstatement : @""];
    [titleAttrStr insertAttributedString:iconString1 atIndex:0];
    titleAttrStr.yy_font = BoldFontWithSize(15);
    titleAttrStr.yy_lineSpacing = 3;
    titleAttrStr.yy_color = ColorWithHex(0x333333);
    self.titleLabel.attributedText = titleAttrStr;
    
    // 创建回答图文混排
    UIImage *img2 = ImageWithName(@"stock_interactionAnswer");
    NSAttributedString *iconString2 = [NSAttributedString yy_attachmentStringWithContent:img2 
                                                                             contentMode:UIViewContentModeCenter 
                                                                         attachmentSize:img2.size 
                                                                            alignToFont:FontWithSize(15) 
                                                                             alignment:YYTextVerticalAlignmentCenter];
    NSMutableAttributedString *contentAttrStr = [[NSMutableAttributedString alloc] initWithString:model.problemreply.length ? model.problemreply : @""];
    [contentAttrStr insertAttributedString:iconString2 atIndex:0];
    contentAttrStr.yy_font = FontWithSize(15);
    contentAttrStr.yy_lineSpacing = 3;
    contentAttrStr.yy_color = ColorWithHex(0x333333);
    self.contentLabel.attributedText = contentAttrStr;
    
    // 设置日期
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.replydate / 1000.0];
    self.timeLabel.text = [date dateStringWithFormatString:@"yyyy-MM-dd"];
    
    // 根据真伪状态设置标签
    if (model.trueandfalse == 1) {
        self.tagLabel.text = @"传闻属实";
        self.tagLabel.hidden = NO;
        self.tagLabel.textColor = ColorWithHex(0x0074fa);
        self.tagLabel.backgroundColor = ColorWithHex(0xe0eeff);
    } else if (model.trueandfalse == 2) {
        self.tagLabel.text = @"传闻不属实";
        self.tagLabel.hidden = NO;
        self.tagLabel.textColor = FMNavColor;
        self.tagLabel.backgroundColor = ColorWithHex(0xffebeb);
    } else {
        self.tagLabel.hidden = YES;
    }
}

- (YYLabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[YYLabel alloc] init];
        _titleLabel.numberOfLines = 0;
        _titleLabel.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 30;
    }
    
    return _titleLabel;
}

- (YYLabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[YYLabel alloc] init];
        _contentLabel.textColor = ColorWithHex(0x333333);
        _contentLabel.font = FontWithSize(15);
        _contentLabel.numberOfLines = 0;
        _contentLabel.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 30;
    }
    
    return _contentLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x999999) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    }
    
    return _timeLabel;
}

- (ZLTagLabel *)tagLabel {
    if (!_tagLabel) {
        _tagLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x0074FA) backgroundColor:ColorWithHex(0xe0eeff) numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _tagLabel.widthPadding = 10;
        _tagLabel.heightPadding = 4;
        UI_View_Radius(_tagLabel, 2);
    }
    
    return _tagLabel;
}

@end
```

### 注意事项
- 使用YYLabel实现图文混排，需要注意图片大小与文字基线对齐
- 使用NSAttributedString的yy_attachmentStringWithContent方法创建图片附件
- 图片和文字的对齐方式需要通过YYTextVerticalAlignment枚举设置
- 对于可能为空的文本内容，需要进行空值检查，避免显示问题
- 使用自定义标签时，需要设置合适的内边距和圆角效果

### 版本记录
| 版本 | 日期 | 修改说明 |
|-----|------|---------|
| 1.0 | 2023-10-01 | 初始版本 |
```

