# 一行多个Label显示

### UI 设计图

<img src="images/一行多个Label.png" alt="一行多个Label" style="zoom:50%;" />

### 页面结构分析

这是一个UITableView上的3种Cell，每个Cell占据一个Section。中签结果和公司概况的Cell结构类似，包含多行，每行分别有左右两个Label。为了复用这种布局，自定义了TwoLabelView组件。中签结果、公司概况每行使用一个TwoLabelView，而发行信息Cell的每行使用2个并排的TwoLabelView。

### 组件说明

- **FMTwoLabelView**: 核心自定义组件，包含左右两个Label，支持灵活配置左Label宽度和右Label左边距
- **UIStackView**: 用于垂直排列多个TwoLabelView，实现自适应高度
- **UITableView**: 主容器，分为多个Section展示不同类型的信息
- **FMF10TwoLabelModel**: 数据模型，包含左右两边的文本内容

### 代码示例

#### Model层
```objc
@interface FMF10TwoLabelModel : NSObject
@property (nonatomic, copy) NSString *left;
@property (nonatomic, copy) id right; // 支持NSString或NSAttributedString
@end

@interface FMIPOCalendarApplyDetailModel : NSObject
@property (nonatomic, strong) FMIPOSharesItemDto *sharesItemDto; // 股票发行信息
@property (nonatomic, strong) FMCompanyBriefInfoDto *companyBriefInfoDto; // 公司简介信息
@property (nonatomic, assign) long long systemTime; // 系统时间
@end
```

#### View层
```objc
// TwoLabelView - 核心复用组件
@interface FMTwoLabelView : UIView
@property (nonatomic, strong) UILabel *leftLabel;
@property (nonatomic, strong) UILabel *rightLabel;
// 左边 label 宽度约束值，默认为95。如果传 0，则 deactive宽度约束
@property (nonatomic, assign) CGFloat leftLabelWidth;
// 右边 label 距离左边的约束，默认为115。如果传 0，则deactive左侧约束
@property (nonatomic, assign) CGFloat rightLabelLeftSpace;
@end

@implementation FMTwoLabelView

- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    UILabel *leftLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:0];
    [self addSubview:leftLabel];
    [leftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(0);
        make.top.equalTo(@0);
        self.leftLableWidthConstraint = make.width.equalTo(95);
        make.bottom.lessThanOrEqualTo(0);
    }];
    self.leftLabel = leftLabel;
    
    UILabel *rightLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor: ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self addSubview:rightLabel];
    [rightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(0);
        make.top.equalTo(0);
        self.rightLabelLeftConstraint = make.left.equalTo(115);
        make.bottom.lessThanOrEqualTo(0);
    }];
    self.rightLabel = rightLabel;
}

- (void)setLeftLabelWidth:(CGFloat)leftLabelWidth {
    _leftLabelWidth = leftLabelWidth;
    
    if (leftLabelWidth > 0) {
        [self.leftLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(leftLabelWidth);
        }];
        [self.leftLableWidthConstraint activate];
    } else {
        [self.leftLableWidthConstraint deactivate];
    }
}

- (void)setRightLabelLeftSpace:(CGFloat)rightLabelLeftSpace {
    _rightLabelLeftSpace = rightLabelLeftSpace;
    
    if (rightLabelLeftSpace > 0) {
        [self.rightLabelLeftConstraint activate];
        [self.rightLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(rightLabelLeftSpace);
        }];
    } else {
        [self.rightLabelLeftConstraint deactivate];
    }
}
@end

// 中签结果Cell - 垂直布局多个TwoLabelView
@interface FMIPOCalendarApplyDetailWinningResultCell : UITableViewCell
@property (nonatomic, strong) NSArray<FMF10TwoLabelModel *> *models;
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UIButton *noResultBtn;
@end

@implementation FMIPOCalendarApplyDetailWinningResultCell

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    [self.contentView addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(0);
        make.left.right.equalTo(0);
        make.bottom.equalTo(-15);
    }];
}

- (void)setModels:(NSArray<FMF10TwoLabelModel *> *)models {
    _models = models;
    
    [self.stackView.arrangedSubviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    if (!models.count) {
        // 无数据时显示提示按钮
        [self.stackView addArrangedSubview:self.noResultBtn];
        [self.noResultBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(50);
            make.width.equalTo(UI_SCREEN_WIDTH - 30);
        }];
    } else {
        // 有数据时遍历创建TwoLabelView
        for (NSInteger i = 0; i < self.models.count; i++) {
            FMTwoLabelView *innerView = [[FMTwoLabelView alloc] init];
            innerView.leftLabel.textColor = ColorWithHex(0x888888);
            innerView.leftLabel.font = FontWithSize(12);
            innerView.rightLabel.textColor = ColorWithHex(0x333333);
            innerView.rightLabel.font = BoldFontWithSize(12);
            
            innerView.leftLabel.text = self.models[i].left;
            // 支持字符串或富文本
            if ([self.models[i].right isKindOfClass:[NSString class]]) {
                innerView.rightLabel.text = self.models[i].right;
            } else {
                innerView.rightLabel.attributedText = self.models[i].right;
            }
            [self.stackView addArrangedSubview:innerView];
            [innerView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.equalTo(UI_SCREEN_WIDTH);
            }];
        }
    }
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentCenter distribution:UIStackViewDistributionEqualSpacing spacing:10 arrangedSubviews:nil];
    }
    return _stackView;
}
@end

// 发行信息Cell - 多个TwoLabelView网格排列
@interface FMIPOCalendarApplyDetailInfoCell : UITableViewCell
@property (nonatomic, strong) NSArray<NSString *> *titles;
@property (nonatomic, strong) NSArray *datas;
@end

@implementation FMIPOCalendarApplyDetailInfoCell

- (void)setTitles:(NSArray<NSString *> *)titles {
    _titles = titles;
    
    // 清空现有视图
    [self.contentView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    // 计算每个TwoLabelView的尺寸和位置
    CGFloat viewWidth = (UI_SCREEN_WIDTH - 45) * 0.5;
    CGFloat viewHeight = 16;
    CGFloat viewVerticalSpacing = 10;
    
    // 创建并布局每个TwoLabelView
    for (NSInteger i = 0; i < self.titles.count; i++) {
        FMTwoLabelView *view = [[FMTwoLabelView alloc] init];
        view.leftLabel.font = FontWithSize(12);
        view.rightLabel.font = BoldFontWithSize(12);
        view.leftLabelWidth = 0;
        view.rightLabelLeftSpace = 0;
        [self.contentView addSubview:view];
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo((i % 2) ? (viewWidth + 30) : 15);
            make.top.equalTo((i / 2) * (viewHeight + viewVerticalSpacing));
            make.width.equalTo(viewWidth);
            make.height.equalTo(viewHeight);
        }];
        view.leftLabel.text = self.titles[i];
        
        // 设置最后一个元素的底部约束
        if (i == self.titles.count - 1) {
            [view mas_makeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(-15);
            }];
        }
    }
}

- (void)setDatas:(NSArray *)datas {
    _datas = datas;
    
    // 更新数据但不重新创建视图
    for (NSInteger i = 0; i < self.contentView.subviews.count; i++) {
        FMTwoLabelView *view = self.contentView.subviews[i];
        if ([datas[i] isKindOfClass:[NSAttributedString class]]) {
            view.rightLabel.attributedText = datas[i];
        } else {
            view.rightLabel.text = datas[i];
        }
    }
}
@end
```

#### Controller层
```objc
@interface FMIPOCalendarApplyDetailViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) FMIPOCalendarApplyDetailModel *model;
@property (nonatomic, strong) NSMutableArray<FMF10TwoLabelModel *> *winResultTwoLabelModles;
@property (nonatomic, strong) NSMutableArray<FMF10TwoLabelModel *> *companyTwoLabelModles;
@property (nonatomic, copy) NSString *stockCode;

@end

@implementation FMIPOCalendarApplyDetailViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"申购详情";
    self.view.backgroundColor = FMWhiteColor;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 0, UI_SAFEAREA_BOTTOM_HEIGHT, 0));
    }];
    
    [self requestData];
}

// TableView数据源方法
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (self.model) {
        return 4;
    }
    return 0;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        // 基本信息Cell
        FMIPOCalendarApplyDetailInfoCell *cell = [tableView reuseCellClass:[FMIPOCalendarApplyDetailInfoCell class]];
        cell.titles = @[@"申购代码", @"发行价", @"发行市盈率", @"行业市盈率", @"发行方式", @"所属行业"];
        // 设置对应数据...
        return cell;
    } else if (indexPath.section == 1) {
        // 发行信息Cell
        FMIPOCalendarApplyDetailInfoCell *cell = [tableView reuseCellClass:[FMIPOCalendarApplyDetailInfoCell class]];
        cell.titles = @[@"发行总数", @"申购上限", @"网上发行量", @"顶格申购"];
        // 设置对应数据...
        return cell;
    } else if (indexPath.section == 2) {
        // 中签结果Cell
        FMIPOCalendarApplyDetailWinningResultCell *cell = [tableView reuseCellClass:[FMIPOCalendarApplyDetailWinningResultCell class]];
        cell.models = self.winResultTwoLabelModles;
        return cell;
    } else {
        // 公司概况Cell
        FMF10CompanyLeftRightInfoCell *cell = [tableView reuseCellClass:[FMF10CompanyLeftRightInfoCell class]];
        cell.models = self.companyTwoLabelModles;
        return cell;
    }
}

// 处理中签结果和公司信息数据
- (void)dealData {
    // 处理中签结果数据...
    // 处理公司信息数据...
}
@end
```

### 注意事项
- FMTwoLabelView的左右Label约束可以灵活配置，通过leftLabelWidth和rightLabelLeftSpace属性控制
- 使用UIStackView垂直排列多个TwoLabelView，可以自动处理高度，避免手动计算
- 对于网格布局（多列显示），需要手动计算每个TwoLabelView的位置
- 右侧Label可以支持普通文本和富文本，处理不同的数据类型
- 考虑无数据情况下的UI处理，如中签结果为空时显示提示按钮

### 版本记录
| 版本 | 日期 | 修改说明 |
|-----|------|---------|
| 1.0 | 2023-10-01 | 初始版本 |
