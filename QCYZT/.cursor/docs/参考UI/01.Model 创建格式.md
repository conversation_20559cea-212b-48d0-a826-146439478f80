### 模型命名

模型采用驼峰命名，前缀加上FM。参考示例：FMStockDetailInteractionModel

### 模型内属性命名

驼峰命名，time或者date这种属性用long long接收; id用NSString *接收， 并用modelCustomPropertyMapper转成非Xcode关键词; is开头属性用BOOL接收； 

### 嵌套处理

使用 YYModel 的modelContainerPropertyGenericClass方法处理

### 参考示例

```objc
@interface CompanyBaseInfoDto : NSObject

@property (nonatomic, copy) NSString *area;

@property (nonatomic, copy) NSString *briefIntroText;

...

@end

@interface CompanyLeader : NSObject

@property (nonatomic, copy) NSString *age;

@property (nonatomic, copy) NSString *annualReward;

@property (nonatomic, assign) BOOL isLimited;  

...

@end

@interface CompanyIpoDto : NSObject

@property (nonatomic, assign) long long listDate; // 上市日期

@property (nonatomic, copy) NSString *companyID; // 公司 id

@property (nonatomic, strong) NSArray<NSString *> *issueMethods; // 发行方式

@property (nonatomic, strong) NSArray<NSString *> *pricingModels; // 发行定价方式

...

@end

@interface FMF10CompanyOverViewModel : NSObject

@property (nonatomic, strong) CompanyBaseInfoDto *baseInfoDto;

@property (nonatomic, strong) CompanyIpoDto *ipoDto;

@property (nonatomic, copy) NSArray<CompanyLeader *> *leaders;

@end

@implementation CompanyLeader

@end

@implementation CompanyBaseInfoDto

@end

@implementation CompanyIpoDto

@end

@implementation FMF10CompanyOverViewModel

+ (NSDictionary *)modelCustomPropertyMapper {

return @{@"companyID":@"id"};

}

+ (NSDictionary *)modelContainerPropertyGenericClass{

return @{

@"baseInfoDto" : CompanyBaseInfoDto.class,

@"ipoDto" : CompanyIpoDto.class,

@"leaders" : CompanyLeader.class

};

}

@end
```



