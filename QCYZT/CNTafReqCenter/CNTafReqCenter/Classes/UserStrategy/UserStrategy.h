// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/UserStrategy.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>
@interface DFDCombinationReq : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_id;
@property (nonatomic, strong) NSString* jce_name;
@property (nonatomic, strong) NSString* jce_content;
@property (nonatomic, strong) NSString* jce_userId;
@property (nonatomic, strong) NSString* jce_condition;
@property (nonatomic, assign) JceInt32 jce_dateType;
@property (nonatomic, strong) NSString* jce_scence;
@property (nonatomic, strong) NSString* jce_factorIds;
@end

@interface DFDCombinationRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_id;
@property (nonatomic, strong) NSString* jce_name;
@property (nonatomic, strong) NSString* jce_content;
@property (nonatomic, strong) NSString* jce_userId;
@property (nonatomic, strong) NSString* jce_condition;
@property (nonatomic, assign) JceInt32 jce_dateType;
@property (nonatomic, strong) NSString* jce_factorIds;
@property (nonatomic, strong) NSArray<NSString*>* jce_factorList;
@end

@interface DFDListCombinationRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<DFDCombinationRsp*>* jce_list;
@end



