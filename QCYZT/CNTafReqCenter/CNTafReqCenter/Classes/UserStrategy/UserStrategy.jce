module DFD
{

    struct CombinationReq
    {
        0 optional int id;
        1 optional string name;
        2 optional string content;
        3 optional string userId;
        4 optional string condition;
        5 optional int dateType; //时间类型，0-单个时间，1-时间区间
    };

    struct CombinationRsp
    {
        0 optional int id;
        1 optional string name;
        2 optional string content;
        3 optional string userId;
        4 optional string condition;
        5 optional int dateType;
    };

    struct ListCombinationRsp
    {
        0 optional vector<CombinationRsp> list;
    };

    interface UserStrategy
    {
        //增加组合
        int addCombination(CombinationReq stReq);

        //获取所有组合
        int listCombination(CombinationReq stReq, out ListCombinationRsp stRsp);

        //修改我的组合
        int editCombination(CombinationReq stReq);

        //删除我的组合
        int deleteCombination(CombinationReq stReq);
    };
};