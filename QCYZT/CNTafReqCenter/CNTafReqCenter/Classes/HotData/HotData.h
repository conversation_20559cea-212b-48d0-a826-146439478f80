// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/HotData.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>

enum {
    DG_SceneType_LHZY = 0
};
#define DG_SceneType NSInteger

@interface DGRecordStockCountReq : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_code;
@end

@interface DGRecordStockCountRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_result;
@end

@interface DGQClientInfo : UPTAFJceObject
@property (nonatomic, strong) NSData* jce_vGuid;
@property (nonatomic, strong) NSString* jce_sXua;
@end

@interface DGFuzzySearchReq : UPTAFJceObject
@property (nonatomic, strong) DGQClientInfo* jce_stClient;
@property (nonatomic, strong) NSString* jce_condition;
@property (nonatomic, assign) JceInt32 jce_sceneType;
@property (nonatomic, assign) JceInt32 jce_date;
@property (nonatomic, assign) JceInt32 jce_offset;
@property (nonatomic, assign) JceInt32 jce_limit;
@end

@interface DGFColumnDescription : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_name;
@property (nonatomic, assign) JceInt16 jce_fieldType;
@property (nonatomic, assign) JceInt16 jce_fieldIndex;
@end

@interface DGFRowData : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_market;
@property (nonatomic, strong) NSString* jce_code;
@property (nonatomic, strong) NSArray<NSString*>* jce_vStrColumn;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vDblColumn;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vIntColumn;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vLongColumn;
@end

@interface DGFuzzySearchRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@property (nonatomic, strong) NSArray<DGFColumnDescription*>* jce_schemaInfo;
@property (nonatomic, strong) NSArray<DGFRowData*>* jce_vStock;
@property (nonatomic, assign) JceInt32 jce_totalNum;
@end

@interface DGSearchReq : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_options;
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@property (nonatomic, strong) NSString* jce_fields;
@property (nonatomic, assign) JceInt32 jce_offset;
@property (nonatomic, assign) JceInt32 jce_limit;
@property (nonatomic, strong) NSString* jce_sortField;
@property (nonatomic, assign) JceBool jce_sortDesc;
@property (nonatomic, strong) NSArray<NSString*>* jce_hqStockList;
@property (nonatomic, strong) DGQClientInfo* jce_stClient;
@property (nonatomic, strong) NSString* jce_collection;
@property (nonatomic, strong) NSString* jce_codes;
@property (nonatomic, strong) NSString* jce_fuzzyOptions;
@end

@interface DGFPoolData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@property (nonatomic, strong) NSArray<DGFColumnDescription*>* jce_schemaInfo;
@property (nonatomic, strong) NSArray<DGFRowData*>* jce_vStock;
@property (nonatomic, assign) JceInt32 jce_totalNum;
@end

@interface DGCompleteFieldsReq : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@property (nonatomic, strong) DGFPoolData* jce_fPoolData;
@property (nonatomic, strong) NSArray<NSString*>* jce_baseFields;
@property (nonatomic, strong) NSString* jce_options;
@end



