// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/HotData.jce'
// **********************************************************************

#ifndef DGHotDataAgent_h
#define DGHotDataAgent_h

#import "HotData.h"

#import <UPTAF/TAFRequest.h>

// MARK: DGHotDataRecordStockCount
@interface DGHotDataRecordStockCountRequest : UPTAFRequest
@property (nonatomic, strong) DGRecordStockCountReq* stReq;
@end

@interface DGHotDataRecordStockCountResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) DGRecordStockCountRsp* stRsp;
@end

// MARK: DGHotDataFuzzySearch
@interface DGHotDataFuzzySearchRequest : UPTAFRequest
@property (nonatomic, strong) DGFuzzySearchReq* stReq;
@end

@interface DGHotDataFuzzySearchResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) DGFuzzySearchRsp* stRsp;
@end

// MARK: DGHotDataSearch
@interface DGHotDataSearchRequest : UPTAFRequest
@property (nonatomic, strong) DGSearchReq* stReq;
@end

@interface DGHotDataSearchResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) DGFPoolData* stRsp;
@end

// MARK: DGHotDataAgent
@interface DGHotDataAgent : NSObject
@property (nonatomic, copy) NSString * servantName;

- (instancetype)initWithServantName:(NSString *)servantName;

- (DGHotDataRecordStockCountRequest*)newRecordStockCountRequest:(DGRecordStockCountReq*)stReq ;
- (DGHotDataFuzzySearchRequest*)newFuzzySearchRequest:(DGFuzzySearchReq*)stReq ;
- (DGHotDataSearchRequest*)newSearchRequest:(DGSearchReq*)stReq ;
@end

#endif
