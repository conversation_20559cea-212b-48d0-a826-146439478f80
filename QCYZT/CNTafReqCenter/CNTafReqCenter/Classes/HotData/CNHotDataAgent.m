//
//  CNHotDataAgent.m
//  CNTafReqCenter
//
//  Created by 彭继宗 on 2024/4/19.
//

#import "CNHotDataAgent.h"
#import "DGHotDataAgent.h"

@implementation CNHotDataAgent

+ (void)requestHotSearchDataForReq:(DGFuzzySearchReq *)req result:(void (^)(DGFuzzySearchRsp * _Nonnull))result
{
    WeakSelf(weakSelf);
    DGHotDataAgent *agent = [[DGHotDataAgent alloc] initWithServantName:@"hot_data"];
   
    [[agent newFuzzySearchRequest:req] enqueue:^(UPTAFResponse *response) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (response.isSuccessful) {
                DGHotDataFuzzySearchResponse *rsp = response.result;
                !result?:result(rsp.stRsp);
            }else{
                !result?:result(nil);
            }
        });
    }];
}

@end
