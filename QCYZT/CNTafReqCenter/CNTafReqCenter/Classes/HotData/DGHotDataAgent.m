// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/HotData.jce'
// **********************************************************************

#import "DGHotDataAgent.h"

// MARK: DGHotDataRecordStockCount
@implementation DGHotDataRecordStockCountRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"recordStockCount";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    DGHotDataRecordStockCountResponse * response = [[DGHotDataRecordStockCountResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: DGRecordStockCountRsp.class];
    return response;
}
@end

@implementation DGHotDataRecordStockCountResponse 
@end

// MARK: DGHotDataFuzzySearch
@implementation DGHotDataFuzzySearchRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"fuzzySearch";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    DGHotDataFuzzySearchResponse * response = [[DGHotDataFuzzySearchResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: DGFuzzySearchRsp.class];
    return response;
}
@end

@implementation DGHotDataFuzzySearchResponse 
@end

// MARK: DGHotDataSearch
@implementation DGHotDataSearchRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"search";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    DGHotDataSearchResponse * response = [[DGHotDataSearchResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: DGFPoolData.class];
    return response;
}
@end

@implementation DGHotDataSearchResponse 
@end

// MARK: DGHotDataAgent
@implementation DGHotDataAgent
- (instancetype)initWithServantName:(NSString *)servantName {
    self = [super init];
    if (self) {
        self.servantName = servantName;
    }
    return self;
}

- (DGHotDataRecordStockCountRequest*)newRecordStockCountRequest:(DGRecordStockCountReq*)stReq {
    DGHotDataRecordStockCountRequest * request = [[DGHotDataRecordStockCountRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (DGHotDataFuzzySearchRequest*)newFuzzySearchRequest:(DGFuzzySearchReq*)stReq {
    DGHotDataFuzzySearchRequest * request = [[DGHotDataFuzzySearchRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (DGHotDataSearchRequest*)newSearchRequest:(DGSearchReq*)stReq {
    DGHotDataSearchRequest * request = [[DGHotDataSearchRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
@end
