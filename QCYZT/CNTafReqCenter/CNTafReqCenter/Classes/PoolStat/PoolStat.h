// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Documents/code/dongniu-ios/Tools/PoolStat.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>

enum {
    MarketIndicatorSys_Pool_ID_Type_PT_FORMULA = 0,
    MarketIndicatorSys_Pool_ID_Type_PT_UPS = 1,
    MarketIndicatorSys_Pool_ID_Type_PT_FUPAN_HIS = 2
};
#define MarketIndicatorSys_Pool_ID_Type NSInteger


enum {
    MarketIndicatorSys_PoolSortType_PST_BY_MAX_CHG_RATE = 0,
    MarketIndicatorSys_PoolSortType_PST_BY_CODE = 1,
    MarketIndicatorSys_PoolSortType_PST_BY_DATE = 2
};
#define MarketIndicatorSys_PoolSortType NSInteger

@interface MarketIndicatorSysPoolIDInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_type;
@property (nonatomic, assign) JceInt32 jce_id;
@property (nonatomic, strong) NSString* jce_upsname;
@end

@interface MarketIndicatorSysRangeStatInfo : UPTAFJceObject
@property (nonatomic, assign) JceDouble jce_beginPrice;
@property (nonatomic, assign) JceDouble jce_highPrice;
@property (nonatomic, assign) JceDouble jce_lastPrice;
@property (nonatomic, assign) JceFloat jce_chgRate;
@end

@interface MarketIndicatorSysStockStat : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_market;
@property (nonatomic, strong) NSString* jce_code;
@property (nonatomic, strong) NSString* jce_name;
@property (nonatomic, strong) NSString* jce_industryCode;
@property (nonatomic, strong) NSString* jce_industryName;
@property (nonatomic, strong) NSDictionary<NSNumber*, MarketIndicatorSysRangeStatInfo*>* jce_mapStatInfo;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mapExtendStr;
@end

@interface MarketIndicatorSysLastestStockStat : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_market;
@property (nonatomic, strong) NSString* jce_code;
@property (nonatomic, strong) NSString* jce_name;
@property (nonatomic, strong) NSString* jce_industryCode;
@property (nonatomic, strong) NSString* jce_industryName;
@property (nonatomic, assign) JceDouble jce_price;
@property (nonatomic, assign) JceFloat jce_chgRatio;
@end

@interface MarketIndicatorSysIndexPoolStat : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_choosenDate;
@property (nonatomic, strong) NSArray<MarketIndicatorSysStockStat*>* jce_vStockStat;
@end

@interface MarketIndicatorSysMapPoolStat : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, MarketIndicatorSysIndexPoolStat*>* jce_mapData;
@end

@interface MarketIndicatorSysRankingListItem : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_choosenDate;
@property (nonatomic, strong) MarketIndicatorSysStockStat* jce_stockStat;
@end

@interface MarketIndicatorSysGetLatestPoolReq : UPTAFJceObject
@property (nonatomic, strong) MarketIndicatorSysPoolIDInfo* jce_poolId;
@property (nonatomic, assign) JceInt32 jce_maxNum;
@end

@interface MarketIndicatorSysGetLatestPoolRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@property (nonatomic, strong) NSArray<MarketIndicatorSysLastestStockStat*>* jce_vStock;
@end

@interface MarketIndicatorSysGetPoolStatReq : UPTAFJceObject
@property (nonatomic, strong) MarketIndicatorSysPoolIDInfo* jce_poolId;
@property (nonatomic, assign) JceInt32 jce_lastestDate;
@property (nonatomic, assign) JceInt32 jce_wantDates;
@property (nonatomic, assign) JceInt32 jce_maxNumPerDay;
@property (nonatomic, strong) NSArray<NSNumber*>* jce_vStatDays;
@end

@interface MarketIndicatorSysGetPoolStatRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<MarketIndicatorSysIndexPoolStat*>* jce_vPoolStat;
@end

@interface MarketIndicatorSysRankingListReq : UPTAFJceObject
@property (nonatomic, strong) MarketIndicatorSysPoolIDInfo* jce_poolId;
@property (nonatomic, assign) JceInt32 jce_minDaysBefore;
@property (nonatomic, assign) JceInt32 jce_maxDaysBefore;
@property (nonatomic, assign) JceInt16 jce_wantNum;
@property (nonatomic, assign) JceInt32 jce_sortPeriod;
@property (nonatomic, assign) JceBool jce_removeDuplicate;
@end

@interface MarketIndicatorSysRankingListRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<MarketIndicatorSysRankingListItem*>* jce_vRankingItem;
@end

@interface MarketIndicatorSysBaiduSearchReq : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_keyword;
@end

@interface MarketIndicatorSysBaiduSearchRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<NSString*>* jce_keywords;
@end

@interface MarketIndicatorSysStockInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@end

@interface MarketIndicatorSysPoolTraceReq : UPTAFJceObject
@property (nonatomic, strong) NSArray<MarketIndicatorSysStockInfo*>* jce_vStock;
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@end

@interface MarketIndicatorSysPoolTraceItem : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@property (nonatomic, assign) JceInt32 jce_poolId;
@property (nonatomic, strong) NSString* jce_poolName;
@property (nonatomic, assign) JceDouble jce_maxIncreaseRatio20;
@property (nonatomic, assign) JceDouble jce_maxIncreaseRatio5;
@end

@interface MarketIndicatorSysPoolTraceRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@property (nonatomic, strong) NSArray<MarketIndicatorSysPoolTraceItem*>* jce_items;
@end

@interface MarketIndicatorSysHotStockInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_market;
@property (nonatomic, strong) NSString* jce_code;
@property (nonatomic, strong) NSString* jce_name;
@property (nonatomic, assign) JceInt32 jce_choosenDate;
@property (nonatomic, strong) NSString* jce_poolName;
@property (nonatomic, strong) NSString* jce_poolId;
@property (nonatomic, assign) JceDouble jce_maxRise;
@end

@interface MarketIndicatorSysHotStockReq : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_wantNum;
@end

@interface MarketIndicatorSysHotStockRsp : UPTAFJceObject
@property (nonatomic, strong) NSArray<MarketIndicatorSysHotStockInfo*>* jce_vStock;
@end



