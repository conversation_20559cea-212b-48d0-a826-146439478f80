// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Documents/code/dongniu-ios/Tools/PoolStat.jce'
// **********************************************************************

#include "PoolStat.h"

@implementation MarketIndicatorSysPoolIDInfo

@synthesize jce_type = J_PROP_NM(o,1,type);
@synthesize jce_id = J_PROP_NM(o,2,id);
@synthesize jce_upsname = J_PROP_NM(o,3,upsname);

- (id)init
{
    if (self = [super init]) {
        J_PROP(upsname) = DefaultJceString;
    }
    return self;
}

@end

@implementation MarketIndicatorSysRangeStatInfo

@synthesize jce_beginPrice = J_PROP_NM(o,1,beginPrice);
@synthesize jce_highPrice = J_PROP_NM(o,2,highPrice);
@synthesize jce_lastPrice = J_PROP_NM(o,3,lastPrice);
@synthesize jce_chgRate = J_PROP_NM(o,4,chgRate);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation MarketIndicatorSysStockStat

@synthesize jce_market = J_PROP_NM(o,1,market);
@synthesize jce_code = J_PROP_NM(o,2,code);
@synthesize jce_name = J_PROP_NM(o,3,name);
@synthesize jce_industryCode = J_PROP_NM(o,4,industryCode);
@synthesize jce_industryName = J_PROP_NM(o,5,industryName);
@synthesize jce_mapStatInfo = J_PROP_EX(o,6,mapStatInfo,M09ONSNumberOMarketIndicatorSysRangeStatInfo);
@synthesize jce_mapExtendStr = J_PROP_EX(o,7,mapExtendStr,M09ONSStringONSString);

- (id)init
{
    if (self = [super init]) {
        J_PROP(code) = DefaultJceString;
        J_PROP(name) = DefaultJceString;
        J_PROP(industryCode) = DefaultJceString;
        J_PROP(industryName) = DefaultJceString;
    }
    return self;
}

@end

@implementation MarketIndicatorSysLastestStockStat

@synthesize jce_market = J_PROP_NM(o,1,market);
@synthesize jce_code = J_PROP_NM(o,2,code);
@synthesize jce_name = J_PROP_NM(o,3,name);
@synthesize jce_industryCode = J_PROP_NM(o,4,industryCode);
@synthesize jce_industryName = J_PROP_NM(o,5,industryName);
@synthesize jce_price = J_PROP_NM(o,6,price);
@synthesize jce_chgRatio = J_PROP_NM(o,7,chgRatio);

- (id)init
{
    if (self = [super init]) {
        J_PROP(code) = DefaultJceString;
        J_PROP(name) = DefaultJceString;
        J_PROP(industryCode) = DefaultJceString;
        J_PROP(industryName) = DefaultJceString;
    }
    return self;
}

@end

@implementation MarketIndicatorSysIndexPoolStat

@synthesize jce_choosenDate = J_PROP_NM(o,1,choosenDate);
@synthesize jce_vStockStat = J_PROP_EX(o,2,vStockStat,VOMarketIndicatorSysStockStat);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation MarketIndicatorSysMapPoolStat

@synthesize jce_mapData = J_PROP_EX(o,1,mapData,M09ONSNumberOMarketIndicatorSysIndexPoolStat);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation MarketIndicatorSysRankingListItem

@synthesize jce_choosenDate = J_PROP_NM(o,1,choosenDate);
@synthesize jce_stockStat = J_PROP_NM(o,2,stockStat);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation MarketIndicatorSysGetLatestPoolReq

@synthesize jce_poolId = J_PROP_NM(o,1,poolId);
@synthesize jce_maxNum = J_PROP_NM(o,2,maxNum);

- (id)init
{
    if (self = [super init]) {
        J_PROP(maxNum) = 10;
    }
    return self;
}

@end

@implementation MarketIndicatorSysGetLatestPoolRsp

@synthesize jce_tradeDate = J_PROP_NM(o,1,tradeDate);
@synthesize jce_vStock = J_PROP_EX(o,2,vStock,VOMarketIndicatorSysLastestStockStat);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation MarketIndicatorSysGetPoolStatReq

@synthesize jce_poolId = J_PROP_NM(o,1,poolId);
@synthesize jce_lastestDate = J_PROP_NM(o,2,lastestDate);
@synthesize jce_wantDates = J_PROP_NM(o,3,wantDates);
@synthesize jce_maxNumPerDay = J_PROP_NM(o,4,maxNumPerDay);
@synthesize jce_vStatDays = J_PROP_EX(o,5,vStatDays,VONSNumber);

- (id)init
{
    if (self = [super init]) {
        J_PROP(wantDates) = 1;
        J_PROP(maxNumPerDay) = 10;
    }
    return self;
}

@end

@implementation MarketIndicatorSysGetPoolStatRsp

@synthesize jce_vPoolStat = J_PROP_EX(o,1,vPoolStat,VOMarketIndicatorSysIndexPoolStat);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation MarketIndicatorSysRankingListReq

@synthesize jce_poolId = J_PROP_NM(o,1,poolId);
@synthesize jce_minDaysBefore = J_PROP_NM(o,2,minDaysBefore);
@synthesize jce_maxDaysBefore = J_PROP_NM(o,3,maxDaysBefore);
@synthesize jce_wantNum = J_PROP_NM(o,4,wantNum);
@synthesize jce_sortPeriod = J_PROP_NM(o,5,sortPeriod);
@synthesize jce_removeDuplicate = J_PROP_NM(o,6,removeDuplicate);

- (id)init
{
    if (self = [super init]) {
        J_PROP(minDaysBefore) = 1;
        J_PROP(maxDaysBefore) = 20;
        J_PROP(wantNum) = 10;
        J_PROP(sortPeriod) = 5;
        J_PROP(removeDuplicate) = NO;
    }
    return self;
}

@end

@implementation MarketIndicatorSysRankingListRsp

@synthesize jce_vRankingItem = J_PROP_EX(o,1,vRankingItem,VOMarketIndicatorSysRankingListItem);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation MarketIndicatorSysBaiduSearchReq

@synthesize jce_keyword = J_PROP_NM(r,0,keyword);

- (id)init
{
    if (self = [super init]) {
        J_PROP(keyword) = DefaultJceString;
    }
    return self;
}

@end

@implementation MarketIndicatorSysBaiduSearchRsp

@synthesize jce_keywords = J_PROP_EX(r,0,keywords,VONSString);

- (id)init
{
    if (self = [super init]) {
        J_PROP(keywords) = DefaultJceArray;
    }
    return self;
}

@end

@implementation MarketIndicatorSysStockInfo

@synthesize jce_shtMarket = J_PROP_NM(o,0,shtMarket);
@synthesize jce_sCode = J_PROP_NM(o,1,sCode);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sCode) = DefaultJceString;
    }
    return self;
}

@end

@implementation MarketIndicatorSysPoolTraceReq

@synthesize jce_vStock = J_PROP_EX(r,0,vStock,VOMarketIndicatorSysStockInfo);
@synthesize jce_tradeDate = J_PROP_NM(r,1,tradeDate);

- (id)init
{
    if (self = [super init]) {
        J_PROP(vStock) = DefaultJceArray;
    }
    return self;
}

@end

@implementation MarketIndicatorSysPoolTraceItem

@synthesize jce_shtMarket = J_PROP_NM(o,0,shtMarket);
@synthesize jce_sCode = J_PROP_NM(o,1,sCode);
@synthesize jce_sName = J_PROP_NM(o,2,sName);
@synthesize jce_tradeDate = J_PROP_NM(o,3,tradeDate);
@synthesize jce_poolId = J_PROP_NM(o,4,poolId);
@synthesize jce_poolName = J_PROP_NM(o,5,poolName);
@synthesize jce_maxIncreaseRatio20 = J_PROP_NM(o,6,maxIncreaseRatio20);
@synthesize jce_maxIncreaseRatio5 = J_PROP_NM(o,7,maxIncreaseRatio5);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sCode) = DefaultJceString;
        J_PROP(sName) = DefaultJceString;
        J_PROP(poolName) = DefaultJceString;
    }
    return self;
}

@end

@implementation MarketIndicatorSysPoolTraceRsp

@synthesize jce_tradeDate = J_PROP_NM(o,0,tradeDate);
@synthesize jce_items = J_PROP_EX(o,1,items,VOMarketIndicatorSysPoolTraceItem);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation MarketIndicatorSysHotStockInfo

@synthesize jce_market = J_PROP_NM(o,0,market);
@synthesize jce_code = J_PROP_NM(o,1,code);
@synthesize jce_name = J_PROP_NM(o,2,name);
@synthesize jce_choosenDate = J_PROP_NM(o,3,choosenDate);
@synthesize jce_poolName = J_PROP_NM(o,4,poolName);
@synthesize jce_poolId = J_PROP_NM(o,5,poolId);
@synthesize jce_maxRise = J_PROP_NM(o,6,maxRise);

- (id)init
{
    if (self = [super init]) {
        J_PROP(code) = DefaultJceString;
        J_PROP(name) = DefaultJceString;
        J_PROP(poolName) = DefaultJceString;
        J_PROP(poolId) = DefaultJceString;
    }
    return self;
}

@end

@implementation MarketIndicatorSysHotStockReq

@synthesize jce_wantNum = J_PROP_NM(o,0,wantNum);

- (id)init
{
    if (self = [super init]) {
        J_PROP(wantNum) = 10;
    }
    return self;
}

@end

@implementation MarketIndicatorSysHotStockRsp

@synthesize jce_vStock = J_PROP_EX(o,0,vStock,VOMarketIndicatorSysHotStockInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end



