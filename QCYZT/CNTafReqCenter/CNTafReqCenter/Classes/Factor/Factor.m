// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/Factor.jce'
// **********************************************************************

#include "Factor.h"

@implementation HQSysAppStockInfo

@synthesize jce_shtSetcode = J_PROP_NM(o,1,shtSetcode);
@synthesize jce_sCode = J_PROP_NM(o,2,sCode);
@synthesize jce_sName = J_PROP_NM(o,3,sName);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sCode) = DefaultJceString;
        J_PROP(sName) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFHeaderInfo

@synthesize jce_vGuid = J_PROP_NM(o,1,vGuid);
@synthesize jce_sXua = J_PROP_NM(o,2,sXua);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sXua) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFactorInfo

@synthesize jce_strJceBuf = J_PROP_NM(o,1,strJceBuf);
@synthesize jce_strUrl = J_PROP_NM(o,2,strUrl);
@synthesize jce_intWidth = J_PROP_NM(o,3,intWidth);
@synthesize jce_intHeight = J_PROP_NM(o,4,intHeight);
@synthesize jce_vJceBuf = J_PROP_NM(o,5,vJceBuf);
@synthesize jce_strLhbUrl = J_PROP_NM(o,6,strLhbUrl);

- (id)init
{
    if (self = [super init]) {
        J_PROP(strJceBuf) = DefaultJceString;
        J_PROP(strUrl) = DefaultJceString;
        J_PROP(strLhbUrl) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppMarginTrade

@synthesize jce_iDate = J_PROP_NM(o,1,iDate);
@synthesize jce_dBuyValue = J_PROP_NM(o,2,dBuyValue);
@synthesize jce_dBuyReturn = J_PROP_NM(o,3,dBuyReturn);
@synthesize jce_dBuyBalance = J_PROP_NM(o,4,dBuyBalance);
@synthesize jce_lSellValue = J_PROP_NM(o,5,lSellValue);
@synthesize jce_lSellReturn = J_PROP_NM(o,6,lSellReturn);
@synthesize jce_lSellBalanceVol = J_PROP_NM(o,7,lSellBalanceVol);
@synthesize jce_dSellBalance = J_PROP_NM(o,8,dSellBalance);
@synthesize jce_dMarginBalance = J_PROP_NM(o,9,dMarginBalance);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppExcraInfo

@synthesize jce_iDate = J_PROP_NM(o,1,iDate);
@synthesize jce_iTypeCode = J_PROP_NM(o,2,iTypeCode);
@synthesize jce_lTradeNum = J_PROP_NM(o,3,lTradeNum);
@synthesize jce_lTradeAmount = J_PROP_NM(o,4,lTradeAmount);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppSecTradeInfo

@synthesize jce_dPrice = J_PROP_NM(o,1,dPrice);
@synthesize jce_lTradeVol = J_PROP_NM(o,2,lTradeVol);
@synthesize jce_dTradeAmt = J_PROP_NM(o,3,dTradeAmt);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppSecTradeDay

@synthesize jce_iDate = J_PROP_NM(o,1,iDate);
@synthesize jce_vSec = J_PROP_EX(o,2,vSec,VOHQSysAppSecTradeInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppShareListGen

@synthesize jce_iDate = J_PROP_NM(o,1,iDate);
@synthesize jce_lNewNum = J_PROP_NM(o,2,lNewNum);
@synthesize jce_lMonNum = J_PROP_NM(o,3,lMonNum);
@synthesize jce_lTolNum = J_PROP_NM(o,4,lTolNum);
@synthesize jce_fRate = J_PROP_NM(o,5,fRate);
@synthesize jce_lShareNum = J_PROP_NM(o,6,lShareNum);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppHMngHoldChgInfo

@synthesize jce_strMngName = J_PROP_NM(o,1,strMngName);
@synthesize jce_intChgVol = J_PROP_NM(o,2,intChgVol);
@synthesize jce_dChgEp = J_PROP_NM(o,3,dChgEp);
@synthesize jce_lBeginVol = J_PROP_NM(o,4,lBeginVol);
@synthesize jce_lAfterVol = J_PROP_NM(o,5,lAfterVol);
@synthesize jce_lTotShare = J_PROP_NM(o,6,lTotShare);

- (id)init
{
    if (self = [super init]) {
        J_PROP(strMngName) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppComPledHold

@synthesize jce_strHolderName = J_PROP_NM(o,1,strHolderName);
@synthesize jce_lPledVol = J_PROP_NM(o,2,lPledVol);
@synthesize jce_fRate = J_PROP_NM(o,3,fRate);
@synthesize jce_fRateTol = J_PROP_NM(o,4,fRateTol);
@synthesize jce_lPledVolSum = J_PROP_NM(o,5,lPledVolSum);
@synthesize jce_fRateSum = J_PROP_NM(o,6,fRateSum);
@synthesize jce_fRateTolSum = J_PROP_NM(o,7,fRateTolSum);

- (id)init
{
    if (self = [super init]) {
        J_PROP(strHolderName) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppComPledDay

@synthesize jce_iDate = J_PROP_NM(o,1,iDate);
@synthesize jce_lPledVolRest = J_PROP_NM(o,2,lPledVolRest);
@synthesize jce_lPledVolShare = J_PROP_NM(o,3,lPledVolShare);
@synthesize jce_fRateCom = J_PROP_NM(o,4,fRateCom);
@synthesize jce_lPledVolCom = J_PROP_NM(o,5,lPledVolCom);
@synthesize jce_vPledHold = J_PROP_EX(o,6,vPledHold,VOHQSysAppComPledHold);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppSHotMoneyInfo

@synthesize jce_iTradeDate = J_PROP_NM(o,1,iTradeDate);
@synthesize jce_iUpStopType = J_PROP_NM(o,2,iUpStopType);
@synthesize jce_iShutStopVol = J_PROP_NM(o,3,iShutStopVol);
@synthesize jce_fShutStopRate = J_PROP_NM(o,4,fShutStopRate);
@synthesize jce_dCirculateValue = J_PROP_NM(o,5,dCirculateValue);
@synthesize jce_dTotalValue = J_PROP_NM(o,6,dTotalValue);
@synthesize jce_strTradePlate = J_PROP_NM(o,7,strTradePlate);
@synthesize jce_strTheme = J_PROP_NM(o,8,strTheme);
@synthesize jce_strStopReason = J_PROP_NM(o,9,strStopReason);
@synthesize jce_iFirstUpStopTime = J_PROP_NM(o,10,iFirstUpStopTime);
@synthesize jce_iLastUpStopTime = J_PROP_NM(o,11,iLastUpStopTime);
@synthesize jce_iFirstDownStopTime = J_PROP_NM(o,12,iFirstDownStopTime);
@synthesize jce_iLastDownStopTime = J_PROP_NM(o,13,iLastDownStopTime);
@synthesize jce_iShutUpStopNum = J_PROP_NM(o,14,iShutUpStopNum);
@synthesize jce_iShutDownStopNum = J_PROP_NM(o,15,iShutDownStopNum);
@synthesize jce_iKeepStopNum = J_PROP_NM(o,16,iKeepStopNum);
@synthesize jce_iDayToStop = J_PROP_NM(o,17,iDayToStop);
@synthesize jce_iStrongDay = J_PROP_NM(o,18,iStrongDay);

- (id)init
{
    if (self = [super init]) {
        J_PROP(strTradePlate) = DefaultJceString;
        J_PROP(strTheme) = DefaultJceString;
        J_PROP(strStopReason) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFSHNHReq

@synthesize jce_eVecMarketType = J_PROP_EX(o,0,eVecMarketType,VONSNumber);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppHStockFactorReq

@synthesize jce_stHeader = J_PROP_NM(o,0,stHeader);
@synthesize jce_shtMarket = J_PROP_NM(o,1,shtMarket);
@synthesize jce_strCode = J_PROP_NM(o,2,strCode);
@synthesize jce_iStartDate = J_PROP_NM(o,3,iStartDate);
@synthesize jce_iEndDate = J_PROP_NM(o,4,iEndDate);
@synthesize jce_vFactorType = J_PROP_EX(o,5,vFactorType,VONSNumber);
@synthesize jce_stSHNHReq = J_PROP_NM(o,6,stSHNHReq);
@synthesize jce_iHoldType = J_PROP_NM(o,7,iHoldType);

- (id)init
{
    if (self = [super init]) {
        J_PROP(strCode) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFRemoteFactorReq

@synthesize jce_stHeader = J_PROP_NM(o,0,stHeader);
@synthesize jce_iType = J_PROP_NM(o,1,iType);
@synthesize jce_lRefreshTime = J_PROP_NM(o,2,lRefreshTime);
@synthesize jce_lLoadTime = J_PROP_NM(o,3,lLoadTime);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFRemoteFactorRsp

@synthesize jce_iType = J_PROP_NM(o,0,iType);
@synthesize jce_vecDate = J_PROP_EX(o,1,vecDate,VONSNumber);
@synthesize jce_lRefreshTime = J_PROP_NM(o,2,lRefreshTime);
@synthesize jce_bReLoad = J_PROP_NM(o,3,bReLoad);
@synthesize jce_lLoadTime = J_PROP_NM(o,4,lLoadTime);

- (id)init
{
    if (self = [super init]) {
        J_PROP(bReLoad) = YES;
    }
    return self;
}

@end

@implementation HQSysAppFMakeMoneyIndexInfo

@synthesize jce_dIncrease = J_PROP_NM(o,0,dIncrease);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFMakeMoneyEffect

@synthesize jce_nDate = J_PROP_NM(o,0,nDate);
@synthesize jce_SHInfo = J_PROP_NM(o,1,SHInfo);
@synthesize jce_SZInfo = J_PROP_NM(o,2,SZInfo);
@synthesize jce_SHSZInfo = J_PROP_NM(o,3,SHSZInfo);
@synthesize jce_KCBZInfo = J_PROP_NM(o,4,KCBZInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFIndexMoneyEffect

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_dIncrease = J_PROP_NM(o,1,dIncrease);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFTrendStrengthIndexInfo

@synthesize jce_dShortTerm = J_PROP_NM(o,0,dShortTerm);
@synthesize jce_dWaveBand = J_PROP_NM(o,1,dWaveBand);
@synthesize jce_dMidTerm = J_PROP_NM(o,2,dMidTerm);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFTrendStrength

@synthesize jce_nDate = J_PROP_NM(o,0,nDate);
@synthesize jce_SHInfo = J_PROP_NM(o,1,SHInfo);
@synthesize jce_SZInfo = J_PROP_NM(o,2,SZInfo);
@synthesize jce_SHSZInfo = J_PROP_NM(o,3,SHSZInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFTrendStrengthDay

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_dShortTerm = J_PROP_NM(o,1,dShortTerm);
@synthesize jce_dWaveBand = J_PROP_NM(o,2,dWaveBand);
@synthesize jce_dMidTerm = J_PROP_NM(o,3,dMidTerm);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFTwiceZTStkIf

@synthesize jce_stk = J_PROP_NM(o,0,stk);
@synthesize jce_ztType = J_PROP_NM(o,1,ztType);
@synthesize jce_iLastZDTime = J_PROP_NM(o,2,iLastZDTime);
@synthesize jce_iFirstZDTime = J_PROP_NM(o,3,iFirstZDTime);
@synthesize jce_lLastZDTVol = J_PROP_NM(o,4,lLastZDTVol);
@synthesize jce_bIsZt = J_PROP_NM(o,5,bIsZt);
@synthesize jce_bIsST = J_PROP_NM(o,6,bIsST);
@synthesize jce_bUnOpenNewStock = J_PROP_NM(o,7,bUnOpenNewStock);

- (id)init
{
    if (self = [super init]) {
        J_PROP(bIsZt) = NO;
        J_PROP(bIsST) = NO;
        J_PROP(bUnOpenNewStock) = NO;
    }
    return self;
}

@end

@implementation HQSysAppFTwiceZTStk

@synthesize jce_nDate = J_PROP_NM(o,0,nDate);
@synthesize jce_vecStk = J_PROP_EX(o,1,vecStk,VOHQSysAppFTwiceZTStkIf);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppISuperShortStrikeInfo

@synthesize jce_stock = J_PROP_NM(o,0,stock);
@synthesize jce_iBuySignal = J_PROP_NM(o,1,iBuySignal);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppISuperShortStrike

@synthesize jce_nDate = J_PROP_NM(o,0,nDate);
@synthesize jce_vecStock = J_PROP_EX(o,1,vecStock,VOHQSysAppISuperShortStrikeInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFSuperShortStrike

@synthesize jce_nDate = J_PROP_NM(o,0,nDate);
@synthesize jce_iBuySignal = J_PROP_NM(o,1,iBuySignal);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFStartTimeInfo

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_iZtType = J_PROP_NM(o,1,iZtType);
@synthesize jce_iLastZDTime = J_PROP_NM(o,2,iLastZDTime);
@synthesize jce_iFirstZDTime = J_PROP_NM(o,3,iFirstZDTime);
@synthesize jce_lLastZDTVol = J_PROP_NM(o,4,lLastZDTVol);
@synthesize jce_bIsZt = J_PROP_NM(o,5,bIsZt);
@synthesize jce_bIsST = J_PROP_NM(o,6,bIsST);
@synthesize jce_bUnOpenNewStock = J_PROP_NM(o,7,bUnOpenNewStock);

- (id)init
{
    if (self = [super init]) {
        J_PROP(bIsZt) = NO;
        J_PROP(bIsST) = NO;
        J_PROP(bUnOpenNewStock) = NO;
    }
    return self;
}

@end

@implementation HQSysAppFGgtMoneyInfo

@synthesize jce_iDate = J_PROP_NM(o,0,iDate);
@synthesize jce_lVolume = J_PROP_NM(o,1,lVolume);
@synthesize jce_dMoney = J_PROP_NM(o,2,dMoney);
@synthesize jce_fRatio = J_PROP_NM(o,3,fRatio);
@synthesize jce_dClosePrice = J_PROP_NM(o,4,dClosePrice);
@synthesize jce_dMoneyAcc = J_PROP_NM(o,5,dMoneyAcc);
@synthesize jce_dHoldValue = J_PROP_NM(o,6,dHoldValue);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFGgtMoneyFlow

@synthesize jce_iDate = J_PROP_NM(o,0,iDate);
@synthesize jce_dMoney = J_PROP_NM(o,1,dMoney);
@synthesize jce_dMoneyAcc = J_PROP_NM(o,2,dMoneyAcc);
@synthesize jce_dClosePrice = J_PROP_NM(o,3,dClosePrice);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFGgtHoldStockVolInfo

@synthesize jce_iDate = J_PROP_NM(o,0,iDate);
@synthesize jce_lVolume = J_PROP_NM(o,1,lVolume);
@synthesize jce_fRatio = J_PROP_NM(o,2,fRatio);
@synthesize jce_dHoldValue = J_PROP_NM(o,3,dHoldValue);
@synthesize jce_dClosePrice = J_PROP_NM(o,4,dClosePrice);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFValueLeading

@synthesize jce_iEndDate = J_PROP_NM(o,0,iEndDate);
@synthesize jce_lStkNumOfFundHold = J_PROP_NM(o,1,lStkNumOfFundHold);
@synthesize jce_fChainMoreOrLess = J_PROP_NM(o,2,fChainMoreOrLess);
@synthesize jce_strUpdateTime = J_PROP_NM(o,3,strUpdateTime);
@synthesize jce_strCode = J_PROP_NM(o,4,strCode);
@synthesize jce_iMarket = J_PROP_NM(o,5,iMarket);
@synthesize jce_dHoldValue = J_PROP_NM(o,6,dHoldValue);
@synthesize jce_fFloatRatio = J_PROP_NM(o,7,fFloatRatio);

- (id)init
{
    if (self = [super init]) {
        J_PROP(strUpdateTime) = DefaultJceString;
        J_PROP(strCode) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFValueLeadingInfoRsp

@synthesize jce_lStkNumOfFundHold = J_PROP_NM(o,0,lStkNumOfFundHold);
@synthesize jce_fChainMoreOrLess = J_PROP_NM(o,1,fChainMoreOrLess);
@synthesize jce_dHoldValue = J_PROP_NM(o,2,dHoldValue);
@synthesize jce_fFloatRatio = J_PROP_NM(o,3,fFloatRatio);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFLHZYBuySellSeat

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_stock = J_PROP_NM(o,1,stock);
@synthesize jce_sOrgUniCode = J_PROP_NM(o,2,sOrgUniCode);
@synthesize jce_sOrgChiName = J_PROP_NM(o,3,sOrgChiName);
@synthesize jce_dBuyAmout = J_PROP_NM(o,4,dBuyAmout);
@synthesize jce_dSellAmount = J_PROP_NM(o,5,dSellAmount);
@synthesize jce_iRankType = J_PROP_NM(o,6,iRankType);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sOrgUniCode) = DefaultJceString;
        J_PROP(sOrgChiName) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppLhzyBranchInfo

@synthesize jce_sOrgUniCode = J_PROP_NM(o,0,sOrgUniCode);
@synthesize jce_sOrgChiName = J_PROP_NM(o,1,sOrgChiName);
@synthesize jce_dBuyAmount = J_PROP_NM(o,2,dBuyAmount);
@synthesize jce_dSellAmount = J_PROP_NM(o,3,dSellAmount);
@synthesize jce_dNetAmount = J_PROP_NM(o,4,dNetAmount);
@synthesize jce_iInfoTypeCode = J_PROP_NM(o,5,iInfoTypeCode);
@synthesize jce_iOrgGroupCode = J_PROP_NM(o,6,iOrgGroupCode);
@synthesize jce_sOrgGroupName = J_PROP_NM(o,7,sOrgGroupName);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sOrgUniCode) = DefaultJceString;
        J_PROP(sOrgChiName) = DefaultJceString;
        J_PROP(sOrgGroupName) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFLHZY

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_dSeatBuyAmout = J_PROP_NM(o,1,dSeatBuyAmout);
@synthesize jce_dSeatSellAmout = J_PROP_NM(o,2,dSeatSellAmout);
@synthesize jce_dOrganSeatBuyAmout = J_PROP_NM(o,3,dOrganSeatBuyAmout);
@synthesize jce_dOrganSeatSellAmout = J_PROP_NM(o,4,dOrganSeatSellAmout);
@synthesize jce_iNumOfOrganSeatBuy = J_PROP_NM(o,5,iNumOfOrganSeatBuy);
@synthesize jce_iNumOfOrganSeatSell = J_PROP_NM(o,6,iNumOfOrganSeatSell);
@synthesize jce_dTopHotMoneySeatBuyAmout = J_PROP_NM(o,7,dTopHotMoneySeatBuyAmout);
@synthesize jce_dTopHotMoneySeatSellAmout = J_PROP_NM(o,8,dTopHotMoneySeatSellAmout);
@synthesize jce_strCode = J_PROP_NM(o,9,strCode);
@synthesize jce_iMarket = J_PROP_NM(o,10,iMarket);
@synthesize jce_strUpdateTime = J_PROP_NM(o,11,strUpdateTime);
@synthesize jce_iRankType = J_PROP_NM(o,12,iRankType);
@synthesize jce_vBuyList = J_PROP_EX(o,13,vBuyList,VOHQSysAppFLHZYBuySellSeat);
@synthesize jce_vSellList = J_PROP_EX(o,14,vSellList,VOHQSysAppFLHZYBuySellSeat);
@synthesize jce_iNumOfYZBuy = J_PROP_NM(o,15,iNumOfYZBuy);
@synthesize jce_iNumOfYZSell = J_PROP_NM(o,16,iNumOfYZSell);
@synthesize jce_iIsZMYZ = J_PROP_NM(o,17,iIsZMYZ);

- (id)init
{
    if (self = [super init]) {
        J_PROP(strCode) = DefaultJceString;
        J_PROP(strUpdateTime) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFLHBInfo

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_stock = J_PROP_NM(o,1,stock);
@synthesize jce_vBuyList = J_PROP_EX(o,2,vBuyList,VOHQSysAppFLHZYBuySellSeat);
@synthesize jce_vSellList = J_PROP_EX(o,3,vSellList,VOHQSysAppFLHZYBuySellSeat);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFLHBList

@synthesize jce_vecLHB = J_PROP_EX(o,0,vecLHB,VOHQSysAppFLHBInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFSHNHInFlowDataInfo

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_iType = J_PROP_NM(o,1,iType);
@synthesize jce_dInFlow = J_PROP_NM(o,2,dInFlow);
@synthesize jce_dInFlowAcc = J_PROP_NM(o,3,dInFlowAcc);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFSHNHInFlowQuotaRemainingDataInfo

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_dQuotaRemainingOfHGT = J_PROP_NM(o,1,dQuotaRemainingOfHGT);
@synthesize jce_dQuotaRemainingOfSGT = J_PROP_NM(o,2,dQuotaRemainingOfSGT);
@synthesize jce_dQuotaRemainingOfGGTSH = J_PROP_NM(o,3,dQuotaRemainingOfGGTSH);
@synthesize jce_dQuotaRemainingOfGGTSZ = J_PROP_NM(o,4,dQuotaRemainingOfGGTSZ);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFSHNHInFlowRspInfo

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_iType = J_PROP_NM(o,1,iType);
@synthesize jce_dQuoteRemaining = J_PROP_NM(o,2,dQuoteRemaining);
@synthesize jce_dInFlowNetDaily = J_PROP_NM(o,3,dInFlowNetDaily);
@synthesize jce_dInFlowAcc = J_PROP_NM(o,4,dInFlowAcc);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFSHNHInFlowRsp

@synthesize jce_vecSHNHInFlow = J_PROP_EX(o,0,vecSHNHInFlow,VOHQSysAppFSHNHInFlowRspInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFNHNetBuyData

@synthesize jce_iTradeDate = J_PROP_NM(o,0,iTradeDate);
@synthesize jce_stk = J_PROP_NM(o,1,stk);
@synthesize jce_dNetBuy1D = J_PROP_NM(o,2,dNetBuy1D);
@synthesize jce_dNetBuy5D = J_PROP_NM(o,3,dNetBuy5D);
@synthesize jce_dNetBuy30D = J_PROP_NM(o,4,dNetBuy30D);
@synthesize jce_dChgRatio = J_PROP_NM(o,5,dChgRatio);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFZTStkInfo

@synthesize jce_stk = J_PROP_NM(o,0,stk);
@synthesize jce_iDate = J_PROP_NM(o,1,iDate);
@synthesize jce_bUnOpenNewStock = J_PROP_NM(o,2,bUnOpenNewStock);

- (id)init
{
    if (self = [super init]) {
        J_PROP(bUnOpenNewStock) = NO;
    }
    return self;
}

@end

@implementation HQSysAppFLHTrend

@synthesize jce_stk = J_PROP_NM(o,0,stk);
@synthesize jce_iTradeDate = J_PROP_NM(o,1,iTradeDate);
@synthesize jce_iRankType = J_PROP_NM(o,2,iRankType);
@synthesize jce_strUpdateTime = J_PROP_NM(o,3,strUpdateTime);

- (id)init
{
    if (self = [super init]) {
        J_PROP(strUpdateTime) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFActiveBranch

@synthesize jce_stk = J_PROP_NM(o,0,stk);
@synthesize jce_iTradeDate = J_PROP_NM(o,1,iTradeDate);
@synthesize jce_sBranchCode = J_PROP_NM(o,2,sBranchCode);
@synthesize jce_sBranchName = J_PROP_NM(o,3,sBranchName);
@synthesize jce_sHotMoneyName = J_PROP_NM(o,4,sHotMoneyName);
@synthesize jce_dBuyAmount = J_PROP_NM(o,5,dBuyAmount);
@synthesize jce_dSellAmount = J_PROP_NM(o,6,dSellAmount);
@synthesize jce_iHotMoneyCode = J_PROP_NM(o,7,iHotMoneyCode);
@synthesize jce_strUpdateTime = J_PROP_NM(o,8,strUpdateTime);
@synthesize jce_sHotMoneyNameDN = J_PROP_NM(o,9,sHotMoneyNameDN);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sBranchCode) = DefaultJceString;
        J_PROP(sBranchName) = DefaultJceString;
        J_PROP(sHotMoneyName) = DefaultJceString;
        J_PROP(strUpdateTime) = DefaultJceString;
        J_PROP(sHotMoneyNameDN) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFActiveBranchRspInfo

@synthesize jce_vecStk = J_PROP_EX(o,0,vecStk,VOHQSysAppStockInfo);
@synthesize jce_sBranchCode = J_PROP_NM(o,1,sBranchCode);
@synthesize jce_sBranchName = J_PROP_NM(o,2,sBranchName);
@synthesize jce_sHotMoneyName = J_PROP_NM(o,3,sHotMoneyName);
@synthesize jce_iZTNum = J_PROP_NM(o,4,iZTNum);
@synthesize jce_dNetBuyAmount = J_PROP_NM(o,5,dNetBuyAmount);
@synthesize jce_iRankNum = J_PROP_NM(o,6,iRankNum);
@synthesize jce_sHotMoneyNameDN = J_PROP_NM(o,7,sHotMoneyNameDN);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sBranchCode) = DefaultJceString;
        J_PROP(sBranchName) = DefaultJceString;
        J_PROP(sHotMoneyName) = DefaultJceString;
        J_PROP(sHotMoneyNameDN) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFActiveBranchRsp

@synthesize jce_vecActiveBranch = J_PROP_EX(o,0,vecActiveBranch,VOHQSysAppFActiveBranchRspInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFHotStkOfJG

@synthesize jce_stk = J_PROP_NM(o,0,stk);
@synthesize jce_dChgRatioOfPeriod = J_PROP_NM(o,1,dChgRatioOfPeriod);
@synthesize jce_iRankingNumOfB = J_PROP_NM(o,2,iRankingNumOfB);
@synthesize jce_iRankingNumOfS = J_PROP_NM(o,3,iRankingNumOfS);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFHotMoneyInfo

@synthesize jce_sGroupName = J_PROP_NM(o,0,sGroupName);
@synthesize jce_iGroupCode = J_PROP_NM(o,1,iGroupCode);
@synthesize jce_iRankNum = J_PROP_NM(o,2,iRankNum);
@synthesize jce_sGroupNameDN = J_PROP_NM(o,3,sGroupNameDN);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sGroupName) = DefaultJceString;
        J_PROP(sGroupNameDN) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFHotStkOfYZ

@synthesize jce_stk = J_PROP_NM(o,0,stk);
@synthesize jce_dChgRatioOfPeriod = J_PROP_NM(o,1,dChgRatioOfPeriod);
@synthesize jce_vecHotMoney = J_PROP_EX(o,2,vecHotMoney,VOHQSysAppFHotMoneyInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFactorDayList

@synthesize jce_mapFactorDay = J_PROP_EX(o,1,mapFactorDay,M09ONSNumberOHQSysAppFactorInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppHStockFactorRsp

@synthesize jce_mapFactor = J_PROP_EX(o,1,mapFactor,M09ONSNumberOHQSysAppFactorDayList);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFLHTrendReq

@synthesize jce_stHeader = J_PROP_NM(o,0,stHeader);
@synthesize jce_iPeriode = J_PROP_NM(o,1,iPeriode);
@synthesize jce_iDate = J_PROP_NM(o,2,iDate);
@synthesize jce_iPage = J_PROP_NM(o,4,iPage);
@synthesize jce_iWantNum = J_PROP_NM(o,5,iWantNum);

- (id)init
{
    if (self = [super init]) {
        J_PROP(iWantNum) = 10;
    }
    return self;
}

@end

@implementation HQSysAppFHotStkReq

@synthesize jce_stHeader = J_PROP_NM(o,0,stHeader);
@synthesize jce_vecStk = J_PROP_EX(o,1,vecStk,VOHQSysAppStockInfo);
@synthesize jce_iPeriode = J_PROP_NM(o,2,iPeriode);
@synthesize jce_iDate = J_PROP_NM(o,3,iDate);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFHotStkRsp

@synthesize jce_mapStkYZNum = J_PROP_EX(o,0,mapStkYZNum,M09ONSStringONSNumber);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFLHBDReq

@synthesize jce_stHeader = J_PROP_NM(o,0,stHeader);
@synthesize jce_iBDType = J_PROP_NM(o,1,iBDType);
@synthesize jce_iDate = J_PROP_NM(o,2,iDate);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFNHNetBuyReq

@synthesize jce_stHeader = J_PROP_NM(o,0,stHeader);
@synthesize jce_iBuyType = J_PROP_NM(o,1,iBuyType);
@synthesize jce_iDate = J_PROP_NM(o,2,iDate);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFTopTenLTGDHold

@synthesize jce_iDate = J_PROP_NM(o,0,iDate);
@synthesize jce_fHoldVal = J_PROP_NM(o,1,fHoldVal);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFLGTValue

@synthesize jce_stk = J_PROP_NM(o,1,stk);
@synthesize jce_iDate = J_PROP_NM(o,2,iDate);
@synthesize jce_fHoldVal = J_PROP_NM(o,3,fHoldVal);
@synthesize jce_fHoldPCT = J_PROP_NM(o,4,fHoldPCT);
@synthesize jce_fPreAddHoldAmout = J_PROP_NM(o,5,fPreAddHoldAmout);
@synthesize jce_fPreAddHoldRatio = J_PROP_NM(o,6,fPreAddHoldRatio);
@synthesize jce_fPreChgRatio = J_PROP_NM(o,7,fPreChgRatio);
@synthesize jce_f5AddHoldAmout = J_PROP_NM(o,8,f5AddHoldAmout);
@synthesize jce_f5AddHoldRatio = J_PROP_NM(o,9,f5AddHoldRatio);
@synthesize jce_f5ChgRatio = J_PROP_NM(o,10,f5ChgRatio);
@synthesize jce_f20AddHoldAmout = J_PROP_NM(o,11,f20AddHoldAmout);
@synthesize jce_f20AddHoldRatio = J_PROP_NM(o,12,f20AddHoldRatio);
@synthesize jce_f20ChgRatio = J_PROP_NM(o,13,f20ChgRatio);
@synthesize jce_f60AddHoldAmout = J_PROP_NM(o,14,f60AddHoldAmout);
@synthesize jce_f60AddHoldRatio = J_PROP_NM(o,15,f60AddHoldRatio);
@synthesize jce_f60ChgRatio = J_PROP_NM(o,16,f60ChgRatio);
@synthesize jce_iContAddHoldDays = J_PROP_NM(o,17,iContAddHoldDays);
@synthesize jce_fPreDayNetAddVol = J_PROP_NM(o,18,fPreDayNetAddVol);
@synthesize jce_fHoldPCTTot = J_PROP_NM(o,19,fHoldPCTTot);
@synthesize jce_fHoldVolume = J_PROP_NM(o,20,fHoldVolume);
@synthesize jce_vecMktType = J_PROP_EX(o,21,vecMktType,VONSNumber);
@synthesize jce_fT3NetBuyAmout = J_PROP_NM(o,22,fT3NetBuyAmout);
@synthesize jce_fT3NetBuyVol = J_PROP_NM(o,23,fT3NetBuyVol);
@synthesize jce_fT3AddHoldRatio = J_PROP_NM(o,24,fT3AddHoldRatio);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFLGTValueList

@synthesize jce_vLGTValue = J_PROP_EX(o,1,vLGTValue,VOHQSysAppFLGTValue);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFLGTHoldJGInfo

@synthesize jce_sJgCode = J_PROP_NM(o,1,sJgCode);
@synthesize jce_sJgName = J_PROP_NM(o,2,sJgName);
@synthesize jce_eJgType = J_PROP_NM(o,3,eJgType);
@synthesize jce_fHoldVal = J_PROP_NM(o,4,fHoldVal);
@synthesize jce_fBuyCost = J_PROP_NM(o,5,fBuyCost);
@synthesize jce_fHoldReturnRate = J_PROP_NM(o,6,fHoldReturnRate);
@synthesize jce_fHoldPCT = J_PROP_NM(o,7,fHoldPCT);
@synthesize jce_iContAddHoldDays = J_PROP_NM(o,8,iContAddHoldDays);
@synthesize jce_f1DayAddAmount = J_PROP_NM(o,9,f1DayAddAmount);
@synthesize jce_fHoldStkSY = J_PROP_NM(o,10,fHoldStkSY);
@synthesize jce_eHoldType = J_PROP_NM(o,11,eHoldType);
@synthesize jce_fUp1AddHold = J_PROP_NM(o,12,fUp1AddHold);
@synthesize jce_fUp5AddHoldAmout = J_PROP_NM(o,13,fUp5AddHoldAmout);
@synthesize jce_fUp20AddHoldAmout = J_PROP_NM(o,14,fUp20AddHoldAmout);
@synthesize jce_fUp60AddHoldAmout = J_PROP_NM(o,15,fUp60AddHoldAmout);
@synthesize jce_fUpHoldStkSY = J_PROP_NM(o,16,fUpHoldStkSY);
@synthesize jce_stk = J_PROP_NM(o,17,stk);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sJgCode) = DefaultJceString;
        J_PROP(sJgName) = DefaultJceString;
        J_PROP(eJgType) = HQSysApp_E_JG_Type_E_JG_TYPE_GENERAL;
    }
    return self;
}

@end

@implementation HQSysAppFLGTHoldJGList

@synthesize jce_vLGTHoldJg = J_PROP_EX(o,1,vLGTHoldJg,VOHQSysAppFLGTHoldJGInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFLGTJGStatsInfo

@synthesize jce_sJgCode = J_PROP_NM(o,1,sJgCode);
@synthesize jce_sJgName = J_PROP_NM(o,2,sJgName);
@synthesize jce_eJgType = J_PROP_NM(o,3,eJgType);
@synthesize jce_iDate = J_PROP_NM(o,4,iDate);
@synthesize jce_fHoldVal = J_PROP_NM(o,5,fHoldVal);
@synthesize jce_fTotBuyAmout = J_PROP_NM(o,6,fTotBuyAmout);
@synthesize jce_fTotReturnRate = J_PROP_NM(o,7,fTotReturnRate);
@synthesize jce_fSuccessRate = J_PROP_NM(o,8,fSuccessRate);
@synthesize jce_iContAddHoldDays = J_PROP_NM(o,9,iContAddHoldDays);
@synthesize jce_iHoldNum = J_PROP_NM(o,10,iHoldNum);
@synthesize jce_f1DayAddAmount = J_PROP_NM(o,11,f1DayAddAmount);
@synthesize jce_f5DayAddAmount = J_PROP_NM(o,12,f5DayAddAmount);
@synthesize jce_f20DayAddAmount = J_PROP_NM(o,13,f20DayAddAmount);
@synthesize jce_f60DayAddAmount = J_PROP_NM(o,14,f60DayAddAmount);
@synthesize jce_i1DayNewNum = J_PROP_NM(o,15,i1DayNewNum);
@synthesize jce_i5DayNewNum = J_PROP_NM(o,16,i5DayNewNum);
@synthesize jce_i20DayNewNum = J_PROP_NM(o,17,i20DayNewNum);
@synthesize jce_i60DayNewNum = J_PROP_NM(o,18,i60DayNewNum);
@synthesize jce_i1DayAddNum = J_PROP_NM(o,19,i1DayAddNum);
@synthesize jce_i5DayAddNum = J_PROP_NM(o,20,i5DayAddNum);
@synthesize jce_i20DayAddNum = J_PROP_NM(o,21,i20DayAddNum);
@synthesize jce_i60DayAddNum = J_PROP_NM(o,22,i60DayAddNum);
@synthesize jce_i1DayReduceNum = J_PROP_NM(o,23,i1DayReduceNum);
@synthesize jce_i5DayReduceNum = J_PROP_NM(o,24,i5DayReduceNum);
@synthesize jce_i20DayReduceNum = J_PROP_NM(o,25,i20DayReduceNum);
@synthesize jce_i60DayReduceNum = J_PROP_NM(o,26,i60DayReduceNum);
@synthesize jce_fUpHoldBuyAmout = J_PROP_NM(o,27,fUpHoldBuyAmout);
@synthesize jce_fUpSuccessRate = J_PROP_NM(o,28,fUpSuccessRate);
@synthesize jce_fUpHoldReturnRate = J_PROP_NM(o,29,fUpHoldReturnRate);
@synthesize jce_fUpTotBuyAmount = J_PROP_NM(o,30,fUpTotBuyAmount);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sJgCode) = DefaultJceString;
        J_PROP(sJgName) = DefaultJceString;
        J_PROP(eJgType) = HQSysApp_E_JG_Type_E_JG_TYPE_GENERAL;
    }
    return self;
}

@end

@implementation HQSysAppFLGTJGStatsList

@synthesize jce_vLGTJg = J_PROP_EX(o,1,vLGTJg,VOHQSysAppFLGTJGStatsInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFNHIndustryInfo

@synthesize jce_blk = J_PROP_NM(o,1,blk);
@synthesize jce_iHoldStkNum = J_PROP_NM(o,2,iHoldStkNum);
@synthesize jce_fPreAddHoldAmout = J_PROP_NM(o,3,fPreAddHoldAmout);
@synthesize jce_f5AddHoldAmout = J_PROP_NM(o,4,f5AddHoldAmout);
@synthesize jce_f20AddHoldAmout = J_PROP_NM(o,5,f20AddHoldAmout);
@synthesize jce_f60AddHoldAmout = J_PROP_NM(o,6,f60AddHoldAmout);
@synthesize jce_fTotHoldAmount = J_PROP_NM(o,7,fTotHoldAmount);
@synthesize jce_iTradeDate = J_PROP_NM(o,8,iTradeDate);
@synthesize jce_fHoldVolume = J_PROP_NM(o,9,fHoldVolume);
@synthesize jce_fHoldValue = J_PROP_NM(o,10,fHoldValue);
@synthesize jce_fHoldPCTTot = J_PROP_NM(o,11,fHoldPCTTot);
@synthesize jce_fHoldPCTFloat = J_PROP_NM(o,12,fHoldPCTFloat);
@synthesize jce_f1DayAddVolume = J_PROP_NM(o,13,f1DayAddVolume);
@synthesize jce_f5DayAddVolume = J_PROP_NM(o,14,f5DayAddVolume);
@synthesize jce_f20DayAddVolume = J_PROP_NM(o,15,f20DayAddVolume);
@synthesize jce_f60DayAddVolume = J_PROP_NM(o,16,f60DayAddVolume);
@synthesize jce_iBlkType = J_PROP_NM(o,17,iBlkType);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFNHIndustryList

@synthesize jce_vecNHIndustry = J_PROP_EX(o,1,vecNHIndustry,VOHQSysAppFNHIndustryInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFNHIndustryStkFlowInfo

@synthesize jce_stk = J_PROP_NM(o,1,stk);
@synthesize jce_fPreAddHoldAmout = J_PROP_NM(o,2,fPreAddHoldAmout);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFNHIndustryStkFlowList

@synthesize jce_vecFlowInStk = J_PROP_EX(o,1,vecFlowInStk,VOHQSysAppFNHIndustryStkFlowInfo);
@synthesize jce_vecFlowOutStk = J_PROP_EX(o,2,vecFlowOutStk,VOHQSysAppFNHIndustryStkFlowInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFTopTenCJInfo

@synthesize jce_stk = J_PROP_NM(o,0,stk);
@synthesize jce_fGZAmount = J_PROP_NM(o,1,fGZAmount);
@synthesize jce_fNetBuyRatio = J_PROP_NM(o,2,fNetBuyRatio);
@synthesize jce_fHoldValue = J_PROP_NM(o,3,fHoldValue);
@synthesize jce_fHoldPCT = J_PROP_NM(o,4,fHoldPCT);
@synthesize jce_fNetBuyAmout = J_PROP_NM(o,5,fNetBuyAmout);
@synthesize jce_iTradeDate = J_PROP_NM(o,6,iTradeDate);
@synthesize jce_iMktType = J_PROP_NM(o,7,iMktType);
@synthesize jce_fBuyAmout = J_PROP_NM(o,8,fBuyAmout);
@synthesize jce_fSellAmout = J_PROP_NM(o,9,fSellAmout);
@synthesize jce_fHoldVol = J_PROP_NM(o,10,fHoldVol);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFTopTenCJStkList

@synthesize jce_vecStk = J_PROP_EX(o,0,vecStk,VOHQSysAppFTopTenCJInfo);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppFStkYJTypeInfo

@synthesize jce_iType = J_PROP_NM(o,0,iType);
@synthesize jce_sContext = J_PROP_NM(o,1,sContext);
@synthesize jce_iDate = J_PROP_NM(o,2,iDate);
@synthesize jce_sNewId = J_PROP_NM(o,3,sNewId);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sContext) = DefaultJceString;
        J_PROP(sNewId) = DefaultJceString;
    }
    return self;
}

@end

@implementation HQSysAppFStkTag

@synthesize jce_mapTag = J_PROP_EX(o,0,mapTag,M09ONSNumberONSString);
@synthesize jce_iLHBType = J_PROP_NM(o,1,iLHBType);
@synthesize jce_iYJType = J_PROP_NM(o,2,iYJType);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation HQSysAppHStockFactorBatchReq

@synthesize jce_stHeader = J_PROP_NM(o,0,stHeader);
@synthesize jce_vStk = J_PROP_EX(o,1,vStk,VOHQSysAppStockInfo);
@synthesize jce_iStartDate = J_PROP_NM(o,2,iStartDate);
@synthesize jce_iEndDate = J_PROP_NM(o,3,iEndDate);
@synthesize jce_vFactorType = J_PROP_EX(o,4,vFactorType,VONSNumber);
@synthesize jce_vMarketType = J_PROP_EX(o,5,vMarketType,VONSNumber);
@synthesize jce_iJGType = J_PROP_NM(o,6,iJGType);
@synthesize jce_iBlkType = J_PROP_NM(o,7,iBlkType);

- (id)init
{
    if (self = [super init]) {
        J_PROP(iJGType) = 2;
        J_PROP(iBlkType) = 1;
    }
    return self;
}

@end



