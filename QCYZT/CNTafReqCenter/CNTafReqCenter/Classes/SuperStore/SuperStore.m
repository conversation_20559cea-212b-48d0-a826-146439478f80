// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/SuperStore.jce'
// **********************************************************************

#include "SuperStore.h"

@implementation QtAstQBlockStockItem

@synthesize jce_market = J_PROP_NM(o,0,market);
@synthesize jce_code = J_PROP_NM(o,1,code);
@synthesize jce_name = J_PROP_NM(o,2,name);
@synthesize jce_price = J_PROP_NM(o,3,price);
@synthesize jce_chgrate = J_PROP_NM(o,4,chgrate);
@synthesize jce_volume = J_PROP_NM(o,5,volume);
@synthesize jce_amount = J_PROP_NM(o,6,amount);
@synthesize jce_dayzt30 = J_PROP_NM(o,7,dayzt30);
@synthesize jce_leadup30 = J_PROP_NM(o,8,leadup30);
@synthesize jce_boardperiod = J_PROP_NM(o,9,boardperiod);
@synthesize jce_mainnetbuy = J_PROP_NM(o,10,mainnetbuy);
@synthesize jce_mainratio = J_PROP_NM(o,11,mainratio);
@synthesize jce_szzl = J_PROP_NM(o,12,szzl);
@synthesize jce_ztreason = J_PROP_NM(o,13,ztreason);
@synthesize jce_firstzttime = J_PROP_NM(o,14,firstzttime);
@synthesize jce_lastzttime = J_PROP_NM(o,15,lastzttime);
@synthesize jce_dragonheadidx = J_PROP_NM(o,16,dragonheadidx);

- (id)init
{
    if (self = [super init]) {
        J_PROP(code) = DefaultJceString;
        J_PROP(name) = DefaultJceString;
        J_PROP(boardperiod) = DefaultJceString;
        J_PROP(ztreason) = DefaultJceString;
    }
    return self;
}

@end

@implementation QtAstQBlockStockList

@synthesize jce_mapStock = J_PROP_EX(o,1,mapStock,M09ONSStringOQtAstQBlockStockItem);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation QtAstQPoolItem

@synthesize jce_market = J_PROP_NM(o,0,market);
@synthesize jce_code = J_PROP_NM(o,1,code);
@synthesize jce_mapDblValue = J_PROP_EX(o,2,mapDblValue,M09ONSStringONSNumber);
@synthesize jce_mapStrValue = J_PROP_EX(o,3,mapStrValue,M09ONSStringONSString);
@synthesize jce_mapLongValue = J_PROP_EX(o,4,mapLongValue,M09ONSStringONSNumber);

- (id)init
{
    if (self = [super init]) {
        J_PROP(code) = DefaultJceString;
    }
    return self;
}

@end

@implementation QtAstQDailyPoolData

@synthesize jce_tradeDate = J_PROP_NM(o,0,tradeDate);
@synthesize jce_vStock = J_PROP_EX(o,1,vStock,VOQtAstQPoolItem);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end



