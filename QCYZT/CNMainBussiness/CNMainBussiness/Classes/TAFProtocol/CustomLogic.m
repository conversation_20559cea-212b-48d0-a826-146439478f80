// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `CustomLogic.jce'
// **********************************************************************

#include "CustomLogic.h"

@implementation CustomWidget

@synthesize jce_sWidgetName = J_PROP_NM(o,0,sWidgetName);
@synthesize jce_sWidgetCode = J_PROP_NM(o,1,sWidgetCode);
@synthesize jce_sIcon = J_PROP_NM(o,2,sIcon);
@synthesize jce_sSubScript = J_PROP_NM(o,3,sSubScript);
@synthesize jce_sLink = J_PROP_NM(o,4,sLink);
@synthesize jce_sDescription = J_PROP_NM(o,5,sDescription);
@synthesize jce_iLoginStatus = J_PROP_NM(o,6,iLoginStatus);
@synthesize jce_sBuryPoint = J_PROP_NM(o,7,sBuryPoint);
@synthesize jce_sSubhead = J_PROP_NM(o,8,sSubhead);
@synthesize jce_sFeature_desc = J_PROP_NM(o,9,sFeature_desc);
@synthesize jce_sIcon_black = J_PROP_NM(o,10,sIcon_black);
@synthesize jce_sFeature_horn_icon_black = J_PROP_NM(o,11,sFeature_horn_icon_black);
@synthesize jce_sPlat = J_PROP_NM(o,12,sPlat);
@synthesize jce_sStatus = J_PROP_NM(o,13,sStatus);
@synthesize jce_vChannelsRange = J_PROP_EX(o,14,vChannelsRange,VONSString);
@synthesize jce_vAuthList = J_PROP_EX(o,15,vAuthList,VONSString);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sWidgetName) = DefaultJceString;
        J_PROP(sWidgetCode) = DefaultJceString;
        J_PROP(sIcon) = DefaultJceString;
        J_PROP(sSubScript) = DefaultJceString;
        J_PROP(sLink) = DefaultJceString;
        J_PROP(sDescription) = DefaultJceString;
        J_PROP(sBuryPoint) = DefaultJceString;
        J_PROP(sSubhead) = DefaultJceString;
        J_PROP(sFeature_desc) = DefaultJceString;
        J_PROP(sIcon_black) = DefaultJceString;
        J_PROP(sFeature_horn_icon_black) = DefaultJceString;
        J_PROP(sPlat) = DefaultJceString;
        J_PROP(sStatus) = DefaultJceString;
    }
    return self;
}

@end

@implementation CustomCategory

@synthesize jce_sName = J_PROP_NM(o,0,sName);
@synthesize jce_sCategoryCode = J_PROP_NM(o,1,sCategoryCode);
@synthesize jce_sDescription = J_PROP_NM(o,2,sDescription);
@synthesize jce_vWidgetList = J_PROP_EX(o,3,vWidgetList,VOCustomWidget);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sName) = DefaultJceString;
        J_PROP(sCategoryCode) = DefaultJceString;
        J_PROP(sDescription) = DefaultJceString;
    }
    return self;
}

@end

@implementation CustomTotalWidgetReq

@synthesize jce_sGuid = J_PROP_NM(o,0,sGuid);
@synthesize jce_sXua = J_PROP_NM(o,1,sXua);
@synthesize jce_sUid = J_PROP_NM(o,2,sUid);
@synthesize jce_vCategoryCodeList = J_PROP_EX(o,3,vCategoryCodeList,VONSString);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sGuid) = DefaultJceString;
        J_PROP(sXua) = DefaultJceString;
        J_PROP(sUid) = DefaultJceString;
    }
    return self;
}

@end

@implementation CustomTotalWidgetRsp

@synthesize jce_iRet = J_PROP_NM(r,0,iRet);
@synthesize jce_sMsg = J_PROP_NM(o,1,sMsg);
@synthesize jce_vCategory = J_PROP_EX(o,2,vCategory,VOCustomCategory);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sMsg) = DefaultJceString;
    }
    return self;
}

@end

@implementation CustomCustomWidgetReq

@synthesize jce_sGuid = J_PROP_NM(o,0,sGuid);
@synthesize jce_sXua = J_PROP_NM(o,1,sXua);
@synthesize jce_sUid = J_PROP_NM(o,2,sUid);
@synthesize jce_sWidgets = J_PROP_NM(o,3,sWidgets);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sGuid) = DefaultJceString;
        J_PROP(sXua) = DefaultJceString;
        J_PROP(sUid) = DefaultJceString;
        J_PROP(sWidgets) = DefaultJceString;
    }
    return self;
}

@end

@implementation CustomCustomWidgetRsp

@synthesize jce_iRet = J_PROP_NM(r,0,iRet);
@synthesize jce_sMsg = J_PROP_NM(o,1,sMsg);
@synthesize jce_vWidget = J_PROP_EX(o,2,vWidget,VOCustomWidget);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sMsg) = DefaultJceString;
    }
    return self;
}

@end



