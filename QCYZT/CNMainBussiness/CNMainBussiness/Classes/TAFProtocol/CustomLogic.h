// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `CustomLogic.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>
@interface CustomWidget : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sWidgetName;
@property (nonatomic, strong) NSString* jce_sWidgetCode;
@property (nonatomic, strong) NSString* jce_sIcon;
@property (nonatomic, strong) NSString* jce_sSubScript;
@property (nonatomic, strong) NSString* jce_sLink;
@property (nonatomic, strong) NSString* jce_sDescription;
@property (nonatomic, assign) JceInt32 jce_iLoginStatus;
@property (nonatomic, strong) NSString* jce_sBuryPoint;
@property (nonatomic, strong) NSString* jce_sSubhead;
@property (nonatomic, strong) NSString* jce_sFeature_desc;
@property (nonatomic, strong) NSString* jce_sIcon_black;
@property (nonatomic, strong) NSString* jce_sFeature_horn_icon_black;
@property (nonatomic, strong) NSString* jce_sPlat;
@property (nonatomic, strong) NSString* jce_sStatus;
@property (nonatomic, strong) NSArray<NSString*>* jce_vChannelsRange;
@property (nonatomic, strong) NSArray<NSString*>* jce_vAuthList;
@end

@interface CustomCategory : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSString* jce_sCategoryCode;
@property (nonatomic, strong) NSString* jce_sDescription;
@property (nonatomic, strong) NSArray<CustomWidget*>* jce_vWidgetList;
@end

@interface CustomTotalWidgetReq : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sGuid;
@property (nonatomic, strong) NSString* jce_sXua;
@property (nonatomic, strong) NSString* jce_sUid;
@property (nonatomic, strong) NSArray<NSString*>* jce_vCategoryCodeList;
@end

@interface CustomTotalWidgetRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iRet;
@property (nonatomic, strong) NSString* jce_sMsg;
@property (nonatomic, strong) NSArray<CustomCategory*>* jce_vCategory;
@end

@interface CustomCustomWidgetReq : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_sGuid;
@property (nonatomic, strong) NSString* jce_sXua;
@property (nonatomic, strong) NSString* jce_sUid;
@property (nonatomic, strong) NSString* jce_sWidgets;
@end

@interface CustomCustomWidgetRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iRet;
@property (nonatomic, strong) NSString* jce_sMsg;
@property (nonatomic, strong) NSArray<CustomWidget*>* jce_vWidget;
@end



