// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `HANewStockServer.jce'
// **********************************************************************

#import "HAZQHANewStockServerAgent.h"

// MARK: HAZQHANewStockServerGetNewStock
@implementation HAZQHANewStockServerGetNewStockRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"getNewStock";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"req" value:self.req];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    HAZQHANewStockServerGetNewStockResponse * response = [[HAZQHANewStockServerGetNewStockResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.rsp = [uniPacket get:@"rsp" forClass: HAZQNewStockRsp.class];
    return response;
}
@end

@implementation HAZQHANewStockServerGetNewStockResponse 
@end

// MARK: HAZQHANewStockServerGetWinLots
@implementation HAZQHANewStockServerGetWinLotsRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"getWinLots";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"req" value:self.req];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    HAZQHANewStockServerGetWinLotsResponse * response = [[HAZQHANewStockServerGetWinLotsResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.rsp = [uniPacket get:@"rsp" forClass: HAZQWinLotsRsp.class];
    return response;
}
@end

@implementation HAZQHANewStockServerGetWinLotsResponse 
@end

// MARK: HAZQHANewStockServerAgent
@implementation HAZQHANewStockServerAgent
- (instancetype)initWithServantName:(NSString *)servantName {
    self = [super init];
    if (self) {
        self.servantName = servantName;
    }
    return self;
}

- (HAZQHANewStockServerGetNewStockRequest*)newGetNewStockRequest:(HAZQNewStockReq*)req {
    HAZQHANewStockServerGetNewStockRequest * request = [[HAZQHANewStockServerGetNewStockRequest alloc] init];
    request.servantName = self.servantName;
    request.req = req;
    return request;
}
- (HAZQHANewStockServerGetWinLotsRequest*)newGetWinLotsRequest:(HAZQWinLotsReq*)req {
    HAZQHANewStockServerGetWinLotsRequest * request = [[HAZQHANewStockServerGetWinLotsRequest alloc] init];
    request.servantName = self.servantName;
    request.req = req;
    return request;
}
@end
