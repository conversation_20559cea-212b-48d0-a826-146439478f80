// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `HANewStockServer.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>
@interface HAZQNewStockReq : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_date;
@end

@interface HAZQNewStock : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_stock_name;
@property (nonatomic, strong) NSString* jce_stock_code;
@property (nonatomic, strong) NSString* jce_close_price;
@property (nonatomic, strong) NSString* jce_market_date;
@property (nonatomic, strong) NSString* jce_exchange_type;
@property (nonatomic, strong) NSString* jce_stype;
@end

@interface HAZQNewStockRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iRet;
@property (nonatomic, strong) NSString* jce_sMsg;
@property (nonatomic, strong) NSArray<HAZQNewStock*>* jce_vNewStockList;
@end

@interface HAZQWinLotsReq : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_cust_no;
@end

@interface HAZQWinLots : UPTAFJceObject
@property (nonatomic, strong) NSString* jce_stock_name;
@property (nonatomic, strong) NSString* jce_stock_code;
@property (nonatomic, strong) NSString* jce_stock_price;
@property (nonatomic, strong) NSString* jce_amount;
@property (nonatomic, strong) NSString* jce_fund_account;
@property (nonatomic, strong) NSString* jce_stock_type;
@end

@interface HAZQWinLotsRsp : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iRet;
@property (nonatomic, strong) NSString* jce_sMsg;
@property (nonatomic, strong) NSArray<HAZQWinLots*>* jce_vWinLots;
@end



