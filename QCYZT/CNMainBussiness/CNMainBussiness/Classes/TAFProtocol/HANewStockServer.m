// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `HANewStockServer.jce'
// **********************************************************************

#include "HANewStockServer.h"

@implementation HAZQNewStockReq

@synthesize jce_date = J_PROP_NM(o,0,date);

- (id)init
{
    if (self = [super init]) {
        J_PROP(date) = DefaultJceString;
    }
    return self;
}

@end

@implementation HAZQNewStock

@synthesize jce_stock_name = J_PROP_NM(o,0,stock_name);
@synthesize jce_stock_code = J_PROP_NM(o,1,stock_code);
@synthesize jce_close_price = J_PROP_NM(o,2,close_price);
@synthesize jce_market_date = J_PROP_NM(o,3,market_date);
@synthesize jce_exchange_type = J_PROP_NM(o,4,exchange_type);
@synthesize jce_stype = J_PROP_NM(o,5,stype);

- (id)init
{
    if (self = [super init]) {
        J_PROP(stock_name) = DefaultJceString;
        J_PROP(stock_code) = DefaultJceString;
        J_PROP(close_price) = DefaultJceString;
        J_PROP(market_date) = DefaultJceString;
        J_PROP(exchange_type) = DefaultJceString;
        J_PROP(stype) = DefaultJceString;
    }
    return self;
}

@end

@implementation HAZQNewStockRsp

@synthesize jce_iRet = J_PROP_NM(r,0,iRet);
@synthesize jce_sMsg = J_PROP_NM(o,1,sMsg);
@synthesize jce_vNewStockList = J_PROP_EX(o,2,vNewStockList,VOHAZQNewStock);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sMsg) = DefaultJceString;
    }
    return self;
}

@end

@implementation HAZQWinLotsReq

@synthesize jce_cust_no = J_PROP_NM(o,0,cust_no);

- (id)init
{
    if (self = [super init]) {
        J_PROP(cust_no) = DefaultJceString;
    }
    return self;
}

@end

@implementation HAZQWinLots

@synthesize jce_stock_name = J_PROP_NM(o,0,stock_name);
@synthesize jce_stock_code = J_PROP_NM(o,1,stock_code);
@synthesize jce_stock_price = J_PROP_NM(o,2,stock_price);
@synthesize jce_amount = J_PROP_NM(o,3,amount);
@synthesize jce_fund_account = J_PROP_NM(o,4,fund_account);
@synthesize jce_stock_type = J_PROP_NM(o,5,stock_type);

- (id)init
{
    if (self = [super init]) {
        J_PROP(stock_name) = DefaultJceString;
        J_PROP(stock_code) = DefaultJceString;
        J_PROP(stock_price) = DefaultJceString;
        J_PROP(amount) = DefaultJceString;
        J_PROP(fund_account) = DefaultJceString;
        J_PROP(stock_type) = DefaultJceString;
    }
    return self;
}

@end

@implementation HAZQWinLotsRsp

@synthesize jce_iRet = J_PROP_NM(r,0,iRet);
@synthesize jce_sMsg = J_PROP_NM(o,1,sMsg);
@synthesize jce_vWinLots = J_PROP_EX(o,2,vWinLots,VOHAZQWinLots);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sMsg) = DefaultJceString;
    }
    return self;
}

@end



