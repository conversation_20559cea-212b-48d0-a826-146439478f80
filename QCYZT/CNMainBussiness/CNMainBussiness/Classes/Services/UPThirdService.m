//
//  UPThirdService.m
//  UPStockMain
//
//  Created by sa<PERSON><PERSON> on 2020/3/3.
//  Copyright © 2020 UpChina. All rights reserved.
//

// 屏蔽bugly

#import <Bugly/Bugly.h>
#import <FLEX/FLEX.h>
//#import <UPTAF/UPTAF.h>
//#import <UPCommon/UPCommon.h>
#import "UPThirdService.h"

@implementation UPThirdService

+(void)initCrashHanlder:(BOOL)fullInit {
    if(fullInit) {
        BuglyConfig * config = [[BuglyConfig alloc] init];
        config.blockMonitorEnable = YES;
        config.blockMonitorTimeout = 15;
        config.reportLogLevel = BuglyLogLevelDebug;

#ifdef DEBUG
        config.debugMode = YES;
#endif

        config.deviceIdentifier = [UPTAFManager GUIDString];
        config.version = [NSBundle mainBundle].infoDictionary[@"CFBundleShortVersionString"];

//#if (defined DEBUG) || (defined UP_BUILD_TEST)
//        [Bugly startWithAppId:@"9a6050485c" developmentDevice:YES config:config];
//#else
        [Bugly startWithAppId:@"6f7e71350e" config:config];
//#endif

        [Bugly setUserValue:[UPTAFManager GUIDString] forKey:@"GUID"];
        [Bugly setUserValue:[UPTAFManager XUA] forKey:@"XUA"];
    } else {
        BuglyConfig * config = [[BuglyConfig alloc] init];
        config.version = [NSBundle mainBundle].infoDictionary[@"CFBundleShortVersionString"];
        [Bugly startWithAppId:@"6f7e71350e" config:config];
    }
}

+(void)showAppDebugTool {
#ifdef DEBUG
    [UPToastView show:@"FLEX不支持Debug版本,请使用真机测试!"];
#else
    [[FLEXManager sharedManager] showExplorer];
#endif
}

@end


