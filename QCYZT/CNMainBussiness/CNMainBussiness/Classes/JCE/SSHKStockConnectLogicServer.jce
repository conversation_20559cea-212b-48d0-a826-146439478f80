module DataCenter
{
    //一、沪深港通当日余额
    //沪港通当日余额
    //HKSH_MON_FLOW
    //深港通当日余额
    //HK_SZMON_FLOW
    struct SSHKDailyBalance
    {
        0 optional int iPubTime;       //更新时间,Unix时间戳
        1 optional int iMktType;       //沪深港通分类（1.沪股通 2.港股通(沪) 3.深股通 4.港股通(深)）
        2 optional string fBalance;    //当日额度余额(单位：亿元)
        3 optional int fDailyLimit;    //当日额度(单位：亿元)
        4 optional string fMoneyFlow;  //资金流出(单位：亿元)
    };

    //二、沪深港资金流向、资金余额的日分时走势
    struct SSHKMoneyFlow
    {
        0 optional int iPubTime;       //更新时间,Unix时间戳
        1 optional int iMktType;       //沪深港通分类（1.沪股通 2.港股通(沪) 3.深股通 4.港股通(深)）
        2 optional string fBalance;    //当日额度余额(单位：亿元)
        3 optional int fDailyLimit;    //当日额度(单位：亿元)
        4 optional string fMoneyFlow;  //资金流出(单位：亿元)
    };

    //三、沪深港资金流向、资金余额的历史分日走势
    struct SSHKMoneyFlowHS
    {
        0 optional int iPubTime;       //更新时间,YYYYMMDD
        1 optional int iMktType;       //沪深港通分类（1.沪股通 2.港股通(沪) 3.深股通 4.港股通(深)）
        2 optional string fBalance;    //当日额度余额(单位：亿元)
        3 optional int fDailyLimit;    //当日额度(单位：亿元)
        4 optional string fMoneyFlow;  //资金流出(单位：亿元)
    };

    //四、沪深港通前十成交股
    //(HKSH_TEN_DEAL和HKSZ_TEN_DEAL)
    struct SSHKStockTOP10
    {
        0 optional int iTradeDate; //截止日期
        1 optional int iSampType;   //标的类型（1.沪股通 2.港股通(沪) 3.深股通 4.港股通(深)）
        2 optional int iSecUniCode; //股票名称
        3 optional string sStockName; //股票名称
        4 optional string sStockCode; //股票代码
        5 optional int iMktTypePar; //市场(1.深圳,2.上海,3.香港)
        6 optional int iRank; //排名按交易日期和标的类型参数来分组按买入及卖出金额(TRADE_AMT)降序排名
        7 optional string fClosePrice; //收盘价
        8 optional string fChgRate; //涨跌幅
        9 optional string fBuyNet; //净买额//（4）净买额=买入金额-卖出金额；
        10 optional string fBuyAmount; //买入金额
        11 optional string fSellAmount; //卖出金额
        12 optional string fTradingAmount; //成交金额
    };
    //Current & MaxTradeDate

    //五、沪深港通前十成交股
    //(HKSH_TEN_DEAL和HKSZ_TEN_DEAL)
    struct SSHKStockTOP10ByCode
    {
        0 optional int iTradeDate; //截止日期
        1 optional int iSampType;   //标的类型（1.沪股通 2.港股通(沪) 3.深股通 4.港股通(深)）
        2 optional int iSecUniCode; //股票名称
        3 optional string sStockName; //股票名称
        4 optional string sStockCode; //股票代码
        5 optional int iMktTypePar; //市场(1.深圳,2.上海,3.香港)
        6 optional int iRank; //排名按交易日期和标的类型参数来分组按买入及卖出金额(TRADE_AMT)降序排名
        7 optional string fClosePrice; //收盘价
        8 optional string fChgRate; //涨跌幅
        9 optional string fBuyNet; //净买额//（4）净买额=买入金额-卖出金额；
        10 optional string fBuyAmount; //买入金额
        11 optional string fSellAmount; //卖出金额
        12 optional string fTradingAmount; //成交金额
    };
    //Current & MaxTradeDate

    //六、沪深港通标的证券涨幅榜
    struct SSHKStockRise
    {
        0 optional string sAStockName; //沪深股票名称
        1 optional string sAStockCode; //沪深股票代码
        2 optional int iMktTypePar; //沪深股票市场
        3 optional string fAPrice; //沪深股票价格
        4 optional string fAChgRatio; //沪深股票涨跌幅
        5 optional string sHStockName; //港股名称
        6 optional string sHStockCode;  //港股代码
        7 optional string fHPrice;  //港股价格
        8 optional string fHChgRatio; //港股涨跌幅
        9 optional string fAHPrice; //AH股价格（AH股=(A股价/H股人民币价)）
    };

    //六、沪深港通标的证券信息
    struct SSHKStockRiseByCode
    {
        0 optional string sStockName; //股票名称
        1 optional string sStockCode; //股票代码
        2 optional int iSampType; //标的类型（1.沪股通 2.港股通(沪) 3.深股通 4.港股通(深)）
    };

    struct SSHKDailyBalanceReq
    {
        0 optional string sBusId;       //接入业务名
    };

    struct SSHKDailyBalanceRsp
    {
        0 optional int iRet;            //返回状态
        1 optional string sMsg;         //提示信息
        2 optional vector<SSHKDailyBalance> vSSHKDailyBalance;
    };

    enum E_MktType
    {
        hgt = 1, //沪股通
        ggth = 2, //港股通(沪)
        sgt = 3, //深股通
        ggts = 4, //港股通(深)
    };

    struct SSHKMoneyFlowReq
    {
        0 optional string sBusId;       //接入业务名
        1 optional vector<E_MktType> E_MktTypes;       //沪深港通分类（1.沪股通 2.港股通(沪) 3.深股通 4.港股通(深)）
    };

    struct SSHKMoneyFlowRsp
    {
        0 optional int iRet;            //返回状态
        1 optional string sMsg;         //提示信息
        2 optional vector<SSHKMoneyFlow> vSSHKMoneyFlow;
    };

    struct SSHKMoneyFlowHSReq
    {
        0 optional string sBusId;       //接入业务名
        1 optional int iStartDate;      //开始日期
        2 optional int iEndDate;        //结束日期
    };

    struct SSHKMoneyFlowHSRsp
    {
        0 optional int iRet;            //返回状态
        1 optional string sMsg;         //提示信息
        2 optional vector<SSHKMoneyFlowHS> vSSHKMoneyFlowHS;
    };

    struct SSHKStockTOP10Req
    {
        0 optional string sBusId;       //接入业务名
    };

    struct SSHKStockTOP10Rsp
    {
        0 optional int iRet;            //返回状态
        1 optional string sMsg;         //提示信息
        2 optional vector<SSHKStockTOP10> vSSHKStockTOP10;
        3 optional int iTotal;
    };

    struct SSHKStockTOP10ByCodeReq
    {
        0 optional string sBusId;       //接入业务名
        1 optional int iMktTypePar;      //市场
        2 optional string sSecCode;      //代码
    };

    struct SSHKStockTOP10ByCodeRsp
    {
        0 optional int iRet;            //返回状态
        1 optional string sMsg;         //提示信息
        2 optional vector<SSHKStockTOP10ByCode> vSSHKStockTOP10ByCode;
        3 optional int iTotal;
    };

    struct SSHKStockRiseReq
    {
        0 optional string sBusId;       //接入业务名
        1 optional string sPlateUniCode; //板块(板块代码分别是5004230001(沪股通标的证券)，5004230002 (港股通(沪)标的证券)，5004240001 (深股通标的证券)，5004240002 (港股通(深)标的证券)，5004210004 (AH股)；)
    };

    struct SSHKStockRiseRsp
    {
        0 optional int iRet;            //返回状态
        1 optional string sMsg;         //提示信息
        2 optional vector<SSHKStockRise> vSSHKStockRise;
        3 optional int iTotal;
    };

    struct SSHKStockRiseByCodeReq
    {
        0 optional string sBusId;       //接入业务名
        1 optional int iMktTypePar;      //市场
        2 optional string sSecCode;      //代码
    };

    struct SSHKStockRiseByCodeRsp
    {
        0 optional int iRet;            //返回状态
        1 optional string sMsg;         //提示信息
        2 optional vector<SSHKStockRiseByCode> vSSHKStockRiseByCode;
    };

    //五、沪深港通成交榜查询出口
    //六、沪深港通成交榜查询入口
    interface SSHKStockConnectLogicServer
    {
        //一、沪深港通当日余额
        int getSSHKDailyBalance(SSHKDailyBalanceReq req, out SSHKDailyBalanceRsp rsp);
        //二、沪深港资金流向、资金余额的日分时走势
        int getSSHKMoneyFlow(SSHKMoneyFlowReq req, out SSHKMoneyFlowRsp rsp);
        //三、沪深港资金流向、资金余额的历史分日走势
        int getSSHKMoneyFlowHS(SSHKMoneyFlowHSReq req, out SSHKMoneyFlowHSRsp rsp);
        //四、沪深港通前十成交股
        int getSSHKStockTOP10(SSHKStockTOP10Req req, out SSHKStockTOP10Rsp rsp);
        //五、沪深港通前十成交股
        int getSSHKStockTOP10ByCode(SSHKStockTOP10ByCodeReq req, out SSHKStockTOP10ByCodeRsp rsp);
        //六、沪深港通标的证券涨幅榜
        int getSSHKStockRiseList(SSHKStockRiseReq req, out SSHKStockRiseRsp rsp);
        //六、沪深港通标的证券信息
        int getSSHKStockRiseByCode(SSHKStockRiseByCodeReq req, out SSHKStockRiseByCodeRsp rsp);

    };
};
