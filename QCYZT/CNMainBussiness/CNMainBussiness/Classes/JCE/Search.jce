#include "/home/<USER>/PickStock/libPickInterface.a/PickData.jce"

module PickStock
{
    struct ReqClient
    {
        0 optional string sUserId; //客户端ID
        1 optional string sXua;   //客户端信息
    };
    
    struct GetStockReq
    {
        0 optional ReqClient stClient;
        1 optional vector<ConditionItem> vCondList;
        
        //指定选股日期，如果填0，表示为最近一个交易日 
        2 optional int iDate = 0;
		3 optional int iMaxNum = 0;  // 最大返回的股票数量，0代表返回所有
		4 optional E_TARGET_HQ_DETAIL eHqDetail = HQ_DETAIL_NONE;  // 选择性返回行情数据
		5 optional int iSortColumn = 0; // 按照某一列排序，0代表默认排序
		6 optional E_SORT_TYPE eSort = SORT_TYPE_DESC; // 升序 or 降序
    };
    
    struct GetStockRsp
    {
        1 optional vector<TargetStockInfo> vStock; 
        2 optional int iTotalNum; // 实际的选股结果总数（分页）
        3 optional int iTime; // 数据最新更新时间-年月日（例如20180101，即2018年1月1日）
    };
    
    struct GetStockHqReq
    {
    	1 optional vector<UniqueStock> vStockId;
    };
    
    struct GetStockHqRsp
    {
    	1 optional vector<SearchStockHq> vStockHq;
    };
    
    struct GetSortMapReq
    {
    	1 optional bool bAll = true;
    };
    
    struct GetSortMapRsp
    {
    	1 optional map<E_CONDITION_FACTOR, int> mFactor2Sort;
    };
    
    interface Search
    {         
        int getStock(GetStockReq stReq, out GetStockRsp stRsp);  // 选股
        
        int getSortMap(GetSortMapReq stReq, out GetSortMapRsp stRsp); // 获取排序列
        
        int getStockHq(GetStockHqReq stReq, out GetStockHqRsp stRsp);  // 获取实时行情 - 内部使用 
    };
};