//
//  UPMainLaunchADView.m
//  UPStockMain
//
//  Created by sammy<PERSON> on 2020/5/10.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPTAF/TAFHandler.h>
#import <UPServiceSDK/UPADManager.h>

#import "UPMainLaunchADView.h"

@interface UPMainLaunchADView () <UPTAFHandlerDelegate, UPADDelegate>

@property(nonatomic, strong) UPTAFHandler * handler;

@property(nonatomic, strong) UPADInfo * adInfo;

@property(nonatomic, assign) NSInteger adCounter;
@property(nonatomic, strong) UIButton * skipButton;

@end

@implementation UPMainLaunchADView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.handler = [UPTAFHandler mainHandlerWithDelegate:self];

        if (![UPAppConfig isRving]) {
            [self.handler sendMessageDelayed:0 delayMillis:10];
        } else {
            [UPADManager getAD_wup:kUPADManagerPositionLaunchAD uid:nil delegate:self];

            // 等待1.5秒获取广告
            [self.handler sendMessageDelayed:0 delayMillis:1500];
        }
      
    }
    return self;
}

// MARK: UPTAFHandlerDelegate

- (void)handleMessage:(int)what object:(id)anObject {
    if(what == 0) {
        [self openAD:nil];
    } else if(what == 1) {
        self.adInfo = (UPADInfo *)anObject;
        UPADMaterialInfo * materialInfo = self.adInfo.materials.lastObject;

        //固定为3秒
        self.adCounter = 3;

        if(materialInfo) {
            // 上报展示
            [UPADManager reportShowAdMaterialInfo:kUPADManagerPositionLaunchAD materialId:materialInfo.materialId uid:[UPUserManager uid] putId:materialInfo.iPutId];
            // 广告图片
            {
                UIImageView * imageView = [[UIImageView alloc] init];
                imageView.frame = self.bounds;
                imageView.userInteractionEnabled = YES;
                [imageView up_setImageWithURLString:materialInfo.imageUrl];
                UITapGestureRecognizer * tapRecognizer = [[UITapGestureRecognizer alloc] init];
                [tapRecognizer addTarget:self action:@selector(adDidClick)];
                [imageView addGestureRecognizer:tapRecognizer];

                [self addSubview:imageView];
            }
        } else if(what == 2) {
            self.adCounter -= 1;

            if(self.adCounter > 0) {
                NSString * text = [NSString stringWithFormat:@"跳过 %d", (int)self.adCounter];

                [self.skipButton setTitle:text forState:UIControlStateNormal];

                [self.handler sendMessageDelayed:2 delayMillis:1000];
            } else {
                [self openAD:nil];
            }
        }
    }
    
}

// MARK: UPADDelegate

-(void)upADResponse:(UPADError)error info:(nullable UPADInfo *)info {
    if(error == UPADErrorNone) {
        // TODO: 下载完图片以后再回调, 避免半天展示不出来
        [self.handler removeMessage:0];
        //等待一会   launchScreenView屏太快被盖住了 界面显示效果有点闪烁
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.handler sendMessage:1 object:info];
        });
    } else {
        [self openAD:nil];
    }
}

// MARK: Private

-(void)adDidClick {
    // 上报点击
    [UPADManager reportClickAdMaterialInfo:kUPADManagerPositionLaunchAD materialId:self.adInfo.materials.lastObject.materialId uid:[UPUserManager uid] putId:self.adInfo.materials.lastObject.iPutId];

    [self openAD:self.adInfo.materials.lastObject.url];
}

-(void)skipDidClick {
    [self openAD:nil];
}

-(void)openAD:(NSString *)url {
    [self.handler removeAll];

    __strong id<UPMainLaunchADViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(launchADView:openURL:)]) {
        [delegate launchADView:self openURL:url];
    }
}

@end
