//
//  UPStockPagerNewsList.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/19.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockPagerNewsList.h"
#import <UPCommon/UPCommon.h>
#import <UPUserSDK/UPOptionalManager.h>

@interface UPStockPagerNewsList ()<UITableViewDataSource, UITableViewDelegate>

@property (copy, nonatomic) void (^finished)(void);

@property (strong, nonatomic) UPMarketMonitor *marketMonitor;

@property (assign, nonatomic) dispatch_once_t onceToken;

@property (strong, nonatomic) NSMutableDictionary *heightDict;

@end

@implementation UPStockPagerNewsList

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.tableView];
        [self addSubview:self.emptyView];
        
        [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
        
        [self.emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
        
        [self customizeTableViewHeaderAndFooter];
     
        [self registerCell:self.cellClassArray];
    }
    return self;
}

#pragma mark - Public

- (UITableViewCell *)displayCellAtIndexPath:(NSIndexPath *)indexPath forTableView:(UITableView *)tableView {
    // cell展示逻辑，子类重写
    return [UITableViewCell new];
}

- (UIView *)headerViewInSection {
    return [UIView new];
}

-(CGFloat)heightHeaderViewInSection {
    return 0;
}



- (void)didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    UPNewsListInfo *model = self.modelsArray[indexPath.row];
    if (model.baseInfo.recomType == UPNewsRecomTypeNotice) {
        if(!IsValidateString(model.mediaInfo.pdfUrl)){
            // 无pdf时仍然跳转H5页面
            [UPNewsManager addHasRead:model.baseInfo.newsID];
            [UPRouter navigate:model.baseInfo.linkURL];
            [self.tableView reloadData];

            return;
        }
        
        NSString *articleTitle = @"";
        if (IsValidateString(model.stockArray.firstObject.name)) {
            articleTitle = [NSString stringWithFormat:@"%@公告",model.stockArray.firstObject.name];
        } else {
            articleTitle = IsValidateString(model.baseInfo.title) ? model.baseInfo.title : @"";
        }
        
        NSString *navigateURL = [@"upchina://pdf/main?" up_buildURLWithQueryParams:@{
            @"url" : IsValidateString(model.mediaInfo.pdfUrl) ? model.mediaInfo.pdfUrl : @"",
            @"articleTitle" : articleTitle,
            @"sourceURL" : IsValidateString(model.baseInfo.linkURL) ? model.baseInfo.linkURL : @"",
            @"message" : IsValidateString(model.baseInfo.linkURL) ? model.baseInfo.summary : @"",
            @"showShare" : @(YES)
        }];
        [UPRouter navigate:navigateURL];
    } else {
        [UPRouter navigate:model.baseInfo.linkURL];
    }
    [UPNewsManager addHasRead:model.baseInfo.newsID];
    [self.tableView reloadData];
}

- (void)handleDataArray:(NSArray *)dataArray isMore:(BOOL)isMore {
    if (isMore) {
        NSMutableArray *array = [NSMutableArray arrayWithArray:self.modelsArray];
        [array addObjectsFromArray:dataArray];
        self.modelsArray = [NSArray arrayWithArray:array];
    } else {
        self.modelsArray = dataArray;
    }
    
    // 下拉刷新时回到顶部
    if (!isMore && self.modelsArray.count && self.tableView.numberOfSections && [self.tableView numberOfRowsInSection:0]) {
        
        [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0] atScrollPosition:UITableViewScrollPositionNone animated:NO];
    }
    
    [self.tableView reloadData];

    if (self.shouldMonitor) {
        [self startMonitor];
    }
    
    if (self.modelsArray.count) {
        [self hideEmptyView];
    } else {
        [self showEmptyViewIsError:NO];
    }
    
    self.tableView.up_enableLoadMore = !(dataArray.count < 20);

    if (!isMore) {
        if (self.finished) {
            self.finished();
        }
    }
}

- (NSArray<UPHqStockUnique *> *)getStockArrayWithNewsInfoArray:(NSArray *)dataArray{
    NSMutableArray *mutableArray = [NSMutableArray array];
    [dataArray enumerateObjectsUsingBlock:^(UPNewsListInfo *obj, NSUInteger idx, BOOL *stop) {
        if (![obj isKindOfClass:[UPNewsListInfo class]]) return;
        if (obj.stockArray.count) {
            for (UPNewsStockInfo *stockInfo in obj.stockArray) {
                UPHqStockUnique *unique = [[UPHqStockUnique alloc] init];
                unique.setCode = stockInfo.setCode;
                unique.code = stockInfo.code;
                [mutableArray addObject:unique];
            }
        }
    }];
    return [NSArray arrayWithArray:mutableArray];
}

- (void)monitorStockWithDataArray:(NSArray<UPHqStockUnique *> *)dataArray {
    if (!dataArray.count) {
        return;
    }
    
    UPMarketStockHqReq *hqReq = [[UPMarketStockHqReq alloc] initWithStockArray:dataArray.copy];
    
    __weak typeof(self) weakSelf = self;
    dispatch_once_t *onceTokenPoint = &_onceToken;
    [self.marketMonitor stopMonitorWithTag:0];
    [self.marketMonitor startMonitorStockHq:hqReq tag:0 completionHandler:^(UPMarketStockHqRsp *rsp, NSError *error) {
        // self被释放之后该block仍然会被调用
        if (!weakSelf) return;
        if (rsp.dataArray.count) {
            for (UPHqStockHq *obj in rsp.dataArray) {
                [weakSelf.stockInfoDict setValue:obj forKey:obj.code];
            }
        }

        // 先立即刷新一次，之后每3秒刷一次
//        dispatch_once(onceTokenPoint, ^{
//            [weakSelf performSelector:@selector(reloadData)];
//        });
        // 拉动的时候不刷新，因为有可能会抖动，不加到commonMode中。
        [weakSelf performSelector:@selector(reloadData) withObject:nil afterDelay:3];
    }];
}

- (void)reloadData {
    [[self class] cancelPreviousPerformRequestsWithTarget:self];
    [self.tableView reloadData];
}

- (void)startMonitor {
    [self monitorStockWithDataArray:[self getStockArrayWithNewsInfoArray:self.modelsArray]];
}

- (void)stopMonitor {
    [self.marketMonitor stopMonitorWithTag:0];
}

// cell中展示stockArray中的前两个股票的涨跌幅
- (void)updateStockInfoWithModel:(UPNewsListInfo *)infoModel {
    // 第一个
    if (infoModel.stockArray.count < 1) return;
    
    UPNewsStockInfo *stockInfo = infoModel.stockArray.firstObject;
    UPHqStockHq *hqInfo = [self.stockInfoDict valueForKey:stockInfo.code];
    if (hqInfo) {
        stockInfo.nowPrice = hqInfo.nowPrice;
        stockInfo.changeRatio = hqInfo.changeRatio;
        stockInfo.changeValue = hqInfo.changeValue;
    }
    
    // 第二个
    if (infoModel.stockArray.count < 2) return;
        
    UPNewsStockInfo *stockInfo2 = infoModel.stockArray[1];
    UPHqStockHq *hqInfo2 = [self.stockInfoDict valueForKey:stockInfo2.code];
    if (hqInfo2) {
        stockInfo2.nowPrice = hqInfo2.nowPrice;
        stockInfo2.changeRatio = hqInfo2.changeRatio;
        stockInfo2.changeValue = hqInfo2.changeValue;
    }
}

- (void)goToStockDetailWithSetCode:(NSInteger)setCode code:(NSString *)code {
    [UPRouterUtil goMarketStock:setCode code:code];
}

#pragma mark - Network

-(void)requestDataIsMore:(BOOL)isMore {
    [self requestNewsInfoIsMore:isMore];
}

- (void)requestNewsInfoIsMore:(BOOL)isMore {
    UPNewsIDListReq *req = [self customizeRequestIsMore:isMore];
    if (!req) return;
    
    [UPNewsManager requestBriefNewsList:req completionHandler:^(UPNewsListRsp *rsp, NSError *error) {
        if(!error){
            [self handleDataArray:rsp.newsInfoArray isMore:isMore];
        } else {
            if(!self.modelsArray.count) {
                [self showEmptyViewIsError:YES];
            }
        }
        [self.tableView up_endLoadMore];
        [self.tableView up_endPullRefresh];
    }];
}

- (UPNewsIDListReq *)customizeRequestIsMore:(BOOL)isMore {
    // 默认的请求类生成逻辑，子类可重写
    UPNewsIDListReq *req = [[UPNewsIDListReq alloc] init];
    req.listType = self.newsListType;
    if (self.isSelfChooseStockNews) {
        req.chooseStk = [UPOptionalManager currentOptionalData];
        req.isChooseType = YES;
        if (self.tagInfo) {
            req.tagInfo = self.tagInfo;
        }
    }
    
    if (isMore) {
        UPNewsListInfo *lastModel = self.modelsArray.lastObject;
        req.direction = UPNewsRequestDirectionHistory;
        req.startID = lastModel.baseInfo.newsID;
    } else {
        req.direction = UPNewsRequestDirectionNew;
    }
    req.requestNum = 20;
    return req;
}

#pragma mark - Private

- (void)customizeTableViewHeaderAndFooter {
    self.tableView.up_enablePullRefresh = YES;
    self.tableView.up_refreshStyle = UPRefreshStyleAuto;
    
    [self.tableView up_setPullRefreshTarget:self action:@selector(refresh)];
  
    [self.tableView up_setLoadMoreTarget:self action:@selector(loadMore)];
}

- (void)loadMore {
    [self requestDataIsMore:YES];
}

- (void)refresh {
    [self requestDataIsMore:NO];
}

- (void)registerCell:(NSArray *)array {
    // 初始化时数组为空，需要在设置后再注册
    for (Class cls in array) {
        [self.tableView registerClass:cls forCellReuseIdentifier:NSStringFromClass(cls)];
    }
}

- (void)showEmptyViewIsError:(BOOL)isError {
    [self.emptyView showIsError:isError];
    if (!self.isShowEmtpyNotHideTableView) {
        self.tableView.hidden = YES;
    }
    [self bringSubviewToFront:self.emptyView];
}

- (void)hideEmptyView {
    self.emptyView.hidden = YES;
    self.tableView.hidden = NO;
    [self bringSubviewToFront:self.emptyView];
}
#pragma mark - UPPagerViewListViewDelegate

- (void)listWillAppear {
//    [self requestDataIsMore:NO];
    if (self.shouldMonitor) {
        [self startMonitor];
    }
}

- (void)listWillDisappear{
    [self stopMonitor];
}

- (UIView *)listView {
    return self;
}

- (UIScrollView *)listScrollView {
    return self.tableView;
}

- (void)listViewDidScrollCallback:(void (^)(UIScrollView *scrollView))callback {
    self.scrollCallback = callback;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}

- (void)refreshDataFinished:(void (^)(void))finished {
    self.finished = finished;
    [self requestDataIsMore:NO];
}

#pragma mark - UITableViewDelegate && UITableViewDataSource

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    return [self displayCellAtIndexPath:indexPath forTableView:tableView];
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat height = cell.frame.size.height;
    [self.heightDict setValue:@(height) forKey:[NSString stringWithFormat:@"%zd",indexPath.row]];
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat height = [[self.heightDict valueForKey:[NSString stringWithFormat:@"%zd",indexPath.row]] floatValue];
    if (height) {
        return height;
    } else {
        return UITableViewAutomaticDimension;
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    // 暂时写死为1，后期看情况是否需要拉出属性支持扩展
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.modelsArray.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [self didSelectRowAtIndexPath:indexPath];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [self headerViewInSection];
}


- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return [self heightHeaderViewInSection];
}



#pragma mark - Getter && Setter

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.estimatedRowHeight = 44;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = UIColor.up_bgColor;
    }
    return _tableView;
}

- (UPMarketMonitor *)marketMonitor {
    if(!_marketMonitor) {
        _marketMonitor = [[UPMarketMonitor alloc] init];
    }
    return _marketMonitor;
}

- (NSMutableDictionary *)stockInfoDict {
    if (!_stockInfoDict) {
        _stockInfoDict = [NSMutableDictionary dictionaryWithCapacity:10];
    }
    return _stockInfoDict;
}

- (UPErrorView *)emptyView {
    if (!_emptyView) {
        _emptyView = [UPErrorView new];
        _emptyView.hidden = YES;
        _emptyView.title = @"暂无自选资讯";
    }
    return _emptyView;
}

- (NSMutableDictionary *)heightDict {
    if (!_heightDict) {
        _heightDict = [NSMutableDictionary dictionaryWithCapacity:10];
    }
    return _heightDict;
}
@end
