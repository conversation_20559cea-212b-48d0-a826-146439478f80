//
//  UPHomeMainViewView.m
//  UPStockMain
//
//  Created by lizhixiang on 2022/9/20.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import "UPHomeMainViewView.h"
#import "UPHomeMainLiveViewCell.h"
#import "UPHomeMainViewJZCell.h"
//#import "UPLiveJZModel.h"
//#import "UPLiveRoomModel.h"
//#import <UPDongfangSDK/UPDongfangSDK.h>
#import <UPCommon/UPDFHttpQuestManager.h>


@interface UPHomeMainViewView () <UITableViewDelegate, UITableViewDataSource>


@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *totalDataArray; // 原数据（未加工）

@property (strong, nonatomic) UPErrorView *emptyView;
@property (strong, nonatomic) UIView *tipsView;
@property (strong, nonatomic) UILabel *tipsLable;
@property (strong, nonatomic) UITextView *tipstextView;
@property (strong, nonatomic) UILabel *tipstextLable;
@property (nonatomic, strong) UPPopupView *pop;

@property (nonatomic, copy) void (^scrollCallback) (UIScrollView *scrollView);
@property (nonatomic, strong) UPPopupView *popView;


@end

@implementation UPHomeMainViewView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self layoutUI];
        
        [self.tableView up_setLoadMoreTarget:self action:@selector(onLoadMore)];
        [self addNotificationObserver];

    }
    return self;
}


- (void)addNotificationObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(requestData)
                                                 name:UPNotifyUserInfoUpdated
                                               object:nil];
}

- (void)removeNotificationObserver {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)dealloc {
    [self removeNotificationObserver];
}

- (void)layoutUI {
    [self addSubview:self.tableView];
    [self addSubview:self.emptyView];
    
    [self.emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}


// MARK: - Getter && Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.estimatedRowHeight = 373;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = [UIColor up_colorFromHexString:@"#F4F8FB"];
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
        [_tableView registerClass:[UPHomeMainLiveViewCell class] forCellReuseIdentifier:@"UPHomeMainLiveViewCell"];
        [_tableView registerClass:[UPHomeMainViewJZCell class] forCellReuseIdentifier:@"UPHomeMainViewJZCell"];
    }
    return _tableView;
}

// MARK: - UITableViewDelegate
// cell 行高
//- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
//    return UITableViewAutomaticDimension;
//}


- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
//    if (indexPath.section > 0) {
//    [DNStatisticMananger clickWithId:DNSCENEID_SY_GDXXL];
        if ([UPUserManager uid].length > 0) {
            if (self.totalDataArray.count == 4) {
                if (indexPath.section == 1) {
                    [UPRouter navigate:UPURLHomeViewJZDS];
                } else {
                    if (indexPath.section == 2) {
                        [UPRouter navigate:UPURLHomeViewDSJP];
                    } else {
                        [UPRouter navigate:UPURLHomeViewDSDP];
                    }
                }
            } else {
                if (indexPath.section == 0) {
                    [UPRouter navigate:UPURLHomeViewJZDS];
                } else {
                    if (indexPath.section == 1) {
                        [UPRouter navigate:UPURLHomeViewDSJP];
                    } else {
                        [UPRouter navigate:UPURLHomeViewDSDP];
                    }
                }
            }
          
            
        } else {
            [UPRouterUtil goUserLogin];
        }

//    }
}


- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {

    if (!IsValidateArray((NSArray *)self.totalDataArray[section]) && section ==0) {
        return 0;
    }
    
    return 60;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (!IsValidateArray((NSArray *)self.totalDataArray[section]) && section ==0) {
        return 0;
    }
    if (self.totalDataArray.count == 4) {
        if (section > 0 && [(NSArray *)self.totalDataArray[section] count] >= 3) {
            return 74;
        }
    } else {
        if (section > 0 && [(NSArray *)self.totalDataArray[section] count] >= 2) {
            return 74;
        }
    }
   
    return 10;
    
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    if (section > 0 && [(NSArray *)self.totalDataArray[section] count] >= 3) {
        UIView *vv = [[UIView alloc] init];
        vv.tag = 9999+section;
        vv.backgroundColor = [UIColor up_contentBgColor];
        
        UIView *lineView = [UIView new];
        lineView.backgroundColor = [UIColor up_colorFromHexString:@"#F4F8FB"];
        
        [vv addSubview:lineView];
        [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.centerX.equalTo(vv);
            make.size.mas_equalTo(CGSizeMake(345, 1));
        }];
        
        UILabel *label = [[UILabel alloc] init];
        label.text = @"查看更多";
        label.font = [UIFont up_fontOfSize:14];
        label.textColor = UIColor.up_bgHotStockTextColor;
        label.textAlignment = NSTextAlignmentCenter;
        label.backgroundColor = UIColor.up_contentBgColor;
        [vv addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(vv.mas_top).offset(1);
            make.centerX.width.equalTo(vv);
            make.height.mas_equalTo(@60);
        }];
        
        
        UIView *lineBView = [UIView new];
        lineBView.backgroundColor = [UIColor up_colorFromHexString:@"#F4F8FB"];
        
        [vv addSubview:lineBView];
        [lineBView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.centerX.equalTo(vv);
            make.left.right.equalTo(label);
            make.top.equalTo(label.mas_bottom);
        }];
        
        
        UITapGestureRecognizer * tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(gotoMore:)];
        [vv addGestureRecognizer:tap];
        return vv;
    } else {
        UIView *vv = [[UIView alloc] init];
        vv.backgroundColor = [UIColor up_colorFromHexString:@"#F4F8FB"];
        return vv;
    }
    
    
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView * view = [[UIView alloc] init];
    view.tag = 8880 + section;
    view.backgroundColor = UIColor.up_contentBgColor;
    
    UIView * iconView = [[UIView alloc] init];
    iconView.backgroundColor = UIColor.up_brandColor;
    iconView.frame = CGRectMake(14, 34, 5, 12);
    iconView.layer.cornerRadius = 2.5;
    iconView.layer.masksToBounds = YES;
    [view addSubview:iconView];
    
    UILabel *label = [[UILabel alloc] init];
    label.font = [UIFont up_boldFontOfSize:18];
    label.textColor = UIColor.up_textPrimaryColor;
    label.textAlignment = NSTextAlignmentLeft;
    [view addSubview:label];
    label.frame = CGRectMake(30, 20, 78, 40);
    
    UIImageView * more = [[UIImageView alloc] initWithImage:UPTImg(@"智选/更多")];
    [view addSubview:more];
    [more mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(label);
        make.right.equalTo(view).offset(-15);
        make.width.equalTo(@5);
        make.height.equalTo(@9);
    }];
    
    UIButton *unlockButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [unlockButton setTitle:@"点击解锁" forState:0];
    unlockButton.hidden = YES;
    unlockButton.userInteractionEnabled = NO;
//    [unlockButton addTarget:self action:@selector(unlock) forControlEvents:UIControlEventTouchUpInside];
    [unlockButton setTitleColor:UIColor.up_brandColor forState:0];
    [unlockButton up_setBackgroundColor:[UIColor up_colorFromHexString:@"#14F54949"] forState:0];
    unlockButton.layer.cornerRadius = 12.5;
    unlockButton.layer.masksToBounds = YES;
    unlockButton.titleLabel.font = [UIFont up_mediumfontOfSize:13];
    [view addSubview:unlockButton];
    [unlockButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(label);
        make.right.equalTo(more).offset(-15);
        make.width.equalTo(@80);
        make.height.equalTo(@25);
    }];
    
    if (self.totalDataArray.count == 4) {
        if (section == 0) {
            label.text = @"大咖直播";
        } else if (section == 1) {
            label.text = @"节奏大师";
        } else if (section == 2) {
            label.text = @"大师解盘";
        } else {
            label.text = @"大师点评";
        }
        if (![UPAppConfig isRving]) {
            
            if (section == 1) {
                if ([UPUserManager hasUserRight:kUPUserPrivilegeLTSXY]) {
                    unlockButton.hidden = YES;
                } else {
                    unlockButton.hidden = NO;

                }
            } else if (section > 1) {
                if ([UPUserManager hasUserRight:kUPUserPrivilegeDSTY]) {
                    unlockButton.hidden = YES;
                } else {
                    unlockButton.hidden = NO;

                }
            } else {
                unlockButton.hidden = YES;

            }

        } else {
            unlockButton.hidden = YES;
        }
        
    } else {
        if (section == 0) {
            label.text = @"节奏大师";
        } else if (section == 1) {
            label.text = @"大师解盘";
        } else {
            label.text = @"大师点评";
        }
        if (![UPAppConfig isRving]) {
            
            if (section == 0) {
                if ([UPUserManager hasUserRight:kUPUserPrivilegeLTSXY]) {
                    unlockButton.hidden = YES;
                } else {
                    unlockButton.hidden = NO;

                }
            } else if (section > 0) {
                if ([UPUserManager hasUserRight:kUPUserPrivilegeDSTY]) {
                    unlockButton.hidden = YES;
                } else {
                    unlockButton.hidden = NO;

                }
            } else {
                unlockButton.hidden = YES;

            }

        } else {
            unlockButton.hidden = YES;
        }
        
    }

    
  
    UITapGestureRecognizer * tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(gotoUnLock:)];
    [view addGestureRecognizer:tap];
    
    return view;
}

- (void)gotoMore:(UITapGestureRecognizer *)sender {
//    NSLog(@"点击的是第%ld节",sender.view.tag - 9999);
    [DNStatisticMananger clickWithId:DNSCENEID_SY_GDXXL];

    if (sender.view.tag >= 9999) {
        if ([UPUserManager uid].length > 0) {
            
            if (self.totalDataArray.count == 4) {
                if (sender.view.tag == 9999) {
                    [UPRouter navigate:UPURLHomeViewDKZB];
                } else if (sender.view.tag == 10000) {
                    if (![UPUserManager hasUserRight:kUPUserPrivilegeLTSXY] && ![UPAppConfig isRving]) {
                        [UPRouter navigate:[NSString stringWithFormat:@"%@%@",UPURLLTSXYAdvertising,[[UPOpenWXKFUtil sharedInstance] getCurrentChannel]]];
                    } else {
                        [UPRouter navigate:UPURLHomeViewJZDS];
                    }
                } else {
                    if ([UPUserManager hasUserRight:kUPUserPrivilegeDSTY] || [UPAppConfig isRving]) {
                        if (sender.view.tag == 10001) {
                            [UPRouter navigate:UPURLHomeViewDSJP];
                        } else {
                            [UPRouter navigate:UPURLHomeViewDSDP];
                        }
                    } else {
                        NSString *UrlString = [NSString stringWithFormat:@"%@%@",UPURLDSTYAdvertising,[[UPOpenWXKFUtil sharedInstance] getCurrentChannel]];

                        [UPRouter navigate:UrlString];
                    }
                }
                
            } else {
                 if (sender.view.tag == 9999) {
                    if (![UPUserManager hasUserRight:kUPUserPrivilegeLTSXY] && ![UPAppConfig isRving]) {
                        [UPRouter navigate:[NSString stringWithFormat:@"%@%@",UPURLLTSXYAdvertising,[[UPOpenWXKFUtil sharedInstance] getCurrentChannel]]];
                    } else {
                        [UPRouter navigate:UPURLHomeViewJZDS];
                    }
                } else {
                    if ([UPUserManager hasUserRight:kUPUserPrivilegeDSTY] || [UPAppConfig isRving]) {
                        if (sender.view.tag == 10000) {
                            [UPRouter navigate:UPURLHomeViewDSJP];
                        } else {
                            [UPRouter navigate:UPURLHomeViewDSDP];
                        }
                    } else {
                        NSString *UrlString = [NSString stringWithFormat:@"%@%@",UPURLDSTYAdvertising,[[UPOpenWXKFUtil sharedInstance] getCurrentChannel]];

                        [UPRouter navigate:UrlString];
                    }
                }
                
            }
            
            
        } else {
            [UPRouterUtil goUserLogin];
        }
    }
}



- (void)gotoUnLock:(UIGestureRecognizer *)sender {
    [DNStatisticMananger clickWithId:DNSCENEID_SY_GDXXL];

    if ([UPUserManager uid].length > 0) {
        if (self.totalDataArray.count == 4) {
            if (sender.view.tag == 8880) {
                [UPRouter navigate:UPURLHomeViewDKZB];
            } else if (sender.view.tag == 8881) {
                if (![UPUserManager hasUserRight:kUPUserPrivilegeLTSXY]  && ![UPAppConfig isRving]) {
                    [UPRouter navigate:[NSString stringWithFormat:@"%@%@",UPURLLTSXYAdvertising,[[UPOpenWXKFUtil sharedInstance] getCurrentChannel]]];
                } else {
                    [UPRouter navigate:UPURLHomeViewJZDS];
                }
            } else {
                if ([UPUserManager hasUserRight:kUPUserPrivilegeDSTY] || [UPAppConfig isRving]) {
                    if (sender.view.tag == 8882) {
                        [UPRouter navigate:UPURLHomeViewDSJP];
                    } else {
                        [UPRouter navigate:UPURLHomeViewDSDP];
                    }
                } else {
                    NSString *UrlString = [NSString stringWithFormat:@"%@%@",UPURLDSTYAdvertising,[[UPOpenWXKFUtil sharedInstance] getCurrentChannel]];

                    [UPRouter navigate:UrlString];
                }
            }
        } else {
             if (sender.view.tag == 8880) {
                if (![UPUserManager hasUserRight:kUPUserPrivilegeLTSXY]  && ![UPAppConfig isRving]) {
                    [UPRouter navigate:[NSString stringWithFormat:@"%@%@",UPURLLTSXYAdvertising,[[UPOpenWXKFUtil sharedInstance] getCurrentChannel]]];
                } else {
                    [UPRouter navigate:UPURLHomeViewJZDS];
                }
            } else {
                if ([UPUserManager hasUserRight:kUPUserPrivilegeDSTY] || [UPAppConfig isRving]) {
                    if (sender.view.tag == 8881) {
                        [UPRouter navigate:UPURLHomeViewDSJP];
                    } else {
                        [UPRouter navigate:UPURLHomeViewDSDP];
                    }
                } else {
                    NSString *UrlString = [NSString stringWithFormat:@"%@%@",UPURLDSTYAdvertising,[[UPOpenWXKFUtil sharedInstance] getCurrentChannel]];

                    [UPRouter navigate:UrlString];
                }
            }
        }
        
        
        
    } else {
        [UPRouterUtil goUserLogin];
    }
}

// MARK: - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    
    if (self.totalDataArray.count == 4) {
        if (section == 0) {
            return 1;
        } else {
            if ([(NSArray *)self.totalDataArray[section] count] >=3) {
                return 3;
            }
            return [(NSArray *)self.totalDataArray[section] count];
        }
    } else {
        if ([(NSArray *)self.totalDataArray[section] count] >=3) {
            return 3;
        }
        return [(NSArray *)self.totalDataArray[section] count];
    }
    

    
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.totalDataArray.count;

}


- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.totalDataArray.count == 3) {
        
            UPHomeMainViewJZCell *cell = [[UPHomeMainViewJZCell alloc] init];

            [cell configUIWithLiveModel:self.totalDataArray[indexPath.section][indexPath.row] withType:indexPath.section+1];
            WeakSelf(weakSelf);

            cell.risktipsBlock = ^(UPLiveJZModel * _Nonnull model) {
                
              
                UPPopupView *pop = [[UPPopupView alloc] init];

                CGSize size = [model.riskRemind boundingRectWithSize:CGSizeMake(weakSelf.up_width, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:15]} context:nil].size;
                pop.backgroundColor = UIColor.up_textTipsBgColor;
                pop.contentView = self.tipsView;
                self.tipstextView.text = [NSString stringWithFormat:@"%@",model.riskRemind];
                
                if (!IsValidateString(model.riskRemind)) {
                    self.tipstextView.text = @"以上所有服务由北京首证投资顾问有限公司提供。\n内容均摘自策牛股票相关策略、资讯等渠道，所有观点仅供参考，不构成操作建议。盘中如果操作，请关注相关战法等是否符合进场信号，并自主决策！市场有风险，投资需谨慎。";
                }
                self.tipstextLable.text = @"风险提示";
                
                self.pop = pop;
                [pop showInView:UPRouter.rootViewController.view constranitsHandler:^(UIView * _Nonnull popView) {
                    [weakSelf.tipsView mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.bottom.left.right.equalTo(pop);
                        if (size.height > 385) {
                            make.height.equalTo(@(UPHeight(485)));
                        } else if (size.height < 150) {
                            make.height.equalTo(@(UPHeight(250)));
                        } else {
                            make.height.equalTo(@(UPHeight(size.height + 100)));
                        }
                    }];
                }];
                
    //            [weakSelf.popView showInView:self atPosition:CGPointZero];
            };
            cell.detailBlock = ^(UPLiveJZModel * _Nonnull model) {
                
                if (model.type == 2 && model.contentType != 2) {
                    UPPopupView *pop = [[UPPopupView alloc] init];
                    self.pop = pop;
                    CGSize size = [model.realContent boundingRectWithSize:CGSizeMake(weakSelf.up_width, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:15]} context:nil].size;
                    pop.backgroundColor = UIColor.up_textTipsBgColor;
                    pop.contentView = self.tipsView;
                    self.tipstextView.text = model.realContent;
                    self.tipstextLable.text = @"文章详情";
                    [pop showInView:UPRouter.rootViewController.view constranitsHandler:^(UIView * _Nonnull popView) {
                        [weakSelf.tipsView mas_remakeConstraints:^(MASConstraintMaker *make) {
                            make.bottom.left.right.equalTo(pop);
                            make.height.equalTo(@(UPHeight(550)));
        //                    if (size.height > 385) {
        //                        make.height.equalTo(@(UPHeight(485)));
        //                    } else if (size.height < 150) {
        //                        make.height.equalTo(@(UPHeight(250)));
        //                    } else {
        //                        make.height.equalTo(@(UPHeight(size.height + 100)));
        //                    }
                        }];
                    }];
                } else {
                    [UPRouter navigate:model.detailLink];
                }
                
               
                
            };
            if (indexPath.row == [(NSArray *)self.totalDataArray[indexPath.section] count] -1) {
                cell.lineView.hidden = YES;
            }
            
            return cell;
        
    } else {
        if (indexPath.section == 0) {
            WeakSelf(weakSelf);

            UPHomeMainLiveViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPHomeMainLiveViewCell"];
            if (IsValidateArray((NSArray *)self.totalDataArray[indexPath.section])) {
                [cell configUIWithLiveArr:self.totalDataArray[indexPath.section]];
            }
//            else {
//                [cell configUINoData];
//            }
            cell.jumpUrlBlock = ^(UPLiveRoomModel * _Nonnull model) {
                if ([UPUserManager uid].length > 0) {
                    [UPRouter navigate:model.detailLink];
                } else {
                    [UPRouterUtil goUserLogin];
                }
            };
            cell.clickJoinButtonBlock = ^(UPLiveRoomModel * _Nonnull model) {
                if ([UPUserManager uid].length > 0) {
                    if (model.flag) {
                        if (model.liveState == 0  || model.liveState == 3) {
                            [weakSelf requestBookingStatusData:model];
                        } else if (model.liveState == 1) {
                            NSString *UrlString = [NSString stringWithFormat:@"%@%d",UPURLHomeViewLiveUrl,model.lecturerId];
                            [UPRouter navigate:UrlString];
                        } else {
                            NSString *UrlString = [NSString stringWithFormat:@"%@%d",UPURLHomeViewPlaybackUrl,model.lecturerId];
                            [UPRouter navigate:UrlString];
                        }
                    }
                    else {
                        [UPRouter navigate:model.detailLink];
                    }
                } else {
                    [UPRouterUtil goUserLogin];
                }
                
            };
            return cell;
        } else {
            UPHomeMainViewJZCell *cell = [[UPHomeMainViewJZCell alloc] init];

            [cell configUIWithLiveModel:self.totalDataArray[indexPath.section][indexPath.row] withType:indexPath.section];
            WeakSelf(weakSelf);

            cell.risktipsBlock = ^(UPLiveJZModel * _Nonnull model) {
                
              
                UPPopupView *pop = [[UPPopupView alloc] init];

                CGSize size = [model.riskRemind boundingRectWithSize:CGSizeMake(weakSelf.up_width, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:15]} context:nil].size;
                pop.backgroundColor = UIColor.up_textTipsBgColor;
                pop.contentView = self.tipsView;
                self.tipstextView.text = [NSString stringWithFormat:@"%@",model.riskRemind];
                
                if (!IsValidateString(model.riskRemind)) {
                    self.tipstextView.text = @"以上所有服务由北京首证投资顾问有限公司提供。\n内容均摘自策牛股票相关策略、资讯等渠道，所有观点仅供参考，不构成操作建议。盘中如果操作，请关注相关战法等是否符合进场信号，并自主决策！市场有风险，投资需谨慎。";
                }
                self.tipstextLable.text = @"风险提示";
                
                self.pop = pop;
                [pop showInView:UPRouter.rootViewController.view constranitsHandler:^(UIView * _Nonnull popView) {
                    [weakSelf.tipsView mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.bottom.left.right.equalTo(pop);
                        if (size.height > 385) {
                            make.height.equalTo(@(UPHeight(485)));
                        } else if (size.height < 150) {
                            make.height.equalTo(@(UPHeight(250)));
                        } else {
                            make.height.equalTo(@(UPHeight(size.height + 100)));
                        }
                    }];
                }];
                
    //            [weakSelf.popView showInView:self atPosition:CGPointZero];
            };
            cell.detailBlock = ^(UPLiveJZModel * _Nonnull model) {
                
                if (!(model.type == 2 && model.contentType == 1)) {
                    UPPopupView *pop = [[UPPopupView alloc] init];
                    self.pop = pop;
                    CGSize size = [model.realContent boundingRectWithSize:CGSizeMake(weakSelf.up_width, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:15]} context:nil].size;
                    pop.backgroundColor = UIColor.up_textTipsBgColor;
                    pop.contentView = self.tipsView;
                    if (model.type == 2 && model.contentType == 2) {
                        self.tipstextView.text = model.title;
                    } else {
                        self.tipstextView.text = model.realContent;
                    }
                    
                    self.tipstextLable.text = @"文章详情";
                    [pop showInView:UPRouter.rootViewController.view constranitsHandler:^(UIView * _Nonnull popView) {
                        [weakSelf.tipsView mas_remakeConstraints:^(MASConstraintMaker *make) {
                            make.bottom.left.right.equalTo(pop);
                            make.height.equalTo(@(UPHeight(550)));
        //                    if (size.height > 385) {
        //                        make.height.equalTo(@(UPHeight(485)));
        //                    } else if (size.height < 150) {
        //                        make.height.equalTo(@(UPHeight(250)));
        //                    } else {
        //                        make.height.equalTo(@(UPHeight(size.height + 100)));
        //                    }
                        }];
                    }];
                } else {
                    [UPRouter navigate:model.detailLink];
                }
                
               
                
            };
            if (indexPath.row == [(NSArray *)self.totalDataArray[indexPath.section] count] -1) {
                cell.lineView.hidden = YES;
            }
            
            return cell;
        }
    }
    
    
}

- (void)tipsOpen {
    [self.pop removeFromSuperview];
}

- (UIView *)tipsView {
    if (!_tipsView) {
        _tipsView = [[UIView alloc] init];
        _tipsView.backgroundColor = [UIColor whiteColor];
        _tipsView.layer.cornerRadius = 8;
        
        UILabel *labl = [[UILabel alloc] init];
        labl.text = @"风险提示";
        labl.textColor = UIColor.up_textPrimaryColor;
        labl.font = [UIFont up_boldFontOfSize:18];
        labl.textAlignment = NSTextAlignmentCenter;
        self.tipstextLable = labl;
        [_tipsView addSubview:labl];
        [labl mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.equalTo(_tipsView);
            make.top.equalTo(_tipsView.mas_top).offset(UPWidth(12));
            make.height.equalTo(@20);
        }];
        
        UIImageView *image = [[UIImageView alloc] initWithImage:UPTImg(@"Home/关闭提示")];
        image.userInteractionEnabled = YES;
        image.contentMode = UIViewContentModeCenter;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tipsOpen)];
        [image addGestureRecognizer:tap];
        [_tipsView addSubview:image];
        [image mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(labl);
            make.right.equalTo(_tipsView.mas_right).offset(UPWidth(-15));
            make.size.mas_equalTo(CGSizeMake(UPWidth(20), UPWidth(20)));
        }];
        
        UIView *lineView = [[UIView alloc] init];
        lineView.backgroundColor = UIColor.up_dividerColor;
        [_tipsView addSubview:lineView];
        [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_tipsView);
            make.top.equalTo(labl.mas_bottom).offset(UPWidth(12));
            make.leftMargin.rightMargin.equalTo(@(UPWidth(15)));
            make.height.equalTo(@(UPHeight(0.5)));
        }];
        [_tipsView addSubview:self.tipstextView];
        
        [self.tipstextView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.equalTo(lineView);
            make.top.equalTo(lineView.mas_bottom).offset(UPWidth(21));
            make.bottom.equalTo(_tipsView.mas_bottom).offset(UPHeight(-20));
        }];
//        [_tipsView addSubview:self.tipsLable];
//
//        [self.tipsLable mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.width.centerX.equalTo(lineView);
//            make.top.equalTo(lineView.mas_bottom).offset(UPWidth(21));
//            make.bottom.equalTo(_tipsView.mas_bottom).offset(UPHeight(-20));
//        }];
    
        
        
    }
    
    return _tipsView;
}

- (UITextView *)tipstextView {
    if (!_tipstextView) {
        _tipstextView = [[UITextView alloc] init];
        _tipstextView.scrollEnabled = YES;
        _tipstextView.editable = NO;
        _tipstextView.textColor = UIColor.up_textPrimaryColor;
        _tipstextView.font = [UIFont up_fontOfSize:15];
        _tipstextView.backgroundColor = UIColor.clearColor;
    }
    return _tipstextView;
}

- (UILabel *)tipsLable {
    if (!_tipsLable) {
        _tipsLable = [[UILabel alloc] init];
        _tipsLable.textColor = UIColor.up_textSecondary2Color;
        _tipsLable.font = [UIFont up_fontOfSize:15];
        _tipsLable.numberOfLines = 0;
        _tipsLable.text = @"";
        
    }
    return _tipsLable;
}

- (UPPopupView *)popView {
    if (!_popView) {
        _popView = [[UPPopupView alloc] init];
        UIView *content = [UIView new];
        content.backgroundColor = UIColor.redColor;
        content.layer.cornerRadius = 8;
        
        UILabel *labl = [[UILabel alloc] init];
        labl.text = @"风险提示";
        labl.textColor = UIColor.up_textPrimaryColor;
        labl.font = [UIFont up_boldFontOfSize:18];
        
        [_popView addSubview:content];
        
        [content addSubview:labl];
        
        [content mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.width.equalTo(_popView);
            make.height.equalTo(@(UPHeight(250)));
        }];
        
        [labl mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.top.centerX.equalTo(content);
        }];
        _popView.contentView = content;
        
    }
    
    return _popView;
}


- (UPErrorView *)emptyView {
    if (!_emptyView) {
        _emptyView = [UPErrorView new];
        _emptyView.hidden = YES;
    }
    return _emptyView;
}


// MARK: - Private
- (void)startNewsData:(void (^)(void))finished {
//    [self requestData];
//    // 请求banner
//    [self sendGetAD];
//    // 请求列表
//    [self reqNewsEvents:nil handle:finished];
}
- (void)onLoadMore {
//    UPNewsListInfo *model = self.totalDataArray.lastObject;
//    [self reqNewsEvents:model.baseInfo.newsID handle:nil];
    [self.tableView up_beginLoadMore];
}

// MARK: - UPPagerViewListViewDelegate
- (UIView *)listView {
    return self;
}

- (UIScrollView *)listScrollView {
    return self.tableView;
}

- (void)listViewDidScrollCallback:(void (^)(UIScrollView *scrollView))callback {
    self.scrollCallback = callback;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}

/**
 当下拉刷新时需要调用此方法刷新数据并回调以结束刷新
 */
- (void)refreshDataFinished:(void (^)(void))finished {
    WeakSelf(weakSelf);
    [[UPDFHttpQuestManager sharedInstance] getDFHomeViewData:^(NSArray * _Nonnull dataSourseArr) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.totalDataArray = dataSourseArr;
            [weakSelf.tableView reloadData];
            if (finished) {
                finished();
            }
        });
    }];
//    [self requestData];
   
}

- (void)requestBookingStatusData: (UPLiveRoomModel *)model {
    WeakSelf(weakSelf);
    [[UPDFHttpQuestManager sharedInstance] getDFrequestBookingStatusData:model withCallback:^(NSDictionary * _Nonnull dic) {
        int errcode = [dic[@"code"] intValue];
        if (errcode == 200) {
            if (model.subscribeState) {
                [UPToastView show:@"取消预约成功"];
            } else {
                [UPToastView show:@"预约成功"];

            }
            model.subscribeState = !model.subscribeState;
        } else {
            [UPToastView show:dic[@"msg"]];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.tableView reloadData];
        });
    }];

}


- (void)requestData {
    WeakSelf(weakSelf);
    [[UPDFHttpQuestManager sharedInstance] getDFHomeViewData:^(NSArray * _Nonnull dataSourseArr) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.totalDataArray = dataSourseArr;
            [weakSelf.tableView reloadData];
        });
    }];
}

@end
