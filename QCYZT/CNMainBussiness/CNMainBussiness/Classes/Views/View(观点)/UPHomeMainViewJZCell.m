//
//  UPHomeMainViewJZCell.m
//  UPStockMain
//
//  Created by lizhixiang on 2022/9/20.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import "UPHomeMainViewJZCell.h"
#import <CoreText/CoreText.h>

@interface UPHomeMainViewJZCell ()
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *nameLable;
@property (nonatomic, strong) UILabel *timeLable;
@property (nonatomic, strong) UILabel *detailLable;
@property (nonatomic, strong) UIImageView *urlPdfImageView;
@property (nonatomic, strong) UIImageView *urlImageView;
@property (nonatomic, strong) UIImageView *detailTextImageView;

@property (nonatomic, strong) UIView *tipsView;
@property (nonatomic, strong) UILabel *tipsLable;
@property (nonatomic, strong) UIImageView *tipsImageView;
@property (nonatomic, strong) UPLiveJZModel *model;
@property (nonatomic, strong) NSMutableArray *labelArr;

@property (nonatomic, copy) NSString *showDetailStr;
@end

@implementation UPHomeMainViewJZCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        self.selectionStyle = UITableViewCellSelectionStyleNone;

    }
    return self;
}

- (void)setupUI {
    [self.contentView addSubview:self.iconImageView];
    [self.contentView addSubview:self.nameLable];
    [self.contentView addSubview:self.timeLable];
    [self.contentView addSubview:self.detailLable];
    [self.contentView addSubview:self.detailTextImageView];
    [self.contentView addSubview:self.urlPdfImageView];

    [self.contentView addSubview:self.urlImageView];
    [self.contentView addSubview:self.tipsView];
    [self.tipsView addSubview:self.tipsLable];
    [self.tipsView addSubview:self.tipsImageView];
    
    [self.contentView addSubview:self.lineView];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(UPWidth(17));
        make.top.equalTo(self.contentView.mas_top).offset(UPHeight(25));
        make.width.equalTo(@35);
    }];
    
    [self.nameLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconImageView);
        make.left.equalTo(self.iconImageView.mas_right).offset(UPWidth(10));
    }];
    
    [self.timeLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.iconImageView);
        make.left.equalTo(self.iconImageView.mas_right).offset(UPWidth(10));
    }];

    [self.detailLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconImageView);
        make.top.equalTo(self.iconImageView.mas_bottom).offset(UPHeight(15));
        make.right.equalTo(self.contentView.mas_right).offset(UPWidth(-15));
//        make.height.equalTo(@40);
    }];
    [self.detailTextImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.detailLable);
        make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(12));
//        make.size.mas_equalTo(CGSizeMake(75, 19));
    }];
    
    [self.urlPdfImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.detailLable);
        make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(12));
        make.size.mas_equalTo(CGSizeMake(88, 21));
    }];
    
    [self.urlImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.detailLable);
        make.size.mas_equalTo(CGSizeMake(112, 112));
        make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(12));

    }];
    
    [self.tipsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.detailLable);
        make.top.equalTo(self.urlImageView.mas_bottom).offset(UPHeight(15));
        make.size.mas_equalTo(CGSizeMake(75, 19));
    }];

    [self.tipsLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.tipsView.mas_left).offset(UPWidth(5));
        make.centerY.equalTo(self.tipsView);
    }];
    [self.tipsImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.tipsView.mas_right).offset(UPWidth(-5));
        make.centerY.equalTo(self.tipsView);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.top.equalTo(self.tipsView.mas_bottom).offset(UPHeight(20));
        make.left.equalTo(self.iconImageView);
        make.right.equalTo(self.detailLable.mas_right);
        make.height.equalTo(@0.5);
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

- (void)configUIWithLiveModel:(UPLiveJZModel *)model withType:(int)type{
    self.model = model;
//    self.nameLable.text = model.lecturerName;
    if (type > 1) {
        self.nameLable.text = @"投研团";

        self.iconImageView.image = UPTImg(@"Home/观点-大师");
    } else {
        self.nameLable.text = @"擒龙团";

        self.iconImageView.image = UPTImg(@"Home/观点-节奏");
    }
//    NSDate *targetDate = [NSDate up_dateFromMillisecond:model.publishedAt];
    self.timeLable.text = model.publishTime;//[NSString stringWithFormat:@"%@",[targetDate up_formatDate:kUPDateFormat_yyyyMMddHHmmss]];
    if (!model.flag && ![UPAppConfig isRving]) {
        self.tipsView.hidden = YES;
        self.urlImageView.hidden = YES;
        self.urlPdfImageView.hidden = YES;
        self.detailTextImageView.hidden = YES;
        if (type == 1) {
            self.detailLable.text = @"这属于【实训营】资讯，解锁立享擒龙团特供服务！";
            
        } else {
            self.detailLable.text = @"这属于【大师投研】VIP资讯，解锁立享大师专属服务！";
        }
        [self.lineView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.contentView);
            make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(25));
            make.left.equalTo(self.iconImageView);
            make.right.equalTo(self.detailLable.mas_right);
            make.height.equalTo(@0.5);
        }];
    } else {

        if (model.type == 2 && model.contentType == 2) {
            self.detailLable.text = model.title;
            self.showDetailStr = model.title;
        } else {
            self.detailLable.text = model.realContent;
            self.showDetailStr = model.realContent;
        }
        [self.detailLable mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.iconImageView);
            make.top.equalTo(self.iconImageView.mas_bottom).offset(UPHeight(15));
            make.right.equalTo(self.contentView.mas_right).offset(UPWidth(-15));
//            make.height.equalTo(@40);
        }];
        
        
        NSArray *array = [self getSeparatedLinesFromLabel:self.detailLable];
        if (array.count >= 4 || (model.type ==2 && model.contentType != 2)) {
            
            self.detailTextImageView.hidden = NO;
            if (model.type == 2 && model.contentType == 1) {
                self.detailTextImageView.image = UPTImg(@"Home/查看更多");
            } else {
                self.detailTextImageView.image = UPTImg(@"Home/展开");
            }
            [self.detailTextImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.detailLable);
                make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(10));
//                make.size.mas_equalTo(CGSizeMake(75, 19));
            }];

        } else {
            self.detailTextImageView.hidden = YES;
            self.detailTextImageView.image = UPTImg(@"Home/查看更多");
            [self.detailTextImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.detailLable);
                make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(0));
                make.size.mas_equalTo(CGSizeMake(75, 0));
            }];
        }
        self.tipsView.hidden = NO;
        NSArray *imgList;
        if (IsValidateString(model.pictureList)) {
            imgList= [model.pictureList componentsSeparatedByString:@","];
        }
        
        if (model.type == 2 && model.contentType == 2 && IsValidateString(model.fileUrl)) {
            self.urlPdfImageView.hidden = NO;
//            if (IsValidateString(model.fileUrl)) {
                [self.urlPdfImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(self.detailLable);
                    if (self.detailTextImageView.hidden) {
                        make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(12));
                    } else {
                        make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(12));
                    }
                    make.size.mas_equalTo(CGSizeMake(88, 21));
                }];
//            }
            
        } else {
            self.urlPdfImageView.hidden = YES;
            [self.urlPdfImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.detailLable);
                make.top.equalTo(self.detailTextImageView.mas_bottom);
                make.size.mas_equalTo(CGSizeMake(88, 0));
            }];
        }
        
        if (model.type == 2 && model.contentType == 2) {
            if (IsValidateString(model.coverUrl)) {
                self.urlImageView.hidden = NO;
                [self.urlImageView up_setImageWithURLString:model.coverUrl];
                [self.urlImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    WeakSelf(weakSelf);
                    make.top.equalTo(weakSelf.urlPdfImageView.mas_bottom).offset(UPHeight(10));
                }];
            } else {
                self.urlImageView.hidden = YES;
                [self.urlImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    WeakSelf(weakSelf);
                    make.top.equalTo(weakSelf.urlPdfImageView.mas_bottom);
                }];
            }
            
        } else {
            if (IsValidateArray(imgList)) {
                [self.urlImageView up_setImageWithURLString:imgList[0]];
                [self.urlImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    WeakSelf(weakSelf);
                    make.top.equalTo(weakSelf.urlPdfImageView.mas_bottom).offset(UPHeight(10));
                }];
            } else {
                self.urlImageView.hidden = YES;
                [self.urlImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    WeakSelf(weakSelf);
                    make.top.equalTo(weakSelf.urlPdfImageView.mas_bottom);
                    make.size.mas_equalTo(CGSizeMake(112, 0));

                }];
            }
        }
        
        [self.tipsView mas_updateConstraints:^(MASConstraintMaker *make) {
//            make.left.equalTo(self.detailLable);
            make.top.equalTo(self.urlImageView.mas_bottom).offset(UPHeight(15));
//            if (IsValidateArray(imgList)) {
//                make.top.equalTo(self.urlImageView.mas_bottom).offset(UPHeight(15));
//            } else {
//                if (IsValidateString(model.fileUrl)) {
//                    make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(15));
//                } else {
//                    if (array.count >= 4) {
//                        make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(45));
//                    } else {
//                        make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(15));
//                    }
//                }
//            }
//            make.size.mas_equalTo(CGSizeMake(75, 19));
        }];
        
        [self.lineView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.contentView);
            make.top.equalTo(self.tipsView.mas_bottom).offset(UPHeight(25));
            make.left.equalTo(self.iconImageView);
            make.right.equalTo(self.detailLable.mas_right);
            make.height.equalTo(@0.5);
        }];
                
        
        
        
        
//
//        if (type == 1) {
//            self.urlImageView.hidden = YES;
//            self.urlPdfImageView.hidden = YES;
//            if (IsValidateString(model.fileUrl) || IsValidateArray(imgList)) {
//                self.urlImageView.hidden = NO;
//                self.urlPdfImageView.hidden = NO;
//
//                [self.urlPdfImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                    make.left.equalTo(self.detailLable);
//                    if (self.detailTextImageView.hidden) {
//                        make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(12));
//                    } else {
//                        make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(5));
//                    }
//                    make.size.mas_equalTo(CGSizeMake(88, 21));
//                }];
//
//
//                if (model.type == 2 && model.contentType == 2) {
//                    [self.urlImageView up_setImageWithURLString:model.coverUrl];
//                } else {
//                    if (IsValidateArray(imgList)) {
//                        [self.urlImageView up_setImageWithURLString:imgList[0]];
//                    }
//                }
//
////                if (IsValidateArray(imgList)) {
////                    [self.urlImageView up_setImageWithURLString:imgList[0]];
////                } else {
////                    [self.urlImageView up_setImageWithURLString:model.fileUrl];
////                }
//                [self.urlImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                    make.left.equalTo(self.detailLable);
//                    make.size.mas_equalTo(CGSizeMake(112, 112));
//                    if (array.count >= 4) {
//                        if (self.urlPdfImageView.hidden) {
//                            make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(10));
//                        } else {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(10));
//                        }
//                    } else {
//                        if (self.urlPdfImageView.hidden) {
//                            make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(10));
//                        } else {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(10));
//                        }
////                        make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(10));
//                    }
//                }];
//
//
//
//
//            } else {
//                self.urlPdfImageView.hidden = YES;
//                [self.urlPdfImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                    make.left.equalTo(self.detailLable);
//                    make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(0));
//                    make.size.mas_equalTo(CGSizeMake(88, 0));
//                }];
//            }
//
//
//
//            if (IsValidateArray(imgList)) {
//                self.urlImageView.hidden = NO;
//                [self.urlImageView up_setImageWithURLString:imgList[0]];
//                [self.urlImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                    make.left.equalTo(self.detailLable);
//                    make.size.mas_equalTo(CGSizeMake(112, 112));
//                    if (array.count >= 4) {
//                        if (self.urlPdfImageView.hidden) {
//                            make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(10));
//                        } else {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(10));
//                        }
//                    } else {
//                        if (self.urlPdfImageView.hidden) {
//                            make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(10));
//                        } else {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(10));
//                        }
////                        make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(10));
//                    }
//                }];
//
//            } else {
//                self.urlImageView.hidden = YES;
//                [self.urlImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                    make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(16));
//                    make.left.equalTo(self.detailLable);
//                    make.size.mas_equalTo(CGSizeMake(112, 0));
//
//                }];
//            }
//
//            [self.tipsView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                make.left.equalTo(self.detailLable);
//                if (IsValidateArray(imgList)) {
//                    make.top.equalTo(self.urlImageView.mas_bottom).offset(UPHeight(15));
//                } else {
//                    if (IsValidateString(model.fileUrl)) {
//                        make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(15));
//                    } else {
//                        if (array.count >= 4) {
//                            make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(45));
//                        } else {
//                            make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(15));
//                        }
//                    }
//                }
//                make.size.mas_equalTo(CGSizeMake(75, 19));
//            }];
//
//            [self.lineView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                make.bottom.equalTo(self.contentView);
//                make.top.equalTo(self.tipsView.mas_bottom).offset(UPHeight(25));
//                make.left.equalTo(self.iconImageView);
//                make.right.equalTo(self.detailLable.mas_right);
//                make.height.equalTo(@0.5);
//            }];
//        }
//        else {
//            if (type == 2) {
//                self.urlPdfImageView.hidden = YES;
//                [self.urlPdfImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                    make.left.equalTo(self.detailLable);
//                    make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(0));
//                    make.size.mas_equalTo(CGSizeMake(88, 0));
//                }];
//
//                if (IsValidateArray(imgList)) {
//                    self.urlImageView.hidden = NO;
//                    [self.urlImageView up_setImageWithURLString:imgList[0]];
//                    [self.urlImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                        make.left.equalTo(self.detailLable);
//                        if (array.count >= 4) {
//                            make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(45));
//                        } else {
//                            make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(15));
//                        }
//                        make.size.mas_equalTo(CGSizeMake(112, 112));
//                    }];
//
//                } else {
//                    self.urlImageView.hidden = YES;
//                    [self.urlImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                        make.left.equalTo(self.detailLable);
////                        if (array.count >= 4) {
////                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(45));
////                        } else {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(15));
////                        }
//                        make.size.mas_equalTo(CGSizeMake(112, 0));
//                    }];
//                }
//
//            } else {
//                if (IsValidateString(model.fileUrl)  || IsValidateArray(imgList)) {
//                    self.urlPdfImageView.hidden = NO;
//                    [self.urlPdfImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                        make.left.equalTo(self.detailLable);
//                        if (array.count >= 4) {
//                            make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(10));
//                        } else {
//                            make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(15));
//                        }
////                        make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(12));
//                        make.size.mas_equalTo(CGSizeMake(88, 21));
//                    }];
//                } else {
//                    self.urlPdfImageView.hidden = YES;
//                    [self.urlPdfImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                        make.left.equalTo(self.detailLable);
//                        make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(0));
//                        make.size.mas_equalTo(CGSizeMake(88, 0));
//                    }];
//                }
//
//                if (IsValidateArray(imgList)) {
//                    self.urlImageView.hidden = NO;
//
//                    [self.urlImageView up_setImageWithURLString:imgList[0]];
//                    [self.urlImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                        make.left.equalTo(self.detailLable);
//                        make.size.mas_equalTo(CGSizeMake(112, 112));
////                        if (self.urlPdfImageView.hidden) {
////                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(45));
////                        } else {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(15));
////                        }
//                    }];
//
//                } else {
//                    self.urlImageView.hidden = YES;
//                    [self.urlImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                        if (array.count >= 4) {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(45));
//                        } else {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(15));
//                        }
//                        make.left.equalTo(self.detailLable);
//                        make.size.mas_equalTo(CGSizeMake(112, 0));
//
//                    }];
//                }
//            }
//            CGFloat bntX = 0.0;
//
//            if (array.count >= 4) {
//                if (IsValidateString(model.fileUrl) && IsValidateArray(imgList)) {
//                    bntX = 330;
//                } else if (!IsValidateString(model.fileUrl) && !IsValidateArray(imgList)) {
//                    bntX = 158;
//                } else if (IsValidateString(model.fileUrl) && !IsValidateArray(imgList)) {
//                    bntX = 205;
//                } else {
//                    bntX = 287;
//                }
//            } else {
//                if (IsValidateString(model.fileUrl) && IsValidateArray(imgList)) {
//                    bntX = 295;
//                } else if (!IsValidateString(model.fileUrl) && !IsValidateArray(imgList)) {
//                    bntX = 122;
//                } else if (IsValidateString(model.fileUrl) && !IsValidateArray(imgList)) {
//                    bntX = 170;
//                } else {
//                    bntX = 252;
//                }
//            }
//
////            NSMutableArray *stockArr = [[NSMutableArray alloc] initWithArray: model.mentionStockList];
////            [stockArr addObjectsFromArray:model.mentionVipStockList];
////            if (stockArr.count > 0) {
////                // 按钮高度
////                CGFloat btnH = 18;
////                // 距离左边距
////                CGFloat leftX = 19;
////                // 距离上边距
////                CGFloat topY = 6;
////                // 按钮左右间隙
////                CGFloat marginX = 5;
////                // 按钮上下间隙
////                CGFloat marginY = 5;
////                // 文字左右间隙
////                CGFloat fontMargin = 6;
////                for (int i = 0 ;i < stockArr.count; i++) {
////                    UPLiveStockModel *LVmodel = stockArr[i];
////                    UILabel *lab = [[UILabel alloc] init];
////                    lab.font = [UIFont up_fontOfSize:12];
////                    lab.textColor = UIColor.up_brandColor;
////                    lab.textAlignment = NSTextAlignmentCenter;
////                    lab.layer.borderColor = UIColor.up_brandColor.CGColor;
////                    lab.layer.borderWidth = 0.5;
////                    lab.layer.cornerRadius = 2.f;
////                    lab.text = LVmodel.name;
////                    CGSize size = [LVmodel.name boundingRectWithSize:CGSizeMake(self.detailLable.up_width, 18) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:12] ,NSParagraphStyleAttributeName:[[NSMutableParagraphStyle alloc] init]} context:nil].size;
//////                    CGFloat eqLeft = fmodf(width, self.up_width);
////                    lab.frame = CGRectMake(marginX + leftX, bntX + topY, size.width +12, btnH);
////    //                CGFloat height += ceilf(width/self.up_width) * 24 ;
////                    // 处理换行
////
////                     if (lab.frame.origin.x + lab.frame.size.width + marginX > [UIScreen mainScreen].bounds.size.width - 30) {
////                      // 换行
////                      topY += btnH + marginY;
////
////                      // 重置
////                      leftX = 19;
////                      lab.frame = CGRectMake(marginX + leftX,bntX + topY, size.width +12, btnH);
////                         [lab sizeToFit];
////                         // 重新计算按钮文字左右间隙
////                         CGRect frame = lab.frame;
////                         frame.size.width += fontMargin*2;
////                         lab.frame = frame;
////                     }
////
////                     // 重置高度
////                     CGRect frame = lab.frame;
////                     frame.size.height = btnH;
////                     lab.frame = frame;
////                    [self.labelArr addObject:lab];
////                    [self.contentView addSubview:lab];
////                    leftX += lab.frame.size.width + marginX;
////                    if (i == stockArr.count - 1) {
////                        [self.tipsView mas_remakeConstraints:^(MASConstraintMaker *make) {
////                            make.left.equalTo(self.detailLable);
////                            make.top.equalTo(lab.mas_bottom).offset(UPHeight(15));
////                            make.size.mas_equalTo(CGSizeMake(75, 19));
////                        }];
////                        [self.lineView mas_remakeConstraints:^(MASConstraintMaker *make) {
////                            make.bottom.equalTo(self.contentView);
////                            make.top.equalTo(self.tipsView.mas_bottom).offset(UPHeight(25));
////                            make.left.equalTo(self.iconImageView);
////                            make.right.equalTo(self.detailLable.mas_right);
////                            make.height.equalTo(@0.5);
////                        }];
////                    }
////                }
////            }
////            else {
//                [self.tipsView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                    make.left.equalTo(self.detailLable);
//                    make.size.mas_equalTo(CGSizeMake(75, 19));
//                    if (IsValidateArray(imgList)) {
//                        make.top.equalTo(self.urlImageView.mas_bottom).offset(UPHeight(15));
//                    } else {
//                        if (IsValidateString(model.fileUrl)) {
//                            make.top.equalTo(self.urlPdfImageView.mas_bottom).offset(UPHeight(15));
//                        } else {
//                            if (self.detailTextImageView.hidden) {
//                                make.top.equalTo(self.detailLable.mas_bottom).offset(UPHeight(10));
//                            } else {
//                                make.top.equalTo(self.detailTextImageView.mas_bottom).offset(UPHeight(10));
//                            }
//                        }
//                    }
//                }];
//
//                [self.lineView mas_remakeConstraints:^(MASConstraintMaker *make) {
//                    make.bottom.equalTo(self.contentView);
//                    make.top.equalTo(self.tipsView.mas_bottom).offset(UPHeight(25));
//                    make.left.equalTo(self.iconImageView);
//                    make.right.equalTo(self.detailLable.mas_right);
//                    make.height.equalTo(@0.5);
//                }];
//
//
////            }
//
//            [self setNeedsLayout];
//        }
       
    }
    
}

- (void)showTips {
    self.risktipsBlock(self.model);

//    [UPToastView show:@"aaa"];
}

- (void)showDetail {
    self.detailBlock(self.model);
}

- (void)showPdfs {
    NSArray *imgList;
    if (IsValidateString(self.model.pictureList)) {
        imgList= [self.model.pictureList componentsSeparatedByString:@","];
    }
    
    if (IsValidateString(self.model.fileUrl)) {
        [UPRouter navigate:self.model.fileUrl];

    } else {
        [UPRouter navigate:imgList[0]];

    }
}

- (void)showUrlImage:(UITapGestureRecognizer *)sender {
    if (![(UIImageView *)sender.view image]) {
           return;
       }
    UIView *bgView = [[UIView alloc] init];
    bgView.frame = [UIScreen mainScreen].bounds;
    bgView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.8];
    [[[UIApplication sharedApplication] keyWindow] addSubview:bgView];
    UITapGestureRecognizer *tapBgView = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBgView:)];
    [bgView addGestureRecognizer:tapBgView];
    //必不可少的一步，如果直接把点击获取的imageView拿来玩的话，返回的时候，原图片就完蛋了
    UIImageView *tempImageView = (UIImageView*)sender.view;
    UIImageView *imageView = [[UIImageView alloc] initWithFrame:tempImageView.frame];
    imageView.image = tempImageView.image;
    [bgView addSubview:imageView];
    [UIView animateWithDuration:0.0 animations:^{

        CGRect frame = imageView.frame;

        frame.size.width = bgView.frame.size.width;

        frame.size.height = frame.size.width * (imageView.image.size.height / imageView.image.size.width);

        frame.origin.x = 0.5;

        frame.origin.y = (bgView.frame.size.height - frame.size.height) * 0.5;

        imageView.frame = frame;

    }];

}

-(void)tapBgView:(UITapGestureRecognizer *)tapBgRecognizer {
    [tapBgRecognizer.view removeFromSuperview];
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/观点-节奏")];
    }
    
    return _iconImageView;
}

- (UIImageView *)tipsImageView {
    if (!_tipsImageView) {
        _tipsImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/观点-风险")];
    }
    
    return _tipsImageView;
}


- (UIImageView *)detailTextImageView {
    if (!_detailTextImageView) {
        _detailTextImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/查看更多")];
        _detailTextImageView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(showDetail)];
        [_detailTextImageView addGestureRecognizer:tap];
    }
    
    return _detailTextImageView;
}

- (UIImageView *)urlPdfImageView {
    if (!_urlPdfImageView) {
        _urlPdfImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/观点-查看pdf")];
        _urlPdfImageView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(showPdfs)];
        [_urlPdfImageView addGestureRecognizer:tap];

    }
    return _urlPdfImageView;
}


- (UIImageView *)urlImageView {
    if (!_urlImageView) {
        _urlImageView = [[UIImageView alloc] init];
        _urlImageView.contentMode = UIViewContentModeScaleToFill;
        _urlImageView.layer.cornerRadius = 5.f;
        _urlImageView.layer.masksToBounds = YES;
        _urlImageView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(showUrlImage:)];
        [_urlImageView addGestureRecognizer:tap];
    }
    return _urlImageView;
}

- (UILabel *)nameLable {
    if (!_nameLable) {
        _nameLable = [[UILabel alloc] init];
        _nameLable.textColor = UIColor.up_textPrimaryColor;
        _nameLable.font = [UIFont up_boldFontOfSize:15];
        _nameLable.textAlignment = NSTextAlignmentLeft;
        _nameLable.text = @"擒龙团";
    }
    return _nameLable;
}

- (UILabel *)timeLable {
    if (!_timeLable) {
        _timeLable = [[UILabel alloc] init];
        _timeLable.textColor = UIColor.up_textSecondary2Color;
        
        _timeLable.font = [UIFont up_fontOfSize:12];
        _timeLable.textAlignment = NSTextAlignmentLeft;
        _timeLable.text = @"2022.06.23 12:34:56";
    }
    return _timeLable;
}

- (UILabel *)detailLable {
    if (!_detailLable) {
        _detailLable = [[UILabel alloc] init];
        _detailLable.textColor = UIColor.up_textSecondary1Color;
        _detailLable.font = [UIFont up_fontOfSize:14];
        _detailLable.numberOfLines = 3;
        _detailLable.textAlignment = NSTextAlignmentLeft;
        _detailLable.text = @"";
       
    }
    return _detailLable;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor = [UIColor up_colorFromHexString:@"#E7E9FF"];
    }
    return _lineView;
}


- (UIView *)tipsView {
    if (!_tipsView) {
        _tipsView = [[UIView alloc] init];
        _tipsView.backgroundColor = [UIColor up_colorFromHexString:@"#F4F6F8"];
        _tipsView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(showTips)];
        [_tipsView addGestureRecognizer:tap];
    }
    return _tipsView;
}

- (UILabel *)tipsLable {
    if (!_tipsLable) {
        _tipsLable = [[UILabel alloc] init];
        _tipsLable.textColor = UIColor.up_textSecondary2Color;
        _tipsLable.textAlignment = NSTextAlignmentCenter;
        _tipsLable.font = [UIFont up_fontOfSize:12];
        _tipsLable.text = @"风险提示";
        
    }
    return _tipsLable;
}



- (NSArray *)getSeparatedLinesFromLabel:(UILabel *)label {
    NSString *text = [label text];
    UIFont *font = [label font];
    CGRect rect = [label frame];
    CTFontRef myfont = CTFontCreateWithName((__bridge CFStringRef)([font fontName]), [font pointSize], NULL);
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc] initWithString:text];
    [attStr addAttribute:(NSString *)kCTFontAttributeName value:(__bridge id)myfont range:NSMakeRange(0, attStr.length)];
    CTFramesetterRef frameSetter = CTFramesetterCreateWithAttributedString((__bridge CFAttributedStringRef)attStr);
    CGMutablePathRef path = CGPathCreateMutable();
    NSLog(@"%f",[UIScreen mainScreen].bounds.size.width - 32);
    CGPathAddRect(path, NULL, CGRectMake(0,0,[UIScreen mainScreen].bounds.size.width - 40,100000));
    CTFrameRef frame = CTFramesetterCreateFrame(frameSetter, CFRangeMake(0, 0), path, NULL);
    NSArray *lines = (__bridge NSArray *)CTFrameGetLines(frame);
    NSMutableArray *linesArray = [[NSMutableArray alloc]init];
    for (id line in lines) {
        CTLineRef lineRef = (__bridge CTLineRef )line;
        CFRange lineRange = CTLineGetStringRange(lineRef);
        NSRange range = NSMakeRange(lineRange.location, lineRange.length);
        NSString *lineString = [text substringWithRange:range];
        [linesArray addObject:lineString];
    }
    return linesArray;
    
}

@end
