//
//  UPStockOptionalSubTabView.m
//  UPStockMain
//
//  Created by kds on 2022/9/9.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import "UPStockOptionalSubNoticeView.h"
#import <UPCommon/UPPageView.h>
#import <UPNewsSDK/UPNewsModel.h>
#import "UPStockOptionalSubListView.h"

@interface UPStockOptionalSubNoticeView()<UPTabViewDelegate,UPPageViewDataSource,UPPageViewDelegate,UPErrorViewDelegate>

@property (nonatomic, strong) UPTabView * tabView;

@property (nonatomic, strong) UPPageView * pageView;

@property (nonatomic, strong) UPErrorView * errorView;

@property (nonatomic, strong) UPLoadingView * loadingView;

@property (nonatomic, strong) NSArray * typeArray;

@end

@implementation UPStockOptionalSubNoticeView

#pragma mark 生命周期
- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    self.backgroundColor = UIColor.up_contentBgColor;
    [self setupViews];
    [self setConstraints];
    [self requestTabInfo];
    return self;
}

- (void)setupViews{
    [self addSubview:self.tabView];
    [self addSubview:self.pageView];
    [self addSubview:self.errorView];
}

- (void)setConstraints{
    
    
    
    [self.tabView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self).offset(UPWidth(15));
        make.height.equalTo(@(UPWidth(34)));
    }];
    [self.pageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self);
        make.top.equalTo(self.tabView.mas_bottom);
    }];
    
    [self.errorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self);
    }];
}
#pragma mark 点击事件

#pragma mark setter&getter
- (UPTabView *)tabView{
    if (!_tabView) {
        _tabView = [UPTabView new];
        _tabView.delegate = self;
        _tabView.minItemWidth = UPWidth(116);
        _tabView.itemFontSelected = [UIFont up_fontOfSize:UPWidth(14) weight:UIFontWeightSemibold];
        _tabView.itemColorSelected = UIColor.whiteColor;
        _tabView.itemBackgroundColorSelected = UIColor.up_brandColor;
        _tabView.itemFont = [UIFont up_fontOfSize:UPWidth(14)];
        _tabView.itemColor = [UIColor up_colorFromString:@"#657180"];
        _tabView.itemBackgroundColor = [UIColor up_colorFromString:@"#EFF1F4"];
        _tabView.isFollowLeftContent = YES;
        _tabView.followStyleItemMargin = UPWidth(15);
        _tabView.followStyleLeftMargin = UPWidth(15);
        _tabView.shouldFitTextWidth = YES;
        _tabView.showIndicator = NO;
    }
    return _tabView;
}

- (UPPageView *)pageView{
    if (!_pageView) {
        _pageView = [UPPageView new];
        _pageView.delegate = self;
        _pageView.datasource = self;
    }
    return _pageView;
}

- (UPErrorView *)errorView{
    if (!_errorView) {
        _errorView = [UPErrorView defaultErrorView];
        _errorView.hidden = YES;
        _errorView.delegate = self;
    }
    return _errorView;
}

- (UPLoadingView *)loadingView{
    if (!_loadingView) {
        _loadingView = [UPLoadingView new];
        _loadingView.isFlowerIndicator = YES;
    }
    return _loadingView;
}

- (NSArray*)getTabItemData{
    NSMutableArray * dataArray = [NSMutableArray new];
    NSInteger index = 0;
    for (UPNewsColumnTagInfo * tagInfo in self.typeArray) {
        UPTabViewItem * item = [UPTabViewItem itemWithTitle:tagInfo.title tag:index];
        [dataArray addObject:item];
        index++;
    }
    return dataArray;
}

#pragma mark 相关方法
- (void)createTabData{
    [self.pageView reloadWithSelectIndex:0];
    self.tabView.tabItems = [self getTabItemData];
    self.tabView.selectedIndex = 0;
}

#pragma mark 网络请求
- (void)requestTabInfo {
    [self.loadingView show:@""];
    UPNewsColumnTagReq *tagReq = [[UPNewsColumnTagReq alloc] init];
    tagReq.type = 2;
    WeakSelf(weakSelf);
    [UPNewsManager requestColumnTag:tagReq comlpetionHandler:^(UPNewsColumnTagRsp *rsp, NSError *error) {
        [weakSelf.loadingView hide];
        if (error) {
            [weakSelf.errorView showIsError:YES];
            return;
        }
        weakSelf.typeArray = rsp.tagArray;
        [weakSelf createTabData];
    }];
}

#pragma mark UPTabViewDelegate
-(void)tabView:(UPTabView *)tabView didSelectItem:(UPTabViewItem *)item{
    [self.pageView scrollToIndex:item.tag];
}

-(CGSize)tabView:(UPTabView *)tabView createItemSize:(UPTabViewItem *)item{
    UIFont * font = tabView.itemFont;
    if (tabView.selectedIndex == item.tag) {
        font = tabView.itemFontSelected;
    }
    
    CGSize textSize = [item.title sizeWithAttributes:@{
        NSFontAttributeName: font
    }];
    CGFloat width = textSize.width + 30;
    if (width < 58) {
        width = 58;
    }
    
    CGSize size = CGSizeMake(width, tabView.up_height);
    
    return size;
}

#pragma mark UPPageViewDataSource,UPPageViewDelegate
- (NSInteger)pageViewNumberOfChild:(UPPageView*)pageView{
    return self.typeArray.count;
}

- (UIView *)pageView:(UPPageView *)pageView childForIndex:(NSInteger)index{
    UPStockOptionalSubListView * listView = [UPStockOptionalSubListView new];
    listView.enableRefresh = YES;
    listView.isSelfChooseStockNews = YES;
    listView.newsListType = UPNewsListTypeHAOptionalNotice;
    UPNewsColumnTagInfo * tagInfo = self.typeArray[index];
    listView.tagInfo = tagInfo;
    [listView requestDataIsMore:NO];
    return listView;
}

- (void)pageView:(UPPageView*)pageView didSelect:(NSInteger)index{
    [self.tabView setSelectedItemIndex:index subItemIndex:0 isNeedCallBack:NO];
}

- (void)pageView:(UPPageView*)pageView didUnSelect:(NSInteger)index{
    
}

#pragma mark UPErrorViewDelegate
-(void)errorViewTryAgain:(UPErrorView *)errorView{
    [self requestTabInfo];
}

@end
