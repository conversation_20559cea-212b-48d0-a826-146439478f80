//
//  UPStockOptionalSubListView1.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/9.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockOptionalSubListView.h"
#import "UPStockOptionalNoticeCell.h"
#import "UPStockOptionalNewsCell.h"
#import "UPStockOptionalEventCell.h"
#import "UPStockOptionalQACell.h"
#import "UPStockOptionalResearchCell.h"
#import <UPCommon/UPCommon.h>

@interface UPStockOptionalSubListView ()

@end

@implementation UPStockOptionalSubListView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
         [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(sdiNotifyOptionalDataUpdated) name:UPNotifyOptionalDataUpdated object:nil];
    }
    return self;
}

// MARK: - Observer

- (void)sdiNotifyOptionalDataUpdated {
    // 当前SDK逻辑是在调用接口通知服务器之前向客户端发布了通知，
    // 当前需要延迟执行，SDK更新之后需要变更逻辑
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self requestDataIsMore:NO];
    });
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UPNotifyOptionalDataUpdated object:nil];
}

#pragma mark - Network

- (void)refreshdData {
    [self requestDataIsMore:NO];
}

#pragma mark - Public


- (UPNewsIDListReq *)customizeRequestIsMore:(BOOL)isMore {
    UPNewsIDListReq *req = [super customizeRequestIsMore:isMore];
    req.listType = self.newsListType;
    req.isChooseType = YES;
    return req;
}

- (UITableViewCell *)displayCellAtIndexPath:(NSIndexPath *)indexPath forTableView:(UITableView *)tableView {
    UPNewsListInfo *model = self.modelsArray[indexPath.row];
    
    BOOL isRead = [UPNewsManager isRead:model.baseInfo.newsID];
    model.baseInfo.isRead = isRead;
    
    [self updateStockInfoWithModel:model];
    
    __weak typeof(self) weakSelf = self;

    if (self.newsListType == UPNewsListTypeHAOptionalNotice) {
        // 自选公告
        UPStockOptionalNoticeCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockOptionalNoticeCell"];
        cell.model = model;
        cell.stockClickBlock = ^(NSInteger setCode, NSString * _Nonnull code) {
            [weakSelf goToStockDetailWithSetCode:setCode code:code];
        };
        return cell;
    } else if (self.newsListType == UPNewsListTypeHAOptionalQA) {
        // 自选问董秘
        UPStockOptionalQACell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockOptionalQACell"];
        cell.model = model;
        cell.stockClickBlock = ^(NSInteger setCode, NSString * _Nonnull code) {
            [weakSelf goToStockDetailWithSetCode:setCode code:code];
        };
        return cell;
    } else {
        if (model.baseInfo.recomType == UPNewsRecomTypeResearch) {
            UPStockOptionalResearchCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockOptionalResearchCell"];
            cell.model = model;
            cell.stockClickBlock = ^(NSInteger setCode, NSString * _Nonnull code) {
                [weakSelf goToStockDetailWithSetCode:setCode code:code];
            };
            return cell;
        } else {
            // 自选新闻
            UPStockOptionalNewsCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockOptionalNewsCell"];
            cell.model = model;
            cell.stockClickBlock = ^(NSInteger setCode, NSString * _Nonnull code) {
                [weakSelf goToStockDetailWithSetCode:setCode code:code];
            };
            return cell;
        }
    }
    return [UITableViewCell new];
}

- (void)didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.newsListType == UPNewsListTypeHAOptionalQA) return;
    [super didSelectRowAtIndexPath:indexPath];
}

#pragma mark - Private

#pragma mark - Getter && Setter

- (NSArray *)cellClassArray {
    return @[[UPStockOptionalNoticeCell class], [UPStockOptionalQACell class], [UPStockOptionalNewsCell class], [UPStockOptionalResearchCell class]];
}

- (BOOL)shouldMonitor {
    return YES;
}

- (void)setEnableRefresh:(BOOL)enableRefresh {
    _enableRefresh = enableRefresh;
    self.tableView.up_enablePullRefresh = enableRefresh;
    [self.tableView up_setPullRefreshTarget:self action:@selector(refresh)];
}

- (void)refresh {
    [self requestDataIsMore:NO];
}
@end
