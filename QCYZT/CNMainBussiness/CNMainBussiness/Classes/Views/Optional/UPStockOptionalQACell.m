//
//  UPStockOptionalQACell.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/9.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockOptionalQACell.h"
#import <UPMarketUISDK/NSMutableAttributedString+UPLineSpace.h>
#import <UPMarketUISDK/UPMarketUICalculateUtil.h>
#import <UPMarketUISDK/UPMarketUICompareTool.h>
#import "UIColor+UPStockMain.h"

@interface UPStockOptionalQACell()
@property (strong, nonatomic) UILabel *stockNameLabel;
@property (strong, nonatomic) UILabel *stockCodeLabel;
@property (strong, nonatomic) UILabel *changeLabel;
@property (strong, nonatomic) UIView *bgView;
@property (strong, nonatomic) UILabel *QALabel;
@property (strong, nonatomic) UILabel *questionLabel;
@property (strong, nonatomic) UILabel *answerLabel;
@property (strong, nonatomic) UILabel *dateLabel;
@property (strong, nonatomic) UIView *answerBgView;
@property (strong, nonatomic) UPNewsStockInfo *stockInfo;
@property (nonatomic, strong) UIView * lineView;

@end

@implementation UPStockOptionalQACell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.up_listBgColor;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self.contentView addSubview:self.stockNameLabel];
    [self.contentView addSubview:self.stockCodeLabel];
    [self.contentView addSubview:self.changeLabel];
    [self.contentView addSubview:self.bgView];
    [self.bgView addSubview:self.answerBgView];
    [self.bgView addSubview:self.answerLabel];
    [self.bgView addSubview:self.questionLabel];
    [self.bgView addSubview:self.dateLabel];
    [self.bgView addSubview:self.QALabel];
    [self.contentView addSubview:self.lineView];
    [self addStockClickView];
    
    [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(20);
        make.left.equalTo(self.contentView).offset(20);
        make.width.mas_lessThanOrEqualTo(UPWidth(175));
    }];
    
    [self.stockCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockNameLabel.mas_right).offset(3);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.QALabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bgView).offset(20);
        make.size.mas_equalTo(CGSizeMake(32, 18));
        make.top.equalTo(self.questionLabel);
    }];
    
    [self.changeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockCodeLabel.mas_right).offset(15);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(0);
        make.right.equalTo(self.contentView).offset(0);
        make.top.equalTo(self.contentView).offset(52);
        make.bottom.equalTo(self.contentView).offset(0);
    }];
    
    [self.questionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bgView).offset(20);
        make.top.equalTo(self.bgView);
        make.right.equalTo(self.bgView).offset(-20);
    }];
    
    [self.answerLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(self.answerBgView).offset(10);
        make.right.bottom.equalTo(self.answerBgView).offset(-10);
    }];
    
    [self.answerBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bgView).offset(20);
        make.right.equalTo(self.bgView).offset(-20);
        make.top.equalTo(self.questionLabel.mas_bottom).offset(15);
        make.bottom.equalTo(self.bgView).offset(-48);
    }];
    
    [self.dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.right.equalTo(self.bgView).offset(-20);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.right.equalTo(self.contentView).offset(-15);
        make.bottom.equalTo(self.contentView);
        make.height.equalTo(@(0.5));
    }];
    
}

- (void)addStockClickView {
    UIView *v = [UIView new];
    v.backgroundColor = [UIColor clearColor];
    UITapGestureRecognizer *ges = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(stockClick)];
    [v addGestureRecognizer:ges];
    [self.contentView addSubview:v];
    [v mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self.stockNameLabel);
        make.right.equalTo(self.changeLabel);
    }];
}

- (void)stockClick {
    if (self.stockClickBlock && self.stockInfo) {
        self.stockClickBlock(self.stockInfo.setCode, self.stockInfo.code);
    }
}

- (void)setupStockInfo:(UPNewsStockInfo *)model {
    self.stockNameLabel.hidden = YES;
    self.stockCodeLabel.hidden = YES;
    self.changeLabel.hidden = YES;
    self.stockNameLabel.text = @"";
    self.changeLabel.text = @"";
    
    if (!model) return;

    self.stockNameLabel.hidden = NO;
    self.stockCodeLabel.hidden = NO;
    self.changeLabel.hidden = NO;
    self.stockInfo = model;
   
    self.stockNameLabel.text = model.name;
    self.stockCodeLabel.text = model.code;
    self.changeLabel.textColor = [UPMarketUICompareTool compareWithData:model.changeValue baseData:0 precise:0];
    self.changeLabel.text = [NSString stringWithFormat:@"%.2f %@",model.nowPrice, [UPMarketUICalculateUtil transPercent:model.changeRatio needSymbol:YES]];
}

- (void)QALabelTap {
    
}

#pragma mark - Getter && Setter

- (void)setModel:(UPNewsListInfo *)model {
    _model = model;
    [self setupStockInfo:model.stockArray.firstObject];
    
    self.questionLabel.attributedText = [NSMutableAttributedString up_getlineSpaceAttributedStringWithString:[NSString stringWithFormat:@"          %@",model.baseInfo.title] lineSpace:4];
    
    self.answerLabel.attributedText = [NSMutableAttributedString up_getlineSpaceAttributedStringWithString:model.baseInfo.summary lineSpace:4];
    
//    self.dateLabel.text = [NSString up_newsTimeFormatterWithTimeStamp:model.baseInfo.timestamp];
    self.dateLabel.text = [NSString up_dateStringWithDateFormat:@"yyyy-MM-dd HH:mm" timestamp:model.baseInfo.timestamp];;
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] init];
        _stockNameLabel.font = [UIFont up_fontOfSize:UPWidth(15)];
        _stockNameLabel.textColor = [UIColor up_colorFromHexString:@"#657180"];
    }
    return _stockNameLabel;
}

- (UILabel *)stockCodeLabel {
    if (!_stockCodeLabel) {
        _stockCodeLabel = [[UILabel alloc] init];
        _stockCodeLabel.font = [UIFont up_fontOfSize:UPWidth(15)];
        _stockCodeLabel.textColor = [UIColor up_colorFromHexString:@"#657180"];
    }
    return _stockCodeLabel;
}

- (UILabel *)changeLabel {
    if (!_changeLabel) {
        _changeLabel = [[UILabel alloc] init];
        _changeLabel.font = [UIFont up_fontOfSize:UPWidth(15) weight:UIFontWeightSemibold];
        _changeLabel.textColor = UIColor.up_textSecondaryColor;
    }
    return _changeLabel;
}

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [UIView new];
        _bgView.backgroundColor = UIColor.up_contentBgColor;
//        _bgView.layer.cornerRadius = 5;
//        _bgView.layer.shadowColor = [UIColor blackColor].CGColor;//[UIColor up_colorFromHexString:@"#FFF1F1F1"].CGColor;
//        _bgView.layer.shadowRadius = 5;
//        _bgView.layer.shadowOffset = CGSizeZero;
//        _bgView.layer.shadowOpacity = 0.2;
    }
    return _bgView;
}

- (UILabel *)questionLabel {
    if (!_questionLabel) {
        _questionLabel = [UILabel new];
        _questionLabel.font = [UIFont up_fontOfSize:UPWidth(15)];
        _questionLabel.textColor = UIColor.up_textPrimaryColor;
        _questionLabel.numberOfLines = 0;
    }
    return _questionLabel;
}

- (UILabel *)answerLabel {
    if (!_answerLabel) {
        _answerLabel = [UILabel new];
        _answerLabel.backgroundColor = [UIColor clearColor];
        _answerLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _answerLabel.textColor = UIColor.up_textSecondaryColor;
        _answerLabel.numberOfLines = 0;
    }
    return _answerLabel;
}

- (UILabel *)dateLabel {
    if (!_dateLabel) {
        _dateLabel = [UILabel new];
        _dateLabel.textColor = [UIColor up_textSecondaryColor];
        _dateLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
    }
    return _dateLabel;
}

- (UILabel *)QALabel {
    if (!_QALabel) {
        _QALabel = [UILabel new];
        _QALabel.textColor = [UIColor up_colorFromHexString:@"#ffffff"];
        _QALabel.font = [UIFont up_fontOfSize:11];
        _QALabel.text = @"问答";
        _QALabel.layer.cornerRadius = 2;
        _QALabel.layer.masksToBounds = YES;
        _QALabel.textAlignment = NSTextAlignmentCenter;
        _QALabel.backgroundColor = UIColor.up_brandColor;
        _QALabel.userInteractionEnabled = YES;
        UITapGestureRecognizer *ges = [[UITapGestureRecognizer alloc] init];
        [ges addTarget:self action:@selector(QALabelTap)];
        [_QALabel addGestureRecognizer:ges];
    }
    return _QALabel;
}

- (UIView *)answerBgView {
    if (!_answerBgView) {
        _answerBgView = [UIView new];
        _answerBgView.backgroundColor = [UIColor upstockMain_optinal_qa_card_bg_color];
    }
    return _answerBgView;
}

- (UIView *)lineView{
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = [UIColor up_colorFromHexString:@"#E4E6F2"];
    }
    return _lineView;
}
@end
