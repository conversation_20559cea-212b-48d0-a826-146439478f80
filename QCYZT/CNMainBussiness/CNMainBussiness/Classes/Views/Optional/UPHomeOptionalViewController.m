//
//  UPHomeOptionalViewController.m
//  UPStockMain
//
//  Created by 方恒 on 2020/6/5.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPHomeOptionalViewController.h"
#import "UPStockOptionalSubListView.h"
#import "UPHomeWebView.h"
#import "UPStockOptionalLoginView.h"
#import "UIColor+UPStockMain.h"

@interface UPHomeOptionalViewController ()<UPTabViewDelegate, UIScrollViewDelegate>

@property (nonatomic, strong) UPTabView *tabView;

@property (nonatomic, strong) UIView *scrollContainerView;

@property (nonatomic, copy) NSArray *titles;

// 当前显示的view
@property (nonatomic, strong) UIView<UPPagerViewListViewDelegate> *currentVisibleView;

@property (nonatomic, strong) UPStockOptionalSubListView *xwView;
@property (nonatomic, strong) UPHomeWebView *dsView;
@property (nonatomic, strong) UPStockOptionalSubListView *ggView;
@property (nonatomic, strong) UPStockOptionalSubListView *wdmView;


@property (nonatomic, strong) NSArray *componentViewArr;

@property (nonatomic, copy) void (^scrollCallback) (UIScrollView *scrollView);

@property (nonatomic, strong) UIScrollView *currentListView;

@property (strong, nonatomic) UPStockOptionalLoginView *loginView;

@end

@implementation UPHomeOptionalViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    _titles = @[@"新闻", @"动态", @"公告", @"问董秘"];
    
    _tabView = [[UPTabView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth(self.view.bounds), 50)];
    _tabView.delegate = self;
    _tabView.tabItems = [self getTabItems];
    _tabView.minItemWidth = 40;
    _tabView.showIndicator = NO;
    _tabView.backgroundColor = UIColor.upstockMain_optinal_qa_card_bg_color;
    
    [self.view addSubview:self.tabView];
    [self.view addSubview:self.scrollView];
    [self.tabView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(self.view).mas_offset(0);
        make.height.mas_equalTo(UPWidth(37));
    }];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.tabView.mas_bottom);
        make.bottom.equalTo(self.view);
        make.left.right.equalTo(self.view);
    }];
    
    [self.scrollView addSubview:self.scrollContainerView];
    [self.scrollContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.height.equalTo(self.scrollView);
    }];
    
    __weak typeof(self) weakSelf = self;
    
    UPStockOptionalSubListView *xwView = [[UPStockOptionalSubListView alloc] init];
    xwView.newsListType = UPNewsListTypeHAOptionalNews;
    xwView.scrollCallback = ^(UIScrollView * _Nonnull scrollView) {
        weakSelf.scrollCallback(scrollView);
    };
    [xwView requestDataIsMore:NO];
    _xwView = xwView;
    
    // 动态采用H5
    UPHomeWebView *dsView = [[UPHomeWebView alloc] init];
    dsView.scrollCallback = ^(UIScrollView * _Nonnull scrollView) {
        weakSelf.scrollCallback(scrollView);
    };
    NSString *sUid = UPUserManager.uid ?: @"";
    NSString *token = UPUserManager.userToken.uidToken ?: @"";
    NSString *urlString = [NSString stringWithFormat:@"%@?sUid=%@&token=%@",UPDAURLOptionalDynamic,sUid,token];
    [dsView loadUrlString:urlString];
    _dsView = dsView;
    
    UPStockOptionalSubListView *ggView = [[UPStockOptionalSubListView alloc] init];
    ggView.newsListType = UPNewsListTypeHAOptionalNotice;
    ggView.scrollCallback = ^(UIScrollView * _Nonnull scrollView) {
        weakSelf.scrollCallback(scrollView);
    };
    [ggView requestDataIsMore:NO];
    _ggView = ggView;
    
    UPStockOptionalSubListView *wdmView =[[UPStockOptionalSubListView alloc] init];
    wdmView.newsListType = UPNewsListTypeHAOptionalQA;
    wdmView.scrollCallback = ^(UIScrollView * _Nonnull scrollView) {
        weakSelf.scrollCallback(scrollView);
    };
    [wdmView requestDataIsMore:NO];
    _wdmView = wdmView;
    
    [self.scrollContainerView addSubview:xwView];
    [self.scrollContainerView addSubview:dsView];
    [self.scrollContainerView addSubview:ggView];
    [self.scrollContainerView addSubview:wdmView];
    
    [xwView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(self.scrollContainerView);
        make.width.equalTo(self.view);
    }];
    
    [dsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.scrollContainerView);
        make.left.equalTo(xwView.mas_right).mas_offset(0);
        make.width.equalTo(self.view);
    }];
    
    [ggView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.scrollContainerView);
        make.left.equalTo(dsView.mas_right).mas_offset(0);
        make.width.equalTo(self.view);
    }];
    
    [wdmView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.scrollContainerView);
        make.left.equalTo(ggView.mas_right).mas_offset(0);
        make.width.equalTo(self.view);
        make.right.equalTo(self.scrollContainerView);
    }];
    
    self.componentViewArr = @[xwView, dsView, ggView, wdmView];
    [_tabView setSelectedItemIndex:0 subItemIndex:0];
    
    [self.view addSubview:self.loginView];
    
    [self.loginView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    [self.view bringSubviewToFront:self.loginView];
    
    self.loginView.hidden = [UPUserManager isUserLogin];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                       selector:@selector(notifyUserLogin)
                                    name:UPNotifyUserDidLogin
                                  object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
         selector:@selector(notifyUserLogout)
      name:UPNotifyUserDidLogout
    object:nil];
}

- (void)notifyUserLogin {
    self.loginView.hidden = YES;
}

- (void)notifyUserLogout {
    self.loginView.hidden = NO;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (NSArray<UPTabViewItem *> *)getTabItems {
    NSMutableArray *items = [NSMutableArray arrayWithCapacity:5];
    [self.titles enumerateObjectsUsingBlock:^(NSString *  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        UPTabViewItem *item = [[UPTabViewItem alloc] init];
        item.title = obj;
        item.tag = idx;
        [items addObject:item];
    }];
    return [NSArray arrayWithArray:items];
}

/// MARK: - UPTabViewDelegate
- (void)tabView:(UPTabView *)tabView didSelectItem:(UPTabViewItem *)item {
    
    CGPoint contentOffset = self.scrollView.contentOffset;
    
    contentOffset.x = self.view.bounds.size.width * item.tag;
    
    [self.scrollView setContentOffset:contentOffset animated:NO];

    switch (item.tag) {
        case 0:
            self.currentListView = self.xwView.tableView;
            self.currentVisibleView = self.xwView;
            break;
        case 1:
            self.currentListView = self.dsView.webView.scrollView;
            self.currentVisibleView = self.dsView;
            break;
        case 2:
            self.currentListView = self.ggView.tableView;
            self.currentVisibleView = self.ggView;
            break;
        case 3:
            self.currentListView = self.wdmView.tableView;
            self.currentVisibleView = self.wdmView;
            break;
        default:
            break;
    }
}

/// MARK: - UIScrollViewDelegate
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    if (scrollView != _scrollView) {
        return;
    }
    NSInteger index = (NSInteger) (scrollView.contentOffset.x / scrollView.frame.size.width);
    [self.tabView setSelectedItemIndex:index subItemIndex:0];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}


/// MARK: - UPPagerViewListViewDelegate
- (UIView *)listView {
    return self.view;
}

- (UIScrollView *)listScrollView {
    return self.currentListView;
}

- (void)refreshDataFinished:(void (^)(void))finished {
    // 刷线当前页面数据
    [self.currentVisibleView refreshDataFinished:^{
        !finished ?: finished();
    }];
}

- (void)listViewDidScrollCallback:(nonnull void (^)(UIScrollView * _Nonnull))callback {
    self.scrollCallback = callback;
}

- (void)listScrollViewWillResetContentOffset {
    self.xwView.tableView.contentOffset = CGPointZero;
    self.ggView.tableView.contentOffset = CGPointZero;
    self.wdmView.tableView.contentOffset = CGPointZero;
}

/// MARK: - Lazy Loading

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:CGRectZero];
        _scrollView.delegate = self;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.pagingEnabled = YES;
        _scrollView.bounces = NO;
        _scrollView.scrollsToTop = NO;
    }
    return _scrollView;
}

- (UIView *)scrollContainerView {
    if (!_scrollContainerView) {
        _scrollContainerView = [[UIView alloc] init];
    }
    return _scrollContainerView;
}

- (UPStockOptionalLoginView *)loginView {
    if (!_loginView) {
        _loginView = [UPStockOptionalLoginView new];
    }
    return _loginView;
}
@end
