//
//  UPStockOptionalLoginView.m
//  UPStockMain
//
//  Created by caoxk on 2021/3/19.
//  Copyright © 2021 UpChina. All rights reserved.
//

#import "UIColor+UPStockMain.h"

#import "UPStockOptionalLoginView.h"

@interface UPStockOptionalLoginView ()

@property (strong, nonatomic) UIImageView *imageView;

@property (strong, nonatomic) UILabel *titleLabel;

@property (strong, nonatomic) UIButton *loginBtn;

@end

@implementation UPStockOptionalLoginView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {

        self.backgroundColor = [UIColor upstockmain_option_new_nologin_color];
        [self addSubview:self.imageView];
        [self addSubview:self.titleLabel];
        [self addSubview:self.loginBtn];
        
        [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self);
            make.centerY.equalTo(self).multipliedBy(0.87);
        }];
        
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self);
            make.top.equalTo(self.imageView.mas_bottom).offset(25);
        }];
        
        [self.loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLabel.mas_bottom).offset(15);
            make.left.equalTo(self).offset(30);
            make.right.equalTo(self).offset(-30);
            make.height.mas_equalTo(40);
        }];
        
    }
    return self;
}

- (void)login {
    [UPRouterUtil goUserLogin];
}

- (UIImageView *)imageView {
    if (!_imageView) {
        _imageView = [UIImageView new];
        _imageView.image = UPTImgInModule(@"占位图/默认-无内容", @"UPCommon");
    }
    return _imageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.textColor = [UIColor up_textPrimaryColor];
        _titleLabel.font = [UIFont up_fontOfSize:15];
        _titleLabel.text = @"暂无自选资讯";
    }
    return _titleLabel;
}

- (UIButton *)loginBtn {
    if (!_loginBtn) {
        _loginBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_loginBtn setTitle:@"验证手机号码，登录查看自选资讯" forState:UIControlStateNormal];
        _loginBtn.titleLabel.font = [UIFont up_fontOfSize:15];
        [_loginBtn addTarget:self action:@selector(login) forControlEvents:UIControlEventTouchUpInside];
        
        [_loginBtn setTitleColor:[UIColor up_textHrefColor] forState:UIControlStateNormal];
        
        _loginBtn.layer.borderColor = [UIColor up_textHrefColor].CGColor;
        _loginBtn.layer.cornerRadius = 20;
        _loginBtn.layer.borderWidth = 1;
    }
    return _loginBtn;
}

@end

