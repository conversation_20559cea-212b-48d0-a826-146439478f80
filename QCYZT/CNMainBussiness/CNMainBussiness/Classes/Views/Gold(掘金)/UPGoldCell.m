//
//  UPGoldCell.m
//  UPStockMain
//
//  Created by lwd on 2022/9/14.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import "UPGoldCell.h"
@interface UPGoldCell()
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *introLabel;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UIView *lineView;
@end

@implementation UPGoldCell

-(void)setSelected:(BOOL)selected animated:(BOOL)animated
{
 
}
 
-(void)setHighlighted:(BOOL)highlighted animated:(BOOL)animated
{
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.backgroundColor = UIColor.up_contentBgColor;
        [self setupViews];
        [self setConstraints];
    }
    return self;
}

- (void)setupViews {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.introLabel];
    [self.contentView addSubview:self.timeLabel];
    [self.contentView addSubview:self.iconImageView];
    [self.contentView addSubview:self.lineView];
}

- (void)setConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(20);
        make.left.equalTo(self.contentView.mas_left).offset(20);
        make.right.equalTo(self.iconImageView.mas_left).offset(-14);
    }];
    
    [self.introLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(5);
    }];
    
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.titleLabel);
        make.top.equalTo(self.introLabel.mas_bottom).offset(12);
    }];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel);
        make.width.equalTo(@100);
        make.height.equalTo(@74);
        make.right.equalTo(self.contentView.mas_right).offset(-15);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.timeLabel.mas_bottom).offset(25);
        make.left.equalTo(self.contentView.mas_left).offset(15);
        make.right.equalTo(self.contentView.mas_right).offset(-15);
        make.height.equalTo(@0.5);
        make.bottom.equalTo(self.contentView.mas_bottom);
    }];
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.font = [UIFont up_fontOfSize:16];
        _titleLabel.numberOfLines = 2;
        _titleLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _titleLabel;
}

- (UILabel *)introLabel {
    if (!_introLabel) {
        _introLabel = [[UILabel alloc] init];
        _introLabel.textColor = [UIColor up_colorFromHexString:@"#657180"];
        _introLabel.font = [UIFont up_fontOfSize:13];
        _introLabel.numberOfLines = 1;
        _introLabel.textAlignment = NSTextAlignmentLeft;
        
    }
    return _introLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [[UILabel alloc] init];
        _timeLabel.textColor = [UIColor up_colorFromHexString:@"#9BA5B8"];
        _timeLabel.font = [UIFont up_fontOfSize:13];
        _timeLabel.numberOfLines = 1;
        _timeLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _timeLabel;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/首页-占位图")];
        [_iconImageView setContentMode:UIViewContentModeScaleAspectFill];
        _iconImageView.clipsToBounds = YES;
        _iconImageView.layer.cornerRadius = 6.f;
    }
    return _iconImageView;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor = [UIColor up_colorFromHexString:@"#D7DDE4"];
    }
    return _lineView;
}

- (void)setModel:(UPGoldModel *)model {
    _model = model;
    self.titleLabel.text = model.title;
    self.introLabel.text = model.realContent;
//    NSDate *targetDate = [NSDate up_dateFromMillisecond:model.timestamp];
    self.timeLabel.text = model.publishTime;//[NSString stringWithFormat:@"%@",[targetDate up_formatDate:kUPDateFormat_yyyyMMddHHmmss]];
//    self.timeLabel.text = [NSString up_newsTimeFormatterWithTimeStamp:(model.timestamp/1000)];;
    [self.iconImageView up_setImageWithURL:[NSURL URLWithString:model.coverUrl] placeholderImage:UPTImg(@"Home/掘金-占位图")];
    if (model.isRead) {
        self.titleLabel.textColor = [UIColor up_colorFromHexString:@"#9BA5B8"];
        self.introLabel.textColor = [UIColor up_colorFromHexString:@"#9BA5B8"];
    }else{
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _introLabel.textColor = [UIColor up_colorFromHexString:@"#657180"];
    }
}

@end
