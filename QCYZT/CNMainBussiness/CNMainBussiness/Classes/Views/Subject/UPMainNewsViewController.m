//
//  UPMainNewsViewController.m
//  UPStockMain
//
//  Created by lizhixiang on 2022/11/10.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import "UPMainNewsViewController.h"
#import "UPStockSubjectCell.h"
#import "UPStockSubjectListView.h"

@interface UPMainNewsViewController ()<UITableViewDelegate,UITableViewDataSource>
@property (nonatomic, strong) UPStockSubjectListView *listView;
@property(nonatomic, strong) UIView *navView;
@property(nonatomic, strong) UITableView * tableView;

@end

@implementation UPMainNewsViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"要闻";
    self.hidesNavigationBarWhenPush = NO;
    self.view.backgroundColor = [UIColor up_colorFromHexString:@"#F4F8FB"];
    
//    [self refreshData];
//    CGFloat navHeight = [UIApplication sharedApplication].statusBarFrame.size.height + self.navigationController.navigationBar.frame.size.height;
//    [self.navView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.right.top.equalTo(self.view);
//        make.height.equalTo(@(navHeight));
//    }];
    
    self.listView = [UPStockSubjectListView new];
    [self.view addSubview:self.listView];
    [self.listView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.view);
        make.top.equalTo(self.view.mas_top);//.offset(10);
    }];
    if ([self.listView respondsToSelector:@selector(refreshDataFinished:)]) {
        [self.listView refreshDataFinished:nil];
    }
//    self.listView
    
//    [self.view addSubview:self.tableView];
//    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.right.bottom.equalTo(self.view);
//        make.top.equalTo(self.view.mas_top);//.offset(10);
//    }];
//    [self.tableView reloadData];
    
}


- (BOOL)hidesNavigationBarWhenPush {
    return NO;
}

- (UIColor *)navigationBarTitleColor {
    return UIColor.up_textPrimaryColor;
}

- (UIColor *)navigationBarColor {
    return UIColor.up_titleBarBgColor;
}

- (UIImage *)navigationBarBackImage {
    return UPTImgInModule(@"返回-默认", @"UPCommon");
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}


- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    id model = self.dataArr[indexPath.row];
    
    if ([model isKindOfClass:[UPNewsListInfo class]]) {
        UPNewsListInfo *infoModel = (UPNewsListInfo *)model;
        BOOL isRead = [UPNewsManager isRead:infoModel.baseInfo.newsID];
        infoModel.baseInfo.isRead = isRead;
        
        UPStockSubjectCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockSubjectCell"];
//        [self updateStockInfoWithModel:infoModel];
        cell.model = model;
        return cell;
    }
    return [[UITableViewCell alloc] init];

}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    UPNewsListInfo *model = self.dataArr[indexPath.row];
    if (model.baseInfo.recomType == UPNewsRecomTypeNotice) {
        if(!IsValidateString(model.mediaInfo.pdfUrl)){
            // 无pdf时仍然跳转H5页面
            [UPRouter navigate:model.baseInfo.linkURL];
            return;
        }
        
        NSString *articleTitle = @"";
        if (IsValidateString(model.stockArray.firstObject.name)) {
            articleTitle = [NSString stringWithFormat:@"%@公告",model.stockArray.firstObject.name];
        } else {
            articleTitle = IsValidateString(model.baseInfo.title) ? model.baseInfo.title : @"";
        }
        
        NSString *navigateURL = [@"upchina://pdf/main?" up_buildURLWithQueryParams:@{
            @"url" : IsValidateString(model.mediaInfo.pdfUrl) ? model.mediaInfo.pdfUrl : @"",
            @"articleTitle" : articleTitle,
            @"sourceURL" : IsValidateString(model.baseInfo.linkURL) ? model.baseInfo.linkURL : @"",
            @"message" : IsValidateString(model.baseInfo.linkURL) ? model.baseInfo.summary : @"",
            @"showShare" : @(YES)
        }];
        [UPRouter navigate:navigateURL];
    } else {
        [UPRouter navigate:model.baseInfo.linkURL];
    }
    [UPNewsManager addHasRead:model.baseInfo.newsID];
    [self.tableView reloadData];
}

- (UITableView *)tableView {
    if(!_tableView) {
        _tableView = [[UITableView alloc] init];
        _tableView.backgroundColor = [UIColor up_colorFromHexString:@"#F4F8FB"];
        _tableView.delegate = self;
        _tableView.dataSource = self;

        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
//        [_tableView up_setPullRefreshTarget:self action:@selector(refreshData)];

        [_tableView registerClass:[UPStockSubjectCell class] forCellReuseIdentifier:@"UPStockSubjectCell"];

        [self.view addSubview:_tableView];
    }
    
    return _tableView;
}


@end
