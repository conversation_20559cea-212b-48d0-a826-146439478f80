//
//  UPStockSubjectSectionHeader.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockSubjectSectionHeader.h"

@interface UPStockSubjectSectionHeader()
@property (strong, nonatomic) UILabel *dateLabel;

@end

@implementation UPStockSubjectSectionHeader

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.up_contentBgColor;
        [self layoutUI];
    }
    return self;
}

- (void)layoutUI {
    [self addSubview:self.dateLabel];
    
    [self.dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(20);
        make.centerX.equalTo(self);
        make.bottom.equalTo(self).offset(-10);
    }];
}

#pragma mark - Getter && Setter

- (void)setModel:(UPStockSubjectSectionModel *)model {
    _model = model;
    self.dateLabel.text = model.dateString;
}

- (UILabel *)dateLabel {
    if (!_dateLabel) {
        _dateLabel = [UILabel new];
        _dateLabel.font = [UIFont up_fontOfSize:12];
        _dateLabel.textColor = [UIColor up_textSecondaryColor];
        [_dateLabel sizeToFit];
    }
    return _dateLabel;
}

@end
