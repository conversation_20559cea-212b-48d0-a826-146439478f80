//
//  UPStockSubjectListView.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockSubjectListView.h"
#import "UPStockSubjectCell.h"
#import "UPStockSubjectSectionHeader.h"
#import "UPMainNewsViewController.h"

@interface UPStockSubjectListView()

@end

@implementation UPStockSubjectListView

#pragma mark - Network

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.emptyView.title = @"暂无数据";
        self.emptyView.backgroundColor = nil;
        self.emptyView.userInteractionEnabled = NO;
        self.isShowEmtpyNotHideTableView = YES;
    }
    return self;
}

- (UPNewsIDListReq *)customizeRequestIsMore:(BOOL)isMore {
    UPNewsIDListReq *req = [super customizeRequestIsMore:isMore];
    req.listType = self.newsListType;

    return req;
}

#pragma mark - Public

- (CGFloat)heightHeaderViewInSection {
    if (!self.isDetail) {
        return 0;
    }
    return 40;
}

- (UIView *)headerViewInSection {
    UIView * view = [[UIView alloc] init];
    view.backgroundColor = UIColor.whiteColor;
    view.userInteractionEnabled = YES;
    UIView * iconView = [[UIView alloc] init];
    iconView.backgroundColor = UIColor.up_brandColor;
    iconView.frame = CGRectMake(15, (40-12)/2, 5, 12);
    iconView.layer.cornerRadius = 2.5;
    iconView.layer.masksToBounds = YES;
    [view addSubview:iconView];
    
    UILabel *label = [[UILabel alloc] init];
    label.text = @"市场要闻";
    label.font = [UIFont up_boldFontOfSize:18];
    label.textColor = UIColor.up_textPrimaryColor;
    label.textAlignment = NSTextAlignmentLeft;
    [view addSubview:label];
    label.frame = CGRectMake(31, 0, 78, 40);
    
    UIImageView * more = [[UIImageView alloc] initWithImage:UPTImg(@"智选/更多")];
    [view addSubview:more];
    [more mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(view);
        make.right.equalTo(view).offset(-15);
        make.width.equalTo(@5);
        make.height.equalTo(@9);
    }];
    
    UITapGestureRecognizer * tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(gotoMore)];
    [view addGestureRecognizer:tap];
    
    return view;
}

- (void)gotoMore {

    if (self.jumpMoreBlock) {
        self.jumpMoreBlock(self.modelsArray);
    }
}



- (UITableViewCell *)displayCellAtIndexPath:(NSIndexPath *)indexPath forTableView:(UITableView *)tableView {
    id model = self.modelsArray[indexPath.row];
    
    if ([model isKindOfClass:[UPNewsListInfo class]]) {
        UPNewsListInfo *infoModel = (UPNewsListInfo *)model;
        BOOL isRead = [UPNewsManager isRead:infoModel.baseInfo.newsID];
        infoModel.baseInfo.isRead = isRead;
        
        UPStockSubjectCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockSubjectCell"];
//        [self updateStockInfoWithModel:infoModel];
        cell.model = infoModel;
        return cell;
    } else if ([model isKindOfClass:[UPStockSubjectSectionModel class]]) {
        // 虚拟一个日期cell
        UPStockSubjectSectionHeader *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockSubjectSectionHeader"];
        cell.model = model;
        return cell;
    }
    return [[UITableViewCell alloc] init];
}

- (void)didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    UPNewsListInfo *model = self.modelsArray[indexPath.row];
    if (![model isKindOfClass:[UPNewsListInfo class]]) return;
    [super didSelectRowAtIndexPath:indexPath];
}




- (void)handleDataArray:(NSArray *)dataArray isMore:(BOOL)isMore {
    [super handleDataArray:[self dateDealWithArray:dataArray isMore:isMore] isMore:isMore];
}

- (NSArray *)dateDealWithArray:(NSArray *)dataArray isMore:(BOOL)isMore {
    NSString *lastDate;
    NSMutableArray *totalArray = [NSMutableArray arrayWithCapacity:10];
    
    if (isMore) {
        UPNewsListInfo *infoModel = self.modelsArray.lastObject;
        
        if (![infoModel isKindOfClass:[UPNewsListInfo class]]) return nil;
             
        lastDate = [NSString up_dateStringWithDateFormat:@"yyyy-MM-dd" timestamp:infoModel.baseInfo.timestamp];
    } else {
        lastDate = @"";
    }
    
    for (NSInteger i = 0; i < dataArray.count; i++) {
        UPNewsListInfo *infoModel = dataArray[i];
        NSString *currentDate = [NSString up_dateStringWithDateFormat:@"yyyy-MM-dd" timestamp:infoModel.baseInfo.timestamp];
        
//        if (![currentDate isEqualToString:lastDate]) {
//            UPStockSubjectSectionModel *sectionModel = [UPStockSubjectSectionModel new];
//            sectionModel.tradeType = infoModel.baseInfo.tradeType;
//            
//            NSDate *today = [NSDate date];
//            NSDate *currentDay = [NSDate up_dateFromSecond:infoModel.baseInfo.timestamp];
//            NSString *dateString;
//            if (today.up_year == currentDay.up_year) {
//                dateString = [NSString up_dateStringWithDateFormat:@"MM-dd" timestamp:infoModel.baseInfo.timestamp];
//            } else {
//                dateString = [NSString up_dateStringWithDateFormat:@"yyyy-MM-dd" timestamp:infoModel.baseInfo.timestamp];
//            }
//            
//            NSString *weekDay = [NSString up_getWeekDayWithTimeStamp:infoModel.baseInfo.timestamp];
//            
//            sectionModel.dateString = [NSString stringWithFormat:@"%@ %@",dateString, weekDay];
//            [totalArray addObject:sectionModel];
//            lastDate = currentDate;
//        }
        [totalArray addObject:infoModel];
    }
    return totalArray;
}

#pragma mark - Getter && Setter

- (UPNewsListType)newsListType {
    return UPNewsListTypeNewsYW;
}

- (NSArray<Class> *)cellClassArray {
    return @[[UPStockSubjectSectionHeader class], [UPStockSubjectCell class]];
}

- (BOOL)shouldMonitor {
    return NO;
}

@end
