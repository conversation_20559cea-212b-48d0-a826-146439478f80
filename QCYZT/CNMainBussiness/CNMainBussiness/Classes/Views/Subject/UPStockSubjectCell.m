//
//  UPStockSubjectCell.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockSubjectCell.h"
#import <UPMarketUISDK/UPMarketUICalculateUtil.h>
#import <UPMarketUISDK/UPMarketUICompareTool.h>
#import "UIColor+UPStockMain.h"

@interface UPStockInfoItem : UIView
@property (strong, nonatomic) UILabel *titleLabel;
@property (strong, nonatomic) UILabel *changeLabel;
@property (strong, nonatomic) UPNewsStockInfo *model;
@property (copy, nonatomic) void(^stockClickBlock)(UPNewsStockInfo *stockModel);

@end

@implementation UPStockInfoItem

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self addSubview:self.titleLabel];
        [self addSubview:self.changeLabel];
        self.backgroundColor = UIColor.upstockmain_flash_stock_bg_color;

        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(10);
            make.width.mas_lessThanOrEqualTo(UPWidth(90));
            make.centerY.equalTo(self);
        }];
        
        [self.changeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.titleLabel.mas_right).offset(10);
            make.right.equalTo(self).offset(-10);
            make.centerY.equalTo(self);
        }];
        UITapGestureRecognizer *ges = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(stockClick)];
        [self addGestureRecognizer:ges];
    }
    return self;
}

- (void)stockClick {
    if (self.stockClickBlock) {
        self.stockClickBlock(self.model);
    }
}

- (void)setModel:(UPNewsStockInfo *)model {
    _model = model;
    self.titleLabel.text = model.name;
    self.changeLabel.textColor = [UPMarketUICompareTool compareWithData:model.changeValue baseData:0 precise:0];
    self.changeLabel.text = [UPMarketUICalculateUtil transPercent:model.changeRatio needSymbol:YES];
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont up_fontOfSize:12];
        _titleLabel.textColor = [UIColor up_textSecondaryColor];
    }
    return _titleLabel;
}

- (UILabel *)changeLabel {
    if (!_changeLabel) {
        _changeLabel = [UILabel new];
        _changeLabel.font = [UIFont up_fontOfSize:12];
    }
    return _changeLabel;
}

@end

@interface UPStockSubjectCell()
@property (strong, nonatomic) UILabel *titleLabel;
//@property (strong, nonatomic) UILabel *contentLabel;
@property (strong, nonatomic) UILabel *identifierLabel;
@property (strong, nonatomic) UILabel *dateLabel;
@property (strong, nonatomic) UIImageView *iconImageView;

@property (strong, nonatomic) NSArray<UPStockInfoItem *> *stockItemArray;
@end

@implementation UPStockSubjectCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style
                    reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.up_contentBgColor;
        [self.contentView addSubview:self.iconImageView];

        [self.contentView addSubview:self.titleLabel];
        [self.contentView addSubview:self.identifierLabel];
        [self.contentView addSubview:self.dateLabel];
        
       
        
        [self layoutUI];
    }
    return self;
}


- (void)layoutUI {
    
    UIView *lineview = [[UIView alloc] init];
    lineview.backgroundColor = UIColor.up_dividerColor;
    
    [self.contentView addSubview:lineview];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(UPWidth(20)));
        make.right.equalTo(self.contentView.mas_right).offset(UPWidth(-15));
        make.size.mas_equalTo(CGSizeMake(UPWidth(100), UPHeight(74)));
        make.bottom.equalTo(@(UPWidth(-20)));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(UPWidth(24));
        make.left.equalTo(self.contentView).offset(UPWidth(20));
        make.right.equalTo(self.iconImageView.mas_left).offset(UPWidth(-15));
    }];
    
    
    [self.identifierLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.bottom.equalTo(self.iconImageView.mas_bottom);
    }];
    
    [self.dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.identifierLabel);
        make.right.equalTo(self.titleLabel.mas_right);
    }];
    
    [lineview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.left.equalTo(self.contentView.mas_left).offset(UPWidth(15));
        make.right.equalTo(self.contentView.mas_right).offset(UPWidth(-15));
        make.height.equalTo(@1);
    }];
}

- (void)cleanStockItemView {
    for (UIView *v in self.stockItemArray) {
        [v removeFromSuperview];
    }
    self.stockItemArray = nil;
}

- (void)setModel:(UPNewsListInfo *)model {
    _model = model;
    
    if (model.baseInfo.isRead) {
        self.titleLabel.textColor = [UIColor up_colorFromHexString:@"#9BA5B8"];
        self.dateLabel.textColor = [UIColor up_colorFromHexString:@"#9BA5B8"];
        self.identifierLabel.textColor = [UIColor up_colorFromHexString:@"#9BA5B8"];
    } else {
        self.titleLabel.textColor = UIColor.up_textPrimaryColor;
        self.dateLabel.textColor = [UIColor up_colorFromHexString:@"#9BA5B8"];
        self.identifierLabel.textColor = [UIColor up_colorFromHexString:@"#9BA5B8"];
    }
    NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
    paragraphStyle.lineSpacing = 4;
    NSMutableDictionary *attributes = [NSMutableDictionary dictionary];
    [attributes setObject:paragraphStyle forKey:NSParagraphStyleAttributeName];
    self.titleLabel.attributedText = [[NSAttributedString alloc] initWithString:model.baseInfo.title attributes:attributes];
    self.titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    self.dateLabel.text = [NSString up_newsTimeFormatterWithTimeStamp:model.baseInfo.timestamp];
    
    
    self.identifierLabel.text = model.baseInfo.source;
    
    if (model.mediaInfo.fileURL) {
        self.iconImageView.hidden = NO;
        [self.iconImageView up_setImageWithURLString:model.mediaInfo.fileURL placeholderImage:UPTImg(@"Home/首页-占位图")];
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView).offset(UPWidth(24));
            make.left.equalTo(self.contentView).offset(UPWidth(20));
            make.right.equalTo(self.iconImageView.mas_left).offset(UPWidth(-15));
        }];
    } else {
        self.iconImageView.hidden = YES;

        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.contentView.mas_right).offset(UPWidth(-10));
        }];
    }
    
   
    
    
//    UPNewsTagInfo *tagInfo = model.tagArray.firstObject;
//    self.identifierLabel.text = tagInfo.title;
//    if (IsValidateDict(tagInfo.style)) {
//        self.identifierLabel.backgroundColor =
//        [UIColor up_colorFromHexString:tagInfo.style[@"backgroundColor"]];
//        self.identifierLabel.textColor = [UIColor up_colorFromHexString:tagInfo.style[@"fontColor"]];
//    } else {
//        self.identifierLabel.backgroundColor = [UIColor clearColor];
//        self.identifierLabel.textColor = UIColor.up_textSecondaryColor;
//    }
//    [self.identifierLabel sizeToFit];
    
//    [self cleanStockItemView];
    
//    NSMutableArray *array = [NSMutableArray arrayWithCapacity:10];
//    for (NSInteger i = 0; i < model.stockArray.count; i++) {
//        UPStockInfoItem *item = [UPStockInfoItem new];
//        item.model = model.stockArray[i];
//        item.stockClickBlock = ^(UPNewsStockInfo *stockModel) {
//            [UPRouterUtil goMarketStock:stockModel.setCode code:stockModel.code];
//        };
//        [array addObject:item];
//    }
//
//    self.stockItemArray = [NSArray arrayWithArray:array];
    
//    [self layoutUI];
}

#pragma mark - Getter && Setter

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.layer.cornerRadius = 4.f;
        _iconImageView.layer.masksToBounds = YES;
        [_iconImageView setContentMode:UIViewContentModeScaleAspectFill];
        _iconImageView.clipsToBounds = YES;
}
    
    return _iconImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont up_fontOfSize:16];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.numberOfLines = 2;
    }
    return _titleLabel;
}

//- (UILabel *)contentLabel {
//    if (!_contentLabel) {
//        _contentLabel = [UILabel new];
//        _contentLabel.font = [UIFont up_fontOfSize:13];
//        _contentLabel.textColor = UIColor.up_textSecondaryColor;
//        _contentLabel.numberOfLines = 2;
//    }
//    return _contentLabel;
//}

- (UILabel *)identifierLabel {
    if (!_identifierLabel) {
        _identifierLabel = [UILabel new];
        _identifierLabel.font = [UIFont up_fontOfSize:13];
        _identifierLabel.textAlignment = NSTextAlignmentCenter;
        _identifierLabel.textColor = UIColor.up_textSecondaryColor;
        _identifierLabel.layer.cornerRadius = 4;
        _identifierLabel.layer.masksToBounds = YES;
    }
    return _identifierLabel;
}

- (UILabel *)dateLabel {
    if (!_dateLabel) {
        _dateLabel = [UILabel new];
        _dateLabel.font = [UIFont up_fontOfSize:13];
        _dateLabel.textColor = UIColor.up_textSecondaryColor;
    }
    return _dateLabel;
}

@end
