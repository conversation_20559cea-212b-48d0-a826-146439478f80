//
//  UPNotifyAnimationView.m
//  UPCommon
//
//  Created by caoxk on 2021/11/3.
//

#import "UPNotifyAnimationView.h"
#import <UPBaseUI/UIImageView+ImageLoader.h>

static CGFloat kUPNotifyContentLeftMargin = 15.0;
static CGFloat kUPNotifyContentRightMargin = 15.0;
static CGFloat kUPNotifyContentSummaryTopMargin = 35.0;
static CGFloat kUPNotifyContentSummaryBottomMargin = 15.0;

@interface UPNotifyContentView : UIView

@property (strong, nonatomic) UPTAFPushNotifyMsg *message;

@property (strong, nonatomic) UIImageView *icon;

@property (strong, nonatomic) UILabel *title;

@property (strong, nonatomic) UILabel *summary;

@end

@implementation UPNotifyContentView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self addSubview:self.icon];
        [self addSubview:self.title];
        [self addSubview:self.summary];
        
        [self.title mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(35);
            make.top.equalTo(self).offset(15);
        }];
        
        [self.icon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.title.mas_left);
            make.centerY.equalTo(self.title);
            make.width.height.mas_equalTo(22.5);
        }];
        
        [self.summary mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(kUPNotifyContentLeftMargin);
            make.right.equalTo(self).offset(-kUPNotifyContentRightMargin);
            make.top.equalTo(self).offset(kUPNotifyContentSummaryTopMargin);
        }];
    }
    return self;
}

- (void)setMessage:(UPTAFPushNotifyMsg *)message {
    _message = message;
    
    // 栏目名称取消息中心缓存。tag字段在各推送平台不统一，盯盘tag有内容而其他没有，不使用tag(cat)字段；
    UPTAFMessageColumnInfo *column = [UPTAFMessageManager getColumnInfo:message.uid uidType:message.uidType type:message.type subType:message.subType];
    
    if (IsValidateString(column.name)) {
        self.title.text = column.name;
    } else {
        self.title.text = @"消息推送";
    }
    
    UIImage *placeholder = UPTImg(@"Home/首页-占位图");
    if (IsValidateString(column.icon)) {
        [self.icon up_setImageWithURL:[NSURL URLWithString:column.icon] placeholderImage:placeholder];
    } else {
        self.icon.image = placeholder;
    }
    self.summary.text = message.digest;
}

- (UILabel *)title {
    if (!_title) {
        _title = [UILabel new];
        _title.font = [UIFont up_boldFontOfSize:15];
        _title.textColor = [UIColor up_textPrimaryColor];
    }
    return _title;
}

- (UILabel *)summary {
    if (!_summary) {
        _summary = [UILabel new];
        _summary.font = [UIFont up_fontOfSize:15];
        _summary.textColor = [UIColor up_textPrimaryColor];
        _summary.numberOfLines = 2;
    }
    return _summary;
}

- (UIImageView *)icon {
    if (!_icon) {
        _icon = [UIImageView new];
        [_icon setContentMode:UIViewContentModeScaleAspectFill];
        _icon.clipsToBounds = YES;
    }
    return _icon;
}

@end

static CGFloat kUPNotifyLeftMargin = 15.0;
static CGFloat kUPNotifyRightMargin = 15.0;

@interface UPNotifyAnimationView ()

@property (strong, nonatomic) UPNotifyContentView *content;

@property (strong, nonatomic) NSTimer *timer;

@property (assign, nonatomic) NSInteger leftSeconds;

@property (copy, nonatomic) UPNotifyAnimationViewHandler handler;
@end

@implementation UPNotifyAnimationView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = [UIColor up_contentBgColor];
        self.layer.cornerRadius = 5;
        self.layer.shadowColor = [UIColor blackColor].CGColor;
        self.layer.shadowRadius = 4;
        self.layer.shadowOpacity = 0.4;
        self.layer.shadowOffset = CGSizeMake(0, 0);
        self.userInteractionEnabled = YES;
        self.leftSeconds = 5;
        
        UISwipeGestureRecognizer *swipGesture = [[UISwipeGestureRecognizer alloc] initWithTarget:self action:@selector(swipe)];
        swipGesture.direction = UISwipeGestureRecognizerDirectionUp;
        [self addGestureRecognizer:swipGesture];
        
        UITapGestureRecognizer *tapGes = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(click)];
        [self addGestureRecognizer:tapGes];
    }
    return self;
}

+ (void)showWithMessage:(UPTAFPushNotifyMsg *)message parent:(UIView *)parent handler:(UPNotifyAnimationViewHandler)handler {
    if (!parent) {
        parent = [UIApplication sharedApplication].delegate.window;
    }
    
    if (!parent) return;
    
    UPNotifyAnimationView *notifyView = [UPNotifyAnimationView new];
    notifyView.handler = handler;
    
    UPNotifyContentView *content = [UPNotifyContentView new];
    content.message = message;
    notifyView.content = content;
    
    CGFloat margin = kUPNotifyLeftMargin + kUPNotifyRightMargin + kUPNotifyContentRightMargin + kUPNotifyContentLeftMargin;
    CGSize size = [content.summary sizeThatFits:CGSizeMake(parent.bounds.size.width - margin, CGFLOAT_MAX)];
    
    CGFloat totalHeight = size.height + kUPNotifyContentSummaryTopMargin + kUPNotifyContentSummaryBottomMargin;
    CGFloat totalWidth = parent.up_width - kUPNotifyLeftMargin - kUPNotifyRightMargin;
    
    notifyView.frame = CGRectMake(kUPNotifyLeftMargin, -totalHeight, totalWidth, totalHeight);
    content.frame = notifyView.bounds;
    
    [notifyView addSubview:content];
    [parent addSubview:notifyView];
    [notifyView animationDown];
}

- (void)click {
    if (self.handler) {
        __weak UPTAFPushNotifyMsg *weakMsg = self.content.message;
        self.handler(weakMsg);
    }
    
    if (self.timer) {
        [self.timer invalidate];
        self.timer = nil;
    }
    
    [self animationUp];
}

- (void)swipe {
    [self animationUp];
}

- (void)animationDown {
    [UIView animateWithDuration:0.3 animations:^{
        CGRect frame = self.frame;
        frame.origin.y = 5 + [[UIApplication sharedApplication] statusBarFrame].size.height;
        self.frame = frame;
    } completion:^(BOOL finished) {
        self.timer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(countDown) userInfo:nil repeats:YES];
        [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSRunLoopCommonModes];
        [self.timer fire];
    }];
}

- (void)animationUp {
    [UIView animateWithDuration:0.2 animations:^{
        CGRect frame = self.frame;
        frame.origin.y = -frame.size.height;
        self.frame = frame;
    } completion:^(BOOL finished) {
        if (self.timer) {
            [self.timer invalidate];
            self.timer = nil;
        }
        [self removeFromSuperview];
    }];
}

- (void)countDown {
    self.leftSeconds--;
    if (self.leftSeconds <= 0) {
        [self animationUp];
    }
}

- (void)dealloc {
    if (self.timer) {
        [self.timer invalidate];
        self.timer = nil;
    }
}

@end
