//
//  UPStockPayNewsCell.m
//  UPMarket2
//
//  Created by caoxk on 2021/12/31.
//

#import "UPStockPayNewsCell.h"

@interface UPStockPayNewsCell ()
@property (strong, nonatomic) UILabel *stationLabel;
@property (strong, nonatomic) UILabel *bigTitleLabel;
@property (strong, nonatomic) UILabel *summaryLabel;
@property (strong, nonatomic) UILabel *sourceLabel;
@property (strong, nonatomic) UILabel *dateLabel;
@property (strong, nonatomic) UILabel *payStateLabel;
@property (strong, nonatomic) UIView *lineView;

@end

@implementation UPStockPayNewsCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self layoutUI];
    }
    return self;
}

- (void)layoutUI {
    [self.contentView addSubview:self.stationLabel];
    [self.contentView addSubview:self.bigTitleLabel];
    [self.contentView addSubview:self.payStateLabel];
    [self.contentView addSubview:self.summaryLabel];
    [self.contentView addSubview:self.sourceLabel];
    [self.contentView addSubview:self.dateLabel];
    [self.contentView addSubview:self.lineView];
    
    [self.stationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.top.equalTo(self.contentView).offset(10);
        make.size.mas_equalTo(CGSizeMake(63, 20));
    }];
    
    [self.bigTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.right.equalTo(self.contentView).offset(-15);
        make.top.equalTo(self.contentView).offset(10);
    }];
    
    [self.summaryLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.bigTitleLabel);
        make.top.equalTo(self.bigTitleLabel.mas_bottom).offset(3);
        make.bottom.equalTo(self.contentView).offset(-43);
    }];
    
    [self.sourceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.top.equalTo(self.summaryLabel.mas_bottom).offset(10);
        make.bottom.equalTo(self.contentView).offset(-15);
    }];
    
    [self.payStateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.sourceLabel.mas_right).offset(10);
        make.centerY.equalTo(self.sourceLabel);
        make.size.mas_equalTo(CGSizeMake(50, 17));
    }];
    
    [self.dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView).offset(-15);
        make.centerY.equalTo(self.sourceLabel);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.right.equalTo(self.contentView).offset(-15);
        make.bottom.equalTo(self.sourceLabel).offset(14);
        make.height.mas_equalTo(0.5);
    }];
}

#pragma mark - Getter && Setter

- (void)setModel:(UPNewsListInfo *)model {
    _model = model;
    
    if (model.baseInfo.isRead) {
        self.bigTitleLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.summaryLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.dateLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.sourceLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
    } else {
        self.bigTitleLabel.textColor = UIColor.up_textPrimaryColor;
        self.summaryLabel.textColor = UIColor.up_textPrimaryColor;
        self.dateLabel.textColor = [UIColor up_textSecondary1Color];
        self.sourceLabel.textColor = [UIColor up_textSecondary1Color];
    }
    
    if (model.newsPayType == 2) {
        // 未付费
        self.payStateLabel.text = @"待解锁";
        self.payStateLabel.hidden = NO;
    } else {
        //1:付费-已解锁,0:免费
        self.payStateLabel.text = @"";
        self.payStateLabel.hidden = YES;
    }
    
    if (model.baseInfo.recomType == UPNewsRecomTypeResearchForChance) {
        self.stationLabel.text = @"风口研报";
    } else if(model.baseInfo.recomType == UPNewsRecomTypeEssentialNewsInTrade) {
        self.stationLabel.text = @"盘中宝";
    } else if(model.baseInfo.recomType == UPNewsRecomTypeNineSpecial) {
        self.stationLabel.text = @"九点特供";
    }
    
    NSString *summary = [NSString stringWithFormat:@"%@",model.baseInfo.title];
    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:summary];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 8;
    style.firstLineHeadIndent = 65;
    style.lineBreakMode = NSLineBreakByTruncatingTail;
    
    [attr addAttributes:@{
        NSFontAttributeName : [UIFont up_boldFontOfSize:UPWidth(17)],
        NSParagraphStyleAttributeName : style
    } range:NSMakeRange(0, summary.length)];
    
    self.bigTitleLabel.attributedText = attr;
    self.summaryLabel.text = model.baseInfo.summary;
    self.sourceLabel.text = model.baseInfo.source;
    self.dateLabel.text = [NSString up_newsTimeFormatterWithTimeStamp:model.baseInfo.timestamp];
}

#pragma mark - Private

- (UILabel *)bigTitleLabel {
    if (!_bigTitleLabel) {
        _bigTitleLabel = [UILabel new];
        _bigTitleLabel.font = [UIFont up_boldFontOfSize:UPWidth(17)];
        _bigTitleLabel.textColor = [UIColor up_textPrimaryColor];
        _bigTitleLabel.numberOfLines = 2;
    }
    return _bigTitleLabel;
}

- (UILabel *)summaryLabel {
    if (!_summaryLabel) {
        _summaryLabel = [UILabel new];
        _summaryLabel.font = [UIFont up_fontOfSize:UPWidth(17)];
        _summaryLabel.textColor = [UIColor up_textPrimaryColor];
        _summaryLabel.numberOfLines = 2;
    }
    return _summaryLabel;
}

- (UILabel *)stationLabel {
    if (!_stationLabel) {
        _stationLabel = [UILabel new];
        _stationLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _stationLabel.textColor = [UIColor up_brandColor];
        _stationLabel.textAlignment = NSTextAlignmentCenter;
        _stationLabel.backgroundColor = [UIColor colorWithRed:0.8 green:0.13 blue:0.14 alpha:0.1];
        _stationLabel.layer.cornerRadius = 2;
        _stationLabel.layer.masksToBounds = YES;
    }
    return _stationLabel;
}

- (UILabel *)payStateLabel {
    if (!_payStateLabel) {
        _payStateLabel = [UILabel new];
        _payStateLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _payStateLabel.textColor = [UIColor up_colorFromHexString:@"#2277cc"];
        _payStateLabel.textAlignment = NSTextAlignmentCenter;
        _payStateLabel.backgroundColor = [UIColor colorWithRed:0.13 green:0.46 blue:0.8 alpha:0.1];
        _payStateLabel.layer.cornerRadius = 2;
        _payStateLabel.layer.masksToBounds = YES;
    }
    return _payStateLabel;
}

- (UILabel *)sourceLabel {
    if (!_sourceLabel) {
        _sourceLabel = [UILabel new];
        _sourceLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _sourceLabel.textColor = [UIColor up_textSecondary1Color];
    }
    return _sourceLabel;
}

- (UILabel *)dateLabel {
    if (!_dateLabel) {
        _dateLabel = [UILabel new];
        _dateLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _dateLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _dateLabel;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = [UIColor up_dividerColor];
    }
    return _lineView;
}
@end

