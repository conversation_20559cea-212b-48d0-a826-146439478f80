//
//  UPStockEventListCell.m
//  UPStockMain
//
//  Created by <PERSON><PERSON><PERSON> on 2020/6/12.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPBaseUI/UIImageView+ImageLoader.h>

#import "UPStockEventListCell.h"

@interface UPStockEventListCell () {
    CGFloat _padvel;   // 上下间隔
}

@property (nonatomic, strong) UILabel *titleLabel;        // 标题
@property (nonatomic, strong) UILabel *tagLabel;       // 标签
@property (nonatomic, strong) UILabel *timeLabel;        // 时间

@property (nonatomic, strong) UIImageView *rightImageView;

@end

@implementation UPStockEventListCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;
        _padvel = UPWidth(15);
        [self setupViews];
        [self setConstraints];
    }
    return self;
}

- (void)setupViews {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.tagLabel];
    [self.contentView addSubview:self.timeLabel];
    [self.contentView addSubview:self.rightImageView];
}

- (void)setConstraints {
    CGFloat pad = 15;
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(pad));
        make.right.equalTo(self.rightImageView.mas_left).offset(-pad);
        make.top.equalTo(@(_padvel));
        make.bottom.equalTo(self.tagLabel.mas_top).offset(-10);
    }];
    [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(pad));
        make.bottom.equalTo(@(-_padvel));
        make.width.equalTo(@UPWidth(26));
        make.height.equalTo(@UPWidth(16));
    }];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.tagLabel.mas_centerY);
        make.right.equalTo(self.titleLabel.mas_right);
        make.height.equalTo(self.tagLabel.mas_height);
    }];
    
    [self.rightImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(@(-pad));
        make.width.equalTo(@UPWidth(115));
        make.height.equalTo(@UPWidth(70));
    }];
}

// MARK: - Getter & Setter
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(17)];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.numberOfLines = 2;
    }
    return _titleLabel;
}
- (UILabel *)tagLabel {
    if (!_tagLabel) {
        _tagLabel = [UILabel new];
        _tagLabel.font = [UIFont up_fontOfSize:UPWidth(11)];
        _tagLabel.textAlignment = NSTextAlignmentCenter;
        UIColor *brandColor = UIColor.up_brandColor;
        _tagLabel.textColor = brandColor;
        _tagLabel.backgroundColor = [brandColor colorWithAlphaComponent:0.15];
        
    }
    return _tagLabel;
}
- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UILabel new];
        _timeLabel.font = [UIFont up_fontOfSize:UPWidth(11)];
        _timeLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _timeLabel;
}

- (UIImageView *)rightImageView {
    if (!_rightImageView) {
        _rightImageView = [[UIImageView alloc] init];
        _rightImageView.contentMode = UIViewContentModeScaleAspectFill;
        _rightImageView.layer.masksToBounds = YES;
        _rightImageView.layer.cornerRadius = UPWidth(10);
    }
    return _rightImageView;
}

- (void)setEventModel:(UPNewsListInfo *)eventModel {
    _eventModel = eventModel;
    UPNewsBaseInfo *baseInfo = eventModel.baseInfo;
    
    if (eventModel.baseInfo.isRead) {
        self.titleLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.timeLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
    } else {
        self.titleLabel.textColor = UIColor.up_textPrimaryColor;
        self.timeLabel.textColor = [UIColor up_textSecondary1Color];
    }
    
    // 1. 标题
    self.titleLabel.text = [NSString stringWithFormat:@"%@: %@", baseInfo.title, baseInfo.summary];
    
    // 2. 标签
    NSDictionary <NSNumber *, NSArray*> *tagDic = eventModel.tagDic;
    NSArray *array = [tagDic objectForKey:@(UPNewsTagTypeCustomWord)];
    UPNewsTagInfo *tagInfo = array.firstObject;
    self.tagLabel.text = tagInfo.title;
    
    CGSize size = [tagInfo.title sizeWithAttributes:@{NSFontAttributeName : self.tagLabel.font}];
    
    CGFloat tagWidth = 0.0;
    if (IsValidateString(tagInfo.title)) {
        tagWidth = size.width + 5;
    }
    
    [self.tagLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(15));
        make.bottom.equalTo(@(-_padvel));
        make.width.equalTo(@(tagWidth));
        make.height.equalTo(@UPWidth(16));
    }];
    
    // 3. 时间
    self.timeLabel.text = [NSString up_newsTimeFormatterWithTimeStamp:baseInfo.timestamp];
    
    // 4. 图片
    UPNewsMediaInfo *mediaInfo = eventModel.mediaInfo;
    
    NSURL *iurl = [NSURL URLWithString:mediaInfo.fileURL];
    [self.rightImageView up_setImageWithURL:iurl
                           placeholderImage:UPTImg(@"Home/首页-占位图")];
    
    if (IsValidateString(mediaInfo.fileURL)) {
        [self.rightImageView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@UPWidth(115));
        }];
    } else {
        [self.rightImageView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(0);
        }];
    }
}
@end
