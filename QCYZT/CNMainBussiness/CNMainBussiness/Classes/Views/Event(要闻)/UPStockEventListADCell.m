//
//  UPStockEventListADCell.m
//  UPStockMain
//
//  Created by <PERSON><PERSON><PERSON> on 2020/6/28.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockEventListADCell.h"

@interface UPStockEventListADCell ()
@property (nonatomic, strong) UIImageView *adImageView;  // 广告图

@end

@implementation UPStockEventListADCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;
        [self setupViews];
        [self setConstraints];
    }
    return self;
}

- (void)setupViews {
    [self.contentView addSubview:self.adImageView];
}

- (void)setConstraints {
    CGFloat pad = 15;
    [self.adImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(pad));
        make.right.equalTo(@(-pad));
        make.top.equalTo(@(pad));
        make.height.mas_equalTo(135);
        make.bottom.equalTo(@(-pad));
    }];
}

// MARK: - Getter & Setter
- (UIImageView *)adImageView {
    if (!_adImageView) {
        _adImageView = [[UIImageView alloc] init];
        _adImageView.layer.masksToBounds = YES;
        _adImageView.layer.cornerRadius = UPWidth(5);
        _adImageView.contentMode = UIViewContentModeScaleAspectFill;

    }
    return _adImageView;
}

- (void)setEventModel:(UPNewsListInfo *)eventModel {
    _eventModel = eventModel;
    [self.adImageView up_setImageWithURL:[NSURL URLWithString:eventModel.baseInfo.title] placeholderImage:UPTImg(@"Home/首页-占位图")];
}
@end
