//
//  UPStockPagerNewsList.h
//  UPStockMain
//
//  Created by caoxk on 2020/6/19.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <UPNewsSDK/UPNewsSDK.h>
#import "UPPagerView.h"

NS_ASSUME_NONNULL_BEGIN

/**
* 需要封装和抽离的功能包括
* 1. tableView的初始化、数据源、代理
* 2. 刷新和加载更多
* 3. 新闻类的接口请求
* 4. 无数据页面
* 5. 监听股票的变动，修改model中的股票信息
* 6. UPPagerViewListViewDelegate相关
*/

@interface UPStockPagerNewsList : UIView <UPPagerViewListViewDelegate>

@property (nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);

/// 保存股票信息的字典
@property (copy, nonatomic) NSMutableDictionary *stockInfoDict;

/// 新闻类型，影响新闻列表请求的参数
@property (assign, nonatomic) UPNewsListType newsListType;

/// 需要注册到tableView中的cell，只读，通过get方法设置
@property (copy, nonatomic, readonly) NSArray<Class> *cellClassArray;

/// 数据模型数组
@property (copy, nonatomic) NSArray *modelsArray;

@property (strong, nonatomic) UPErrorView *emptyView;

/// list
@property (strong, nonatomic) UITableView *tableView;

/// 是否监听股票变动，默认为NO，只读，通过get方法设置
@property (assign, nonatomic, readonly) BOOL shouldMonitor;

/**
 新闻子分类信息，例如自选-公告下的分类信息
 */
@property (nonatomic, strong) UPNewsColumnTagInfo *tagInfo;

/**
 是否是自选新闻列表
 */
@property (nonatomic, assign) BOOL isSelfChooseStockNews;


@property (nonatomic, assign) BOOL isShowEmtpyNotHideTableView;


#pragma mark - 子类可以重写的方法
/// cell的展示逻辑
- (UITableViewCell *)displayCellAtIndexPath:(NSIndexPath *)indexPath forTableView:(UITableView *)tableView;

/// tableView点击事件
- (void)didSelectRowAtIndexPath:(NSIndexPath *)indexPath;


- (UIView *)headerViewInSection;
- (CGFloat)heightHeaderViewInSection;


/// 数据处理逻辑
/// @param dataArray 请求来的数据数组
/// @param isMore 刷新/加载更多
- (void)handleDataArray:(NSArray *)dataArray isMore:(BOOL)isMore;

/// 基类请求参数相对简单，子类可以自定义请求参数
- (UPNewsIDListReq *)customizeRequestIsMore:(BOOL)isMore;

/// 基类默认请求新闻的接口，如果需要调用其他接口，需要子类自己实现
-(void)requestDataIsMore:(BOOL)isMore;

/// 将对象数组转化成UPHqStockUnique对象数组
/// 默认实现是对NewsListInfo对象数组的转化
/// @param dataArray 股票信息数组
- (NSArray<UPHqStockUnique *> *)getStockArrayWithNewsInfoArray:(NSArray *)dataArray;

#pragma mark - 通用接口
/// 请求新闻类接口
/// 根据自定义的request发起请求，如果没有则使用默认req对象
/// @param isMore 刷新/加载更多
- (void)requestNewsInfoIsMore:(BOOL)isMore;

/// 监听股票信息
/// @param dataArray 需要监听的股票数组
- (void)monitorStockWithDataArray:(NSArray<UPHqStockUnique *> *)dataArray;

/// 更新股票信息
/// 对指定的model中的stockArray进行股票信息的更新
/// @param infoModel UPNewsListInfo对象
- (void)updateStockInfoWithModel:(UPNewsListInfo *)infoModel;

/// 停止监听
- (void)stopMonitor;


/// 跳转个股详情
- (void)goToStockDetailWithSetCode:(NSInteger)setCode code:(NSString *)code;

@end

NS_ASSUME_NONNULL_END
