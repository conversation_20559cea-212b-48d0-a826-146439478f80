//
//  UIColor+UPStockMain.h
//  UPStockMain
//
//  Created by <PERSON><PERSON><PERSON> on 2020/6/11.
//  Copyright © 2020 UpChina. All rights reserved.
//
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIColor (UPStockMain)
// 快讯标题背景色(#F4F4F4)
@property(class, nonatomic, readonly) UIColor * upstockmain_flash_title_bg_color;
// 快讯股票背景色(#F5F5F5)
@property(class, nonatomic, readonly) UIColor * upstockmain_flash_stock_bg_color;

// 快讯股票背景色(#7F111111)
@property(class, nonatomic, readonly) UIColor * upstockmain_flash_font_shadeBGColor;

// 首证头条类型背景色(#19D22325)
@property(class, nonatomic, readonly) UIColor * upstockMain_headline_news_type_bg_color;

// 首证头条标题颜色(#111111)
@property(class, nonatomic, readonly) UIColor * upsotckMain_headline_news_title_color;

// 问董秘卡片背景色(#F2F2F2->#2F3342)
@property(class, nonatomic, readonly) UIColor * upstockMain_optinal_qa_card_bg_color;

// 7x24竖线(#EBECED->#0F1319)
@property(class, nonatomic, readonly) UIColor * upstockmain_flash_vertical_color;

// 新股申购标题(#FD5C32->#F8F8F8)
@property(class, nonatomic, readonly) UIColor * upstockmain_newstock_title_color;

// 自选资讯未登录页面(#F1F1F1->#F8F8F8)
@property(class, nonatomic, readonly) UIColor * upstockmain_option_new_nologin_color;
@end

NS_ASSUME_NONNULL_END
