//
//  UPMarketUIStockCrosshairLayer+UPMarketUIStockCrosshairLayerHelper.m
//  UPMarketUI-DEMO-iOS
//
//  Created by lejiang on 2023/4/27.
//

#import "UPMarketUIStockCrosshairLayer+UPMarketUIStockCrosshairLayerHelper.h"
#import <UPMarketUISDK/UPMarketUIIndexUtil.h>
#import <UPMarketUISDK/UPMarketUIDrawTool.h>
#import <UPBaseUI/UPThemeMacro.h>
#import <UPMarketUISDK/UIFont+UPMarketUIFont.h>

@implementation UPMarketUIStockCrosshairLayer (UPMarketUIStockCrosshairLayerHelper)

- (CGRect)stockCustomLayerDrawInContext:(CGContextRef)ctx {
    
    CGRect priceBgRect = CGRectZero;
    CGPoint point = self.point;

    NSDictionary *attribute = @{NSFontAttributeName:[UIFont upmarketui_digitalFontOfSize:kUPMarketUIMaskViewTextFont supportFontMode:self.supportedFontMode],NSForegroundColorAttributeName:[UIColor up_colorFromHexString:@"#222222"]};


    CGRect priceStrRect = [UPMarketUIDrawTool rectOfNSString:self.xLabelString attribute:attribute];

    priceBgRect = CGRectMake(point.x - (priceStrRect.size.width + 4) * 0.5, CGRectGetMaxY(self.bounds) - (priceStrRect.size.height + 1), priceStrRect.size.width + 4, priceStrRect.size.height +1);

    //priceBgRect 绘制不超出边界
    if (!CGRectContainsRect(self.bounds, priceBgRect)) {

        CGRect bounds = priceBgRect;
        if (priceBgRect.origin.x < CGRectGetMinX(self.bounds)) {
            bounds.origin.x = CGRectGetMinX(self.bounds) + 1;
            priceBgRect = bounds;
        } else if (priceBgRect.origin.x > CGRectGetMaxX(self.bounds) - CGRectGetWidth(priceBgRect)) {
            bounds.origin.x = CGRectGetMaxX(self.bounds) - CGRectGetWidth(priceBgRect) - 1;
            priceBgRect = bounds;
        }
    }

    CGRect priceRect = CGRectMake(priceBgRect.origin.x + 2, priceBgRect.origin.y - 1, priceStrRect.size.width, priceStrRect.size.height);

    CGContextSetFillColorWithColor(ctx, [UIColor up_colorFromHexString:@"#DDDDDD"].CGColor);
    CGContextFillRect(ctx, priceBgRect);

    

    [self.xLabelString drawInRect:priceRect withAttributes:attribute];
    
    CGRect textRect = CGRectMake(CGRectGetMinX(priceBgRect), CGRectGetMinY(priceBgRect) - 20, CGRectGetWidth(priceBgRect) +3, CGRectGetHeight(priceBgRect));
    CGContextSetFillColorWithColor(ctx, [UIColor up_brandColor].CGColor);
    CGContextFillRect(ctx, textRect);
    
    NSDictionary *attribute1 = @{NSFontAttributeName:[UIFont upmarketui_digitalFontOfSize:kUPMarketUIMaskViewTextFont supportFontMode:self.supportedFontMode],NSForegroundColorAttributeName:[UIColor upmarketui_crossTextColor]};

    [@"查看历史分时" drawInRect:textRect withAttributes:attribute1];
    
    self.xLabelRect = textRect;
    
    return textRect;
};




@end
